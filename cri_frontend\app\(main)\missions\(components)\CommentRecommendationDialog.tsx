import { <PERSON><PERSON> } from "primereact/button";
import { Column, ColumnBodyOptions, ColumnEditorOptions, ColumnEvent } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Dialog } from "primereact/dialog";
import { Action, CriStructview, CommentSerializer } from '@/services/schemas';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { InputText } from "primereact/inputtext";
import {  Stack, Typography } from "@mui/material";
import { getApiCommentUpdateMutationFetcher, getApiCommentUpdateMutationKey, useApiCommentCreate, useApiCommentDestroy, useApiCommentPartialUpdate, useApiCommentsCreate, useApiCommentUpdate } from "@/services/api/api/api";
import { getCookie } from "cookies-next";
import { ConfirmPopup, confirmPopup } from "primereact/confirmpopup";
import React from "react";
import { Chip } from "primereact/chip";
import axios from "axios";

interface CommentRecommendationDialogProps {
    recommendation_id: number
    recommendation_comments: Partial<CommentSerializer>[],
    visible: boolean
    setVisible: Dispatch<SetStateAction<boolean>>
}

const CommentRecommendationDialog = (props: CommentRecommendationDialogProps) => {

    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    const { recommendation_id, recommendation_comments, visible, setVisible } = props
    const [comments, setComments] = useState([])
    const [commentID, setCommentID] = useState(0)
    const dataTableRef = useRef<any>(null)

    const { data, trigger: trigger_create, error } = useApiCommentCreate({
        axios: { headers: { Authorization: `Token ${user?.token}` } }, swr: {
            revalidate: true,
            populateCache: true,
        }
    })

    const { data: data_update, trigger: trigger_update, error: error_udate, swrKey: swrKeyUpdate } = useApiCommentUpdate(commentID, {
        axios: { headers: { Authorization: `Token ${user?.token}` } }, swr: {
            revalidate: true,
            populateCache: true,
        }
    })

    const { data: data_delete, trigger: trigger_delete, error: error_delete } = useApiCommentDestroy(commentID, {
        axios: { headers: { Authorization: `Token ${user?.token}` } }, swr: {
            revalidate: true,
            populateCache: true,
        }
    })

    const textEditor = (options: ColumnEditorOptions) => {
        console.log("##################################", options)
        return <textarea className="w-full" rows={4} value={options.value} onChange={(e) => options.editorCallback(e.target.value)} onKeyDown={(e) => e.stopPropagation()} />;
    }

    const onRowEditComplete = async (e) => {
        let _comments = [...comments!];

        let { newData, index } = e;
        setCommentID(newData.id)
        console.log(newData)
        _comments[index] = newData;
        setComments(_comments)
        if (newData.id)
            trigger_update(newData, {

                revalidate: true,
                populateCache: true,
                onSuccess: () => {
                    //   toast.current?.show({ severity: 'info', summary: 'Création', detail: 'Enregistrement créé' });
                    alert('updated')
                },
                onError: (err) => {
                    //   toast.current?.show({ severity: 'error', summary: 'Création', detail: `${err.response?.statusText}` });
                    alert(err.response)

                },
            })
        else trigger_create(newData, {

            revalidate: true,
            populateCache: true,

            onSuccess: () => {
                //   toast.current?.show({ severity: 'info', summary: 'Création', detail: 'Enregistrement créé' });
                alert('created')
            },
            onError: (err) => {
                //   toast.current?.show({ severity: 'error', summary: 'Création', detail: `${err.response?.statusText}` });
                alert(err.response)

            },
        })


    }

    const onCellEditComplete = (e: ColumnEvent) => {
        let { rowData, newValue, field, originalEvent: event } = e;

        rowData[field] = newValue;

        console.log("settings comment", dataTableRef.current?.props.value!)
        const recommendation_comments_ = [...dataTableRef.current?.props.value!]
        // action_comments_.forEach((cmt)=> cmt.action = action_id)
        setComments(recommendation_comments_)

    }

    const dialogFooter = () => (
        <Stack className="w-full" justifyContent={'end'} alignContent={'center'} alignItems={'center'} direction={'row'} spacing={1}>
            <Typography variant="h5">Enregister les mises à jour : </Typography>
            <Button severity="warning" label="Non" icon="pi pi-times" onClick={() => setVisible(false)} className="p-button-text" />
            <Button severity="success" label="Oui" icon="pi pi-check" onClick={() => { save_comments(); }} autoFocus />
        </Stack>
    )

    const dialogHeader = () => (
        <Stack className="w-full" justifyContent={'space-between'} alignItems={'center'} direction={'row'} spacing={10}>
            <Typography variant="h5">Commentaires de la recommandation n° {recommendation_id}</Typography>
            <Button severity='info' icon='pi pi-plus' label='Ajouter un commentaire' onClick={insert_comment}></Button>
        </Stack>
    )

    const insert_comment = () => {
        const newComment = {
            comment: 'Saisir le commentaire',
            action: null,
            recommendation: recommendation_id,
            created: new Date()
        }
        const comments_ = [newComment, ...comments]
        // action_comments_.push(newComment)
        console.log(comments_, comments)
        setComments([...comments_])


    }

    const save_comments = async () => {
        //Create comment

        trigger_create(comments.filter((cmt) => { return !cmt.id }), {
            revalidate: true,

            onSuccess: () => {
                //   toast.current?.show({ severity: 'info', summary: 'Création', detail: 'Enregistrement créé' });
                alert('saved')
            },
            onError: (err) => {
                //   toast.current?.show({ severity: 'error', summary: 'Création', detail: `${err.response?.statusText}` });
                alert(err.response)

            },
        })

        //Update comment        
        comments.filter((cmt) => { return cmt.id }).forEach(async (comment) => {

            trigger_update(comment,
                {
                    revalidate: true,
                    // fetcher:await  getApiCommentUpdateMutationFetcher(comment.id,{url:getApiCommentUpdateMutationKey(comment.id)}),
                    onSuccess: () => {
                        //   toast.current?.show({ severity: 'info', summary: 'Création', detail: 'Enregistrement créé' });
                        alert('saved')
                    },
                    onError: (err) => {
                        //   toast.current?.show({ severity: 'error', summary: 'Création', detail: `${err.response?.statusText}` });
                        alert(err.response)

                    },
                })
        })
    }

    function delete_comment(id: any) {
        console.log('Comment : ---------> ', id)
        setCommentID(curr => { console.log(curr); return id })

        trigger_delete({
        }, {

            revalidate: true,

            onSuccess: () => {
                //   toast.current?.show({ severity: 'info', summary: 'Création', detail: 'Enregistrement créé' });
                alert('deleted')
            },
            onError: (err) => {
                //   toast.current?.show({ severity: 'error', summary: 'Création', detail: `${err.response?.statusText}` });
                alert(err.response)

            },
        })
        const comments_ = [...comments]

        setComments(comments_.filter((cmt: CommentSerializer) => cmt.id !== id))
    }

    const accept = (id) => {
        delete_comment(id)
        // toast.current.show({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 });
    }

    const reject = () => {
        // toast.current.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
    }

    const confirm1 = (event, id) => {
        confirmPopup({
            target: event.currentTarget,
            message: `Are you sure you want to delete comment ${id}?`,
            icon: 'pi pi-exclamation-triangle',
            defaultFocus: 'accept',
            accept: () => accept(id),
            reject
        });
    }

    const actionBodyTemplate = (rowData: any, options: ColumnBodyOptions) => {
        return (
            <React.Fragment>
                {!options.rowEditor?.editing && <Button icon="pi pi-pencil" rounded outlined severity="info" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.element?.props.onClick(e) }} />}
                {options.rowEditor?.editing && <Button icon="pi pi-save" rounded outlined severity="success" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.onSaveClick(e) }} />}
                {options.rowEditor?.editing && <Button icon="pi pi-times" rounded outlined severity="info" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.onCancelClick(e) }} />}
                {!options.rowEditor?.editing && <Button icon="pi pi-trash" rounded outlined severity="danger" onClick={(e) => { setCommentID(rowData.id); setCommentID(rowData.id); confirm1(e, rowData.id) }} />}
            </React.Fragment>
        );
    }
    const actionMeraciBodyTemplate = (rowData: any, options: ColumnBodyOptions) => {
        
        return (
              rowData.type ==='Non Retenue'  ? <Stack className='flex gap-2 justify-content-center'>
            <Stack direction={'row'} className='flex gap-2 justify-content-center'>
                <Button icon="pi pi-check" rounded outlined severity="success" onClick={(e) => {
                    setCommentID(rowData.id);
                    trigger_update({ validMeraci: 'Validé',comment:rowData.comment,type:rowData.type }, { fetcher: async (key: string, options) => { return await axios.patch(`http://************:3001/api/comment/${rowData.id}/`,{ validMeraci: 'Validé',comment:rowData.comment,type:rowData.type }, options) } })
                }} />
                <Button icon="pi pi-times" rounded outlined severity="danger" onClick={(e) => {
                    setCommentID(rowData.id);
                    trigger_update({ validMeraci: 'Non Validé',comment:rowData.comment,type:rowData.type}, { fetcher: async (key: string, options) => { return await axios.patch(`http://************:3001/api/comment/${rowData.id}/`,{ validMeraci: 'Non Validé',comment:rowData.comment,type:rowData.type}, options) } })
                }} />
            </Stack>
            <div><Chip className={`w-full ${rowData.validMeraci ==='Validé' ? 'bg-green-300'  :'bg-red-300' } text-lg border-1 border-dashed font-bold`} 
            label={rowData.validMeraci } 
            icon={rowData.validMeraci ==='Validé' ? 'pi pi-check text-green-600 text-xl'  : 'pi pi-times text-red-600 text-xl' }></Chip></div>
            </Stack> : '/'
        );
    }
    const actionDirecteurBodyTemplate = (rowData: any, options: ColumnBodyOptions) => {
        console.log(rowData)
        return (
            rowData.type ==='Non Retenue'  ? <Stack className='flex gap-2 justify-content-center'>
            <Stack direction={'row'} className='flex gap-2 justify-content-center'>
                <Button icon="pi pi-check" rounded outlined severity="success" onClick={(e) => {
                    setCommentID(rowData.id);
                    // trigger_update({ validDirecteur: 'Validé' }, { fetcher: async (key: string, options) => { return await axios.patch(`http://************:3001/api/comment/${rowData.id}/`,{ validDirecteur: 'Validé' }, options.args) } })
                }} />
                <Button icon="pi pi-times" rounded outlined severity="danger" onClick={(e) => {
                    setCommentID(rowData.id);
                    // trigger_update({ validDirecteur: 'Non Validé' }, { fetcher: async (key: string, options) => { return await axios.patch(`http://************:3001/api/comment/${rowData.id}/`,{ validDirecteur: 'Non Validé' }, options.args) } })
                }} />
            </Stack>
            <div><Chip className={`w-full ${rowData.validDirecteur ==='Validé' ? 'bg-green-300'  :'bg-red-300' } text-lg border-1 border-dashed font-bold`} 
            label={rowData.validDirecteur} 
            icon={rowData.validDirecteur ==='Validé' ? 'pi pi-check text-green-600 text-xl'  : 'pi pi-times text-red-600 text-xl' }></Chip></div>
            </Stack> :'/'
        );
    }
    useEffect(() => { console.log("useeffect dialog", recommendation_comments); setComments(recommendation_comments) }, [recommendation_comments])
    //update
    // useEffect(() => { console.log(action_comments); setComments(action_comments) }, [commentID])
    return (
        <Dialog
            modal={false} maximizable resizable header={dialogHeader} visible={visible} style={{ width: '60vw' }} onHide={() => { if (!visible) return; setVisible(false); }}>

            <DataTable<Partial<CommentSerializer>[]> onRowEditComplete={onRowEditComplete} ref={dataTableRef} editMode="row" value={comments} rows={10} paginator stripedRows scrollable resizableColumns>
                <Column align={'left'} field={'comment'} onCellEditComplete={onCellEditComplete} editor={textEditor} body={(data) => data.comment} header={'Commentaire'} sortable style={{ width: '70%' }} />
                <Column align={'center'} field={'created'} body={(data) => new Date(data.created).toLocaleString('fr')} header={'Date'} sortable style={{ width: '12%' }} />
                <Column align={'center'} field={'created_by'} body={(data) => data.created_by ? `${data.created_by?.last_name} ${data.created_by?.first_name}` : ''} header={'Utilisateur'} sortable style={{ width: '12%' }} />
                <Column align={'center'} field={'type'} body={(data)=> <Chip label={data.type}></Chip>} header={'Statut'} sortable style={{ width: '12%' }} />
                <Column
                    header="Validation MERACI"
                    align={'center'}
                    style={{ width: '10%' }}
                    body={actionMeraciBodyTemplate}
                />
                <Column
                    header="Validation DIRECTEUR"
                    align={'center'}
                    style={{ width: '10%' }}
                    body={actionDirecteurBodyTemplate}
                />
                <Column rowEditor={true} header={'Action'} sortableDisabled={true} field={"action"}
                    body={actionBodyTemplate} sortable style={{ width: '6%' }} />
            </DataTable>
            <ConfirmPopup />
            <span>{commentID}</span>

        </Dialog>
    )
}

export default CommentRecommendationDialog