import { SignJWT, jwtVerify } from 'jose'
import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'
import { prisma } from './prisma'
import bcrypt from 'bcryptjs'

console.log('🔍 Initializing Custom Auth...')
console.log('🔍 Database URL:', process.env.DATABASE_URL ? 'Set' : 'Missing')
console.log('🔍 Auth Secret:', process.env.AUTH_SECRET ? 'Set' : 'Missing')

const secret = new TextEncoder().encode(
  process.env.AUTH_SECRET || 'fallback-secret-key-change-in-production'
)

export interface User {
  id: string
  email: string
  username: string
  name: string
  isStaff: boolean
  isSuperuser: boolean
  isActive: boolean
}

export interface Session {
  user: User
  expires: string
}

// Create JWT token
export async function createToken(user: User): Promise<string> {
  return await new SignJWT({ user })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(secret)
}

// Verify JWT token
export async function verifyToken(token: string): Promise<{ user: User } | null> {
  try {
    const { payload } = await jwtVerify(token, secret)
    return payload as { user: User }
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}

// Get session from request
export async function getSession(request?: NextRequest): Promise<Session | null> {
  try {
    let token: string | undefined

    if (request) {
      // For middleware/server components
      token = request.cookies.get('auth-token')?.value
    } else {
      // For API routes
      const cookieStore = cookies()
      token = cookieStore.get('auth-token')?.value
    }

    if (!token) {
      return null
    }

    const payload = await verifyToken(token)
    if (!payload) {
      return null
    }

    return {
      user: payload.user,
      expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
    }
  } catch (error) {
    console.error('Get session error:', error)
    return null
  }
}

// Authenticate user
export async function authenticate(email: string, password: string): Promise<User | null> {
  try {
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user || !user.isActive) {
      return null
    }

    // Find account with password
    const account = await prisma.account.findFirst({
      where: {
        userId: user.id,
        providerId: "credential"
      }
    })

    if (!account?.password) {
      return null
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, account.password)
    
    if (!isValidPassword) {
      return null
    }

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() }
    })

    return {
      id: user.id,
      email: user.email,
      username: user.username,
      name: user.username,
      isStaff: user.isStaff,
      isSuperuser: user.isSuperuser,
      isActive: user.isActive,
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}

// For middleware compatibility
export async function auth(request: NextRequest): Promise<Session | null> {
  return getSession(request)
}

console.log('✅ Custom Auth configured successfully')
