"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/opportunity_sheet/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx":
/*!*************************************************************************!*\
  !*** ./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTableOpportunitySheet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-client)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_dialog__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/dialog */ \"(app-client)/./node_modules/primereact/dialog/dialog.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _editForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./editForm */ \"(app-client)/./app/(main)/themes/(components)/editForm.tsx\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _app_ability__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ability */ \"(app-client)/./app/ability.ts\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-client)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"(app-client)/./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTableOpportunitySheet(data_) {\n    var _getCookie;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_6__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const [theme_id, setThemeId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const { push } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeCreate)();\n    const { data: data_modify, error: error_modify, isPending: isMutating_modify, mutate: trigger_modify } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeUpdate)();\n    const { data: data_delete, error: error_delete, isPending: isMutating_delete, mutate: trigger_delete } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeDestroy)();\n    const [detailsDialogVisible, setDetailsDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(\"PAGINATION\", data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const getSeverity = (str)=>{\n        switch(str){\n            case \"Vice Pr\\xe9sident\":\n                return \"success\";\n            case \"Contr\\xf4le Interne\":\n                return \"warning\";\n            case \"Audit Interne\":\n                return \"warning\";\n            case \"Structures\":\n                return \"danger\";\n            default:\n                return null;\n        }\n    };\n    const accept = ()=>{\n        trigger_delete({}, {\n            onSuccess: ()=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"Enregistrement supprim\\xe9\"\n                });\n            },\n            onError: (error)=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"\".concat(error.code)\n                });\n            }\n        });\n    };\n    const reject = ()=>{\n        toast.current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        console.log(\"theme_id\", theme_id);\n    }, [\n        theme_id\n    ]);\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"risks\",\n                \"goals\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__.Editor, {\n                            onChange: (e)=>{\n                                row._valuesCache.content = e.target.getContent();\n                            },\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    accessorFn: (row)=>new Date(row[key]),\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        return new Date(cell.getValue()).toLocaleDateString(\"fr\");\n                    },\n                    id: key\n                };\n            }\n            if (key === \"concerned_structures\") {\n                var _data__data_type_properties_key_title2, _struct_code_mnemonique;\n                return {\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 1,\n                            children: cell.getValue().map((struct)=>{\n                                var _struct, _struct1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        backgroundColor: \"var(--pink-300)\",\n                                        color: \"black\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    label: (_struct_code_mnemonique = (_struct = struct) === null || _struct === void 0 ? void 0 : _struct.code_mnemonique) !== null && _struct_code_mnemonique !== void 0 ? _struct_code_mnemonique : (_struct1 = struct) === null || _struct1 === void 0 ? void 0 : _struct1.code_stru\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 186\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 84\n                        }, this) : \"/\";\n                    }\n                };\n            }\n            if (key === \"proposing_structures\") {\n                var _data__data_type_properties_key_title3, _struct_code_mnemonique1;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 1,\n                            children: cell.getValue().map((struct)=>{\n                                var _struct, _struct1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        backgroundColor: \"var(--cyan-300)\",\n                                        color: \"black\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    label: (_struct_code_mnemonique1 = (_struct = struct) === null || _struct === void 0 ? void 0 : _struct.code_mnemonique) !== null && _struct_code_mnemonique1 !== void 0 ? _struct_code_mnemonique1 : (_struct1 = struct) === null || _struct1 === void 0 ? void 0 : _struct1.code_stru\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 186\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 84\n                        }, this) : \"/\";\n                    }\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            severity: getSeverity(cell.getValue()),\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"validated\") {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    size: 120,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                            className: \"text-green-500\",\n                            size: \"2x\",\n                            icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCheckSquare\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 65\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                            className: \"text-red-500\",\n                            size: \"2x\",\n                            icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 145\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: cell.getValue() === \"Non lanc\\xe9e\" ? \"danger\" : cell.getValue() === \"En cours\" ? \"success\" : \"info\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"type\") {\n                var _data__data_type_properties_key_title7;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: cell.getValue() === \"Command\\xe9e\" ? \"danger\" : cell.getValue() === \"Planifi\\xe9e\" ? \"warning\" : cell.getValue() === \"AI\" ? \"info\" : \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title8;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"title\") {\n                var _data__data_type_properties_key_title9;\n                return {\n                    header: (_data__data_type_properties_key_title9 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title9 !== void 0 ? _data__data_type_properties_key_title9 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum , \n                    muiEditTextFieldProps: {\n                        // select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"white-space-normal\",\n                            children: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"domain\") {\n                var _data__data_type_properties_key_title10, _cell_getValue_title;\n                return {\n                    header: (_data__data_type_properties_key_title10 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title10 !== void 0 ? _data__data_type_properties_key_title10 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue_title = (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.title) !== null && _cell_getValue_title !== void 0 ? _cell_getValue_title : \"/\";\n                    }\n                };\n            }\n            if (key === \"process\") {\n                var _data__data_type_properties_key_title11, _cell_getValue_title1;\n                return {\n                    header: (_data__data_type_properties_key_title11 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title11 !== void 0 ? _data__data_type_properties_key_title11 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue_title1 = (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.title) !== null && _cell_getValue_title1 !== void 0 ? _cell_getValue_title1 : \"/\";\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title12, _data__data_type_properties_key_title13;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title12 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title12 !== void 0 ? _data__data_type_properties_key_title12 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title13 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title13 !== void 0 ? _data__data_type_properties_key_title13 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_15__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: false,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading,\n            //showProgressBars: isLoading, //progress bars while refetching\n            isSaving: isMutating_create\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_16__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                size: 140,\n                enableHiding: true,\n                grow: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiSvgIcon-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiInputBase-input\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiTableSortLabel-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        // editDisplayMode: 'modal',\n        // createDisplayMode: 'modal',\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            console.log(\"onEditingRowSave\", values);\n            const { created, modified, id, ...rest } = values;\n            setThemeId(id);\n            trigger_modify({\n                id: id,\n                data: rest\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    table.setEditingRow(null); //exit editing mode\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"success\",\n                        summary: \"Modification\",\n                        detail: \"Enregistrement modifi\\xe9\"\n                    });\n                },\n                onError: (error)=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        summary: \"Modification\",\n                        detail: \"\".concat(error.message)\n                    });\n                    console.log(\"onEditingRowSave\", error.message);\n                    row._valuesCache = {\n                        error: error.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            console.log(\"onCreatingRowSave\", values);\n            const { created, modified, id, ...rest } = values;\n            trigger_create(rest, {\n                revalidate: true,\n                populateCache: true,\n                onSuccess: ()=>{\n                    var _toast_current;\n                    table.setCreatingRow(null);\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"success\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                },\n                onError: (err)=>{\n                    var _err_response, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.statusText)\n                    });\n                    console.log(\"onCreatingRowSave\", err.response);\n                    row._valuesCache = {\n                        error: err.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                    },\n                    \"& .MuiDialog-container\": {\n                        //  display: 'none',\n                        \"& .MuiPaper-root\": {\n                            maxWidth: \"60vw\"\n                        }\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"add\",\n                        a: \"theme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"delete\",\n                        a: \"theme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"add\",\n                        a: \"arbitratedtheme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            icon: \"pi pi-bell\",\n                            rounded: true,\n                            color: rowActionEnabled ? \"secondary\" : \"primary\",\n                            size: \"small\",\n                            \"aria-label\": \"edit\",\n                            onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 700,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 699,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 670,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        renderEditRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        // renderDetailPanel: ({ row }) =>\n        //   row.original.staff ?\n        //     (\n        //       <Box\n        //         sx={{\n        //           display: 'grid',\n        //           margin: 'auto',\n        //           //gridTemplateColumns: '1fr 1fr',\n        //           width: '100vw',\n        //         }}\n        //       >\n        //         <TabView>\n        //           <TabPanel header={data_.data_type.properties[\"staff\"].title} leftIcon=\"pi pi-user mr-2\">\n        //             <ul>{row.original.staff.staff.map((user, idx) => <a key={user.email + row.original.code} href={\"mailto:\" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>\n        //           </TabPanel>\n        //           <TabPanel header={\"Assistants\"} rightIcon=\"pi pi-user ml-2\">\n        //             <ul>{row.original.staff.assistants.map((user, idx) => <a key={user.email + row.original.code} href={\"mailto:\" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>\n        //           </TabPanel>\n        //           <TabPanel header=\"Lettre\" leftIcon=\"pi pi-file-word mr-2\" rightIcon=\"pi pi-file-pdf ml-2\">\n        //             <Button icon=\"pi pi-check\" rounded onClick={() => setVisible(true)} disabled={row.original.document === null} />\n        //             <Sidebar key={row.original.id} header={<h2>Lettre de mission : {row.original.code}</h2>} visible={visible} onHide={() => setVisible(false)} className=\"w-full md:w-9 lg:w-8\">\n        //               <div className=\"flex flex-column\talign-items-center justify-content-center gap-1\">\n        //                 {row.original.document !== null ?\n        //                   <Document\n        //                     file={row.original.document}\n        //                     onLoadSuccess={onDocumentLoadSuccess}\n        //                   >\n        //                     <Page pageNumber={pageNumber} />\n        //                   </Document> : <p>No Document</p>}\n        //                 <div className='flex flex-column\talign-items-center justify-content-center gap-1' >\n        //                   <p>\n        //                     Page {pageNumber || (numPages ? 1 : '--')} of {numPages || '--'}\n        //                   </p>\n        //                   <div className='flex flex-row\talign-items-center justify-content-center gap-1' >\n        //                     <Button\n        //                       type=\"button\"\n        //                       disabled={pageNumber <= 1}\n        //                       onClick={previousPage}\n        //                     >\n        //                       Previous\n        //                     </Button>\n        //                     <Button\n        //                       type=\"button\"\n        //                       disabled={pageNumber >= numPages}\n        //                       onClick={nextPage}\n        //                     >\n        //                       Next\n        //                     </Button>\n        //                   </div>\n        //                 </div>\n        //               </div>\n        //             </Sidebar>\n        //           </TabPanel>          </TabView>\n        //       </Box >\n        //     ) : <></>,\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-eye\",\n                        onClick: ()=>{\n                            setThemeId(row.original.id);\n                            setDetailsDialogVisible(true);\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 777,\n                        columnNumber: 9\n                    }, this),\n                    _app_ability__WEBPACK_IMPORTED_MODULE_7__[\"default\"].can(\"change\", \"theme\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            setThemeId(row.original.id);\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 9\n                    }, this),\n                    _app_ability__WEBPACK_IMPORTED_MODULE_7__[\"default\"].can(\"delete\", \"theme\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>{\n                            setThemeId(row.original.id);\n                            (0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            });\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 781,\n                        columnNumber: 43\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 796,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 776,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_15__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 808,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_19__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 809,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dialog__WEBPACK_IMPORTED_MODULE_20__.Dialog, {\n                maximizable: true,\n                header: \"THEME ID : \".concat(theme_id),\n                visible: detailsDialogVisible,\n                style: {\n                    width: \"90vw\"\n                },\n                onHide: ()=>{\n                    if (!detailsDialogVisible) return;\n                    setDetailsDialogVisible(false);\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 810,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTableOpportunitySheet, \"Q0KDj6Olwz2kzfOmmcPt2mUIdjY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeUpdate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeDestroy,\n        material_react_table__WEBPACK_IMPORTED_MODULE_15__.useMaterialReactTable\n    ];\n});\n_c = GenericTableOpportunitySheet;\nvar _c;\n$RefreshReg$(_c, \"GenericTableOpportunitySheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx\n"));

/***/ })

});