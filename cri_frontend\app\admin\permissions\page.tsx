'use client'

import React, { useState, useEffect, useRef } from 'react'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'
import { InputText } from 'primereact/inputtext'
import { Dialog } from 'primereact/dialog'
import { Dropdown } from 'primereact/dropdown'
import { Toast } from 'primereact/toast'
import { Toolbar } from 'primereact/toolbar'
import { Tag } from 'primereact/tag'
import { FilterMatchMode } from 'primereact/api'
import { Can } from '@/app/Can'

interface Permission {
  id: string
  action: string
  subject: string
  conditions?: any
  fields?: string[]
  createdAt: Date
  updatedAt: Date
  roleCount?: number
}

const actionOptions = [
  { label: 'Create', value: 'create' },
  { label: 'Read', value: 'read' },
  { label: 'Update', value: 'update' },
  { label: 'Delete', value: 'delete' },
  { label: 'Manage', value: 'manage' },
  { label: 'View', value: 'view' },
  { label: 'Edit', value: 'edit' },
  { label: 'Add', value: 'add' },
  { label: 'Change', value: 'change' }
]

const subjectOptions = [
  { label: 'User', value: 'User' },
  { label: 'Mission', value: 'Mission' },
  { label: 'Recommendation', value: 'Recommendation' },
  { label: 'Plan', value: 'Plan' },
  { label: 'Theme', value: 'Theme' },
  { label: 'Domain', value: 'Domain' },
  { label: 'Process', value: 'Process' },
  { label: 'Action', value: 'Action' },
  { label: 'Comment', value: 'Comment' },
  { label: 'Document', value: 'Document' },
  { label: 'MissionDocument', value: 'MissionDocument' },
  { label: 'Arbitration', value: 'Arbitration' },
  { label: 'Constat', value: 'Constat' }
]

export default function PermissionsPage() {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [globalFilter, setGlobalFilter] = useState('')
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null)
  const [permissionDialog, setPermissionDialog] = useState(false)
  const [deletePermissionDialog, setDeletePermissionDialog] = useState(false)
  const [permission, setPermission] = useState<Partial<Permission>>({})
  const toast = useRef<Toast>(null)

  useEffect(() => {
    fetchPermissions()
  }, [])

  const fetchPermissions = async () => {
    try {
      setLoading(true)
      // Mock data for now - replace with actual API call
      setTimeout(() => {
        setPermissions([
          {
            id: '1',
            action: 'create',
            subject: 'Mission',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            roleCount: 3
          },
          {
            id: '2',
            action: 'read',
            subject: 'Mission',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            roleCount: 5
          },
          {
            id: '3',
            action: 'update',
            subject: 'Mission',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            roleCount: 3
          },
          {
            id: '4',
            action: 'delete',
            subject: 'Mission',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            roleCount: 2
          },
          {
            id: '5',
            action: 'create',
            subject: 'Recommendation',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            roleCount: 3
          },
          {
            id: '6',
            action: 'read',
            subject: 'Recommendation',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            roleCount: 5
          }
        ])
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error fetching permissions:', error)
      setLoading(false)
    }
  }

  const openNew = () => {
    setPermission({})
    setPermissionDialog(true)
  }

  const hideDialog = () => {
    setPermissionDialog(false)
  }

  const hideDeletePermissionDialog = () => {
    setDeletePermissionDialog(false)
  }

  const savePermission = () => {
    // Save permission logic here
    toast.current?.show({ severity: 'success', summary: 'Successful', detail: 'Permission saved', life: 3000 })
    setPermissionDialog(false)
    setPermission({})
  }

  const editPermission = (permission: Permission) => {
    setPermission({ ...permission })
    setPermissionDialog(true)
  }

  const confirmDeletePermission = (permission: Permission) => {
    setSelectedPermission(permission)
    setDeletePermissionDialog(true)
  }

  const deletePermission = () => {
    // Delete permission logic here
    toast.current?.show({ severity: 'success', summary: 'Successful', detail: 'Permission deleted', life: 3000 })
    setDeletePermissionDialog(false)
    setSelectedPermission(null)
  }

  const onInputChange = (e: any, name: string) => {
    const val = (e.target && e.target.value) || ''
    setPermission(prev => ({ ...prev, [name]: val }))
  }

  const onDropdownChange = (e: any, name: string) => {
    setPermission(prev => ({ ...prev, [name]: e.value }))
  }

  const leftToolbarTemplate = () => {
    return (
      <div className="flex flex-wrap gap-2">
        <Can I="create" a="Permission">
          <Button label="New Permission" icon="pi pi-plus" severity="success" onClick={openNew} />
        </Can>
      </div>
    )
  }

  const rightToolbarTemplate = () => {
    return (
      <div className="flex align-items-center gap-2">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            type="search"
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search permissions..."
          />
        </span>
      </div>
    )
  }

  const actionBodyTemplate = (rowData: Permission) => {
    return (
      <div className="flex gap-2">
        <Can I="update" a="Permission">
          <Button
            icon="pi pi-pencil"
            rounded
            outlined
            className="mr-2"
            onClick={() => editPermission(rowData)}
          />
        </Can>
        <Can I="delete" a="Permission">
          <Button
            icon="pi pi-trash"
            rounded
            outlined
            severity="danger"
            onClick={() => confirmDeletePermission(rowData)}
          />
        </Can>
      </div>
    )
  }

  const actionTagTemplate = (rowData: Permission) => {
    const getSeverity = (action: string) => {
      switch (action) {
        case 'create': return 'success'
        case 'read': return 'info'
        case 'update': return 'warning'
        case 'delete': return 'danger'
        case 'manage': return 'contrast'
        default: return 'secondary'
      }
    }

    return <Tag value={rowData.action} severity={getSeverity(rowData.action)} />
  }

  const subjectTagTemplate = (rowData: Permission) => {
    return <Tag value={rowData.subject} severity="secondary" />
  }

  const roleCountTemplate = (rowData: Permission) => {
    return <span className="font-semibold">{rowData.roleCount || 0}</span>
  }

  const dateBodyTemplate = (rowData: Permission) => {
    return new Date(rowData.createdAt).toLocaleDateString()
  }

  const permissionDialogFooter = (
    <div>
      <Button label="Cancel" icon="pi pi-times" outlined onClick={hideDialog} />
      <Button label="Save" icon="pi pi-check" onClick={savePermission} />
    </div>
  )

  const deletePermissionDialogFooter = (
    <div>
      <Button label="No" icon="pi pi-times" outlined onClick={hideDeletePermissionDialog} />
      <Button label="Yes" icon="pi pi-check" severity="danger" onClick={deletePermission} />
    </div>
  )

  return (
    <div className="card">
      <Toast ref={toast} />
      
      <Toolbar className="mb-4" left={leftToolbarTemplate} right={rightToolbarTemplate} />

      <DataTable
        value={permissions}
        selection={selectedPermission}
        onSelectionChange={(e) => setSelectedPermission(e.value)}
        dataKey="id"
        paginator
        rows={10}
        rowsPerPageOptions={[5, 10, 25]}
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} permissions"
        globalFilter={globalFilter}
        loading={loading}
        header="Permissions Management"
        responsiveLayout="scroll"
      >
        <Column field="action" header="Action" body={actionTagTemplate} sortable filter filterPlaceholder="Search by action" />
        <Column field="subject" header="Subject" body={subjectTagTemplate} sortable filter filterPlaceholder="Search by subject" />
        <Column field="roleCount" header="Used by Roles" body={roleCountTemplate} sortable />
        <Column field="createdAt" header="Created" body={dateBodyTemplate} sortable />
        <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '12rem' }} />
      </DataTable>

      <Dialog
        visible={permissionDialog}
        style={{ width: '450px' }}
        header="Permission Details"
        modal
        className="p-fluid"
        footer={permissionDialogFooter}
        onHide={hideDialog}
      >
        <div className="field">
          <label htmlFor="action">Action</label>
          <Dropdown
            id="action"
            value={permission.action}
            onChange={(e) => onDropdownChange(e, 'action')}
            options={actionOptions}
            placeholder="Select an action"
            required
            autoFocus
          />
        </div>
        <div className="field">
          <label htmlFor="subject">Subject</label>
          <Dropdown
            id="subject"
            value={permission.subject}
            onChange={(e) => onDropdownChange(e, 'subject')}
            options={subjectOptions}
            placeholder="Select a subject"
            required
          />
        </div>
      </Dialog>

      <Dialog
        visible={deletePermissionDialog}
        style={{ width: '450px' }}
        header="Confirm"
        modal
        footer={deletePermissionDialogFooter}
        onHide={hideDeletePermissionDialog}
      >
        <div className="confirmation-content">
          <i className="pi pi-exclamation-triangle mr-3" style={{ fontSize: '2rem' }} />
          {selectedPermission && (
            <span>
              Are you sure you want to delete permission <b>{selectedPermission.action} {selectedPermission.subject}</b>?
            </span>
          )}
        </div>
      </Dialog>
    </div>
  )
}
