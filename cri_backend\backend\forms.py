from django import forms
from django.forms import ModelForm
from django.contrib.admin.widgets import *
from .models import *
from django.contrib import admin
# from ajax_select.fields import AutoCompleteSelectField, AutoCompleteSelectMultipleField
# from dal import autocomplete

from tinymce import widgets as tinymce_widgets


###############################################################################
class ThemeForm(ModelForm):
    def __init__(self, *args, **kwargs):

        r = super(ThemeForm, self).__init__(
            *args, **kwargs)
        self.fields['proposing_structures'] = \
            forms.ModelMultipleChoiceField(
                required=False,
                queryset=CriStructview.objects.all(),
                widget=RelatedFieldWidgetWrapper(
                    FilteredSelectMultiple('Structures Proposantes', is_stacked=False),
                    rel=Theme._meta.get_field("proposing_structures").remote_field,
            admin_site=admin.site,
            can_add_related=False,
                    ),
                label='Structures Proposantes'
                )
        self.fields['concerned_structures'] = \
            forms.ModelMultipleChoiceField(
                required=False,
                queryset=CriStructview.objects.all(),
                widget=RelatedFieldWidgetWrapper(
                    FilteredSelectMultiple('Structures concernéee', is_stacked=False),
                    rel=Theme._meta.get_field("concerned_structures").remote_field,
            admin_site=admin.site,
            can_add_related=False,
                    ),
                label='Structures concernéee'
                )
        self.fields['risks'] = \
            forms.ModelMultipleChoiceField(required=False,
                queryset=Risk.objects.all(),
                widget=RelatedFieldWidgetWrapper(FilteredSelectMultiple(
                    'Risques', is_stacked=False),
                    rel=Theme._meta.get_field("risks").remote_field,
                    admin_site=admin.site,
                    can_add_related=True,
                    ),
                label='Risques')
        self.fields['goals'] = \
            forms.ModelMultipleChoiceField(required=False,
                queryset=Goal.objects.all(),
                widget=RelatedFieldWidgetWrapper(
                FilteredSelectMultiple(
                    'Objectifs', is_stacked=False), rel=Theme._meta.get_field("goals").remote_field,
            admin_site=admin.site,
            can_add_related=True,
                    ),
                label='Objectifs')

        return r
    proposing_structures = forms.ModelMultipleChoiceField(required=False,label='Structures Proposantes',queryset=CriStructview.objects.all(), widget=FilteredSelectMultiple("Structures proposantes", is_stacked=False))
    concerned_structures = forms.ModelMultipleChoiceField(required=False,label='Structures concernéee',queryset=CriStructview.objects.all(), widget=FilteredSelectMultiple("Structures concernéee", is_stacked=False))
    risks = forms.ModelMultipleChoiceField(label='Risques',queryset=Risk.objects.all(), widget=FilteredSelectMultiple("Risques", is_stacked=False))
    goals = forms.ModelMultipleChoiceField(label='Objectifs',queryset=Goal.objects.all(), widget=FilteredSelectMultiple("Objectifs", is_stacked=False))


    class Meta:
        model = Theme
        fields = [field.name for field in model._meta.fields if field.name not in["id","created","modified","created_by","modified_by"]]
    class Media:
        extend = False
        css = {
            'all': [
                'admin/css/widgets.css'
            ]
        }
        js = (
            'js/django_global.js',
            '/admin/jsi18n/',
            'admin/js/jquery.init.js',
            'admin/js/core.js',
            'admin/js/prepopulate_init.js',
            'admin/js/prepopulate.js',
            'admin/js/SelectBox.js',
            'admin/js/SelectFilter2.js',
            'admin/js/admin/RelatedObjectLookups.js',
        )  

class MissionForm(ModelForm):
    def __init__(self, *args, **kwargs):
        r = super(MissionForm, self).__init__(
            *args, **kwargs)
        self.fields['staff'] = \
            forms.ModelMultipleChoiceField(
                queryset=User.objects.all(),
                widget=RelatedFieldWidgetWrapper(
                    FilteredSelectMultiple(
                    "Equipe CI/AI", is_stacked=False),
                     rel=Mission._meta.get_field("staff").remote_field,
            admin_site=admin.site,
            can_add_related=False,),
                label='Staff')
        self.fields['assistants'] = \
            forms.ModelMultipleChoiceField(required=False,
                queryset=User.objects.all(),
                widget=RelatedFieldWidgetWrapper(                    
                    FilteredSelectMultiple(
                    "Assistants", is_stacked=False),
                    rel=Mission._meta.get_field("assistants").remote_field,
            admin_site=admin.site,
            can_add_related=False,
                    ),
                label='Assistants')
        return r
    staff = forms.ModelMultipleChoiceField(queryset=User.objects.all(), widget=FilteredSelectMultiple("Staff", is_stacked=False))
    assistants = forms.ModelMultipleChoiceField(queryset=User.objects.all(), widget=FilteredSelectMultiple("Assistants", is_stacked=False))

    class Meta:
        model = Mission
        fields = [field.name for field in model._meta.fields if field.name not in ["id","created","modified","created_by","modified_by"]]

    class Media:
        extend = True
        css = {
            'all': [
                'admin/css/widgets.css'
            ]
        }
        js = (
            'js/django_global.js',
            '/admin/jsi18n/',
            'admin/js/jquery.init.js',
            'admin/js/core.js',
            'admin/js/prepopulate_init.js',
            'admin/js/prepopulate.js',
            'admin/js/SelectBox.js',
            'admin/js/SelectFilter2.js',
            'admin/js/admin/RelatedObjectLookups.js',
        )  

class PlanForm(ModelForm):
    def __init__(self, *args, **kwargs):

        r = super(PlanForm, self).__init__(
            *args, **kwargs)
        # self.fields['themes'] = \
        #     forms.ModelMultipleChoiceField(
        #         queryset=Theme.objects.all(),
        #         widget=RelatedFieldWidgetWrapper(
        #             FilteredSelectMultiple(
        #             "Thèmes", is_stacked=False),
        #              rel=Plan._meta.get_field("themes").remote_field,
        #     admin_site=admin.site,
        #     can_add_related=True,),
        #         label='Thèmes')
        # self.fields['controlled_structures'] = \
        #     forms.ModelMultipleChoiceField(required=False,
        #         queryset=StructureLQS.objects.all(),
        #         widget=RelatedFieldWidgetWrapper(                    
        #             FilteredSelectMultiple(
        #             "Structures contrôlées", is_stacked=False),
        #             rel=Plan._meta.get_field("controlled_structures").remote_field,
        #     admin_site=admin.site,
        #     can_add_related=False,
        #             ),
        #         label='Structures contrôlées')
        return r
    # themes = forms.ModelMultipleChoiceField(queryset=Theme.objects.all(), widget=FilteredSelectMultiple("Thèmes", is_stacked=False))
    #controlled_structures = forms.ModelMultipleChoiceField(queryset=StructureLQS.objects.all(), widget=FilteredSelectMultiple("Structures contrôlées", is_stacked=False))
    


    class Meta:
        model = Plan
        fields = [field.name for field in model._meta.fields if field.name not in["id","created","modified","created_by","modified_by"]]
    class Media:
        extend = False
        css = {
            'all': [
                'admin/css/widgets.css'
            ]
        }
        js = (
            'js/django_global.js',
            '/admin/jsi18n/',
            'admin/js/jquery.init.js',
            'admin/js/core.js',
            'admin/js/prepopulate_init.js',
            'admin/js/prepopulate.js',
            'admin/js/SelectBox.js',
            'admin/js/SelectFilter2.js',
            'admin/js/admin/RelatedObjectLookups.js',
        )  

# class FRAPForm(ModelForm):
#     def __init__(self, *args, **kwargs):
#         r = super(FRAPForm, self).__init__(
#             *args, **kwargs)
#         # self.fields['causes'] = \
#         #     forms.ModelMultipleChoiceField(
#         #         queryset=Cause.objects.all(),
#         #         widget=RelatedFieldWidgetWrapper(
#         #             FilteredSelectMultiple(
#         #             "Causes", is_stacked=False),
#         #              rel=FRAP._meta.get_field("causes").remote_field,
#         #     admin_site=admin.site,
#         #     can_add_related=True,),
#         #         label='Causes')
#         # self.fields['consequences'] = \
#         #     forms.ModelMultipleChoiceField(
#         #         queryset=Consequence.objects.all(),
#         #         widget=RelatedFieldWidgetWrapper(
#         #             FilteredSelectMultiple(
#         #             "Conséquences", is_stacked=False),
#         #              rel=FRAP._meta.get_field("consequences").remote_field,
#         #     admin_site=admin.site,
#         #     can_add_related=True,),
#         #         label='Conséquences')
#         self.fields['recommendations'] = \
#             forms.ModelMultipleChoiceField(required=False,
#                 queryset=RecommendationRef.objects.all(),
#                 widget=RelatedFieldWidgetWrapper(                    
#                     FilteredSelectMultiple(
#                     "Recommendations", is_stacked=False),
#                     rel=FRAP._meta.get_field("recommendations").remote_field,
#             admin_site=admin.site,
#             can_add_related=True,
#                     ),
#                 label='Recommendations')
#         self.fields['authored_by'] = \
#             forms.ModelMultipleChoiceField(required=False,
#                 queryset=User.objects.all(),
#                 widget=RelatedFieldWidgetWrapper(                    
#                     FilteredSelectMultiple(
#                     "Rédigée par", is_stacked=False),
#                     rel=FRAP._meta.get_field("authored_by").remote_field,
#             admin_site=admin.site,
#             can_add_related=True,
#                     ),
#                 label='Rédigée par')
#         # print("vvvvvvvvvvv%s"%self.instance)
#         return r
#     # causes = forms.ModelMultipleChoiceField(queryset=Cause.objects.all(), widget=FilteredSelectMultiple("Causes", is_stacked=False))
#     # consequences = forms.ModelMultipleChoiceField(queryset=Consequence.objects.all(), widget=FilteredSelectMultiple("Conséquences", is_stacked=False))
#     recommendations = forms.ModelMultipleChoiceField(queryset=User.objects.all(), widget=FilteredSelectMultiple("Recommendations", is_stacked=False))
#     authored_by  = forms.ModelMultipleChoiceField(queryset=User.objects.all(), widget=FilteredSelectMultiple("Rédigée par", is_stacked=False))
#     class Meta:
#         model = FRAP
#         fields = [field.name for field in model._meta.fields if field.name not in ["id","created","modified","created_by","modified_by"]]
#         widgets = {'fact': tinymce_widgets.AdminTinyMCE(attrs={'cols': 80, 'rows': 30})}
#         # widgets = {
#         #     'created_by': autocomplete.ModelSelect2Multiple(url='user-autocomplete')
#         # }

#     class Media:
#         extend = True
#         css = {
#             'all': [
#                 'admin/css/widgets.css'
#             ]
#         }
#         js = (
#             'js/django_global.js',
#             '/admin/jsi18n/',
#             'admin/js/jquery.init.js',
#             'admin/js/core.js',
#             # 'admin/js/prepopulate_init.js',
#             # 'admin/js/prepopulate.js',           
#             'admin/js/admin/RelatedObjectLookups.js',
#             'admin/js/SelectBox.js',
#             'admin/js/SelectFilter2.js',
#         )  