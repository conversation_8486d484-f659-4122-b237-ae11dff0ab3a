"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx":
/*!*******************************************************************!*\
  !*** ./app/(main)/plans/(components)/GenericTAbleArbitration.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_editor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/editor */ \"(app-client)/./node_modules/primereact/editor/editor.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// import { Editor } from '@tinymce/tinymce-react';\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie, _users, _users1, _plans, _arbitrations;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: plans, isLoading: plans_isLoading, error: plans_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList)();\n    const { data: arbitrations, isLoading: arbitrations_isLoading, error: arbitrations_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList)();\n    const { data: users, isLoading: users_isLoading, error: users_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList)();\n    const users_data = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _users;\n        return ((_users = users) === null || _users === void 0 ? void 0 : _users.data.results) || [];\n    }, [\n        (_users = users) === null || _users === void 0 ? void 0 : _users.data\n    ]);\n    const [arbitrationID, setArbitrationID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [picklistTargetValueTeam, setPicklistTargetValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [picklistSourceValueTeam, setPicklistSourceValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(users_data);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { mutate: arbitration_create_trigger, isPending: isCreateMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate)();\n    const { mutate: arbitration_patch_trigger, isPending: isPatchMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate)();\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const accept_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"created\",\n                \"modified\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    // Cell: ({ cell }) => <div>{parse(cell.getValue<string>())}</div>,\n                    // Cell: ({ cell }) => { if ([\"description\", \"content\",\"report\"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Rapport\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_editor__WEBPACK_IMPORTED_MODULE_7__.Editor, {\n                                    // initialValue={row.original[key]}\n                                    // tinymceScriptSrc=\"http://localhost:3000/tinymce/tinymce.min.js\"\n                                    // apiKey='none'\n                                    value: row.original.report,\n                                    // onChange={(e) => { row._valuesCache.report = e.target.getContent() }}\n                                    onTextChange: (e)=>{\n                                        row._valuesCache.report = e.htmlValue;\n                                    },\n                                    style: {\n                                        height: \"320px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (key === \"plan\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"plan\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_8__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            children: cell.getValue().code\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 38\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { row } = param;\n                        var _row__valuesCache_plan, _row__valuesCache_plan1, _plans_data, _plans;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_9__.Dropdown, {\n                                    optionLabel: \"name\",\n                                    placeholder: \"Choisir un plan\",\n                                    onChange: (e)=>{\n                                        var _plans, _plans1;\n                                        console.log(e);\n                                        setRowTobe({\n                                            ...rowTobe,\n                                            plan: (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data.results.find((plan)=>plan.id === e.value.code)\n                                        });\n                                        row._valuesCache = {\n                                            ...row._valuesCache,\n                                            plan: (_plans1 = plans) === null || _plans1 === void 0 ? void 0 : _plans1.data.results.find((plan)=>plan.id === e.value.code)\n                                        };\n                                    },\n                                    value: {\n                                        code: ((_row__valuesCache_plan = row._valuesCache.plan) === null || _row__valuesCache_plan === void 0 ? void 0 : _row__valuesCache_plan.id) || null,\n                                        name: ((_row__valuesCache_plan1 = row._valuesCache.plan) === null || _row__valuesCache_plan1 === void 0 ? void 0 : _row__valuesCache_plan1.code) || null\n                                    },\n                                    options: (_plans = plans) === null || _plans === void 0 ? void 0 : (_plans_data = _plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.results.map((plan)=>{\n                                        return {\n                                            code: plan.id,\n                                            name: \"\".concat(plan.type, \" \").concat(plan.exercise)\n                                        };\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            var _data__data_type_properties_key_title3;\n            if (key === \"id\") return {\n                header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                accessorKey: key,\n                id: key,\n                Edit: ()=>null\n            };\n            var _data__data_type_properties_key_title4;\n            if (key === \"team\") return {\n                header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                accessorKey: key,\n                id: key,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: cell.getValue().map((usr)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__.getUserFullname)(usr)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    console.log(\"[ARBITRATION]\", row._valuesCache.team);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"font-bold\",\n                                children: \"Membres\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_10__.PickList, {\n                                source: picklistTargetValueTeam.length === 0 ? picklistSourceValueTeam : picklistSourceValueTeam.filter((user)=>picklistTargetValueTeam.map((user)=>user.username).includes(user.username)),\n                                id: \"picklist_team\",\n                                target: picklistTargetValueTeam.length > 0 ? picklistTargetValueTeam : row._valuesCache.team,\n                                sourceHeader: \"De\",\n                                targetHeader: \"A\",\n                                itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            item.first_name,\n                                            \" \",\n                                            item.last_name\n                                        ]\n                                    }, item.username, true, void 0, void 0),\n                                onChange: (e)=>{\n                                    console.log(\"source Team\", e.source);\n                                    setPicklistSourceValueTeam([\n                                        ...e.source\n                                    ]);\n                                    setPicklistTargetValueTeam([\n                                        ...e.target\n                                    ]);\n                                    row._valuesCache.team = e.target;\n                                },\n                                sourceStyle: {\n                                    height: \"200px\"\n                                },\n                                targetStyle: {\n                                    height: \"200px\"\n                                },\n                                filter: true,\n                                filterBy: \"username,email,first_name,last_name\",\n                                filterMatchMode: \"contains\",\n                                sourceFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\",\n                                targetFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true);\n                }\n            };\n            else {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), [\n        (_users1 = users) === null || _users1 === void 0 ? void 0 : _users1.data,\n        (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data,\n        (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data\n    ]);\n    console.log(plans);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_11__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data ? data_.data_.data : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data ? data_.data_.data.length : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                report: false,\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_12__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onEditingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_patch_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setEditingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" mis \\xe0 ajour\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onCreatingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_create_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setCreatingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" cr\\xe9\\xe9\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"add\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"align-content-center \",\n                                children: \"Cr\\xe9ation nouveau arbitrage\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: createVisible,\n                    onHide: ()=>{\n                        table.setCreatingRow(null);\n                        setCreateVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: internalEditComponents\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 493,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 492,\n                columnNumber: 7\n            }, this);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"align-content-center \",\n                                children: [\n                                    \"Editer l'arbitrage n\\xb0 \",\n                                    row.original.id\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: editVisible,\n                    onHide: ()=>{\n                        table.setEditingRow(null);\n                        setEditVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: [\n                            internalEditComponents,\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 517,\n                columnNumber: 7\n            }, this);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.report);\n        },\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"update\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                setArbitrationID(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true);\n                                console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept: accept_row_deletion,\n                                    reject: reject_row_deletion\n                                })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 543,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 571,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_19__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 571,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"uua31+vusobRieuQlddYnb78zOA=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_11__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx\n"));

/***/ })

});