"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/recommendations/followup/page",{

/***/ "(app-client)/./app/(main)/recommendations/(components)/GenericTAbleFollowUp.tsx":
/*!**************************************************************************!*\
  !*** ./app/(main)/recommendations/(components)/GenericTAbleFollowUp.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTableRecommendationFolluwUp; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-client)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _editForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./editForm */ \"(app-client)/./app/(main)/recommendations/(components)/editForm.tsx\");\n/* harmony import */ var _followup_recommendation_id_page__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../followup/[recommendation_id]/page */ \"(app-client)/./app/(main)/recommendations/followup/[recommendation_id]/page.tsx\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var primereact_datatable__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! primereact/datatable */ \"(app-client)/./node_modules/primereact/datatable/datatable.esm.js\");\n/* harmony import */ var primereact_column__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/column */ \"(app-client)/./node_modules/primereact/column/column.esm.js\");\n/* harmony import */ var primereact_progressbar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/progressbar */ \"(app-client)/./node_modules/primereact/progressbar/progressbar.esm.js\");\n/* harmony import */ var primereact_overlaypanel__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! primereact/overlaypanel */ \"(app-client)/./node_modules/primereact/overlaypanel/overlaypanel.esm.js\");\n/* harmony import */ var primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! primereact/inputtextarea */ \"(app-client)/./node_modules/primereact/inputtextarea/inputtextarea.esm.js\");\n/* harmony import */ var _CommentActionDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CommentActionDialog */ \"(app-client)/./app/(main)/recommendations/(components)/CommentActionDialog.tsx\");\n/* harmony import */ var primereact_badge__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/badge */ \"(app-client)/./node_modules/primereact/badge/badge.esm.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-client)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"(app-client)/./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var primereact_togglebutton__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/togglebutton */ \"(app-client)/./node_modules/primereact/togglebutton/togglebutton.esm.js\");\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var _lib_schemas__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/schemas */ \"(app-client)/./lib/schemas.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTableRecommendationFolluwUp(data_) {\n    var _getCookie;\n    _s();\n    // const ReactJson = useMemo(() => dynamic(() => import('react-json-view'), { ssr: false }), []);\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_7__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const { push } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const planActionDataTableRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const overlayProofPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const overlayValidationAcceptationPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const [overlayValidationAcceptationTitle, setOverlayValidationAcceptationTitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [recommendation_id, setRecommendationId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [recommendation$, setRecommendation$] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [action_comment, setActionComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [action_comments, setActionComments] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [actionId, setActionId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [detailsDialogVisible, setDetailsDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [actionCommentDialogVisible, setActionCommentDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const [clickEvent, setClickEvent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const open = Boolean(anchorEl);\n    // Hooks\n    const { data: comment_create_data, isPending: comment_create_isMutating, error: comment_create_error, mutate: trigger_comment_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiCommentCreate)();\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationCreate)();\n    const { data: data_modify, error: error_modify, isPending: isMutating_modify, mutate: trigger_modify } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationPartialUpdate)();\n    const { data: data_delete, error: error_delete, isPending: isMutating_delete, mutate: trigger_delete } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationDestroy)();\n    const { data: data_action_update, error: error_action_update, isPending: isMutating_action_update, mutate: trigger_action_update } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiActionPartialUpdate)();\n    // Functions\n    function onPaginationChange(state) {\n        console.log(\"PAGINATION\", data_.pagination);\n        data_.pagination.set(state);\n    }\n    function updateActionAcceptation(e, actionId) {\n        setActionId(actionId);\n        // setClickEvent(e);\n        if (!e.value) {\n            setOverlayValidationAcceptationTitle(\"Acceptation : \".concat(actionId));\n            overlayValidationAcceptationPanelRef.current.toggle(e.originalEvent);\n        } else {\n            trigger_action_update({\n                id: actionId,\n                data: {\n                    accepted: true\n                }\n            });\n        }\n    }\n    function updateActionValidation(e, actionId) {\n        setActionId(actionId);\n        if (!e.value) {\n            setOverlayValidationAcceptationTitle(\"Validation : \".concat(actionId));\n            overlayValidationAcceptationPanelRef.current.toggle(e.originalEvent);\n        } else {\n            trigger_action_update({\n                id: actionId,\n                data: {\n                    validated: true\n                }\n            });\n        }\n    }\n    function attachementViewProofClick(event) {\n        overlayProofPanelRef.current.toggle(event);\n    }\n    function addCommentClick(event, action_id, recommendation_id_) {\n        var _data__data__data_results_find_actions_find, _data__data__data_results_find;\n        setActionId(action_id);\n        console.log(action_id, recommendation_id_);\n        console.log(data_.data_.data.results);\n        console.log(data_.data_.data.results.filter((recommendation)=>recommendation.id === recommendation_id_));\n        setActionComments((_data__data__data_results_find = data_.data_.data.results.find((recommendation)=>recommendation.id === recommendation_id_)) === null || _data__data__data_results_find === void 0 ? void 0 : (_data__data__data_results_find_actions_find = _data__data__data_results_find.actions.find((action)=>action.id === action_id)) === null || _data__data__data_results_find_actions_find === void 0 ? void 0 : _data__data__data_results_find_actions_find.comments.sort((a, b)=>new Date(b.created).getTime() - new Date(a.created).getTime()));\n        if (!actionCommentDialogVisible) setActionCommentDialogVisible(true);\n    // overlayProofPanelRef.current.toggle(event);\n    }\n    const accept = ()=>{\n        trigger_delete({\n            id: actionId\n        }, {\n            onSuccess: ()=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"Enregistrement supprim\\xe9\"\n                });\n            },\n            onError: (error)=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"\".concat(error.code)\n                });\n            }\n        });\n    };\n    const reject = ()=>{\n        toast.current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        console.log(\"recommendation_id\", recommendation_id);\n    }, [\n        recommendation_id,\n        actionId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n    // updateActionAcceptation(clickEvent,actionId)\n    }, [\n        actionId\n    ]);\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"causes\",\n                \"comments\",\n                \"constats\",\n                \"actions\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__.Editor, {\n                            onChange: (e)=>{\n                                row._valuesCache.content = e.target.getContent();\n                            },\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    accessorFn: (row)=>new Date(row[key]),\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            if (key === \"concerned_structure\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    accessorFn: (row)=>row[key].code_mnemonique,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            style: {\n                                fontSize: 12,\n                                fontWeight: \"bold\",\n                                fontFamily: \"monospace\",\n                                color: \"var(--text-color)\",\n                                background: \"transparent\",\n                                border: \" 2px dotted green\",\n                                borderRadius: 50\n                            },\n                            label: cell.getValue() || row.original.concerned_structure.code_stru\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"validated\") {\n                var _data__data_type_properties_key_title3;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            severity: cell.getValue() ? \"success\" : \"danger\",\n                            icon: cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__.FontAwesomeIcon, {\n                                size: \"2x\",\n                                icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_16__.faSquareCheck\n                            }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__.FontAwesomeIcon, {\n                                size: \"2x\",\n                                icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_16__.faRectangleXmark\n                            }, void 0, false, void 0, void 0),\n                            value: \"\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"accepted\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            severity: cell.getValue() ? \"success\" : \"danger\",\n                            icon: cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__.FontAwesomeIcon, {\n                                size: \"2x\",\n                                icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_16__.faCircleCheck\n                            }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__.FontAwesomeIcon, {\n                                size: \"2x\",\n                                icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_16__.faCircleXmark\n                            }, void 0, false, void 0, void 0),\n                            value: \"\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"priority\") {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            className: \"w-9rem text-md\",\n                            severity: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_10__.getPrioritySeverity)(cell.getValue()),\n                            value: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"recommendation\") {\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"white-space-normal\",\n                            children: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title7;\n                return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0][\"$ref\"] ? data_.data_type.properties[key].allOf[0][\"$ref\"].enum : [],\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            severity: cell.getValue() === \"VP\" ? \"danger\" : cell.getValue() === \"STRUCT\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"VP\" ? \"Vice Pr\\xe9sident\" : cell.getValue() === \"STRUCT\" ? \"Structures\" : \"Contr\\xf4le Interne\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"status\") {\n                var _data__data_type_properties_key_title8;\n                return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            severity: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_10__.getStatusSeverity)(cell.getValue()),\n                            value: cell.getValue().toUpperCase(),\n                            className: \"w-9rem text-md\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 64\n                        }, this) : \"N/D\";\n                    }\n                };\n            }\n            if (key === \"type\") {\n                var _data__data_type_properties_key_title9;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title9 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title9 !== void 0 ? _data__data_type_properties_key_title9 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            severity: cell.getValue() === \"Command\\xe9e\" ? \"danger\" : cell.getValue() === \"Planifi\\xe9e\" ? \"warning\" : cell.getValue() === \"AI\" ? \"info\" : \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title10;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title10 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title10 !== void 0 ? _data__data_type_properties_key_title10 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"mission\") {\n                var _data__data_type_properties_key_title11;\n                return {\n                    header: (_data__data_type_properties_key_title11 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title11 !== void 0 ? _data__data_type_properties_key_title11 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            className: \"w-full\",\n                            style: {\n                                fontSize: 12,\n                                fontFamily: \"monospace\",\n                                color: \"var(--text-color)\",\n                                background: \"transparent\",\n                                border: \" 2px solid orange\"\n                            },\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"theme\") {\n                var _data__data_type_properties_key_title12;\n                return {\n                    header: (_data__data_type_properties_key_title12 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title12 !== void 0 ? _data__data_type_properties_key_title12 : key,\n                    accessorKey: \"theme.theme.title\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key\n                };\n            }\n            if (key === \"numrecommandation\") {\n                var _data__data_type_properties_key_title13;\n                return {\n                    header: (_data__data_type_properties_key_title13 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title13 !== void 0 ? _data__data_type_properties_key_title13 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_badge__WEBPACK_IMPORTED_MODULE_17__.Badge, {\n                            style: {\n                                background: \"transparent\",\n                                border: \"2px solid var(--primary-500)\",\n                                fontSize: 12,\n                                fontWeight: \"bold\",\n                                fontFamily: \"monospace\",\n                                color: \"var(--text-color)\"\n                            },\n                            size: \"large\",\n                            value: \"\".concat(cell.getValue()),\n                            severity: \"warning\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key][\"$ref\"] && data_.data_type.properties[key][\"$ref\"].enum) {\n                var _data__data_type_properties_key_title14;\n                return {\n                    header: (_data__data_type_properties_key_title14 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title14 !== void 0 ? _data__data_type_properties_key_title14 : key,\n                    // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+\" ,\"; }, \"\"):\"\",\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.row.original[key];\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        children: data_.data_type.properties[key][\"$ref\"].enum,\n                        SelectProps: {\n                        }\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title15, _data__data_type_properties_key_title16;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title15 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title15 !== void 0 ? _data__data_type_properties_key_title15 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title16 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title16 !== void 0 ? _data__data_type_properties_key_title16 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    // const actionBodyTemplate = (rowData: any, options: ColumnBodyOptions) => {\n    //     return (\n    //         <React.Fragment>\n    //             {!options.rowEditor?.editing && <Button icon=\"pi pi-pencil\" rounded outlined severity=\"info\" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.element?.props.onClick(e) }} />}\n    //             {options.rowEditor?.editing && <Button icon=\"pi pi-save\" rounded outlined severity=\"success\" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.onSaveClick(e) }} />}\n    //             {options.rowEditor?.editing && <Button icon=\"pi pi-times\" rounded outlined severity=\"info\" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.onCancelClick(e) }} />}\n    //             {!options.rowEditor?.editing && <Button icon=\"pi pi-trash\" rounded outlined severity=\"danger\" onClick={(e) => { setCommentID(rowData.id); setCommentID(rowData.id); confirm1(e, rowData.id) }} />}\n    //         </React.Fragment>\n    //     );\n    // }\n    const generateRecommandationActionsColumns = ()=>{\n        let columns = [];\n        for (const [key, value] of Object.entries(_lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"created_by\",\n                \"dependencies\",\n                \"modified_by\",\n                \"created\",\n                \"modified\",\n                \"id\"\n            ].includes(key);\n        })){\n            if (key === \"description\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n                    field: key,\n                    body: (data)=>data.description,\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 22\n                }, this));\n            } else if (key === \"job_leader\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n                    field: key,\n                    body: (data)=>{\n                        var _data_job_leader, _data_job_leader1;\n                        return \"\".concat((_data_job_leader = data.job_leader) === null || _data_job_leader === void 0 ? void 0 : _data_job_leader.last_name, \" \").concat((_data_job_leader1 = data.job_leader) === null || _data_job_leader1 === void 0 ? void 0 : _data_job_leader1.first_name);\n                    },\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"start_date\",\n                \"end_date\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n                    field: key,\n                    body: (data)=>new Date(data[key]).toLocaleDateString(),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 571,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"progress\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressbar__WEBPACK_IMPORTED_MODULE_19__.ProgressBar, {\n                            value: data[key]\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 577,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"status\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_15__.Tag, {\n                            className: \"w-7rem\",\n                            value: data[key].toUpperCase()\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"accepted\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_togglebutton__WEBPACK_IMPORTED_MODULE_20__.ToggleButton, {\n                            checked: data.accepted,\n                            onLabel: \"Oui\",\n                            offLabel: \"Non\",\n                            onIcon: \"pi pi-check\",\n                            offIcon: \"pi pi-times\",\n                            onChange: (e)=>{\n                                updateActionAcceptation(e, data.id);\n                            }\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"validated\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_togglebutton__WEBPACK_IMPORTED_MODULE_20__.ToggleButton, {\n                            checked: data.validated,\n                            onLabel: \"Oui\",\n                            offLabel: \"Non\",\n                            onIcon: \"pi pi-check\",\n                            offIcon: \"pi pi-times\",\n                            onChange: (e)=>{\n                                updateActionValidation(e, data.id);\n                            }\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"proof\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                            severity: \"warning\",\n                            icon: \"pi pi-paperclip\",\n                            onClick: attachementViewProofClick\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_13__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 589,\n                    columnNumber: 22\n                }, this));\n            }\n        }\n        var _data_comments_length;\n        columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_18__.Column, {\n            align: \"center\",\n            field: \"comment\",\n            body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                    rounded: true,\n                    outlined: true,\n                    severity: \"info\",\n                    icon: \"pi pi-comments\",\n                    onClick: (e)=>{\n                        console.log(\"addCommentClick\", data);\n                        addCommentClick(e, data.id, data.recommendation);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_badge__WEBPACK_IMPORTED_MODULE_17__.Badge, {\n                        value: (_data_comments_length = data.comments.length) !== null && _data_comments_length !== void 0 ? _data_comments_length : 0,\n                        severity: \"danger\"\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0),\n            header: \"Commentaires\",\n            sortable: true,\n            style: {\n                width: \"25%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n            lineNumber: 594,\n            columnNumber: 18\n        }, this));\n        return columns;\n    };\n    console.log(\"#######################\", data_);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_22__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data.results ? data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading,\n            showProgressBars: data_.isLoading,\n            isSaving: isMutating_create\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_23__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 160,\n                enableHiding: true,\n                grow: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiSvgIcon-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiInputBase-input\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiTableSortLabel-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            //validate data\n            //save data to api\n            const { created, modified, id, mission, recommendation, concerned_structure, causes, actions_add, ...rest } = values;\n            // values._changedValues.actions= values._changedValues.actions.filter(action => action.id === undefined)\n            values._changedValues.actions.forEach((action)=>action.job_leader = action.job_leader.id);\n            console.log(\"onEditingRowSave\", values, rest, values._changedValues);\n            trigger_modify({\n                id: id,\n                data: values._changedValues\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"Enregistrement modifi\\xe9\"\n                    });\n                },\n                onError: (error)=>{\n                    var _toast_current;\n                    return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"\".concat(error.message)\n                    });\n                }\n            });\n            table.setEditingRow(null); //exit editing mode\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            const { created, modified, id, ...rest } = values;\n            values.actions = values.actions.filter((action)=>action.id === null || action.id === undefined).forEach((action)=>action.job_leader = action.job_leader.id);\n            console.log(\"onCreatingRowSave\", values);\n            trigger_create({\n                data: values\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.statusText)\n                    });\n                    console.log(\"onCreatingRowSave\", err.response);\n                    row._valuesCache = {\n                        error: err.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n        //clear any validation errors\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                    },\n                    \"& .MuiDialog-container\": {\n                        // display: 'none'\n                        \"& .MuiPaper-root\": {\n                            maxWidth: \"50vw\"\n                        }\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 848,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 847,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 9\n                    }, this),\n                    isMutating_action_update && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_25__.ProgressSpinner, {\n                        style: {\n                            width: \"32px\",\n                            height: \"32px\"\n                        },\n                        strokeWidth: \"8\",\n                        fill: \"var(--surface-ground)\",\n                        animationDuration: \".5s\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 876,\n                        columnNumber: 38\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 846,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        renderEditRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        renderDetailPanel: (param)=>/*#__PURE__*/ {\n            let { row } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                sx: {\n                    display: \"grid\",\n                    margin: \"auto\",\n                    //gridTemplateColumns: '1fr 1fr',\n                    width: \"100vw\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_27__.TabView, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_27__.TabPanel, {\n                        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex align-items-center justify-content-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                                    tooltip: \"Ajouter/Editer les actions\",\n                                    size: \"small\",\n                                    icon: \"pi pi-plus\",\n                                    onClick: ()=>{\n                                        row._valuesCache.actions_add = true;\n                                        setRecommendationId(row.original.id);\n                                        table.setEditingRow(row);\n                                        setEditVisible(true), console.log(\"editing row ...\");\n                                    },\n                                    rounded: true,\n                                    outlined: true\n                                }, void 0, false, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Plan d'actions\"\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        rightIcon: \"pi pi-thumbs-up ml-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_28__.DataTable, {\n                                ref: planActionDataTableRef,\n                                onRowClick: (event)=>setActionId(event.data.id),\n                                tableStyle: {\n                                    maxWidth: \"70vw\"\n                                },\n                                value: row.original.actions,\n                                rows: 5,\n                                paginator: true,\n                                resizableColumns: true,\n                                responsiveLayout: \"scroll\",\n                                children: generateRecommandationActionsColumns()\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: actionId\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 906,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_overlaypanel__WEBPACK_IMPORTED_MODULE_29__.OverlayPanel, {\n                                showCloseIcon: true,\n                                closeOnEscape: true,\n                                dismissable: false,\n                                className: \"grid w-3\",\n                                pt: {\n                                    content: {\n                                        className: \"w-full h-15rem\t\"\n                                    },\n                                    closeButton: {\n                                        style: {\n                                            left: \"-1rem\",\n                                            right: \"0\"\n                                        }\n                                    }\n                                },\n                                ref: overlayProofPanelRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_30__.InputTextarea, {\n                                                rows: 5,\n                                                className: \"w-full\",\n                                                placeholder: \"Commentaire\",\n                                                onChange: (e)=>{\n                                                    setActionComment(e.target.value);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                                                className: \"w-full\",\n                                                icon: \"pi pi-save\",\n                                                onClick: (e)=>{\n                                                    trigger_comment_create({\n                                                        recommendation: null,\n                                                        action: actionId,\n                                                        comment: action_comment,\n                                                        type: \"\"\n                                                    }, {\n                                                        revalidate: true,\n                                                        populateCache: true\n                                                    });\n                                                    overlayProofPanelRef.current.toggle(e);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 927,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 908,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_overlaypanel__WEBPACK_IMPORTED_MODULE_29__.OverlayPanel, {\n                                showCloseIcon: true,\n                                closeOnEscape: true,\n                                dismissable: false,\n                                className: \"grid w-3\",\n                                pt: {\n                                    content: {\n                                        className: \"w-full h-18rem\t\"\n                                    },\n                                    closeButton: {\n                                        style: {\n                                            left: \"-1rem\",\n                                            right: \"0\"\n                                        }\n                                    }\n                                },\n                                ref: overlayValidationAcceptationPanelRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: overlayValidationAcceptationTitle\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_30__.InputTextarea, {\n                                                rows: 5,\n                                                className: \"w-full\",\n                                                placeholder: \"Commentaire\",\n                                                onChange: (e)=>{\n                                                    setActionComment(e.target.value);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                                lineNumber: 957,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                                                className: \"w-full\",\n                                                icon: \"pi pi-save\",\n                                                onClick: (e)=>{\n                                                    trigger_comment_create({\n                                                        data: {\n                                                            recommendation: null,\n                                                            action: actionId,\n                                                            comment: action_comment,\n                                                            type: overlayValidationAcceptationTitle.includes(\"Validation\") ? \"Validation\" : \"Acceptation\",\n                                                            value: overlayValidationAcceptationTitle.includes(\"Validation\") ? \"Non Valid\\xe9\" : \"Non Accept\\xe9\"\n                                                        }\n                                                    });\n                                                    trigger_action_update(overlayValidationAcceptationTitle.includes(\"Validation\") ? {\n                                                        id: actionId,\n                                                        data: {\n                                                            validated: false\n                                                        }\n                                                    } : {\n                                                        id: actionId,\n                                                        data: {\n                                                            accepted: false\n                                                        }\n                                                    });\n                                                    overlayValidationAcceptationPanelRef.current.toggle(e);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 937,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommentActionDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                visible: actionCommentDialogVisible,\n                                setVisible: setActionCommentDialogVisible,\n                                action_comments: action_comments,\n                                action_id: actionId\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 899,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 891,\n                columnNumber: 7\n            }, this);\n        },\n        // Les buttons action de la ligne (row)\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                            tooltip: \"voir d\\xe9tails recommandation\",\n                            size: \"small\",\n                            icon: \"pi pi-eye\",\n                            onClick: ()=>{\n                                setRecommendationId(row.original.id);\n                                setRecommendation$(row.original);\n                                setDetailsDialogVisible(true);\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 987,\n                            columnNumber: 42\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 987,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                            tooltip: \"Modifier recommandation\",\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                row._valuesCache.actions_add = false;\n                                setRecommendationId(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true), console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 42\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                            tooltip: \"Supprimer recommandation\",\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>{\n                                setRecommendationId(row.original.id);\n                                (0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_31__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept,\n                                    reject\n                                });\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 989,\n                            columnNumber: 42\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 989,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_31__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 1004,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 986,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_22__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 1011,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_32__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 1012,\n                columnNumber: 7\n            }, this),\n            recommendation$ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_followup_recommendation_id_page__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                params: {\n                    recommendation: recommendation$,\n                    detailsDialogVisible,\n                    setDetailsDialogVisible\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 1013,\n                columnNumber: 27\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTableRecommendationFolluwUp, \"sjdK9xF+VM1Y6D3Wg1oeXHgcBLk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiCommentCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationPartialUpdate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationDestroy,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiActionPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_22__.useMaterialReactTable\n    ];\n});\n_c = GenericTableRecommendationFolluwUp;\nvar _c;\n$RefreshReg$(_c, \"GenericTableRecommendationFolluwUp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/recommendations/(components)/GenericTAbleFollowUp.tsx\n"));

/***/ })

});