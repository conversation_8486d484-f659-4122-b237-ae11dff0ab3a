"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/opportunity_sheet/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx":
/*!*************************************************************************!*\
  !*** ./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTableOpportunitySheet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-client)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_dialog__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/dialog */ \"(app-client)/./node_modules/primereact/dialog/dialog.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _editForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./editForm */ \"(app-client)/./app/(main)/themes/(components)/editForm.tsx\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _app_ability__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ability */ \"(app-client)/./app/ability.ts\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-client)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"(app-client)/./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTableOpportunitySheet(data_) {\n    var _getCookie;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_6__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const [theme_id, setThemeId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const { push } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeCreate)();\n    const { data: data_modify, error: error_modify, isPending: isMutating_modify, mutate: trigger_modify } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeUpdate)();\n    const { data: data_delete, error: error_delete, isPending: isMutating_delete, mutate: trigger_delete } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeDestroy)();\n    const [detailsDialogVisible, setDetailsDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(\"PAGINATION\", data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const getSeverity = (str)=>{\n        switch(str){\n            case \"Vice Pr\\xe9sident\":\n                return \"success\";\n            case \"Contr\\xf4le Interne\":\n                return \"warning\";\n            case \"Audit Interne\":\n                return \"warning\";\n            case \"Structures\":\n                return \"danger\";\n            default:\n                return null;\n        }\n    };\n    const accept = ()=>{\n        trigger_delete({}, {\n            onSuccess: ()=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"Enregistrement supprim\\xe9\"\n                });\n            },\n            onError: (error)=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"\".concat(error.code)\n                });\n            }\n        });\n    };\n    const reject = ()=>{\n        toast.current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        console.log(\"theme_id\", theme_id);\n    }, [\n        theme_id\n    ]);\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"risks\",\n                \"goals\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__.Editor, {\n                            onChange: (e)=>{\n                                row._valuesCache.content = e.target.getContent();\n                            },\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    accessorFn: (row)=>new Date(row[key]),\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        return new Date(cell.getValue()).toLocaleDateString(\"fr\");\n                    },\n                    id: key\n                };\n            }\n            if (key === \"concerned_structures\") {\n                var _data__data_type_properties_key_title2, _struct_code_mnemonique;\n                return {\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 1,\n                            children: cell.getValue().map((struct)=>{\n                                var _struct, _struct1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        backgroundColor: \"var(--pink-300)\",\n                                        color: \"black\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    label: (_struct_code_mnemonique = (_struct = struct) === null || _struct === void 0 ? void 0 : _struct.code_mnemonique) !== null && _struct_code_mnemonique !== void 0 ? _struct_code_mnemonique : (_struct1 = struct) === null || _struct1 === void 0 ? void 0 : _struct1.code_stru\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 186\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 84\n                        }, this) : \"/\";\n                    }\n                };\n            }\n            if (key === \"proposing_structures\") {\n                var _data__data_type_properties_key_title3, _struct_code_mnemonique1;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 1,\n                            children: cell.getValue().map((struct)=>{\n                                var _struct, _struct1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        backgroundColor: \"var(--cyan-300)\",\n                                        color: \"black\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    label: (_struct_code_mnemonique1 = (_struct = struct) === null || _struct === void 0 ? void 0 : _struct.code_mnemonique) !== null && _struct_code_mnemonique1 !== void 0 ? _struct_code_mnemonique1 : (_struct1 = struct) === null || _struct1 === void 0 ? void 0 : _struct1.code_stru\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 186\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 84\n                        }, this) : \"/\";\n                    }\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            severity: getSeverity(cell.getValue()),\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"validated\") {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    size: 120,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                            className: \"text-green-500\",\n                            size: \"2x\",\n                            icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCheckSquare\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 65\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                            className: \"text-red-500\",\n                            size: \"2x\",\n                            icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 145\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: cell.getValue() === \"Non lanc\\xe9e\" ? \"danger\" : cell.getValue() === \"En cours\" ? \"success\" : \"info\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"type\") {\n                var _data__data_type_properties_key_title7;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: cell.getValue() === \"Command\\xe9e\" ? \"danger\" : cell.getValue() === \"Planifi\\xe9e\" ? \"warning\" : cell.getValue() === \"AI\" ? \"info\" : \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title8;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"title\") {\n                var _data__data_type_properties_key_title9;\n                return {\n                    header: (_data__data_type_properties_key_title9 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title9 !== void 0 ? _data__data_type_properties_key_title9 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum , \n                    muiEditTextFieldProps: {\n                        // select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"white-space-normal\",\n                            children: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"domain\") {\n                var _data__data_type_properties_key_title10, _cell_getValue_title;\n                return {\n                    header: (_data__data_type_properties_key_title10 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title10 !== void 0 ? _data__data_type_properties_key_title10 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue_title = (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.title) !== null && _cell_getValue_title !== void 0 ? _cell_getValue_title : \"/\";\n                    }\n                };\n            }\n            if (key === \"process\") {\n                var _data__data_type_properties_key_title11, _cell_getValue_title1;\n                return {\n                    header: (_data__data_type_properties_key_title11 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title11 !== void 0 ? _data__data_type_properties_key_title11 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue_title1 = (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.title) !== null && _cell_getValue_title1 !== void 0 ? _cell_getValue_title1 : \"/\";\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title12, _data__data_type_properties_key_title13;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title12 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title12 !== void 0 ? _data__data_type_properties_key_title12 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title13 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title13 !== void 0 ? _data__data_type_properties_key_title13 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_15__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.results ? data_.results : [],\n        rowCount: data_.error ? 0 : data_.results ? data_.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: false,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading,\n            //showProgressBars: isLoading, //progress bars while refetching\n            isSaving: isMutating_create\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_16__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                size: 140,\n                enableHiding: true,\n                grow: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiSvgIcon-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiInputBase-input\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiTableSortLabel-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        // editDisplayMode: 'modal',\n        // createDisplayMode: 'modal',\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            console.log(\"onEditingRowSave\", values);\n            const { created, modified, id, ...rest } = values;\n            setThemeId(id);\n            trigger_modify({\n                id: id,\n                data: rest\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    table.setEditingRow(null); //exit editing mode\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"success\",\n                        summary: \"Modification\",\n                        detail: \"Enregistrement modifi\\xe9\"\n                    });\n                },\n                onError: (error)=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        summary: \"Modification\",\n                        detail: \"\".concat(error.message)\n                    });\n                    console.log(\"onEditingRowSave\", error.message);\n                    row._valuesCache = {\n                        error: error.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            console.log(\"onCreatingRowSave\", values);\n            const { created, modified, id, ...rest } = values;\n            trigger_create(rest, {\n                revalidate: true,\n                populateCache: true,\n                onSuccess: ()=>{\n                    var _toast_current;\n                    table.setCreatingRow(null);\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"success\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                },\n                onError: (err)=>{\n                    var _err_response, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.statusText)\n                    });\n                    console.log(\"onCreatingRowSave\", err.response);\n                    row._valuesCache = {\n                        error: err.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                    },\n                    \"& .MuiDialog-container\": {\n                        //  display: 'none',\n                        \"& .MuiPaper-root\": {\n                            maxWidth: \"60vw\"\n                        }\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"add\",\n                        a: \"theme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"delete\",\n                        a: \"theme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"add\",\n                        a: \"arbitratedtheme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            icon: \"pi pi-bell\",\n                            rounded: true,\n                            color: rowActionEnabled ? \"secondary\" : \"primary\",\n                            size: \"small\",\n                            \"aria-label\": \"edit\",\n                            onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 700,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 699,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 670,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        renderEditRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        // renderDetailPanel: ({ row }) =>\n        //   row.original.staff ?\n        //     (\n        //       <Box\n        //         sx={{\n        //           display: 'grid',\n        //           margin: 'auto',\n        //           //gridTemplateColumns: '1fr 1fr',\n        //           width: '100vw',\n        //         }}\n        //       >\n        //         <TabView>\n        //           <TabPanel header={data_.data_type.properties[\"staff\"].title} leftIcon=\"pi pi-user mr-2\">\n        //             <ul>{row.original.staff.staff.map((user, idx) => <a key={user.email + row.original.code} href={\"mailto:\" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>\n        //           </TabPanel>\n        //           <TabPanel header={\"Assistants\"} rightIcon=\"pi pi-user ml-2\">\n        //             <ul>{row.original.staff.assistants.map((user, idx) => <a key={user.email + row.original.code} href={\"mailto:\" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>\n        //           </TabPanel>\n        //           <TabPanel header=\"Lettre\" leftIcon=\"pi pi-file-word mr-2\" rightIcon=\"pi pi-file-pdf ml-2\">\n        //             <Button icon=\"pi pi-check\" rounded onClick={() => setVisible(true)} disabled={row.original.document === null} />\n        //             <Sidebar key={row.original.id} header={<h2>Lettre de mission : {row.original.code}</h2>} visible={visible} onHide={() => setVisible(false)} className=\"w-full md:w-9 lg:w-8\">\n        //               <div className=\"flex flex-column\talign-items-center justify-content-center gap-1\">\n        //                 {row.original.document !== null ?\n        //                   <Document\n        //                     file={row.original.document}\n        //                     onLoadSuccess={onDocumentLoadSuccess}\n        //                   >\n        //                     <Page pageNumber={pageNumber} />\n        //                   </Document> : <p>No Document</p>}\n        //                 <div className='flex flex-column\talign-items-center justify-content-center gap-1' >\n        //                   <p>\n        //                     Page {pageNumber || (numPages ? 1 : '--')} of {numPages || '--'}\n        //                   </p>\n        //                   <div className='flex flex-row\talign-items-center justify-content-center gap-1' >\n        //                     <Button\n        //                       type=\"button\"\n        //                       disabled={pageNumber <= 1}\n        //                       onClick={previousPage}\n        //                     >\n        //                       Previous\n        //                     </Button>\n        //                     <Button\n        //                       type=\"button\"\n        //                       disabled={pageNumber >= numPages}\n        //                       onClick={nextPage}\n        //                     >\n        //                       Next\n        //                     </Button>\n        //                   </div>\n        //                 </div>\n        //               </div>\n        //             </Sidebar>\n        //           </TabPanel>          </TabView>\n        //       </Box >\n        //     ) : <></>,\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-eye\",\n                        onClick: ()=>{\n                            setThemeId(row.original.id);\n                            setDetailsDialogVisible(true);\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 777,\n                        columnNumber: 9\n                    }, this),\n                    _app_ability__WEBPACK_IMPORTED_MODULE_7__[\"default\"].can(\"change\", \"theme\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            setThemeId(row.original.id);\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 9\n                    }, this),\n                    _app_ability__WEBPACK_IMPORTED_MODULE_7__[\"default\"].can(\"delete\", \"theme\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>{\n                            setThemeId(row.original.id);\n                            (0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            });\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 781,\n                        columnNumber: 43\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 796,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 776,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_15__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 808,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_19__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 809,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dialog__WEBPACK_IMPORTED_MODULE_20__.Dialog, {\n                maximizable: true,\n                header: \"THEME ID : \".concat(theme_id),\n                visible: detailsDialogVisible,\n                style: {\n                    width: \"90vw\"\n                },\n                onHide: ()=>{\n                    if (!detailsDialogVisible) return;\n                    setDetailsDialogVisible(false);\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 810,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTableOpportunitySheet, \"Q0KDj6Olwz2kzfOmmcPt2mUIdjY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeUpdate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeDestroy,\n        material_react_table__WEBPACK_IMPORTED_MODULE_15__.useMaterialReactTable\n    ];\n});\n_c = GenericTableOpportunitySheet;\nvar _c;\n$RefreshReg$(_c, \"GenericTableOpportunitySheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx\n"));

/***/ })

});