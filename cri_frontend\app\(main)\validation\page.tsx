'use client';


import { MRT_PaginationState } from 'material-react-table';
import { useEffect, useState } from 'react';
import { getCookie } from 'cookies-next';
import { DataTable } from 'primereact/datatable';
import { Column, ColumnBodyOptions } from 'primereact/column';
import { Card } from 'primereact/card';
import React from 'react';
import { Button } from 'primereact/button';
import { Accordion, AccordionTab } from 'primereact/accordion';
import axios from 'axios';
import { useApiActionList, useApiCommentList, useApiCommentPartialUpdate, useApiRecommendationList } from '@/hooks/useNextApi';

const TableDemo = () => {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    const {data: comments } = useApiCommentList()
    const { data:actions} = useApiActionList()
    const { data: recommendations} = useApiRecommendationList()
    const [commentID, setCommentID] = useState(0);
    const [commentValidator, setCommentValidator] = useState('');
    const { data: data_update, mutate: trigger_update, error: error_udate } = useApiCommentPartialUpdate()
    useEffect(() => {
    }, [commentID])
    const actionMeraciBodyTemplate = (rowData: any, options: ColumnBodyOptions) => {
        return (
            <div className='flex gap-2'>
                <Button icon="pi pi-check" rounded outlined severity="success" onClick={(e) => {
                    trigger_update({ validMeraci: 'Validé' }, { fetcher: async (key: string, options) => { return await axios.patch(`http://10.39.107.98:3001/api/comment/${rowData.id}/`,{ validMeraci: 'Validé' }, options.args) } })
                }} />
                <Button icon="pi pi-times" rounded outlined severity="danger" onClick={(e) => {
                    trigger_update({ validMeraci: 'Non Validé' }, { fetcher: async (key: string, options) => { return await axios.patch(`http://10.39.107.98:3001/api/comment/${rowData.id}/`,{ validMeraci: 'Non Validé' }, options.args) } })

                }} />
            </div>
        );
    }
    const actionDirecteurBodyTemplate = (rowData: any, options: ColumnBodyOptions) => {
        return (
            <div className='flex gap-2'>
                <Button icon="pi pi-check" rounded outlined severity="success" onClick={(e) => {
                    trigger_update({ validDirecteur: 'Validé' }, { fetcher: async (key: string, options) => { return await axios.patch(`http://10.39.107.98:3001/api/comment/${rowData.id}/`,{ validDirecteur: 'Validé' }, options.args) } })
                }} />
                <Button icon="pi pi-times" rounded outlined severity="danger" onClick={(e) => {
                    trigger_update({ validDirecteur: 'Non Validé' }, { fetcher: async (key: string, options) => { return await axios.patch(`http://10.39.107.98:3001/api/comment/${rowData.id}/`,{ validDirecteur: 'Non Validé' }, options.args) } })
                }} />
            </div>
        );
    }
    if (comments.isLoading) return (<div></div>)
    return (
        <div className="grid">
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Missions</span>
                            <div className="text-900 font-medium text-xl">{comments?.data.count! ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-blue-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-briefcase text-blue-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">24 new </span>
                    <span className="text-500">since last visit</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Commandées</span>
                            <div className="text-900 font-medium text-xl">{comments?.data.count ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-orange-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-microsoft text-orange-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">{cmd_missions?.data.count ?? 0}</span>
                    <span className="text-500">since last week</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Planifiées</span>
                            <div className="text-900 font-medium text-xl">{comments?.data.count ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-cyan-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-clock text-cyan-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">520 </span>
                    <span className="text-500">newly registered</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">En cours</span>
                            <div className="text-900 font-medium text-xl">{comments?.data.count ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-bolt text-green-500 text-xl" />

                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">85 </span>
                    <span className="text-500">responded</span> */}
                </div>
            </div>
            <div className="col-12">
                <Accordion activeIndex={0}>
                    <AccordionTab header="Actions">
                        <DataTable<Partial<CommentRead>[]> stripedRows value={comments.data?.data.results.filter((comment: CommentRead) => comment.action)} rows={10} scrollable paginator resizableColumns>

                            <Column align={'left'} field={'view'} body={(data) => data.comment} header={'Commentaire'} sortable style={{ width: '70%' }} />
                            <Column align={'left'} field={'comment'} body={(data) => data.comment} header={'Commentaire'} sortable style={{ width: '70%' }} />
                            <Column align={'center'} field={'created'} body={(data) => new Date(data.created).toLocaleString('fr')} header={'Date'} sortable style={{ width: '12%' }} />
                            <Column align={'center'} field={'created_by'} body={(data) => data.created_by ? `${data.created_by?.last_name} ${data.created_by?.first_name}` : ''} header={'Utilisateur'} sortable style={{ width: '12%' }} />
                            <Column align={'center'} field={'type'} header={'type'} body={(data) => data.type === '' ? '/' : `${data.value}`} sortable style={{ width: '12%' }} />
                            <Column align={'center'} rowEditor={true} header={'MERACI'} sortableDisabled={true} field={"action"} body={actionMeraciBodyTemplate}
                                sortable style={{ width: '6%' }} />
                            <Column rowEditor={true} align={'center'} header={'DIRECTEUR'} sortableDisabled={true} field={"action"} body={actionDirecteurBodyTemplate}
                                sortable style={{ width: '6%' }} />
          
                        </DataTable>
                    </AccordionTab>
                    <AccordionTab header="Recommandations">
                        <DataTable<Partial<CommentRead>[]> stripedRows value={comments.data?.data.results.filter((comment: CommentRead) => comment.recommendation)} rows={10} scrollable paginator resizableColumns>
                            <Column align={'left'} field={'comment'} body={(data) => data.comment} header={'Commentaire'} sortable style={{ width: '70%' }} />
                            <Column align={'center'} field={'created'} body={(data) => new Date(data.created).toLocaleString('fr')} header={'Date'} sortable style={{ width: '12%' }} />
                            <Column align={'center'} field={'created_by'} body={(data) => data.created_by ? `${data.created_by?.last_name} ${data.created_by?.first_name}` : ''} header={'Utilisateur'} sortable style={{ width: '12%' }} />
                            <Column align={'center'} field={'type'} header={'type'} body={(data) => data.type === '' ? '/' : `${data.value}`} sortable style={{ width: '12%' }} />
                            <Column align={'center'} rowEditor={true} header={'MERACI'} sortableDisabled={true} field={"action"} body={actionMeraciBodyTemplate}
                                sortable style={{ width: '6%' }} />
                            <Column rowEditor={true} align={'center'} header={'DIRECTEUR'} sortableDisabled={true} field={"action"} body={actionDirecteurBodyTemplate}
                                sortable style={{ width: '6%' }} />

                        </DataTable>
                    </AccordionTab>
                </Accordion>
            </div>
        </div>
    );
};

export default TableDemo;
