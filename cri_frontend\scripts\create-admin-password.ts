import { PrismaClient } from '@prisma/client'
import { hash } from 'better-auth/crypto'

const prisma = new PrismaClient()

async function createAdminPassword() {
  console.log('🔐 Setting admin password...')
  
  const email = '<EMAIL>'
  const password = 'admin123' // Change this to your desired password
  
  try {
    // Hash the password using BetterAuth's crypto
    const hashedPassword = await hash(password)
    
    // Update the user with the hashed password
    const user = await prisma.user.update({
      where: { email },
      data: {
        // Note: BetterAuth handles password storage differently
        // We'll create an account record for password authentication
      },
    })
    
    // Create an account record for password authentication
    await prisma.account.upsert({
      where: {
        provider_providerAccountId: {
          provider: 'credential',
          providerAccountId: user.id,
        },
      },
      update: {
        // BetterAuth stores password hash in a different way
        // This is a simplified approach
      },
      create: {
        userId: user.id,
        type: 'credential',
        provider: 'credential',
        providerAccountId: user.id,
      },
    })
    
    console.log('✅ Admin password set successfully!')
    console.log('🔑 Admin credentials:')
    console.log(`   Email: ${email}`)
    console.log(`   Password: ${password}`)
    console.log('   Role: Super Admin')
    console.log('')
    console.log('🌐 You can now login at: http://localhost:3001/login')
    
  } catch (error) {
    console.error('❌ Error setting admin password:', error)
  }
}

createAdminPassword()
  .catch((e) => {
    console.error('❌ Error:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
