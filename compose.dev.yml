services:
  backend:
    build:
      context: ./cri_backend
      dockerfile: dev.Dockerfile
    ports:
      - "3001:3001"
    volumes:
      - ./cri_backend:/app
    networks:
      - cri_network
  next-app:
    container_name: cri-frontend-dev-app
    build:
      context: ./cri_frontend
      dockerfile: dev.Dockerfile
    env_file:
      - ./cri_frontend/.env.local
    volumes:
      # - ./cri_frontend/src:/app/src
      - ./cri_frontend/public:/app/public
    restart: always
    ports:
      - 3000:3000
    networks:
      - cri_network

networks:
  cri_network:
