"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx":
/*!*********************************************************!*\
  !*** ./app/(main)/themes/(components)/GenericTAble.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_chip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/chip */ \"(app-client)/./node_modules/primereact/chip/chip.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const [theme_id, setThemeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: arbitrations, isLoading, error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList)();\n    const { data: themes, isLoading: isLoading_themes, error: error_themes } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList)();\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate)();\n    const { data: data_update, error: error_update, isPending: isMutating_update, mutate: trigger_update } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate)();\n    const getSeverity = (str)=>{\n        switch(str){\n            case \"Vice Pr\\xe9sident\":\n                return \"success\";\n            case \"Contr\\xf4le Interne\":\n                return \"warning\";\n            case \"Audit Interne\":\n                return \"warning\";\n            case \"Structures\":\n                return \"danger\";\n            default:\n                return null;\n        }\n    };\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                header: \"ID\",\n                accessorKey: \"id\",\n                size: 70,\n                Edit: ()=>null\n            },\n            {\n                header: \"Aribtrage\",\n                accessorKey: \"arbitration\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"arbitration\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-11rem text-sm\",\n                        severity: row.original.arbitration.plan.code.includes(\"Audit\") ? \"danger\" : \"info\",\n                        value: row.original.arbitration.plan.code\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_arbitration, _row__valuesCache_arbitration1, _arbitrations;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _arbitrations, _arbitrations1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                arbitration: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data.results.find((arbi)=>arbi.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                arbitration: (_arbitrations1 = arbitrations) === null || _arbitrations1 === void 0 ? void 0 : _arbitrations1.data.results.find((arbi)=>arbi.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un\",\n                        value: {\n                            name: ((_row__valuesCache_arbitration = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration === void 0 ? void 0 : _row__valuesCache_arbitration.id) || null,\n                            code: ((_row__valuesCache_arbitration1 = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration1 === void 0 ? void 0 : _row__valuesCache_arbitration1.id) || null\n                        },\n                        options: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data.results.map((arbi)=>{\n                            return {\n                                code: arbi.id,\n                                name: arbi.plan.type\n                            };\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Propos\\xe9 par\",\n                accessorKey: \"theme\",\n                id: \"theme_proposed_by\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                Edit: ()=>null,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-9rem text-sm\",\n                        severity: getSeverity(cell.getValue().proposedBy),\n                        value: cell.getValue().proposedBy\n                    }, row.original.code + row.original.created, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 36\n                    }, this);\n                }\n            },\n            {\n                header: \"Structures proposantes\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"proposing_structures\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    console.log(cell.getValue().proposing_structures);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: cell.getValue().proposing_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                style: {\n                                    backgroundColor: \"green\",\n                                    color: \"white\"\n                                },\n                                label: val.code_mnemonique\n                            }, \"thm\".concat(row.original.theme.id, \"_ps\").concat(idx), false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 20\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Structures concern\\xe9es\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"concerned_structures\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [\n                            \" \",\n                            cell.getValue().concerned_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                    style: {\n                                        backgroundColor: \"green\",\n                                        color: \"white\"\n                                    },\n                                    label: val.code_mnemonique\n                                }, \"thm\".concat(row.original.theme.id, \"_cs\").concat(idx), false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 137\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Domaine\",\n                accessorKey: \"domain\",\n                id: \"domain\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.domain.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Processus\",\n                accessorKey: \"process\",\n                id: \"process\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.process.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Intitul\\xe9\",\n                accessorKey: \"theme\",\n                id: \"title\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.theme.title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_theme, _row__valuesCache_theme1, _themes;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _themes, _themes1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                theme: (_themes = themes) === null || _themes === void 0 ? void 0 : _themes.data.results.find((thm)=>thm.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                theme: (_themes1 = themes) === null || _themes1 === void 0 ? void 0 : _themes1.data.results.find((thm)=>thm.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un th\\xe8me\",\n                        value: {\n                            name: ((_row__valuesCache_theme = row._valuesCache.theme) === null || _row__valuesCache_theme === void 0 ? void 0 : _row__valuesCache_theme.title) || null,\n                            code: ((_row__valuesCache_theme1 = row._valuesCache.theme) === null || _row__valuesCache_theme1 === void 0 ? void 0 : _row__valuesCache_theme1.id) || null\n                        },\n                        options: (_themes = themes) === null || _themes === void 0 ? void 0 : _themes.data.results.map((thm)=>{\n                            return {\n                                code: thm.id,\n                                name: thm.title\n                            };\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Remarque\",\n                accessorKey: \"note\",\n                id: \"note\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.note\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>/*#__PURE__*/ {\n                    let { row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                        id: \"note\",\n                        initialValue: row.original.note,\n                        tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                        apiKey: \"none\",\n                        init: {\n                            height: 500,\n                            menubar: true,\n                            plugins: [\n                                \"advlist\",\n                                \"autolink\",\n                                \"lists\",\n                                \"link\",\n                                \"image\",\n                                \"charmap\",\n                                \"print\",\n                                \"preview\",\n                                \"anchor\",\n                                \"searchreplace\",\n                                \"visualblocks\",\n                                \"code\",\n                                \"fullscreen\",\n                                \"insertdatetime\",\n                                \"media\",\n                                \"table\",\n                                \"paste\",\n                                \"code\",\n                                \"help\",\n                                \"wordcount\"\n                            ],\n                            toolbar: \"undo redo | formatselect | bold italic backcolor |                                       alignleft aligncenter alignright alignjustify |                                       bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                        },\n                        onChange: (e)=>{\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                note: e.target.getContent()\n                            };\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 30\n                    }, this);\n                }\n            }\n        ], [\n        isLoading,\n        isLoading_themes\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableEditing: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            setThemeId(row.original.id);\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let update_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_update({\n                id: rest.id,\n                data: update_values\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"Th\\xe8me modifi\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.message);\n                    row._valuesCache = {\n                        error: err.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, row, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let insert_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_create(insert_values, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.message);\n                    row._valuesCache = {\n                        error: err.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: (param)=>{\n            let { table } = param;\n            //clear any validation errors\n            table.setCreatingRow(null);\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                //about:\"edit modal\",\n                // open: editVisible || createVisible,\n                maxWidth: \"md\"\n            };\n        // sx: {\n        //   //  '& .MuiDialog-root': {\n        //   //    width :'70vw'\n        //   //  },\n        //   // '& .MuiDialog-container': {\n        //   //   width :'70vw'\n        //   // },\n        //   zIndex: 1100,\n        // }\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_4__.Can, {\n                    I: \"add\",\n                    a: \"ArbitratedTheme\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            })\n    });\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n        lineNumber: 601,\n        columnNumber: 26\n    }, this);\n    console.log(\"-----------------------------------------\", arbitrations);\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_10__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 605,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_13__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 605,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"bDGdo1+71fnDMhQHJ0dTBFzwcyk=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx\n"));

/***/ })

});