'use client'
import { User } from '@/services/schemas';
import { Box, DialogActions, DialogContent, Stack } from '@mui/material';
import {
  MRT_EditActionButtons,
  MRT_RowData,
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef
} from 'material-react-table';
import { MRT_Localization_FR } from 'material-react-table/locales/fr';
import { Button } from 'primereact/button';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useMemo, useRef, useState } from 'react';
import parse from 'html-react-parser';
import { useApiPlanCreate, useApiPlanUpdate } from '@/services/api/api/api';
import { getCookie } from 'cookies-next';
import { Can } from '@/app/Can';


// import 'react-pdf/dist/Page/AnnotationLayer.css';
// import 'react-pdf/dist/Page/TextLayer.css';
//TODO make loading here

//If using TypeScript, define the shape of your data (optional, but recommended)
// interface GenericTableProps {
//   data_: any; 
//   isLoading?: boolean;
// }

export default function GenericTable<T extends MRT_RowData>(data_: { isLoading: any; data_: any, error: any, data_type: any | undefined, pagination: any }) {
  const user = JSON.parse(getCookie('user')?.toString() || '{}')
  const [plan_id, setPlanID] = useState(0)
  const { data, isPending:isMutating, error, mutate:trigger } = useApiPlanCreate({
    axios: { headers: { Authorization: `Token ${user?.token}` } }, 
  })
  const { data: plan_data_update, isPending: plan_isMutating, error: plan_update_error, mutate: trigger_plan_update } = useApiPlanUpdate({
    axios: { headers: { Authorization: `Token ${user?.token}` } }
  })

  // pdfjs.GlobalWorkerOptions.workerSrc = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js";
  const toast = useRef<Toast | null>(null);

  const [visible, setVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  function onPaginationChange(state: any) {
    console.log(data_.pagination);
    data_.pagination.set(state)
  };
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);


  const accept = () => {
    toast.current?.show({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 });
  };

  const reject = () => {
    toast.current?.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
  };

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setPageNumber(1);
  }
  function changePage(offset: number) {
    setPageNumber(prevPageNumber => prevPageNumber + offset);
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [rowActionEnabled, setRowActionEnabled] = useState(false);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const columns = useMemo<MRT_ColumnDef<T>[]>(
    () =>
      Object.entries(data_.data_type.properties).filter(([key, value], index) => !['modified_by', 'created_by', 'code'].includes(key)).map(([key, value], index) => {

        if (key === "type") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            muiEditTextFieldProps: {
              select: true,
            },
            editVariant: 'select',
            editSelectOptions: ['Audit Interne', 'Contrôle Interne', 'Hors Plan'],
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                className='w-9rem text-sm'
                key={row.original.code + row.original.created}
                severity={
                  cell.getValue<String>() === "Contrôle Interne" ?
                    "danger" : cell.getValue<String>() === "Audit Interne" ? "warning" : cell.getValue<String>() === 'Hors Plan' ? "info" : 'success'
                }
                value={cell.getValue<String>()}
              >

              </Tag>
          }

        }
        if (key === "exercise") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },

            muiTableFooterCellProps: {
              align: 'center',
            },
            muiEditTextFieldProps: {
              type: 'number'
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                className='w-9rem text-sm'
                key={row.original.code + row.original.created}
                severity={
                  'success'
                }
                value={cell.getValue<String>()} />
          }

        }
        else return {
          header: data_.data_type.properties[key].title ?? key,
          accessorKey: key,
          id: key,
          Edit: () => null,


        }
      })
    ,
    [],
  );

  const table = useMaterialReactTable({
    columns,
    data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [data_.data_.data], //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,
    enableRowSelection: true, //enable some features
    enableColumnOrdering: true, //enable a feature for all columns
    enableGlobalFilter: true, //turn off a feature
    enableGrouping: true,
    enableRowActions: true,
    enableStickyHeader: true,
    enableStickyFooter: true,
    enableColumnResizing: true,
    enableRowNumbers: true,
    enableEditing: true,
    manualPagination: true,
    initialState: {
      pagination: { pageSize: 5, pageIndex: 1 },
      columnVisibility: { created_by: false, created: false, modfied_by: false, modified: false, modified_by: false, staff: false, assistants: false, id: false, document: false },
      density: 'compact',
      showGlobalFilter: true,
      sorting: [{ id: 'id', desc: false }],
    },
    state: {
      pagination: data_.pagination.pagi,
      isLoading: data_.isLoading, //cell skeletons and loading overlay
      //showProgressBars: isLoading, //progress bars while refetching
      // isSaving: isSavingTodos, //progress bars and save button spinners
    },
    localization: MRT_Localization_FR,
    onPaginationChange: onPaginationChange,
    displayColumnDefOptions: {
      'mrt-row-pin': {
        enableHiding: true,
      },
      'mrt-row-expand': {
        enableHiding: true,
      },
      'mrt-row-actions': {
        // header: 'Edit', //change "Actions" to "Edit"
        size: 100,
        enableHiding: true,
      },
      'mrt-row-numbers': {
        enableHiding: true, //now row numbers are hidable too
      },
    },
    defaultColumn: {
      grow: true,
      enableMultiSort: true,
    },
    muiTablePaperProps: ({ table }) => ({

      className: "p-datatable-gridlines text-900 font-medium text-xl",
      classes: { root: 'p-datatable-gridlines text-900 font-medium text-xl' },
      sx: {
        height: `calc(100vh - 9rem)`,

        backgroundColor: "var(--surface-card)",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        "& .MuiTablePagination-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
        "& .MuiBox-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
      },
    }),
    onEditingRowSave: async ({ table, values, row }) => {
      //validate data
      //save data to api
      console.log("onEditingRowSave", values)
      const result = await trigger_plan_update({id:222,data:values}, {
        revalidate: true,
        onSuccess: () => {
          table.setEditingRow(null); //exit creating mode
          toast.current!.show({ severity: 'success', summary: 'Info', detail: `Plan mis à jour` });
        },
        onError: (error) => {
          toast.current!.show({ severity: 'error', summary: 'Info', detail: `${error.response?.statusText}` });
          console.log("onEditingRowSave", error.response);
          row._valuesCache = { error: error.response, ...row._valuesCache };
          return;
        }
      })

    },
    onEditingRowCancel: () => {
      //clear any validation errors
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });
    },
    onCreatingRowSave: ({ table, values, row }) => {
      //validate data
      //save data to api
      console.log("onCreatingRowSave", values)
      trigger({data:values}, {
        revalidate: true,
        populateCache: true,
        onSuccess: () => {
          table.setCreatingRow(null); //exit creating mode
          toast.current!.show({ severity: 'success', summary: 'Info', detail: `Plan créé` });
        },
        onError(error) {
          toast.current!.show({ severity: 'error', summary: 'Info', detail: `${error.response?.statusText}` });
          console.log("onCreatingRowSave", error.response);
          row._valuesCache = { error: error.response, ...row._valuesCache };
          return;
        },
      })
    },
    onCreatingRowCancel: () => {
      //clear any validation errors
    },
    muiTableFooterProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        "& .MuiTableFooter-root": {
          backgroundColor: "var(--surface-card) !important",
        },
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableContainerProps: ({ table }) => ({
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        height: table.getState().isFullScreen ? `calc(100vh)` : `calc(100vh - 9rem - ${table.refs.topToolbarRef.current?.offsetHeight}px - ${table.refs.bottomToolbarRef.current?.offsetHeight}px)`

      },
    }),
    muiPaginationProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {

        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableHeadCellProps: {
      sx: {
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTopToolbarProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important"
      },

    },
    muiTableBodyProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        //stripe the rows, make odd rows a darker color
        '& tr:nth-of-type(odd) > td': {
          backgroundColor: 'var(--surface-card)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
        '& tr:nth-of-type(even) > td': {
          backgroundColor: 'var(--surface-border)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
      },
    },
    renderTopToolbarCustomActions: ({ table }) => (
      <Stack direction={"row"} spacing={1}>
        {/* <Can I="add" a='plan'> */}
          <Button
            icon="pi pi-plus"
            rounded
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={(event) => {
              table.setCreatingRow(true); setCreateVisible(true), console.log("creating row ...");
            }}
            size="small"
          />
        {/* </Can> */}
        <Can I="delete" a='plan'>
          <Button
            rounded
            disabled={table.getIsSomeRowsSelected()}
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
            icon="pi pi-trash"
            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}
            size="small"
          />
        </Can>
      </Stack>
    ),
    muiDetailPanelProps: () => ({
      sx: (theme) => ({
        backgroundColor:
          theme.palette.mode === 'dark'
            ? 'rgba(255,210,244,0.1)'
            : 'rgba(0,0,0,0.1)',
      }),
    }),
    renderRowActions: ({ cell, row, table }) => (
      <span className="p-buttonset flex p-1">
        <Can I="edit" a='plan'>
          <Button size='small' icon="pi pi-pencil" onClick={() => {
            setPlanID(row.original.id);
            table.setEditingRow(row);
            setEditVisible(true);
            console.log("editing row ...");
          }}
            rounded
            outlined
          />
        </Can>
        <Can I="edit" a='plan'>
          <Button size='small' icon="pi pi-trash" rounded outlined
            onClick={(event) => confirmPopup({
              target: event.currentTarget,
              message: 'Voulez-vous supprimer cette ligne?',
              icon: 'pi pi-info-circle',
              // defaultFocus: 'reject',
              acceptClassName: 'p-button-danger',
              acceptLabel: 'Oui',
              rejectLabel: 'Non',
              accept,
              reject
            })}
          />
        </Can>
        <ConfirmPopup />
      </span>
      // </Box>
    ),
  });

  return <><MaterialReactTable table={table} /><Toast ref={toast} /></>;
}
