"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx":
/*!*******************************************************************!*\
  !*** ./app/(main)/plans/(components)/GenericTAbleArbitration.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/editor */ \"(app-client)/./node_modules/primereact/editor/editor.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var _lib_enums__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/enums */ \"(app-client)/./lib/enums.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// import { Editor } from '@tinymce/tinymce-react';\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie, _plans, _arbitrations;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: plans, isLoading: plans_isLoading, error: plans_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList)();\n    const { data: arbitrations, isLoading: arbitrations_isLoading, error: arbitrations_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList)();\n    const { data: users, isLoading: users_isLoading, error: users_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList)();\n    const users_data = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _users;\n        return ((_users = users) === null || _users === void 0 ? void 0 : _users.data.results) || [];\n    }, [\n        users_isLoading\n    ]);\n    const [arbitrationID, setArbitrationID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [picklistTargetValueTeam, setPicklistTargetValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [picklistSourceValueTeam, setPicklistSourceValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(users_data);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { mutate: arbitration_create_trigger, isPending: isCreateMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate)();\n    const { mutate: arbitration_patch_trigger, isPending: isPatchMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate)();\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const accept_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    console.log(\"USERS {{{{{{{{{{{{{{{{{{\", users_data);\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"created\",\n                \"modified\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    // Cell: ({ cell }) => <div>{parse(cell.getValue<string>())}</div>,\n                    // Cell: ({ cell }) => { if ([\"description\", \"content\",\"report\"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Rapport\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_editor__WEBPACK_IMPORTED_MODULE_8__.Editor, {\n                                    // initialValue={row.original[key]}\n                                    // tinymceScriptSrc=\"http://localhost:3000/tinymce/tinymce.min.js\"\n                                    // apiKey='none'\n                                    value: row.original.report,\n                                    // onChange={(e) => { row._valuesCache.report = e.target.getContent() }}\n                                    onTextChange: (e)=>{\n                                        row._valuesCache.report = e.htmlValue;\n                                    },\n                                    style: {\n                                        height: \"320px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (key === \"plan\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"plan\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_9__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            children: cell.getValue().code\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 38\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { row } = param;\n                        var _row__valuesCache_plan, _row__valuesCache_plan1, _row__valuesCache_plan2, _plans_data, _plans;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n                                    optionLabel: \"name\",\n                                    placeholder: \"Choisir un plan\",\n                                    onChange: (e)=>{\n                                        var _plans, _plans1;\n                                        console.log(e);\n                                        setRowTobe({\n                                            ...rowTobe,\n                                            plan: (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data.results.find((plan)=>plan.id === e.value.code)\n                                        });\n                                        row._valuesCache = {\n                                            ...row._valuesCache,\n                                            plan: (_plans1 = plans) === null || _plans1 === void 0 ? void 0 : _plans1.data.results.find((plan)=>plan.id === e.value.code)\n                                        };\n                                    },\n                                    value: {\n                                        code: ((_row__valuesCache_plan = row._valuesCache.plan) === null || _row__valuesCache_plan === void 0 ? void 0 : _row__valuesCache_plan.id) || null,\n                                        name: \"\".concat((0,_lib_enums__WEBPACK_IMPORTED_MODULE_7__.transformerPlanLabel)((_row__valuesCache_plan1 = row._valuesCache.plan) === null || _row__valuesCache_plan1 === void 0 ? void 0 : _row__valuesCache_plan1.type), \"|\").concat((_row__valuesCache_plan2 = row._valuesCache.plan) === null || _row__valuesCache_plan2 === void 0 ? void 0 : _row__valuesCache_plan2.exercise) || null\n                                    },\n                                    options: (_plans = plans) === null || _plans === void 0 ? void 0 : (_plans_data = _plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.results.map((plan)=>{\n                                        return {\n                                            code: plan.id,\n                                            name: \"\".concat((0,_lib_enums__WEBPACK_IMPORTED_MODULE_7__.transformerPlanLabel)(plan.type), \"|\").concat(plan.exercise)\n                                        };\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            var _data__data_type_properties_key_title3;\n            if (key === \"id\") return {\n                header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                accessorKey: key,\n                id: key,\n                Edit: ()=>null\n            };\n            var _data__data_type_properties_key_title4;\n            if (key === \"team\") return {\n                header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                accessorKey: key,\n                id: key,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: cell.getValue().map((usr)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__.getUserFullname)(usr)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    console.log(\"[ARBITRATION]\", row._valuesCache.team);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"font-bold\",\n                                children: \"Membres\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_11__.PickList, {\n                                source: picklistTargetValueTeam.length === 0 ? picklistSourceValueTeam : picklistSourceValueTeam.filter((user)=>picklistTargetValueTeam.map((user)=>user.username).includes(user.username)),\n                                id: \"picklist_team\",\n                                target: picklistTargetValueTeam.length > 0 ? picklistTargetValueTeam : row._valuesCache.team,\n                                sourceHeader: \"De\",\n                                targetHeader: \"A\",\n                                itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            item.firstName,\n                                            \" \",\n                                            item.lastName\n                                        ]\n                                    }, item.username, true, void 0, void 0),\n                                onChange: (e)=>{\n                                    console.log(\"source Team\", e.source);\n                                    setPicklistSourceValueTeam([\n                                        ...e.source\n                                    ]);\n                                    setPicklistTargetValueTeam([\n                                        ...e.target\n                                    ]);\n                                    row._valuesCache.team = e.target;\n                                },\n                                sourceStyle: {\n                                    height: \"200px\"\n                                },\n                                targetStyle: {\n                                    height: \"200px\"\n                                },\n                                filter: true,\n                                filterBy: \"username,email,firstName,lastName\",\n                                filterMatchMode: \"contains\",\n                                sourceFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\",\n                                targetFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true);\n                }\n            };\n            else {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), [\n        users_isLoading,\n        (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data,\n        (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_12__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data ? data_.data_.data : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data ? data_.data_.data.length : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                report: false,\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_13__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onEditingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_patch_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setEditingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" mis \\xe0 ajour\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onCreatingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            var _values_team_map;\n            rest.team = (_values_team_map = (_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) !== null && _values_team_map !== void 0 ? _values_team_map : [];\n            arbitration_create_trigger(rest, {\n                onSuccess: ()=>{\n                    table.setCreatingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" cr\\xe9\\xe9\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"add\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"align-content-center \",\n                                children: \"Cr\\xe9ation nouveau arbitrage\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: createVisible,\n                    onHide: ()=>{\n                        table.setCreatingRow(null);\n                        setCreateVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: internalEditComponents\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"align-content-center \",\n                                children: [\n                                    \"Editer l'arbitrage n\\xb0 \",\n                                    row.original.id\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: editVisible,\n                    onHide: ()=>{\n                        table.setEditingRow(null);\n                        setEditVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: [\n                            internalEditComponents,\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, this);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.report);\n        },\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"update\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                setArbitrationID(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true);\n                                console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept: accept_row_deletion,\n                                    reject: reject_row_deletion\n                                })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 565,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_20__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 565,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"uua31+vusobRieuQlddYnb78zOA=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_12__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx\n"));

/***/ })

});