import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const recommendationId = searchParams.get('recommendationId')
    const missionId = searchParams.get('missionId')
    const userId = searchParams.get('userId')

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.comment = {
        contains: search,
        mode: 'insensitive'
      }
    }
    
    if (recommendationId) {
      where.recommendationId = parseInt(recommendationId)
    }
    
    if (missionId) {
      where.recommendation = {
        missionId: parseInt(missionId)
      }
    }
    
    if (userId) {
      where.createdById = userId
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Fetch comments with relations
    const comments = await prisma.comment.findMany({
      where,
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            email: true,
            firstName: true,
            lastName: true,
          }
        },
        recommendation: {
          include: {
            mission: {
              select: {
                id: true,                
                code: true,
              }
            }
          }
        }
      },
      orderBy: {
        created: 'desc'
      },
      skip,
      take: limit,
    })

    // Get total count for pagination
    const total = await prisma.comment.count({ where })

    return NextResponse.json({
      data: comments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Comments fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { comment, recommendationId } = body

    if (!comment || !recommendationId) {
      return NextResponse.json(
        { error: 'Comment and recommendationId are required' },
        { status: 400 }
      )
    }

    // Create comment
    const newComment = await prisma.comment.create({
      data: {
        comment,
        recommendationId: parseInt(recommendationId),
        createdBy: session.user.id,
      },
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            email: true,
            firstName: true,
            lastName: true,
          }
        },
        recommendation: {
          include: {
            mission: {
              select: {
                id: true,
                code: true,
              }
            }
          }
        }
      }
    })

    return NextResponse.json(newComment, { status: 201 })
  } catch (error) {
    console.error('Comment creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
