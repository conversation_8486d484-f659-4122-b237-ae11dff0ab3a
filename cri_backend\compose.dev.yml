services:
  backend:
    build:
      context: ./cri_backend
      dockerfile: dev.Dockerfile
    ports:
      - "3001:3001"
    volumes:
      - ./cri_backend:/app
    command: gunicorn --config gunicorn_config.py app.wsgi:application
    networks:
      - cri_network
  next-app:
    container_name: cri-frontend-dev-app
    build:
      context: ./cri_frontend
      dockerfile: dev.Dockerfile

    # Set environment variables directly in the compose file
    # environment:
      # ENV_VARIABLE: ${ENV_VARIABLE}
      # NEXT_PUBLIC_ENV_VARIABLE: ${NEXT_PUBLIC_ENV_VARIABLE}

    # Set environment variables based on the .env file
    env_file:
      - ./cri_frontend/.env.local
    volumes:
      # - ./cri_frontend/src:/app/src
      - ./cri_frontend/public:/app/public
    restart: always
    ports:
      - 3000:3000
    networks:
      - cri_network

# Define a network, which allows containers to communicate
# with each other, by using their container name as a hostname
networks:
  cri_network:
