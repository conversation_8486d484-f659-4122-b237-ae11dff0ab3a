'use client'
import { User } from '@//schemas';
import { Box, DialogActions, DialogContent, Stack } from '@mui/material';
import {
  MRT_EditActionButtons,
  MRT_RowData,
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef
} from 'material-react-table';
import { MRT_Localization_FR } from 'material-react-table/locales/fr';
import { Button } from 'primereact/button';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useMemo, useRef, useState } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';
import { useApiDomainCreate, useApiDomainD<PERSON>roy, useApiDomainUpdate } from '@/hooks/useNextApi';
import { Domain } from '@prisma/client';
import parse from 'html-react-parser';

interface DropdownItem {
  name: string;
  code: string;
}
export default function GenericTable<T extends MRT_RowData>(data_: { isLoading: any; data_: any, error: any, data_type: any | undefined, pagination: any }) {

  const toast = useRef<Toast | null>(null);
  const {mutate : createDomain} = useApiDomainCreate()
  const {mutate : updateDomain} = useApiDomainUpdate()
  const {mutate : deleteDomain} = useApiDomainDestroy()
  const [visible, setVisible] = useState(false);
  const [parentDropDown, setParentDropDown] = useState<DropdownItem | null>();
  const [editVisible, setEditVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  function onPaginationChange(state: any) {
    console.log(data_.pagination);
    data_.pagination.set(state)
  };
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);


  const accept = () => {
    toast.current?.show({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 });
  };

  const reject = () => {
    toast.current?.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
  };

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setPageNumber(1);
  }
  function changePage(offset: number) {
    setPageNumber(prevPageNumber => prevPageNumber + offset);
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [rowActionEnabled, setRowActionEnabled] = useState(false);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const columns = useMemo<MRT_ColumnDef<T>[]>(
    () =>
      Object.entries(data_.data_type.properties).filter(([key, value], index) => !['modified_by', 'created_by',].includes(key)).map(([key, value], index) => {
        if (['report', 'content', 'note', 'order', 'comment', 'description'].includes(key)) {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Cell: ({ cell }) => { if (["description", "content", "report"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },
            Edit: ({ cell, column, row, table }) => {
              return <Editor
                initialValue={row.original[key]}
                tinymceScriptSrc="http://localhost:3000/tinymce/tinymce.min.js"
                apiKey='none'
                init={{
                  height: 500,
                  menubar: true,
                  plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'print', 'preview', 'anchor',
                    'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'paste', 'code', 'help', 'wordcount'
                  ],
                  toolbar:
                    'undo redo | formatselect | bold italic backcolor | \
                        alignleft aligncenter alignright alignjustify | \
                        bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen'
                }}
              />
                ;
            },
          }
        }
        if (key === "parent") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: 'parent.title',
            muiTableHeadCellProps: {
              align: 'left',
            },
            muiTableBodyCellProps: {
              align: 'left',
            },
            Edit: ({ cell, column, row, table }) => {
              const onChange = (event: DropdownChangeEvent) => {
                console.log("################AAAAAAAAAAAAAAAAAAAA####################", event.value);
                (row._valuesCache as any).parentId = event.value.code;
                (row._valuesCache as any).parent = data_.data_.data?.results?.find(dom => dom.id===event.value.code);
                setParentDropDown(event.value);
              };

              return <Dropdown optionLabel="name"
              value={{ code: row._valuesCache.parentId || null, name:  `${row._valuesCache.parent?.title}` || null }}
              onChange={onChange} filter
              options={data_.data_.data?.results?.map(function (val: Domain) { return { "code": val.id, "name": val.title } }) || []} />;
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
          }
        }
        if (data_.data_type.properties[key].format === 'date-time' || data_.data_type.properties[key].format === 'date') {

          return {
            accessorFn: (row) => new Date(key == "created" ? row.created : row.modified),
            header: data_.data_type.properties[key].title ?? key,
            filterVariant: 'date',
            filterFn: 'lessThan',
            sortingFn: 'datetime',
            accessorKey: key,
            Cell: ({ cell }) => cell.getValue<Date>()?.toLocaleDateString('fr'),
            id: key,
            Edit: () => null,
          }
        }
        if (key === "proposed_by") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0]['$ref'] ? data_.data_type.properties[key].allOf[0]['$ref'].enum : []
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Tag
              key={row.original.code + row.original.created}
              severity={cell.getValue<String>() === "VP" ?
                "danger" : cell.getValue<String>() === "STRUCT" ? "success" : "info"
              }
              value={cell.getValue<String>() === "VP" ?
                "Vice Président" : cell.getValue<String>() === "STRUCT" ? "Structures" : "Contrôle Interne"}
            >

            </Tag>
          }
        }
        if (key === "etat") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,
            muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',
              // children: data_.data_type.properties[key]['$ref'].enum,
              SelectProps: {
                // multiple: true,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            id: key,
            Cell: ({ cell, row }) => <Tag
              key={row.original.code + row.original.created}
              severity={cell.getValue<String>() === "NS" ?
                "danger" : cell.getValue<String>() === "EC" ? "success" : "info"
              }
              value={cell.getValue<String>() === "NS" ?
                "Non lancée" : cell.getValue<String>() === "SP" ? "Suspendue" : cell.getValue<String>() === "EC" ? "En cours" : "Clôturée"}
            >

            </Tag>
          }
        }
        if (key === "exercise") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },

            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                key={row.original.code + row.original.created}
                severity={
                  'success'
                }
                value={cell.getValue<String>()}
              >

              </Tag>
          }

        }
        if (data_.data_type.properties[key]['$ref'] && data_.data_type.properties[key]['$ref'].enum) {
          console.log("#######enum##########", key, value)
          return {
            header: data_.data_type.properties[key].title ?? key,
            // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+" ,"; }, ""):"",
            accessorKey: key,
            id: key,
            Cell: ({ cell, row }) => cell.row.original[key],
            editSelectOptions: data_.data_type.properties[key]['$ref'].enum,
            muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',
              children: data_.data_type.properties[key]['$ref'].enum,
              SelectProps: {
                // multiple: true,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
          }
        }
        else {
          if (key === "id") return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Edit: () => null,
          }; else
            return {
              header: data_.data_type.properties[key].title ?? key,
              accessorKey: key,
              id: key,
              // Edit: () => null,
            };

        }
      })
    ,
    [],
  );

  console.log("############## data from api ", data_)

  const table = useMaterialReactTable({
    columns,
    data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [data_.data_.data], //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,
    enableRowSelection: true, //enable some features
    enableColumnOrdering: true, //enable a feature for all columns
    enableGlobalFilter: true, //turn off a feature
    enableGrouping: true,
    enableRowActions: true,
    enableRowPinning: true,
    enableStickyHeader: true,
    enableStickyFooter: true,
    enableColumnPinning: true,
    enableColumnResizing: true,
    enableRowNumbers: true,
    enableExpandAll: true,
    enableEditing: true,
    enableExpanding: true,
    manualPagination: true,

    initialState: {
      pagination: { pageSize: 5, pageIndex: 1 },
      columnVisibility: { created_by: false, created: false, modfied_by: false, modified: false, modified_by: false, staff: false, assistants: false, id: false, document: false },
      density: 'compact',
      showGlobalFilter: true,
      sorting: [{ id: 'id', desc: false }],
    },
    state: {
      pagination: data_.pagination.pagi,
      isLoading: data_.isLoading, //cell skeletons and loading overlay
      //showProgressBars: isLoading, //progress bars while refetching
      // isSaving: isSavingTodos, //progress bars and save button spinners
    },
    localization: MRT_Localization_FR,
    onPaginationChange: onPaginationChange,
    displayColumnDefOptions: {
      'mrt-row-pin': {
        enableHiding: true,
      },
      'mrt-row-expand': {
        enableHiding: true,
      },
      'mrt-row-actions': {
        // header: 'Edit', //change "Actions" to "Edit"
        size: 100,
        enableHiding: true,

        //use a text button instead of a icon button
        // Cell: ({ row, table }) => (
        //   <Button onClick={() => table.setEditingRow(row)}>Edit Customer</Button>
        // ),
      },
      'mrt-row-numbers': {
        enableHiding: true, //now row numbers are hidable too
      },
    },
    defaultColumn: {
      grow: true,
      enableMultiSort: true,
    },
    muiTablePaperProps: ({ table }) => ({
      //elevation: 0, //change the mui box shadow
      //customize paper styles
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      classes: { root: 'p-datatable-gridlines text-900 font-medium text-xl' },

      sx: {
        height: `calc(100vh - 9rem)`,
        // height: `calc(100vh - 200px)`,
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        "& .MuiTablePagination-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
        "& .MuiBox-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
      },
    }),
    editDisplayMode: 'modal',
    createDisplayMode: 'modal',
    onEditingRowSave: ({ table, values }) => {
      //validate data
      //save data to api
      console.log("onEditingRowSave", values)
      table.setEditingRow(null); //exit editing mode
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Sauvegarde en cours' });
    },
    onEditingRowCancel: () => {
      //clear any validation errors
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });
    },
    onCreatingRowSave: ({ table, values }) => {
      //validate data
      //save data to api
      console.log("onCreatingRowSave", values)
      delete values.id
      createDomain(values)
      table.setCreatingRow(null); //exit creating mode
    },
    onCreatingRowCancel: () => {
      //clear any validation errors
    },
    muiEditRowDialogProps: ({ row, table }) => ({
      //optionally customize the dialog
      // about:"edit modal",
      open: editVisible || createVisible,
      sx: {
        '& .MuiDialog-root': {
          display: 'none'
        },
        '& .MuiDialog-container': {
          display: 'none'
        },
        zIndex: 1100

      }
    }),
    muiTableFooterProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        "& .MuiTableFooter-root": {
          backgroundColor: "var(--surface-card) !important",
        },
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableContainerProps: ({ table }) => ({
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        height: table.getState().isFullScreen ? `calc(100vh)` : `calc(100vh - 9rem - ${table.refs.topToolbarRef.current?.offsetHeight}px - ${table.refs.bottomToolbarRef.current?.offsetHeight}px)`

      },
    }),
    muiPaginationProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {

        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableHeadCellProps: {
      sx: {
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTopToolbarProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important"
      },

    },
    muiTableBodyProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        //stripe the rows, make odd rows a darker color
        '& tr:nth-of-type(odd) > td': {
          backgroundColor: 'var(--surface-card)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
        '& tr:nth-of-type(even) > td': {
          backgroundColor: 'var(--surface-border)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
      },
    },
    renderTopToolbarCustomActions: ({ table }) => (
      <Stack direction={"row"} spacing={1}>
        {/* <SpeedDial
          direction='down'
          ariaLabel="SpeedDial basic example"
          sx={{ zIndex: 30 }}
          icon={<Image src={'/images/parking/pdf.png'} alt={'PDF ICON'} width={32} height={32} />}
        >
          <SpeedDialAction
            className="text-black dark:!text-bodydark1 border-stroke bg-bodydark dark:border-strokedark dark:bg-bodydark "
            onClick={() => { handleClose(); handleExportRows(table.getRowModel().rows); }} tooltipTitle={"Exporter toutes les lignes"}></SpeedDialAction>
          <SpeedDialAction
            className="text-black dark:!text-bodydark1 border-stroke bg-bodydark dark:border-strokedark dark:bg-bodydark "
            onClick={() => { handleClose(); handleExportRows(table.getRowModel().rows); }} tooltipTitle={"Exporter les lignes de la Page"}></SpeedDialAction>
          <SpeedDialAction
            className="text-black dark:!text-bodydark1 border-stroke bg-bodydark dark:border-strokedark dark:bg-bodydark "
            onClick={() => { handleClose(); handleExportRows(table.getRowModel().rows); }} tooltipTitle={"Exporter les lignes selectionnées"}></SpeedDialAction>
        </SpeedDial> */}
        {/* <Tooltip title="Exporter PDF"> */}
        <Button
          icon="pi pi-plus"
          rounded
          // id="basic-button"
          aria-controls={open ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={(event) => {
            table.setCreatingRow(true); setCreateVisible(true), console.log("creating row ...");
          }}

          size="small"
        >
        </Button>
        <Button
          rounded
          disabled={table.getIsSomeRowsSelected()}
          // id="basic-button"
          aria-controls={open ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
          icon="pi pi-trash"
          // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}
          size="small"
        >
        </Button>
        <Button icon="pi pi-bell" rounded color={rowActionEnabled ? "secondary" : "primary"} size="small"
          aria-label="edit" onClick={() => setRowActionEnabled(!rowActionEnabled)}>
        </Button>


      </Stack>
      // <Menu
      //   sx={{
      //     display: 'flex',
      //     gap: '16px',
      //     padding: '8px',
      //     flexWrap: 'wrap',
      //   }} open={true}      >
      //   <Button
      //     disabled={table.getPrePaginationRowModel().rows.length === 0}
      //     //export all rows, including from the next page, (still respects filtering and sorting)
      //     onClick={() =>
      //       handleExportRows(table.getRowModel().rows)
      //     }
      //     startIcon={<FileDownloadIcon />}
      //   >
      //     Exporter toutes les lignes
      //   </Button>
      //   <Button
      //     disabled={table.getRowModel().rows.length === 0}
      //     //export all rows as seen on the screen (respects pagination, sorting, filtering, etc.)
      //     onClick={() => handleExportRows(table.getRowModel().rows)}
      //     startIcon={<PictureAsPdf />}
      //   >
      //     Exporter les lignes de la Page
      //   </Button>
      //   <Button
      //     disabled={
      //       !table.getIsSomeRowsSelected() && !table.getIsAllRowsSelected()
      //     }
      //     //only export selected rows
      //     onClick={() => handleExportRows(table.getSelectedRowModel().rows)}
      //     startIcon={<FileDownloadIcon />}
      //   >
      //     Exporter les lignes selectionnées
      //   </Button>
      // </Menu>
    ),
    muiDetailPanelProps: () => ({
      sx: (theme) => ({
        backgroundColor:
          theme.palette.mode === 'dark'
            ? 'rgba(255,210,244,0.1)'
            : 'rgba(0,0,0,0.1)',
      }),
    }),

    renderCreateRowDialogContent: ({ internalEditComponents, row, table }) => <> <div style={{ zIndex: '1302 !important' }}>
      <Sidebar position='right' header={
        <div className='flex flex-row w-full flex-wrap justify-content-between'><span className='align-content-center '>Création {data_.data_type.name} {row.original.code}</span>
          <DialogActions>
            <MRT_EditActionButtons variant="text" table={table} row={row} />
          </DialogActions>
        </div>} visible={createVisible} onHide={() => { table.setCreatingRow(null); setCreateVisible(false) }} className="w-full md:w-9 lg:w-8">
        <DialogContent
          sx={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}
        >

          {internalEditComponents} {/* or render custom edit components here */}
        </DialogContent>

      </Sidebar>
    </div></>,
    renderEditRowDialogContent: ({ internalEditComponents, row, table }) => <><div style={{ zIndex: '1302 !important' }}>
      <Sidebar position='right' header={
        <div className='flex flex-row w-full flex-wrap justify-content-between'><span className='align-content-center '>Editer {data_.data_type.name} {row.original.code}</span>
          <DialogActions>
            <MRT_EditActionButtons variant="text" table={table} row={row} />
          </DialogActions>
        </div>} visible={editVisible} onHide={() => { table.setEditingRow(null); setEditVisible(false) }} className="w-full md:w-9 lg:w-8">
        <DialogContent
          sx={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}
        >

          {internalEditComponents} {/* or render custom edit components here */}
        </DialogContent>

      </Sidebar>
    </div></>,
    renderDetailPanel: ({ row }) =>
      row.original.description ? parse(row.original.description) :
        row.original.content ? parse(row.original.content) :
          row.original.staff ?
            row.original.staff.length > 0 ? (
              <Box
                sx={{
                  display: 'grid',
                  margin: 'auto',
                  //gridTemplateColumns: '1fr 1fr',
                  width: '100vw',
                }}
              >
                <TabView>

                  <TabPanel header={data_.data_type.properties["staff"].title} leftIcon="pi pi-user mr-2">

                    <ul>{row.original.staff.map((user, idx) => <a key={user.email + row.original.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>
                  </TabPanel>
                  <TabPanel header={data_.data_type.properties["assistants"].title} rightIcon="pi pi-user ml-2">

                    <ul>{row.original.assistants.map((user, idx) => <a key={user.email + row.original.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>

                  </TabPanel>
                  <TabPanel header="Lettre" leftIcon="pi pi-file-word mr-2" rightIcon="pi pi-file-pdf ml-2">
                    <Button icon="pi pi-check" rounded onClick={() => setVisible(true)} disabled={row.original.document === null} />
                    <Sidebar key={row.original.id} header={<h2>Lettre de mission : {row.original.code}</h2>} visible={visible} onHide={() => setVisible(false)} className="w-full md:w-9 lg:w-8">

                      <div className="flex flex-column	align-items-center justify-content-center gap-1">
                        {row.original.document !== null ?
                          <Document
                            file={row.original.document}
                            onLoadSuccess={onDocumentLoadSuccess}
                          >
                            <Page pageNumber={pageNumber} />
                          </Document> : <p>No Document</p>}
                        <div className='flex flex-column	align-items-center justify-content-center gap-1' >
                          <p>
                            Page {pageNumber || (numPages ? 1 : '--')} of {numPages || '--'}
                          </p>
                          <div className='flex flex-row	align-items-center justify-content-center gap-1' >
                            <Button
                              type="button"
                              disabled={pageNumber <= 1}
                              onClick={previousPage}
                            >
                              Previous
                            </Button>
                            <Button
                              type="button"
                              disabled={pageNumber >= numPages}
                              onClick={nextPage}
                            >
                              Next
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Sidebar>
                  </TabPanel>          </TabView>
              </Box >
            ) : null : <></>,
    renderRowActions: ({ cell, row, table }) => (
      // <Box sx={{ display: 'flex', gap: '1rem' }}>
      <span className="p-buttonset flex p-1">
        <Button size='small' icon="pi pi-pencil" onClick={() => { table.setEditingRow(row); setEditVisible(true), console.log("editing row ..."); }} rounded outlined />
        <Button size='small' icon="pi pi-trash" rounded outlined
          onClick={(event) => confirmPopup({
            target: event.currentTarget,
            message: 'Voulez-vous supprimer cette ligne?',
            icon: 'pi pi-info-circle',
            // defaultFocus: 'reject',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Oui',
            rejectLabel: 'Non',
            accept,
            reject
          })}
        />
        <ConfirmPopup />
      </span>
      // </Box>
    ),
    // mrtTheme: (theme) => ({
    //   baseBackgroundColor: "#1f2937",
    //   draggingBorderColor: theme.palette.secondary.main,
    // }),

  });
  // console.log(data_.isLoading)
  //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable
  //but the useMaterialReactTable hook will be the most recommended way to define table options
  return <><MaterialReactTable table={table} /><Toast ref={toast} /></>;
}
