{"name": "sakai-react", "version": "10.1.0", "private": true, "scripts": {"generate:schema": "dotenv -e .env.local -- cross-var openapi-ts -i \"$NEXT_PUBLIC_URL_API/api/schema/?format=json\" -o $NEXT_PUBLIC_REST_CLIENT_PATH/openapi_client", "generate:api": "orval", "dev": "next dev", "build": "next build", "start": "next start", "format": "prettier --write \"{app,demo,layout,types}/**/*.{js,ts,tsx,d.ts}\"", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:seed-entities": "tsx prisma/seed-entities-only.ts", "db:reset": "prisma db push --force-reset && npm run db:seed", "admin:reset": "tsx scripts/reset-admin.ts", "admin:check": "tsx scripts/check-password-storage.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@casl/ability": "^6.7.3", "@casl/prisma": "^1.5.1", "@casl/react": "^4.0.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hey-api/client-fetch": "^0.1.3", "@meilisearch/instant-meilisearch": "^0.19.0", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@mui/x-date-pickers": "^7.7.0", "@novu/react": "^2.6.3", "@prisma/client": "^6.8.2", "@react-pdf-viewer/core": "3.6.0", "@react-pdf-viewer/default-layout": "3.6.0", "@react-pdf-viewer/locales": "1.0.0", "@tanstack/react-query": "^5.74.8", "@tanstack/react-query-devtools": "^5.74.8", "@thednp/dommatrix": "^2.0.11", "@tinymce/tinymce-react": "^5.1.1", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.5", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "chance": "^1.1.11", "chart.js": "4.2.1", "cookies-next": "^4.2.1", "jsonwebtoken": "^9.0.2", "jose": "^5.2.0", "core-js": "^3.37.1", "gantt-task-react": "^0.3.9", "html-react-parser": "^5.1.10", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "material-react-table": "^2.13.0", "next": "13.4.8", "openapi-fetch": "^0.9.7", "oracledb": "^6.5.1", "pdfjs-dist": "^2.16.105", "primeflex": "^3.3.1", "primeicons": "7.0.0", "primereact": "10.2.1", "prisma": "^6.8.2", "quill": "^2.0.3", "raw-loader": "^4.0.2", "react": "18.2.0", "react-big-scheduler": "^0.2.7", "react-big-scheduler-stch": "^1.3.1", "react-dnd": "14.0.5", "react-dnd-html5-backend": "14.0.1", "react-dom": "18.2.0", "react-query": "^3.39.3", "reflect-metadata": "^0.2.2", "safe-html": "^1.0.0", "sanitize-html": "^2.13.0", "swr": "^2.2.5", "ts-node": "^10.9.2", "typescript": "5.1.3", "usehooks-ts": "^3.1.0"}, "devDependencies": {"@hey-api/openapi-ts": "^0.46.3", "@openapi-contrib/openapi-schema-to-json-schema": "^5.1.0", "@types/node": "20.3.1", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "@types/sanitize-html": "^2.11.0", "copy-webpack-plugin": "^12.0.2", "cross-var": "1.1.0", "dotenv-cli": "8.0.0", "eslint": "8.43.0", "eslint-config-next": "13.4.6", "nextjs-node-loader": "^1.1.5", "openapi-typescript": "^7.0.0-rc.0", "orval": "7.3.0", "prettier": "^2.8.8", "sass": "^1.63.4", "tsx": "^4.19.4"}}