// Custom auth client to match the previous API
export const signIn = {
  email: async (credentials: { email: string; password: string; rememberMe?: boolean; callbackURL?: string }, callbacks?: {
    onRequest?: () => void;
    onResponse?: () => void;
    onSuccess?: (ctx: any) => void;
    onError?: (ctx: { error: { message: string } }) => void;
  }) => {
    try {
      callbacks?.onRequest?.();

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: credentials.email,
          password: credentials.password,
        }),
      });

      callbacks?.onResponse?.();

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        callbacks?.onError?.({ error: { message: errorData.error || 'Lo<PERSON> failed' } });
        return;
      }

      const result = await response.json();
      callbacks?.onSuccess?.(result);
    } catch (error) {
      callbacks?.onResponse?.();
      callbacks?.onError?.({ error: { message: '<PERSON><PERSON> failed' } });
    }
  }
};

export const signOut = async () => {
  try {
    await fetch('/api/auth/logout', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return { success: true };
  } catch (error) {
    console.error('Logout error:', error);
    return { success: false };
  }
};

export const signUp = {
  email: async (data: any) => {
    // Implement registration if needed
    throw new Error('Registration not implemented');
  }
};

// Custom useSession hook
export function useSession() {
  // This would be implemented using React context
  // For now, return a placeholder
  return {
    data: null,
    status: 'loading'
  };
}