import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    const { email, password, username } = await request.json()

    // Basic validation
    if (!email || !password || !username) {
      return NextResponse.json(
        { error: 'Email, password, and username are required' },
        { status: 400 }
      )
    }

    // Check if admin already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    let user;

    if (existingUser) {
      // User exists, create/update their authentication
      console.log('User exists, creating authentication...')

      // Hash the password
      const hashedPassword = await bcrypt.hash(password, 12)

      // Check if account already exists
      const existingAccount = await prisma.account.findFirst({
        where: {
          userId: existingUser.id,
          providerId: 'credential'
        }
      })

      if (existingAccount) {
        // Update existing account with new password
        await prisma.account.update({
          where: { id: existingAccount.id },
          data: { password: hashedPassword }
        })
      } else {
        // Create new account record
        await prisma.account.create({
          data: {
            userId: existingUser.id,
            password: hashedPassword,
            accountId : existingUser.id,
            providerId: 'credential',

          }
        })
      }

      // Update the existing user record
      user = await prisma.user.update({
        where: { email },
        data: {
          username,
          firstName: 'Admin',
          lastName: 'User',
          isActive: true,
          isStaff: true,
          isSuperuser: true,
          emailVerified: new Date(),
        },
      })
    } else {
      // User doesn't exist, create new one
      const hashedPassword = await bcrypt.hash(password, 12)

      // Create user first
      user = await prisma.user.create({
        data: {
          email,
          username,
          name: username,
          firstName: 'Admin',
          lastName: 'User',
          isActive: true,
          isStaff: true,
          isSuperuser: true,
          emailVerified: new Date(),
        },
      })

      // Create account record with password
      await prisma.account.create({
        data: {
          userId: user.id,
          providerId: 'credential',
          accountId: user.id,
          password: hashedPassword,
        }
      })
    }

    // Assign Super Admin role
    const superAdminRole = await prisma.role.findUnique({
      where: { name: 'Super Admin' }
    })

    if (superAdminRole) {
      await prisma.userRole.upsert({
        where: {
          userId_roleId: {
            userId: user.id,
            roleId: superAdminRole.id,
          },
        },
        update: {},
        create: {
          userId: user.id,
          roleId: superAdminRole.id,
        },
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Admin user created successfully',
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        isStaff: user.isStaff,
        isSuperuser: user.isSuperuser,
      },
    })

  } catch (error) {
    console.error('Error creating admin:', error)
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message,
        type: error.name
      },
      { status: 500 }
    )
  }
}
