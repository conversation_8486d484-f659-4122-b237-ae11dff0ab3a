'use client';

import BlockViewerNoHeader from '@/utilities/components/BlockViewerNoHeader';
import { $CommentRead, CommentRead, Action, $Action, Recommendation } from '@/services/openapi_client';

import parse from 'html-react-parser';
import sanitizeHtml from 'sanitize-html';
import { Chip } from 'primereact/chip';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { ProgressBar } from 'primereact/progressbar';
import { Tag } from 'primereact/tag';
import { TabPanel, TabView } from 'primereact/tabview';
import { Dialog } from 'primereact/dialog';
import { useEffect, useState } from 'react';
import { Stack, Typography } from '@mui/material';
import { getRecommendationPrioritySeverity, getUserFullname } from '@/utilities/functions/utils';
import { usebaseData } from '@/utilities/hooks/useBaseData';
import { useParams, useSearchParams } from 'next/navigation';

const RecommendationDetails = ({ params }: { params: { recommendation: Recommendation, detailsDialogVisible: boolean, setDetailsDialogVisible: Function } }) => {
    const searchParams = useParams()
    const { recommendation, detailsDialogVisible, setDetailsDialogVisible } = params;
    const { users } = usebaseData()

    const generateColumnsForActions = () => {
        let columns = [];
        for (const [key, value] of Object.entries($Action.properties).filter(([key, value], index) => !['dependencies', 'created_by', 'modified_by', 'created', 'modified', 'recommendation', 'comments', 'id'].includes(key))) {
            if (key === 'description') columns.push(<Column field={key} body={(data) => parse(sanitizeHtml(data.description))} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            else if (key === 'job_leader') columns.push(<Column field={key} body={(data) => `${data.job_leader.last_name} ${data.job_leader.first_name}`} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            else if (['start_date', 'end_date'].includes(key)) columns.push(<Column field={key} body={(data) => new Date(data[key]).toLocaleDateString()} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            else if (['progress'].includes(key)) columns.push(<Column field={key} body={(data) => <ProgressBar value={data[key]}></ProgressBar>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            else if (['status'].includes(key)) columns.push(<Column field={key} body={(data) => <Tag value={data[key]}></Tag>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            else if (['validated', 'accepted'].includes(key)) columns.push(<Column field={key} align={'center'} body={(data) => data[key] ? <i className="pi pi-check-circle" style={{ color: 'green' }}></i> : <i className="pi pi-times-circle" style={{ color: 'red' }}></i>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            else columns.push(<Column field={key} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
        }
        return columns;
    }
    const generateColumnsForComments = () => {
        let columns = [];
        for (const [key, value] of Object.entries($CommentRead.properties).filter(([key, value], index) => !['accepted', 'action', 'validated', 'id', 'modified_by', 'recommendation', 'validMeraci', 'validDirecteur'].includes(key))) {
            if (key === 'recommendation') columns.push(<Column field={key} body={(data) => parse(sanitizeHtml(data.description))} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '10%' }} />)
            else if (key === 'comment') columns.push(<Column field={key} header={$CommentRead.properties[key].title ? $CommentRead.properties[key].title : key} sortable style={{ width: '10%' }} />)
            else if (['created', 'modified'].includes(key)) columns.push(<Column field={key} body={(data) => new Date(data[key]).toLocaleDateString()} header={$CommentRead.properties[key].title ? $CommentRead.properties[key].title : key} sortable style={{ width: '10%' }} />)
            // else if (['validated', 'accepted'].includes(key)) columns.push(<Column field={key} body={(data) => data[key]?<i className="pi pi-check-circle" style={{ color: 'green' }}></i>:<i className="pi pi-times-circle" style={{ color: 'red' }}></i> } header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            else if (['created_by'].includes(key)) columns.push(<Column field={key} body={(data) => `${data[key].last_name} ${data[key].first_name}`} header={'utilisateur'} sortable style={{ width: '10%' }} />)
            else columns.push(<Column field={key} header={$CommentRead.properties[key].title ? $CommentRead.properties[key].title : key} sortable />)
        }
        return columns;
    }

    return (
        <Dialog maximizable header={
            <Stack direction={'row'} spacing={2} alignItems={'center'} alignContent={'center'} >
                <Typography variant='h5'>Recommendation</Typography> <Tag className='surface-900 text-lg w-4rem' value={`# ${recommendation?.numrecommandation}`}></Tag>
                <Tag className='bg-yellow-300 text-lg border-1 border-dashed' value={recommendation?.status}></Tag>
                <Tag className='bg-cyan-500 text-lg border-1 border-dashed' value={recommendation?.accepted ? 'Acceptée' : 'Non-Acceptée'}></Tag>
                <Tag className='bg-pink-500 text-lg border-1 border-dashed' value={recommendation?.validated ? 'Validée' : 'Non-Validée'}></Tag>
            </Stack>
        }
            visible={detailsDialogVisible} style={{ width: '70vw' }} onHide={() => { if (!detailsDialogVisible) return; setDetailsDialogVisible(false); }}>
            <div className="grid">
                <div className="col-12 ">
                    <BlockViewerNoHeader
                        header={`Mission ${recommendation?.mission}`}
                        containerClassName="surface-0 px-4 py-2 md:px-6 lg:px-6"
                        status={recommendation?.status}
                        priority={recommendation?.priority}
                    >
                        <div className="surface-0">
                            <ul className="list-none p-0 m-0">

                                <li className="flex align-items-center py-3 px-2 flex-wrap">
                                    <div className="text-500 w-6 md:w-2 font-medium">Recommandation</div>
                                    <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1 line-height-3">{recommendation?.recommendation}</div>
                                </li>
                                <li className="flex align-items-center py-3 px-2 border-top-1  border-300 flex-wrap">
                                    <div className="text-500 w-6 md:w-2 font-medium">Mission</div>
                                    <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">
                                        {/* {recommendation?.mission} */}
                                        <Tag
                                            className='w-12rem'
                                            style={{ fontSize: 12, fontFamily: "monospace", color: 'var(--text-color)', background: 'transparent', border: ' 2px solid orange' }}
                                            severity={
                                                'success'
                                            }
                                            value={recommendation?.mission}
                                        />
                                    </div>
                                </li>
                                <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                    <div className="text-500 w-6 md:w-2 font-medium">Structure concernée</div>
                                    <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">
                                        <Chip style={{ fontSize: 12, fontWeight: "bold", fontFamily: "monospace", color: 'var(--text-color)', background: 'transparent', border: ' 2px dotted green', borderRadius: 50 }} label={recommendation?.concerned_structure.code_mnemonique || recommendation?.concerned_structure.code_stru || recommendation?.concerned_structure.libell_stru} className="mr-2" />
                                    </div>
                                </li>
                                <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                    <div className="text-500 w-6 md:w-2 font-medium">Responsable</div>
                                    <div className="text-900 font-bold w-full md:w-8 md:flex-order-0 flex-order-1">{`${getUserFullname(users.data?.data.results.find(usr => usr.username === recommendation?.responsible))}`} ({`${recommendation?.responsible}`})</div>
                                </li>
                                {/* <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Statut</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">{`${recommendation?.status}`}</div>
                            </li>
                            <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Acceptation</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">{`${recommendation?.accepted}`}</div>
                            </li>
                            <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Validation</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">{`${recommendation?.validated}`}</div>
                            </li> */}
                                <li className="flex align-items-center py-6 px-2 border-top-1 border-300 flex-wrap">
                                    <TabView className='w-full'>
                                        <TabPanel header={"Actions"} rightIcon="pi pi-thumbs-up ml-2" className='align-content-center align-items-center justify-content-center'>
                                            <DataTable<Action[]> style={{ width: '100%' }} value={recommendation?.actions} rows={5} paginator scrollable resizableColumns>
                                                {generateColumnsForActions()}
                                            </DataTable>
                                        </TabPanel>
                                        <TabPanel header={"Commentaires"} rightIcon="pi pi-thumbs-up ml-2" className='align-content-center align-items-center justify-content-center'>
                                            <DataTable<CommentRead[]> style={{ width: '100%' }} value={recommendation?.comments} rows={5} paginator scrollable resizableColumns>
                                                {generateColumnsForComments()}
                                            </DataTable>
                                        </TabPanel>
                                    </TabView>
                                </li>
                                <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                    {/* <CommentTimeLine data={recommendation?.comments}/> */}
                                </li>
                            </ul>
                        </div>
                    </BlockViewerNoHeader>
                </div>
            </div>
        </Dialog>
    );
};

export default RecommendationDetails;
