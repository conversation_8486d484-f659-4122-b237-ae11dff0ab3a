"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-client)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RootLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../layout/context/layoutcontext */ \"(app-client)/./layout/context/layoutcontext.tsx\");\n/* harmony import */ var primereact_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/api */ \"(app-client)/./node_modules/primereact/api/api.esm.js\");\n/* harmony import */ var primereact_resources_primereact_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! primereact/resources/primereact.css */ \"(app-client)/./node_modules/primereact/resources/primereact.css\");\n/* harmony import */ var primeflex_primeflex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! primeflex/primeflex.css */ \"(app-client)/./node_modules/primeflex/primeflex.css\");\n/* harmony import */ var primeicons_primeicons_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! primeicons/primeicons.css */ \"(app-client)/./node_modules/primeicons/primeicons.css\");\n/* harmony import */ var _styles_layout_layout_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../styles/layout/layout.scss */ \"(app-client)/./styles/layout/layout.scss\");\n/* harmony import */ var _styles_demo_Demos_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../styles/demo/Demos.scss */ \"(app-client)/./styles/demo/Demos.scss\");\n/* harmony import */ var _contexts_CustomAuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/CustomAuthContext */ \"(app-client)/./contexts/CustomAuthContext.tsx\");\n/* harmony import */ var _utilities_hooks_useSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utilities/hooks/useSpinner */ \"(app-client)/./utilities/hooks/useSpinner.tsx\");\n/* harmony import */ var _utilities_hooks_useToast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utilities/hooks/useToast */ \"(app-client)/./utilities/hooks/useToast.tsx\");\n/* harmony import */ var _utilities_service_fr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utilities/service/fr */ \"(app-client)/./utilities/service/fr.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(app-client)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.QueryClient();\nfunction RootLayout(param) {\n    let { children } = param;\n    (0,primereact_api__WEBPACK_IMPORTED_MODULE_12__.addLocale)(\"fr\", _utilities_service_fr__WEBPACK_IMPORTED_MODULE_10__[\"default\"][\"fr\"]);\n    (0,primereact_api__WEBPACK_IMPORTED_MODULE_12__.locale)(\"fr\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        id: \"theme-css\",\n                        href: \"/themes/lara-light-indigo/theme.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"/tinymce/tinymce.min.js\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_api__WEBPACK_IMPORTED_MODULE_12__.PrimeReactProvider, {\n                    value: {\n                        locale: \"fr\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.QueryClientProvider, {\n                        client: queryClient,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CustomAuthContext__WEBPACK_IMPORTED_MODULE_7__.AuthProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utilities_hooks_useToast__WEBPACK_IMPORTED_MODULE_9__.ToastContextProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utilities_hooks_useSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinnerProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_1__.LayoutProvider, {\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_14__.ReactQueryDevtools, {}, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 9\n    }, this);\n}\n_c = RootLayout;\nvar _c;\n$RefreshReg$(_c, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/layout.tsx\n"));

/***/ })

});