'use client';

import { useApiArbitratedtheme<PERSON>ist, useApiPlanList, useApiUsersList } from '@/services/api/api/api';
import { $MissionEtatEnum, $MissionTypeEnum } from '@/services/openapi_client';
import { ArbitratedTheme, MissionSerializerRead, Plan, User } from '@/services/schemas';
import { getMissionEtatSeverity, getMissionTypeSeverity } from '@/utilities/functions/utils';
import { Box, DialogContent, Stack, Typography } from '@mui/material';
import { getCookie } from 'cookies-next';
import { MRT_EditActionButtons, MRT_TableInstance, type MRT_Row } from 'material-react-table';
import Link from 'next/link';
import { addLocale, locale } from 'primereact/api';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Dialog } from 'primereact/dialog';
import { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';
import { PaginatorPageChangeEvent } from 'primereact/paginator';
import { PickList, PickListChangeEvent } from 'primereact/picklist';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Sidebar } from 'primereact/sidebar';
import { Tag } from 'primereact/tag';
import { FormEvent } from 'primereact/ts-helpers';
import React, { useEffect, useMemo, useRef, useState, type ReactNode } from 'react';
import ThemeEditForm from '../../themes/(components)/editForm';
import ThemeEditFormDecoupled from '../../themes/(components)/editFormDecoupled';
import { usebaseData } from '@/utilities/hooks/useBaseData';
import { FileUpload, FileUploadHandlerEvent, FileUploadHeaderTemplateOptions, FileUploadSelectEvent, ItemTemplateOptions } from 'primereact/fileupload';
import { ProgressBar } from 'primereact/progressbar';
import { AutoComplete, AutoCompleteCompleteEvent, AutoCompleteUnselectEvent } from 'primereact/autocomplete';


interface DropdownItem {
    name: string;
    code: string | number;
}

const MissionEditForm = (props: { internalEditComponents: ReactNode[]; row: MRT_Row<MissionSerializerRead>; table: MRT_TableInstance<MissionSerializerRead>; }) => {
    ///////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////    
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    const steps = ['Mission', 'Equipe CI', 'Assistants'];
    // ///////////////////////////////////////////////////////////////////////////
    // const customUploader = (event:FileUploadSelectEvent) => {
    //     setDocument(event.files)
    //     props.row._valuesCache = { ...props.row._valuesCache, ...{ document: event.files } }
    // }
    // const onTemplateRemove = (file, callback) => {
    //     callback();
    // };
    // const headerTemplate = (options:FileUploadHeaderTemplateOptions) => {
    //     const { className, chooseButton, uploadButton, cancelButton } = options;
    //     console.log("++++++++----------+++++++++++++",cancelButton)
    //     return (
    //         <div className={className} style={{ backgroundColor: 'transparent', display: 'flex', alignItems: 'center' }}>               
    //             <Button style={{width:'38px'}} rounded severity='success' icon='pi pi-fw pi-images'onClick={chooseButton.props.onClick}></Button>    
    //             <Button style={{width:'38px'}} rounded severity='danger' icon='pi pi-fw pi-times' onClick={cancelButton.props.onClick}></Button>    
    //             {/* <div className="flex align-items-center gap-3 ml-auto">
    //                 <span>{1200} / 1 MB</span>
    //                 <ProgressBar value={50} showValue={false} style={{ width: '10rem', height: '12px' }}></ProgressBar>
    //             </div> */}
    //         </div>
    //     );
    // };
    // const itemTemplate = (file : object, props: ItemTemplateOptions) => {
    //     return (
    //         <div className="flex align-items-center flex-wrap">
    //             <div className="flex align-items-center gap-4" style={{ width: '40%' }}>
    //                 <img alt={file.name} role="presentation" src={file.type.includes('image')? file.objectURL :'/images/pdf.webp' } width={50} />
    //                 <span className="flex flex-column text-left ml-3">
    //                     {file.name}
    //                     <small>{new Date().toLocaleDateString()}</small>
    //                 </span>
    //             </div>
    //             <Tag value={props.formatSize} severity="warning" className="px-3 py-2" />
    //             <Button type="button" icon="pi pi-times" className="p-button-outlined p-button-rounded p-button-danger ml-auto" onClick={() => onTemplateRemove(file, props.onRemove)} />
    //         </div>
    //     );
    // };
    // ///////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    const [page, setPage] = useState(1);
    const fileUploadRef = useRef<FileUpload>(null);

    const [addThemeDialogVisible, setAddThemeDialogVisible] = useState(false);
    // const { data: plans, isLoading: plan_isLoading, error: plan_error } = useApiPlanList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})    
    // const { data: themes, isLoading: theme_isLoading, error: theme_error } = useApiArbitratedthemeList({ mission: [] },{ axios: { headers: { Authorization: `Token ${user?.token}` } }})
    // const { data: users, isLoading, error } = useApiUsersList({ page: page, page_size: 40000 },{ axios: { headers: { Authorization: `Token ${user?.token}` } }});
    const { users, plans, arbitratedThemes: themes, } = usebaseData()
    const [selectedDocument, setSelectedDocument] = useState(false)
    //TODO MOVE THIS TO USEEFFECT
    const mission_theme: ArbitratedTheme | undefined | null = props.row.id === "mrt-row-create" ? undefined : props.row.original.theme//themes?.data.data.results.filter((theme: ArbitratedTheme) => theme.id === props.row.original.theme.id)[0];
    const mission_plan: Plan | undefined | null = props.row.id === "mrt-row-create" ? undefined : plans?.data.data.results.filter((plan: Plan) => plan.code === props.row.original.plan)[0];
    const users_list: User[] | undefined = useMemo(() => users?.data?.data.results ?? [], [users]);


    //TODO check create new record
    // const [picklistSourceValueTeam, setPicklistSourceValueTeam] = useState<User[]>(props.row.id === "mrt-row-create" ? users_list : users_list?.filter((val, idx) => !props.row.original.staff.map((user) => user.username).includes(val.username)));
    // const [picklistTargetValueTeam, setPicklistTargetValueTeam] = useState<User[]>(props.row.original.staff ?? []);
    const [sourceValueTeam, setSourceValueTeam] = useState<User[]>(props.row.id === "mrt-row-create" ? users_list : users_list?.filter((val, idx) => !props.row.original.staff.map((user) => user.username).includes(val.username)));
    const [filteredSourceValueTeam, setFilteredSourceValueTeam] = useState<User[]>([]);
    const [targetValueTeam, setTargetValueTeam] = useState<User[]>(props.row.original.staff ?? []);
    const [sourceValueAssistants, setSourceValueAssistants] = useState<User[]>(props.row.id === "mrt-row-create" ? users_list : users_list?.filter((val, idx) => !props.row.original.staff.map((user) => user.username).includes(val.username)));
    const [filteredSourceValueAssistants, setFilteredSourceValueAssistants] = useState<User[]>([]);
    const [targetValueAssistants, setTargetValueAssistants] = useState<User[]>(props.row.original.assistants ?? []);
    // const [picklistSourceValueAssistants, setPicklistSourceValueAssistants] = useState<User[]>(props.row.id === "mrt-row-create" ? users_list : users_list?.filter((val, idx) => !props.row.original.assistants.map((user) => user.username).includes(val.username)));
    // const [picklistTargetValueAssistants, setPicklistTargetValueAssistants] = useState<User[]>(props.row.original.assistants ?? []);
    const searchTeam = (event: AutoCompleteCompleteEvent) => {
        // Timeout to emulate a network connection
        console.log("AutoCompleteCompleteEvent", event, sourceValueTeam)
        setTimeout(() => {
            let _filteredUsers;

            if (!event.query.trim().length) {
                _filteredUsers = [...sourceValueTeam];
            }
            else {
                _filteredUsers = sourceValueTeam.filter((user: User) => {
                    return user.first_name?.toLowerCase().startsWith(event.query.toLowerCase()) || user.last_name?.toLowerCase().startsWith(event.query.toLowerCase()) || user.username?.toLowerCase().startsWith(event.query.toLowerCase());
                });
            }

            setFilteredSourceValueTeam(_filteredUsers);
        }, 250);
    }
    const searchAssistants = (event: AutoCompleteCompleteEvent) => {
        // Timeout to emulate a network connection
        console.log("AutoCompleteCompleteEvent", event, sourceValueTeam)
        setTimeout(() => {
            let _filteredUsers;

            if (!event.query.trim().length) {
                _filteredUsers = [...sourceValueAssistants];
            }
            else {
                _filteredUsers = sourceValueAssistants.filter((user: User) => {
                    return user.first_name?.toLowerCase().startsWith(event.query.toLowerCase()) || user.last_name?.toLowerCase().startsWith(event.query.toLowerCase()) || user.username?.toLowerCase().startsWith(event.query.toLowerCase());
                });
            }

            setFilteredSourceValueAssistants(_filteredUsers);
        }, 250);
    }
    const teamItemTemplate = (item: User) => {
        return (
            <div className="flex align-items-center">{item.first_name} {item.last_name}</div>
        );
    };
    const selectedTeamItemTemplate = (item: User) => {
        return (
            <div className="flex align-items-center">{item.first_name} {item.last_name}</div>
        );
    };
    const unselectTeam = (event: AutoCompleteUnselectEvent) => console.log(event)
    const [editVisible, setEditVisible] = useState(true);
    const [missionCode, setMissionCode] = useState(props.row.original.code || '');
    const [missionExercise, setMissionExercise] = useState<string | null>(props.row.original.exercise || null);
    const [dropdownItemType, setDropdownItemType] = useState<DropdownItem | null>(props.row.id === "mrt-row-create" ? { "name": null, "code": null } : { "name": props.row.original.type, "code": props.row.original.type });
    const [dropdownItemEtat, setDropdownItemEtat] = useState<DropdownItem | null>({ "name": props.row.original.etat, "code": props.row.original.etat });
    const [dropdownItemPlan, setDropdownItemPlan] = useState<DropdownItem | undefined | null>(props.row.id === "mrt-row-create" ? null : props.row.original.plan ? { "name": mission_plan?.code, "code": mission_plan?.id } : null);
    const [dropdownItemTheme, setDropdownItemTheme] = useState<DropdownItem | null>(props.row.id !== "mrt-row-create" ? { "name": mission_theme?.theme?.title, "code": mission_theme?.id } : null);

    const [dropdownItemSupervisor, setDropdownItemSupervisor] = useState<DropdownItem | null>(props.row.id !== "mrt-row-create" ? { "name": `${props.row.original.supervisor?.last_name} ${props.row.original.supervisor?.first_name}`, "code": props.row.original.supervisor?.id } : null);
    const [dropdownItemHead, setDropdownItemHead] = useState<DropdownItem | null>(props.row.id !== "mrt-row-create" ? props.row.original.head ? { "name": `${props.row.original.head?.last_name} ${props.row.original.head?.first_name}`, "code": props.row.original.head?.id } : null : null);

    const [startDate, setStartDate] = useState(props.row.id === "mrt-row-create" ? new Date() : new Date(props.row.original.start_date))
    const [endDate, setEndDate] = useState(props.row.id === "mrt-row-create" ? new Date() : new Date(props.row.original.end_date))
    const [document, setDocument] = useState(props.row.id === "mrt-row-create" ? null : props.row.original.document)
    const [activeStep, setActiveStep] = React.useState(0);
    const [skipped, setSkipped] = React.useState(new Set<number>());
    useEffect(() => {
        // setPicklistTargetValueTeam(props.row.original.staff ?? [])
        // setPicklistSourceValueTeam(props.row.id === "mrt-row-create" ? users_list : users_list?.filter((val, idx) => !props.row.original.staff.map((user) => user.username).includes(val.username)));
        // setPicklistTargetValueAssistants(props.row.original.assistants ?? [])
        // setPicklistSourceValueAssistants(props.row.id === "mrt-row-create" ? users_list : users_list?.filter((val, idx) => !props.row.original.assistants.map((user) => user.username).includes(val.username)));
        // setDropdownItemTheme({ "name": mission_theme?.theme.title || "", "code": mission_theme?.theme.id || "" });
        // !dropdownItemTheme && setDropdownItemTheme(props.row.id !== "mrt-row-create" ? { "name": props.row.original.theme.theme.title, "code": props.row.original.theme.theme.id } : null)
        // // setDropdownItemPlan(props.row.id !== "mrt-row-create" ? props.row.original.plan ? { "name": props.row.original.plan, "code": mission_plan!.id }:null : null)
        // !dropdownItemHead && setDropdownItemHead(props.row.id !== "mrt-row-create" ? { "name": `${props.row.original.head?.last_name} ${props.row.original.head?.first_name}`, "code": props.row.original.head?.id } : null)
        // !dropdownItemSupervisor && setDropdownItemSupervisor(props.row.id !== "mrt-row-create" ? { "name": `${props.row.original.supervisor?.last_name} ${props.row.original.supervisor?.first_name}`, "code": props.row.original.supervisor?.id } : null)
        // console.log("useeffect mission", users_list)
    }, [users]);
    useEffect(() => {
        console.log("useeffect editform mission")
        console.log(props.row.original, props.row._valuesCache)
        props.row._valuesCache = mission
        props.row._valuesCache = { ...props.row._valuesCache, ...{ document: fileUploadRef.current?.getFiles() } }

    }, [fileUploadRef.current?.state?.files])
    ///////////////////////////////////////////////////////////////////////////
    const mission = {
        "head": props.row.id !== "mrt-row-create" ? props.row.original.head.id : null,
        "supervisor": props.row.id !== "mrt-row-create" ? props.row.original.supervisor.id : null,
        "staff": props.row.id !== "mrt-row-create" ? props.row.original.staff.map(user => user.id) : [],
        "assistants": props.row.id !== "mrt-row-create" ? props.row.original.assistants.map(user => user.id) : [],
        "plan": mission_plan?.id,
        "theme": mission_theme?.id,
        "exercise": props.row.original.exercise ?? null,
        "type": props.row.original.type,
        "code": props.row.original.code,
        "etat": props.row.original.etat,
        "start_date": props.row.id === "mrt-row-create" ? new Date().toISOString().split('T')[0] : new Date(props.row.original.start_date).toISOString().split('T')[0],
        "end_date": props.row.id === "mrt-row-create" ? new Date().toISOString().split('T')[0] : new Date(props.row.original.end_date).toISOString().split('T')[0],
    }
    ///////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    const [first, setFirst] = useState(1);
    const [rows, setRows] = useState(100);

    const onPageChange = (event: PaginatorPageChangeEvent) => {
        setPage(event.page + 1)
        setFirst(event.first);
        setRows(event.rows);
    };
    const handleTeam = (e: any) => {
        // setPicklistSourceValueTeam(e.source);
        // console.log('source Team', e.source)
        // setPicklistTargetValueTeam(e.target);
        // console.log('target Team', e.target)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ staff: e.value.map((member: User) => member.id) } }
    }
    const handleHead = (e: DropdownChangeEvent) => {
        console.log("handleHead", e.value)
        setDropdownItemHead(e.value);
        props.row._valuesCache = { ...props.row._valuesCache, ...{ head: e.value.code } }
    }
    const handleSupervisor = (e: DropdownChangeEvent) => {
        console.log("handleSupervisor", e.value)
        setDropdownItemSupervisor(e.value);
        props.row._valuesCache = { ...props.row._valuesCache, ...{ supervisor: e.value.code } }
    }
    const handleAssistant = (e: any) => {
        // setPicklistSourceValueAssistants(e.source);
        // console.log('source assistants', e.source)
        // setPicklistTargetValueAssistants(e.target);
        // console.log('target assistants', e.target)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ assistants: e.value.map((member: User) => member.id) } }
    }
    const handleType = (e: DropdownChangeEvent) => {
        console.log("handleType", e.value)
        setDropdownItemType(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ type: e.value.name } }
    }
    const handleCode = (e: React.ChangeEvent<HTMLInputElement>) => {
        console.log("handleCode", e.target.value)
        setMissionCode(e.target.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ code: e.target.value } }

    }
    const handlePlan = (e: DropdownChangeEvent) => {
        console.log("handlePlan", e.value)
        setDropdownItemPlan(e.value)
        setMissionExercise(null)

        props.row._valuesCache = { ...props.row._valuesCache, ...{ exercise: null }, ...{ plan: e.value.code } }

    }
    const handleExercise = (e: React.ChangeEvent<HTMLInputElement>) => {
        console.log("handleExercise", e.target.value)
        setMissionExercise(e.target.value)
        setDropdownItemPlan(null)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ exercise: e.target.value }, ...{ plan: null } }

    }
    const handleDocument = (e: React.ChangeEvent<HTMLInputElement>) => {
        console.log("handleDocument", e.currentTarget.files)
        console.log("handleDocument", props.row._valuesCache)
        setDocument(e.currentTarget.files)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ document: e.currentTarget.files } }

    }
    const handleState = (e: DropdownChangeEvent) => {
        console.log("handleState", e.value)
        setDropdownItemEtat(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ etat: e.value.name } }

    }
    const handleTheme = (e: DropdownChangeEvent) => {
        console.log("handleTheme", e.value)
        setDropdownItemTheme(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ theme: e.value.code } }
    }
    const handleStartDate = (e: FormEvent<Date, React.SyntheticEvent<Element, Event>>) => {
        console.log("handleStartDate", e.value)
        setStartDate(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ start_date: new Date(e.value).toISOString().split('T')[0] } }
    }
    const handleEndDate = (e: FormEvent<Date, React.SyntheticEvent<Element, Event>>) => {
        console.log("handleEndDate", e.value)
        setEndDate(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ end_date: new Date(e.value).toISOString().split('T')[0] } }
    }
    ///////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    const isStepOptional = (step: number) => {
        return step === 1;
    };
    const isStepSkipped = (step: number) => {
        return skipped.has(step);
    };
    const handleNext = () => {
        let newSkipped = skipped;
        if (isStepSkipped(activeStep)) {
            newSkipped = new Set(newSkipped.values());
            newSkipped.delete(activeStep);
        }

        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        setSkipped(newSkipped);
    };
    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };
    const handleSkip = () => {
        if (!isStepOptional(activeStep)) {
            // You probably want to guard against something like this,
            // it should never occur unless someone's actively trying to break something.
            throw new Error("You can't skip a step that isn't optional.");
        }

        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        setSkipped((prevSkipped) => {
            const newSkipped = new Set(prevSkipped.values());
            newSkipped.add(activeStep);
            return newSkipped;
        });
    };
    const handleReset = () => {
        setActiveStep(0);
    };
    const selectedEtatTemplate = (option, props) => {
        // console.log(option?.name)
        if (option) {
            return (
                <div className="flex align-items-center">
                    <Tag className='text-sm' severity={getMissionEtatSeverity(option.name)}>{option.name}</Tag>
                </div>
            );
        }

        return <span>{props.placeholder}</span>;
    };
    const selectedTypeTemplate = (option, props) => {
        // console.log(option?.name)
        if (option) {
            return (
                <div className="flex align-items-center">
                    <Tag className='text-sm' severity={getMissionTypeSeverity(option.name)}>{option.name}</Tag>
                </div>
            );
        }

        return <span>{props.placeholder}</span>;
    };
    const etatOptionTemplate = (option) => {
        return (
            <div className="flex align-items-center">
                <Tag className='text-sm' severity={getMissionEtatSeverity(option.name)}>{option.name}</Tag>
            </div>
        );
    };
    const typeOptionTemplate = (option) => {
        return (
            <div className="flex align-items-center">
                <Tag className='text-sm' severity={getMissionTypeSeverity(option.name)}>{option.name}</Tag>
            </div>
        );
    };
    const panelFooterTemplate = () => {
        return (
            <div className="py-2 px-3">
                {dropdownItemEtat ? (
                    <span>
                        <b>{dropdownItemEtat.name}</b> sélectionné.
                    </span>
                ) : (
                    'Aucune option séléctionnée.'
                )}
            </div>
        );
    };
    ///////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    if (users.isLoading || themes.isLoading || plans.isLoading) return (<ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />)
    if (users.error || themes.error || plans.error) return (<div>{users.error.message}</div>)

    return (
        <>
            <div style={{ zIndex: '1302 !important' }}>
                <Sidebar position='right'
                    closeOnEscape={true}
                    showCloseIcon={false}
                    header={<div className='flex flex-row w-full flex-wrap justify-content-between'>
                        <Typography variant='h5' >{props.row.id === 'mrt-row-create' ? "Nouvelle" : "Editer"} Mission <b>{props.row.original?.code}</b></Typography>
                        {/* <DialogActions> */}
                        <MRT_EditActionButtons variant="text" table={props.table} row={props.row} />
                        {/* </DialogActions> */}
                    </div>}
                    visible={editVisible}
                    onHide={() => { props.table.setEditingRow(null); setEditVisible(false) }}
                    className="w-full md:w-5 lg:w-5"
                >
                    <DialogContent className='h-full'
                        sx={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}
                    >
                        {/* <Stepper activeStep={activeStep} className='sticky top-0'>
                            {steps.map((label, index) => {
                                const stepProps: { completed?: boolean } = {};
                                const labelProps: {
                                    optional?: React.ReactNode;
                                } = {};
                                if (isStepOptional(index)) {
                                    labelProps.optional = (
                                        <Typography variant="caption">Optional</Typography>
                                    );
                                }
                                if (isStepSkipped(index)) {
                                    stepProps.completed = false;
                                }
                                return (
                                    <Step key={label}
                                        {...stepProps}
                                        sx={{
                                            '& .MuiStepLabel-root .Mui-completed': {
                                                color: 'secondary.dark', // circle color (COMPLETED)
                                            },
                                            '& .MuiStepLabel-label.Mui-completed.MuiStepLabel-alternativeLabel':
                                            {
                                                color: 'white', // Just text label (COMPLETED)
                                            },
                                            '& .MuiStepLabel-root .Mui-active': {
                                                color: 'var(--primary-color)', // circle color (ACTIVE)
                                            },
                                            '& .MuiStepLabel-label.Mui-active.MuiStepLabel-alternativeLabel':
                                            {
                                                color: 'white', // Just text label (ACTIVE)
                                            },
                                            '& .MuiStepLabel-root .Mui-active .MuiStepIcon-text': {
                                                fill: 'white', // circle's number (ACTIVE)
                                            },
                                        }}
                                    >
                                        <StepLabel {...labelProps}>{label}</StepLabel>
                                    </Step>
                                );
                            })}
                        </Stepper> */}
                        <div className="col-12" >

                            {activeStep === steps.length ? (
                                <React.Fragment>
                                    <Typography sx={{ mt: 2, mb: 1 }}>
                                        All steps completed - you&apos;re finished
                                    </Typography>
                                    <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
                                        <Box sx={{ flex: '1 1 auto' }} />
                                        <Button onClick={handleReset}>Reset</Button>
                                    </Box>
                                </React.Fragment>
                            ) : (
                                <React.Fragment>
                                    {/* <Typography sx={{ mt: 2, mb: 1 }}>Step {activeStep }</Typography> */}
                                    {activeStep === 0 && (
                                        <div className="card h-full">
                                            <div className="p-fluid formgrid grid">
                                                <div className="field col-12 md:col-6">
                                                    <label htmlFor="code">Code</label>
                                                    <InputText className={props.row._valuesCache.error?.data?.["code"] ? 'p-invalid' : ''} id="code" type="text" defaultValue={missionCode} onChange={(e) => handleCode(e)} />
                                                    {props.row._valuesCache.error?.data?.["code"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["code"][0]}</small>}

                                                </div>
                                                <div className="field col-12 md:col-6">
                                                    <label htmlFor="etat">Etat</label>
                                                    <Dropdown className={props.row._valuesCache.error?.data?.["etat"] ? 'p-invalid' : ''} valueTemplate={selectedEtatTemplate} itemTemplate={etatOptionTemplate} panelFooterTemplate={panelFooterTemplate} filter id="etat" value={dropdownItemEtat} onChange={(e) => handleState(e)} options={$MissionEtatEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
                                                    {props.row._valuesCache.error?.data?.["etat"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["etat"][0]}</small>}
                                                </div>
                                                <div className="field col-12 md:col-6">
                                                    <label htmlFor="type">Type</label>
                                                    <Dropdown className={props.row._valuesCache.error?.data?.["type"] ? 'p-invalid' : ''} valueTemplate={selectedTypeTemplate} itemTemplate={typeOptionTemplate} filter id="type" value={dropdownItemType} onChange={(e) => handleType(e)} options={$MissionTypeEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
                                                    {props.row._valuesCache.error?.data?.["type"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["type"][0]}</small>}
                                                </div>
                                                {dropdownItemType?.name === "Planifiée" ?
                                                    <div className="field col-12 md:col-6">
                                                        <label htmlFor="plan">Plan</label>
                                                        <Dropdown className={props.row._valuesCache.error?.data?.["plan"] ? 'p-invalid' : ''} filter id="plan" value={dropdownItemPlan} onChange={(e) => handlePlan(e)} options={plans?.data.data.results.map(function (val: Plan) { return { "name": val.code, "code": val.id } })} optionLabel="name" placeholder="Choisir un plan"></Dropdown>
                                                        {props.row._valuesCache.error?.data?.["plan"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["plan"][0]}</small>}
                                                    </div>
                                                    :
                                                    <div className="field col-12 md:col-6">
                                                        <label htmlFor="exercise">Exercice</label>
                                                        <InputText className={props.row._valuesCache.error?.data?.["exercise"] ? 'p-invalid' : ''} id="exercise" defaultValue={missionExercise} onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleExercise(e)} ></InputText>
                                                        {props.row._valuesCache.error?.data?.["exercise"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["exercise"][0]}</small>}
                                                    </div>
                                                }
                                                <div className="field col-12 md:col-12">
                                                    <Stack className='w-full' spacing={1} direction={'row'} alignItems={'center'} alignContent={'center'} >
                                                        <div className='w-full'>
                                                            <label htmlFor="theme">Thème</label>
                                                            <Dropdown filter className={props.row._valuesCache.error?.data?.["theme"] ? 'p-invalid' : ''} id="theme" value={dropdownItemTheme} onChange={(e) => handleTheme(e)} options={themes?.data?.data.results.map(function (val: ArbitratedTheme) { return { "name": val.theme.title, "code": val.id } })} optionLabel="name" placeholder="Choisir un thème"></Dropdown>
                                                            <small id="username-help">Choisir parmis les thèmes arbitrés</small>
                                                            {props.row._valuesCache.error?.data?.["theme"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["theme"][0]}</small>}
                                                        </div>
                                                        {
                                                            dropdownItemType?.name === "Planifiée" || dropdownItemType?.name === null ?
                                                                <></> :
                                                                <Button className='h-3rem' icon='pi pi-plus' onClick={() => { setAddThemeDialogVisible(true) }}></Button>
                                                        }
                                                    </Stack>
                                                    <ThemeEditFormDecoupled editDialogVisible={addThemeDialogVisible} setEditDialogVisible={setAddThemeDialogVisible} />
                                                </div>

                                                <div className="field col-12 md:col-6">
                                                    <label htmlFor="start_date">Date Début</label>
                                                    <Calendar id="start_date" className={props.row._valuesCache.error?.data?.["start_date"] ? 'p-invalid' : ''} value={startDate} onChange={(e) => handleStartDate(e)} />
                                                    {props.row._valuesCache.error?.data?.["start_date"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["start_date"][0]}</small>}
                                                </div>
                                                <div className="field col-12 md:col-6">
                                                    <label htmlFor="end_date">Date Fin</label>
                                                    <Calendar id="end_date" className={props.row._valuesCache.error?.data?.["end_date"] ? 'p-invalid' : ''} value={endDate} onChange={(e) => handleEndDate(e)} locale='fr' />
                                                    {props.row._valuesCache.error?.data?.["end_date"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["end_date"][0]}</small>}
                                                </div>
                                                <div className="field col-12 md:col-12">
                                                    <label htmlFor="head">Chef de mission</label>
                                                    <Dropdown
                                                        virtualScrollerOptions={{ itemSize: 38 }} filter className={props.row._valuesCache.error?.data?.["head"] ? 'p-invalid' : ''} id="head" value={dropdownItemHead} onChange={(e) => handleHead(e)} options={users?.data.data.results.map(function (val) { return { "name": `${val.last_name} ${val.first_name}`, "code": val.id } })} optionLabel="name" placeholder="Choisir un chef de mission"></Dropdown>
                                                    {props.row._valuesCache.error?.data?.["head"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["head"][0]}</small>}
                                                </div>
                                                <div className="field col-12 md:col-12">
                                                    <label htmlFor="supervisor">Superviseur</label>
                                                    <Dropdown
                                                        virtualScrollerOptions={{ itemSize: 38 }}
                                                        filter className={props.row._valuesCache.error?.data?.["supervisor"] ? 'p-invalid' : ''} id="supervisor" value={dropdownItemSupervisor} onChange={(e) => handleSupervisor(e)} options={users?.data.data.results.map(function (val) { return { "name": `${val.last_name} ${val.first_name}`, "code": val.id } })} optionLabel="name" placeholder="Choisir un superviseur"></Dropdown>
                                                    {props.row._valuesCache.error?.data?.["supervisor"] && props.row._valuesCache.error?.data?.["supervisor"].map((err) => <small className='p-error'>{err}</small>)}
                                                </div>
                                                <div className="field col-12 md:col-12">
                                                    <label htmlFor="picklist_staff">Equipe CI</label>
                                                    <div className="card">
                                                        <AutoComplete 
                                                            onUnselect={unselectTeam}
                                                            selectedItemTemplate={selectedTeamItemTemplate}
                                                            itemTemplate={teamItemTemplate}
                                                            virtualScrollerOptions={{ itemSize: 38 }}
                                                            field="first_name"
                                                            multiple
                                                            value={targetValueTeam}
                                                            suggestions={filteredSourceValueTeam}
                                                            completeMethod={searchTeam}
                                                            onChange={(e) => { handleTeam(e); console.log(e); setTargetValueTeam(e.value) }}
                                                            placeholder='Choisir les membres de la missions'
                                                        />
                                                        {/* <PickList
                                                            //  sourceHeader={<Typography sx={{ mt: 2, mb: 1 }}>Step {activeStep }</Typography>}
                                                            id='picklist_staff'
                                                            // locale='fr'
                                                            source={picklistSourceValueTeam}
                                                            target={picklistTargetValueTeam}
                                                            sourceHeader="De"
                                                            targetHeader="A"
                                                            itemTemplate={(item) => <div key={item.id}>{item.first_name} {item.last_name}</div>}
                                                            onChange={(e) => {
                                                                console.log('source Team', e.source)
                                                                handleTeam(e)
                                                            }}
                                                            dataKey="id"
                                                            sourceStyle={{ height: '200px' }}
                                                            targetStyle={{ height: '200px' }}
                                                            filter filterBy='username,email,first_name,last_name'
                                                            filterMatchMode='contains'
                                                            sourceFilterPlaceholder="Rechercher par nom & prénom" targetFilterPlaceholder="Rechercher par nom & prénom"
                                                            pt={{listWrapper:{}}}
                                                        >
                                                        </PickList> */}
                                                    </div>
                                                </div>
                                                <div className="field col-12 md:col-12">
                                                    <label htmlFor="picklist_assistants">Assistants</label>
                                                    <div className="card">
                                                        <AutoComplete 
                                                            onUnselect={unselectTeam}
                                                            selectedItemTemplate={selectedTeamItemTemplate}
                                                            itemTemplate={teamItemTemplate}
                                                            virtualScrollerOptions={{ itemSize: 38 }}
                                                            field="first_name"
                                                            multiple
                                                            value={targetValueAssistants}
                                                            suggestions={filteredSourceValueAssistants}
                                                            completeMethod={searchAssistants}
                                                            onChange={(e) => {handleAssistant(e); console.log(e); setTargetValueAssistants(e.value) }}
                                                            placeholder='Choisir les assistants de la missions'
                                                        />

                                                        {/* <PickList
                                                            // locale='fr'
                                                            dataKey="id"
                                                            id='picklist_assistants'
                                                            source={picklistSourceValueAssistants}
                                                            target={picklistTargetValueAssistants}
                                                            sourceHeader="De"
                                                            targetHeader="A"
                                                            itemTemplate={(item) => <div key={item.id}>{item.first_name} {item.last_name}</div>}
                                                            onChange={(e) => {
                                                                handleAssistant(e)
                                                            }}
                                                            sourceStyle={{ height: '200px' }}
                                                            targetStyle={{ height: '200px' }}
                                                            filter filterBy='username,email,first_name,last_name'
                                                            filterMatchMode='contains'
                                                            sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                                        >
                                                        </PickList> */}
                                                    </div>
                                                </div>
                                                {/* <div className="field col-12">
                                                    <label htmlFor="mission_docs">Documents</label>
                                                    <FileUpload id="mission_docs"
                                                        ref={fileUploadRef}
                                                        name="docs[]"
                                                        multiple 
                                                        accept="image/*,application/pdf"
                                                        maxFileSize={10000000}
                                                        emptyTemplate={<p className="m-0">Drag and drop files to here to upload.</p>}
                                                        // headerTemplate={headerTemplate} 
                                                        itemTemplate={itemTemplate}
                                                        uploadOptions={{style:{display:'none'}}}
                                                        onSelect={customUploader} 
                                                        // customUpload
                                                    />
                                                </div> */}
                                            </div>
                                        </div>
                                    )}
                                    {/* {activeStep === 1 && (
                                        <div className="card h-full">
                                            <div className="p-fluid formgrid grid">
                                                <div className="field col-12 md:col-12">
                                                    <label htmlFor="head">Chef de mission</label>
                                                    <Dropdown filter id="head" value={dropdownItemHead} onChange={(e) => handleHead(e)} options={users?.data.results.map(function (val) { return { "name": `${val.last_name} ${val.first_name}`, "code": val.id } })} optionLabel="name" placeholder="Choisir un chef de mission"></Dropdown>
                                                </div>
                                                <div className="field col-12 md:col-12">
                                                    <label htmlFor="supervisor">Superviseur</label>
                                                    <Dropdown filter id="supervisor" value={dropdownItemSupervisor} onChange={(e) => handleSupervisor(e)} options={users?.data.results.map(function (val) { return { "name": `${val.last_name} ${val.first_name}`, "code": val.id } })} optionLabel="name" placeholder="Choisir un superviseur"></Dropdown>
                                                </div>

                                                <div className="field col-6">
                                                    <label htmlFor="picklist_staff">Equipe CI</label>
                                                    <PickList
                                                        //  sourceHeader={<Typography sx={{ mt: 2, mb: 1 }}>Step {activeStep }</Typography>}
                                                        id='picklist_staff'
                                                        locale='fr'
                                                        source={picklistSourceValueTeam}
                                                        target={picklistTargetValueTeam}
                                                        sourceHeader="De"
                                                        targetHeader="A"
                                                        itemTemplate={(item) => <div>{item.first_name} {item.last_name}</div>}
                                                        onChange={(e) => {
                                                            handleTeam(e)
                                                        }}
                                                        sourceStyle={{ height: '200px' }}
                                                        targetStyle={{ height: '200px' }}
                                                        filter filterBy='username,email,first_name,last_name'
                                                        filterMatchMode='contains'


                                                        sourceFilterPlaceholder="Search by name" targetFilterPlaceholder="Search by name"
                                                    >
                                                    </PickList>

                                                </div>
                                                <div className="field col-6">
                                                    <PickList
                                                        locale='fr'
                                                        id='picklist_assistants'
                                                        source={picklistSourceValueAssistants}
                                                        target={picklistTargetValueAssistants}
                                                        sourceHeader="De"
                                                        targetHeader="A"
                                                        itemTemplate={(item) => <div>{item.first_name} {item.last_name}</div>}
                                                        onChange={(e) => {
                                                            handleAssistant(e)
                                                        }}
                                                        sourceStyle={{ height: '200px' }}
                                                        targetStyle={{ height: '200px' }}
                                                        filter filterBy='username,email,first_name,last_name'
                                                        filterMatchMode='contains'
                                                        sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                                    >
                                                    </PickList>
                                                    <Paginator first={first} rows={rows} totalRecords={users?.data.count ?? 0} onPageChange={onPageChange} rowsPerPageOptions={[100, 200, 300]}></Paginator>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                    {activeStep === 2 && (
                                        <div className="card h-full">
                                            <div className="field col-12">
                                                <PickList
                                                    locale='fr'
                                                    id='picklist_assistants'
                                                    source={picklistSourceValueAssistants}
                                                    target={picklistTargetValueAssistants}
                                                    sourceHeader="De"
                                                    targetHeader="A"
                                                    itemTemplate={(item) => <div>{item.first_name} {item.last_name}</div>}
                                                    onChange={(e) => {
                                                        handleAssistant(e)
                                                    }}
                                                    sourceStyle={{ height: '200px' }}
                                                    targetStyle={{ height: '200px' }}
                                                    filter filterBy='username,email,first_name,last_name'
                                                    filterMatchMode='contains'
                                                    sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                                >
                                                </PickList>
                                                <Paginator first={first} rows={rows} totalRecords={users?.data.count ?? 0} onPageChange={onPageChange} rowsPerPageOptions={[100, 200, 300]}></Paginator>
                                            </div>
                                        </div>
                                    )} */}

                                </React.Fragment>
                            )}
                        </div>
                        {/* <Box className='sticky bottom-0' sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
                            <Button
                                color="inherit"
                                disabled={activeStep === 0}
                                onClick={handleBack}
                            // sx={{ mr: 1 }}
                            >
                                Back
                            </Button>
                            <Box sx={{ flex: '1 1 auto' }} />
                            {isStepOptional(activeStep) && (
                                <Button color="inherit" onClick={handleSkip}>
                                    Skip
                                </Button>
                            )}
                            <Button onClick={handleNext}>
                                {activeStep === steps.length - 1 ? 'Finish' : 'Next'}
                            </Button>
                        </Box> */}
                    </DialogContent>
                </Sidebar>
            </div >
        </>
    )
};

export default MissionEditForm;
