{"folders": [{"path": "./cri_backend", "name": "BACKEND"}, {"path": "./cri_frontend", "name": "FRONTEND"}], "settings": {"explorer.compactFolders": false, "svg.preview.background": "white", "sqltools.connections": [{"previewLimit": 50, "driver": "SQLite", "name": "tes t", "database": "${workspaceFolder:CRI}/cri_backend/db.sqlite3"}], "sqltools.useNodeRuntime": true, "cSpell.words": ["authtoken", "casl", "Constat", "constats", "crfs", "etat", "filterset", "<PERSON><PERSON><PERSON>", "maximizable", "viewsets"]}}