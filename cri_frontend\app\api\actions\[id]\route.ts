import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const actionId = parseInt(params.id)

    const action = await prisma.action.findUnique({
      where: { id: actionId },
      include: {
        recommendation: {
          include: {
            mission: {
              select: {
                id: true,
                code: true,
                title: true,
                type: true,
              }
            }
          }
        },
        jobLeader: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        comments: {
          include: {
            createdBy: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
              }
            }
          },
          orderBy: {
            created: 'desc'
          }
        },
        dependencies: {
          select: {
            id: true,
            description: true,
            status: true,
            progress: true,
          }
        }
      }
    })

    if (!action) {
      return NextResponse.json({ error: 'Action not found' }, { status: 404 })
    }

    return NextResponse.json(action)
  } catch (error) {
    console.error('Action fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to update actions (staff only)
    if (!session.user.isStaff) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const actionId = parseInt(params.id)
    const body = await request.json()

    // Check if action exists
    const existingAction = await prisma.action.findUnique({
      where: { id: actionId }
    })

    if (!existingAction) {
      return NextResponse.json({ error: 'Action not found' }, { status: 404 })
    }

    // Update action
    const updatedAction = await prisma.action.update({
      where: { id: actionId },
      data: {
        description: body.description,
        status: body.status,
        progress: body.progress,
        startDate: body.startDate ? new Date(body.startDate) : null,
        endDate: body.endDate ? new Date(body.endDate) : null,
        jobLeaderId: body.jobLeaderId,
        proof: body.proof,
        modified: new Date(),
        modifiedBy: session.user.id,
      },
      include: {
        recommendation: {
          include: {
            mission: {
              select: {
                id: true,
                code: true,
                title: true,
                type: true,
              }
            }
          }
        },
        jobLeader: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        comments: {
          include: {
            createdBy: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
              }
            }
          },
          orderBy: {
            created: 'desc'
          }
        }
      }
    })

    return NextResponse.json(updatedAction)
  } catch (error) {
    console.error('Action update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to delete actions (staff only)
    if (!session.user.isStaff) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const actionId = parseInt(params.id)

    // Check if action exists
    const existingAction = await prisma.action.findUnique({
      where: { id: actionId }
    })

    if (!existingAction) {
      return NextResponse.json({ error: 'Action not found' }, { status: 404 })
    }

    // Delete action
    await prisma.action.delete({
      where: { id: actionId }
    })

    return NextResponse.json({ message: 'Action deleted successfully' })
  } catch (error) {
    console.error('Action deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
