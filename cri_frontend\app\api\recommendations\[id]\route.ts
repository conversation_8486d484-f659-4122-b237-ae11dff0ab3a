import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/recommendations/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    const recommendation = await prisma.recommendation.findUnique({
      where: { id },
      include: {
        mission: {
          select: {
            id: true,
            code: true,
            type: true,
          },
        },
        concernedStructure: true,
        causes: true,
        constats: true,
        actions: {
          include: {
            jobLeader: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        comments: {
          include: {
            createdByUser: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    })
    
    if (!recommendation) {
      return NextResponse.json(
        { error: 'Recommendation not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(recommendation)
  } catch (error) {
    console.error('Error fetching recommendation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/recommendations/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const body = await request.json()
    
    const {
      numrecommandation,
      recommendation,
      concernedStructureId,
      priority,
      status,
      validated,
      accepted,
      responsible,
      causes,
      constats,
    } = body
    
    // Prepare update data
    const updateData: any = {}
    
    if (numrecommandation !== undefined) updateData.numrecommandation = numrecommandation
    if (recommendation !== undefined) updateData.recommendation = recommendation
    if (concernedStructureId !== undefined) updateData.concernedStructureId = BigInt(concernedStructureId)
    if (priority !== undefined) updateData.priority = priority
    if (status !== undefined) updateData.status = status
    if (validated !== undefined) updateData.validated = validated
    if (accepted !== undefined) updateData.accepted = accepted
    if (responsible !== undefined) updateData.responsible = responsible
    
    // Handle many-to-many relationships
    if (causes !== undefined) {
      updateData.causes = { set: causes.map((id: number) => ({ id })) }
    }
    if (constats !== undefined) {
      updateData.constats = { set: constats.map((id: number) => ({ id })) }
    }
    
    const updatedRecommendation = await prisma.recommendation.update({
      where: { id },
      data: updateData,
      include: {
        mission: {
          select: {
            id: true,
            code: true,
            type: true,
          },
        },
        concernedStructure: true,
        causes: true,
        constats: true,
        actions: true,
        comments: true,
      },
    })
    
    return NextResponse.json(updatedRecommendation)
  } catch (error) {
    console.error('Error updating recommendation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/recommendations/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    await prisma.recommendation.delete({
      where: { id },
    })
    
    return NextResponse.json({ message: 'Recommendation deleted successfully' })
  } catch (error) {
    console.error('Error deleting recommendation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
