import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'
import { serializePaginatedResponse } from '@/lib/bigint-serializer'

// GET /api/structures
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''

    const skip = (page - 1) * limit

    const where: any = {}

    if (search) {
      where.OR = [
        { libellStru: { contains: search, mode: 'insensitive' as const } },
        { codeStru: { contains: search, mode: 'insensitive' as const } },
        { codeMnemonique: { contains: search, mode: 'insensitive' as const } },
        { codeUnit: { contains: search, mode: 'insensitive' as const } },
      ]
    }

    const [structures, total] = await Promise.all([
      prisma.criStructview.findMany({
        where,
        skip,
        take: limit,
        include: {
          structureCorrespondents: {
            include: {
              correspondents: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                }
              }
            }
          },
          structureInterim: {
            include: {
              interim: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                }
              }
            }
          },
          themeProposing: {
            select: {
              id: true,
              title: true,
              validated: true,
              code: true,
            }
          },
          themeConcerned: {
            select: {
              id: true,
              title: true,
              validated: true,
              code: true,
            }
          }
        },
        orderBy: { id: 'desc' },
      }),
      prisma.criStructview.count({ where }),
    ])

    // Convert BigInt to string for JSON serialization
    const responseData = serializePaginatedResponse({
      results: structures,
      count: total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    })

    return NextResponse.json({
      data: responseData,
    })
  } catch (error) {
    console.error('Error fetching structures:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
