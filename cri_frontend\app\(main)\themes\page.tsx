'use client';

import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import GenericTable from './(components)/GenericTAble';
import { getCookie } from 'cookies-next';
import { ArbitratedTheme } from '@prisma/client';

const ThemesTable = () => {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')

    const { data: themes, isLoading: isLoading, error: error } = useApiArbitratedthemeList({},{
        axios: { headers: { Authorization: `Token ${user?.token}` } }});
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex: 0,
        pageSize: 5, //customize the default page size
    });
    if (isLoading) return (<div></div>)
    return (
        <div className="grid">

            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes proposés par C.I</span>
                            <div className="text-900 font-medium text-xl">{themes?.data.results.filter((thm)=>thm.theme.proposed_by ==='Contrôle Interne').length ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-orange-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-eye text-orange-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">{cmd_missions?.data.count ?? 0}</span>
                    <span className="text-500">since last week</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes proposés par Structures</span>
                            <div className="text-900 font-medium text-xl">{themes?.data.results.filter((thm)=>thm.theme.proposed_by ==='Structures').length  ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-cyan-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-building text-cyan-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">520 </span>
                    <span className="text-500">newly registered</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes proposés par VP</span>
                            <div className="text-900 font-medium text-xl">{themes?.data.results.filter((thm)=>thm.theme.proposed_by ==='Vice Président').length  ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-bolt text-green-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">85 </span>
                    <span className="text-500">responded</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes proposés par A.I</span>
                            <div className="text-900 font-medium text-xl">{themes?.data.results.filter((thm)=>thm.theme.proposed_by ==='Audit Interne').length  ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-eye-slash text-green-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">85 </span>
                    <span className="text-500">responded</span> */}
                </div>
            </div>
            <div className="col-12">
                <GenericTable<ArbitratedTheme> data_={themes} isLoading={isLoading} error={error} data_type={$ArbitratedTheme} pagination={{ "set": setPagination, "pagi": pagination }}></GenericTable>
            </div>
        </div>
    );
};

export default ThemesTable;
