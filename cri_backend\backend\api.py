# from ninja_extra import (
#     ModelConfig,
#     ModelControllerBase,
#     ModelSchemaConfig,
#     api_controller,
#     NinjaExtraAPI,
#     permissions
# )
# from .models import *
# ###############################################################################
# ###############################################################################
# @api_controller("/missions",permissions=[permissions.IsAuthenticated ])
# class MissionModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=Mission,
#         schema_config=ModelSchemaConfig(read_only_fields=["id", "code"],depth=2),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/plans",permissions=[permissions.IsAuthenticated ])
# class PlanModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=Plan,
#         schema_config=ModelSchemaConfig(read_only_fields=["id",],depth=2),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/risks",permissions=[permissions.IsAuthenticated ])
# class RiskModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=Risk,
#         schema_config=ModelSchemaConfig(read_only_fields=["id", "title"],exclude=["recommendations"],depth=2),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/exercises",permissions=[permissions.IsAuthenticated ])
# class ExerciseModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=Exercise,
#         schema_config=ModelSchemaConfig(read_only_fields=["year"]),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/themes",permissions=[permissions.IsAuthenticated ])
# class ThemeModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=Theme,
#         schema_config=ModelSchemaConfig(depth=2),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/fraps",permissions=[permissions.IsAuthenticated ])
# class FRAPModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=FRAP,
#         schema_config=ModelSchemaConfig(depth=2),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/recommendations",permissions=[permissions.IsAuthenticated ])
# class RecommendationModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=Recommendation,
#         schema_config=ModelSchemaConfig(depth=2,read_only_fields=["risks"]),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/causes",permissions=[permissions.IsAuthenticated ])
# class CauseModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=Cause,
#         schema_config=ModelSchemaConfig(depth=2),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/consequences",permissions=[permissions.IsAuthenticated ])
# class ConsequenceModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=Consequence,
#         schema_config=ModelSchemaConfig(depth=2),
#     )
# ###############################################################################
# ###############################################################################
# @api_controller("/risk_impacts",permissions=[permissions.IsAuthenticated ])
# class RiskImpactModelController(ModelControllerBase):
#     model_config = ModelConfig(
#         model=RiskImpact,
#         schema_config=ModelSchemaConfig(depth=2),
#     )
# ###############################################################################
# ###############################################################################
# app = NinjaExtraAPI(version="api-1.0.2")

# app.register_controllers(
#     RecommendationModelController,
#     FRAPModelController,
#     ExerciseModelController,
#     ThemeModelController,
#     MissionModelController,
#     PlanModelController,
#     RiskModelController,
#     ConsequenceModelController,
#     RiskImpactModelController,
#     CauseModelController
# )