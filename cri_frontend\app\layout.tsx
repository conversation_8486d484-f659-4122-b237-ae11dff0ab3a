'use client';
import { LayoutProvider } from '../layout/context/layoutcontext';
import { addLocale, locale, PrimeReactProvider } from 'primereact/api';
import 'primereact/resources/primereact.css';
import 'primeflex/primeflex.css';
import 'primeicons/primeicons.css';
import '../styles/layout/layout.scss';
import '../styles/demo/Demos.scss';

import { AuthProvider } from '@/contexts/CustomAuthContext';
import { LoadingSpinnerProvider } from '@/utilities/hooks/useSpinner';
import { ToastContextProvider } from '@/utilities/hooks/useToast';
import locales from "@/utilities/service/fr";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
// import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
// const queryClient = new QueryClient();
interface RootLayoutProps {
    children: React.ReactNode;
}
const queryClient = new QueryClient()
export default function RootLayout({ children }: RootLayoutProps) {
    addLocale('fr', locales['fr']);
    locale('fr')

    return (
        <html lang="en" suppressHydrationWarning>
            <head>
                <link id="theme-css" href={`/themes/lara-light-indigo/theme.css`} rel="stylesheet"></link>
                <script src="/tinymce/tinymce.min.js"></script>
            </head>
            <body>
                <PrimeReactProvider value={{ locale: 'fr' }}>
                    <QueryClientProvider client={queryClient}>
                        <AuthProvider>
                            <ToastContextProvider>
                                <LoadingSpinnerProvider>
                                    <LayoutProvider>{children}</LayoutProvider>
                                </LoadingSpinnerProvider>
                            </ToastContextProvider>
                        </AuthProvider>
                        <ReactQueryDevtools />
                    </QueryClientProvider>
                </PrimeReactProvider>
            </body>
        </html>
    );
}
