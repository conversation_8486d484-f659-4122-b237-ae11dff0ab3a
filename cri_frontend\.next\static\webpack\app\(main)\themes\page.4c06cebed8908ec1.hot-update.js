"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx":
/*!*********************************************************!*\
  !*** ./app/(main)/themes/(components)/GenericTAble.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_chip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/chip */ \"(app-client)/./node_modules/primereact/chip/chip.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const [theme_id, setThemeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: arbitrations, isLoading, error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList)();\n    const { data: themes, isLoading: isLoading_themes, error: error_themes } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList)();\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate)();\n    const { data: data_update, error: error_update, isPending: isMutating_update, mutate: trigger_update } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate)();\n    const getSeverity = (str)=>{\n        switch(str){\n            case \"Vice Pr\\xe9sident\":\n                return \"success\";\n            case \"Contr\\xf4le Interne\":\n                return \"warning\";\n            case \"Audit Interne\":\n                return \"warning\";\n            case \"Structures\":\n                return \"danger\";\n            default:\n                return null;\n        }\n    };\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                header: \"ID\",\n                accessorKey: \"id\",\n                size: 70,\n                Edit: ()=>null\n            },\n            {\n                header: \"Aribtrage\",\n                accessorKey: \"arbitration\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"arbitration\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-11rem text-sm\",\n                        severity: row.original.arbitration.plan.code.includes(\"Audit\") ? \"danger\" : \"info\",\n                        value: row.original.arbitration.plan.code\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_arbitration_plan, _row__valuesCache_arbitration, _row__valuesCache_arbitration1, _arbitrations;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _arbitrations, _arbitrations1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                arbitration: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data.results.find((arbi)=>arbi.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                arbitration: (_arbitrations1 = arbitrations) === null || _arbitrations1 === void 0 ? void 0 : _arbitrations1.data.results.find((arbi)=>arbi.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un\",\n                        value: {\n                            name: ((_row__valuesCache_arbitration = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration === void 0 ? void 0 : (_row__valuesCache_arbitration_plan = _row__valuesCache_arbitration.plan) === null || _row__valuesCache_arbitration_plan === void 0 ? void 0 : _row__valuesCache_arbitration_plan.code) || null,\n                            code: ((_row__valuesCache_arbitration1 = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration1 === void 0 ? void 0 : _row__valuesCache_arbitration1.id) || null\n                        },\n                        options: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data.results.map((arbi)=>{\n                            return {\n                                code: arbi.id,\n                                name: arbi.plan.code\n                            };\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Propos\\xe9 par\",\n                accessorKey: \"theme\",\n                id: \"theme_proposed_by\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                Edit: ()=>null,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-9rem text-sm\",\n                        severity: getSeverity(cell.getValue().proposedBy),\n                        value: cell.getValue().proposedBy\n                    }, row.original.code + row.original.created, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 36\n                    }, this);\n                }\n            },\n            {\n                header: \"Structures proposantes\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"proposing_structures\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    console.log(cell.getValue().proposing_structures);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: cell.getValue().proposing_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                style: {\n                                    backgroundColor: \"green\",\n                                    color: \"white\"\n                                },\n                                label: val.code_mnemonique\n                            }, \"thm\".concat(row.original.theme.id, \"_ps\").concat(idx), false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 20\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Structures concern\\xe9es\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"concerned_structures\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [\n                            \" \",\n                            cell.getValue().concerned_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                    style: {\n                                        backgroundColor: \"green\",\n                                        color: \"white\"\n                                    },\n                                    label: val.code_mnemonique\n                                }, \"thm\".concat(row.original.theme.id, \"_cs\").concat(idx), false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 137\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Domaine\",\n                accessorKey: \"domain\",\n                id: \"domain\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.domain.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Processus\",\n                accessorKey: \"process\",\n                id: \"process\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.process.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Intitul\\xe9\",\n                accessorKey: \"theme\",\n                id: \"title\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.theme.title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_theme, _row__valuesCache_theme1, _themes;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _themes, _themes1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                theme: (_themes = themes) === null || _themes === void 0 ? void 0 : _themes.data.results.find((thm)=>thm.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                theme: (_themes1 = themes) === null || _themes1 === void 0 ? void 0 : _themes1.data.results.find((thm)=>thm.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un\",\n                        value: {\n                            name: ((_row__valuesCache_theme = row._valuesCache.theme) === null || _row__valuesCache_theme === void 0 ? void 0 : _row__valuesCache_theme.title) || null,\n                            code: ((_row__valuesCache_theme1 = row._valuesCache.theme) === null || _row__valuesCache_theme1 === void 0 ? void 0 : _row__valuesCache_theme1.id) || null\n                        },\n                        options: (_themes = themes) === null || _themes === void 0 ? void 0 : _themes.data.results.map((thm)=>{\n                            return {\n                                code: thm.id,\n                                name: thm.title\n                            };\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Remarque\",\n                accessorKey: \"note\",\n                id: \"note\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.note\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>/*#__PURE__*/ {\n                    let { row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                        id: \"note\",\n                        initialValue: row.original.note,\n                        tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                        apiKey: \"none\",\n                        init: {\n                            height: 500,\n                            menubar: true,\n                            plugins: [\n                                \"advlist\",\n                                \"autolink\",\n                                \"lists\",\n                                \"link\",\n                                \"image\",\n                                \"charmap\",\n                                \"print\",\n                                \"preview\",\n                                \"anchor\",\n                                \"searchreplace\",\n                                \"visualblocks\",\n                                \"code\",\n                                \"fullscreen\",\n                                \"insertdatetime\",\n                                \"media\",\n                                \"table\",\n                                \"paste\",\n                                \"code\",\n                                \"help\",\n                                \"wordcount\"\n                            ],\n                            toolbar: \"undo redo | formatselect | bold italic backcolor |                                       alignleft aligncenter alignright alignjustify |                                       bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                        },\n                        onChange: (e)=>{\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                note: e.target.getContent()\n                            };\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 30\n                    }, this);\n                }\n            }\n        ], [\n        arbitrations,\n        themes\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableEditing: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            setThemeId(row.original.id);\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let update_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_update({\n                id: rest.id,\n                data: update_values\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"Th\\xe8me modifi\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.message);\n                    row._valuesCache = {\n                        error: err.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, row, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let insert_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_create(insert_values, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.message);\n                    row._valuesCache = {\n                        error: err.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: (param)=>{\n            let { table } = param;\n            //clear any validation errors\n            table.setCreatingRow(null);\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                //about:\"edit modal\",\n                // open: editVisible || createVisible,\n                maxWidth: \"md\"\n            };\n        // sx: {\n        //   //  '& .MuiDialog-root': {\n        //   //    width :'70vw'\n        //   //  },\n        //   // '& .MuiDialog-container': {\n        //   //   width :'70vw'\n        //   // },\n        //   zIndex: 1100,\n        // }\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_4__.Can, {\n                    I: \"add\",\n                    a: \"ArbitratedTheme\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            })\n    });\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n        lineNumber: 601,\n        columnNumber: 26\n    }, this);\n    console.log(\"-----------------------------------------\", arbitrations);\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_10__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 605,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_13__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 605,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"bDGdo1+71fnDMhQHJ0dTBFzwcyk=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx\n"));

/***/ })

});