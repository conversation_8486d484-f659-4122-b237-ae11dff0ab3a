import { Calendar } from "primereact/calendar";
import { Dropdown } from "primereact/dropdown";
import { InputTextarea } from "primereact/inputtextarea";

const ActionItem = (props: any) => {
    let dropdownItemEtat = null
    function setDropdownItemEtat(value: any): void {
        throw new Error('Function not implemented.');
    }

    return <div className="card p-fluid formgrid grid" style={{ backgroundColor: "var(--surface-ground)" }}>
        <div className="field col-12">
            <label htmlFor="description ">Description</label>
            <InputTextarea rows={5} id="description " value={''} onChange={(e) => { }} placeholder="action"></InputTextarea>
        </div>
        <div className="field col-12 md:col-6">
            <label htmlFor="status">Status</label>
            <Dropdown filter id="status" value={dropdownItemEtat} onChange={(e) => setDropdownItemEtat(e.value)} options={$MissionEtatEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
        </div>
        <div className="field col-12 md:col-6">
            <label htmlFor="validated">Validation</label>
            <Dropdown filter id="validated" value={dropdownItemEtat} onChange={(e) => setDropdownItemEtat(e.value)} options={$MissionEtatEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
        </div>
        <div className="field col-12 md:col-6">
            <label htmlFor="accepted">Acceptation</label>
            <Dropdown filter id="accepted" value={dropdownItemEtat} onChange={(e) => setDropdownItemEtat(e.value)} options={$MissionEtatEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
        </div>
        <div className="field col-12 md:col-6">
            <label htmlFor="job_leader">Job Leader</label>
            <Dropdown filter id="job_leader" value={dropdownItemEtat} onChange={(e) => setDropdownItemEtat(e.value)} options={$MissionEtatEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
        </div>
        <div className="field col-12 md:col-6">
            <label htmlFor="start_date">Date Début</label>
            <Calendar id="start_date" value={new Date()} />
        </div>
        <div className="field col-12 md:col-6">
            <label htmlFor="end_date">Date Fin</label>
            <Calendar id="end_date" value={new Date()} locale='fr' />
        </div>
    </div>
}
export default ActionItem;