import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/recommendations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const missionId = searchParams.get('missionId')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { recommendation: { contains: search, mode: 'insensitive' as const } },
        { responsible: { contains: search, mode: 'insensitive' as const } },
      ]
    }
    
    if (missionId) {
      where.missionId = parseInt(missionId)
    }
    
    const [recommendations, total] = await Promise.all([
      prisma.recommendation.findMany({
        where,
        skip,
        take: limit,
        include: {
          mission: {
            select: {
              id: true,
              code: true,
              type: true,
            },
          },
          concernedStructure: true,
          causes: true,
          constats: true,
          actions: {
            include: {
              jobLeader: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          comments: {
            include: {
              createdByUser: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
        orderBy: { id: 'desc' },
      }),
      prisma.recommendation.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: recommendations,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching recommendations:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/recommendations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      numrecommandation,
      recommendation,
      concernedStructureId,
      priority,
      status,
      validated,
      accepted,
      responsible,
      missionId,
      causes,
      constats,
    } = body
    
    const newRecommendation = await prisma.recommendation.create({
      data: {
        numrecommandation,
        recommendation,
        concernedStructureId: BigInt(concernedStructureId),
        priority,
        status,
        validated,
        accepted,
        responsible,
        missionId,
        causes: causes ? { connect: causes.map((id: number) => ({ id })) } : undefined,
        constats: constats ? { connect: constats.map((id: number) => ({ id })) } : undefined,
      },
      include: {
        mission: {
          select: {
            id: true,
            code: true,
            type: true,
          },
        },
        concernedStructure: true,
        causes: true,
        constats: true,
        actions: true,
        comments: true,
      },
    })
    
    return NextResponse.json(newRecommendation, { status: 201 })
  } catch (error) {
    console.error('Error creating recommendation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
