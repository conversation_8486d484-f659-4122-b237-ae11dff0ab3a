'use client';
import { ChildContainerProps } from '@/types';
// import { ToastContextProps } from '@/types/layout';
import React, { useState, createContext, useContext, useRef } from 'react';
import { Toast } from 'primereact/toast';

export const ToastContext = createContext()
    
export const ToastContextProvider = ({ children }: ChildContainerProps) => {
    const toastRef = useRef<Toast>(null)
    return  <ToastContext.Provider value={toastRef}>
                <Toast position="bottom-center" ref={toastRef}/>
                { children }
            </ToastContext.Provider>
   
}

export const useToast = () => {
    const  toastRef  = useContext(ToastContext)
    return  toastRef 
}