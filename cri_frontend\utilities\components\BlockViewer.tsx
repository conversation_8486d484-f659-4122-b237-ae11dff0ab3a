import { Tooltip } from 'primereact/tooltip';
import { classNames } from 'primereact/utils';
import React, { useRef, useState } from 'react';

interface BlockViewerProps {
    header: string | React.ReactNode;
    code?: string;
    new?: boolean;
    free?: boolean;
    priority?: string;
    status?: string;
    containerClassName?: string;
    editStyle?: React.CSSProperties;
    children: React.ReactNode;
}

const BlockViewer = (props: BlockViewerProps) => {
    const [blockView, setBlockView] = useState('Editer');
    const actionCopyRef = useRef(null);
    const statusBadgeRef = useRef(null);
    const priorityBadgeRef = useRef(null);

    const copyCode = async (event: React.MouseEvent<HTMLButtonElement>) => {
        await navigator.clipboard.writeText(props.code!);
        event.preventDefault();
    };

    return (
        <div className="block-viewer">
            <div className="block-section">
                <div className="block-header">
                    <span className="block-title">
                        <span>{props.header}</span>
                        {/* {props.new && <span className="badge-new">New</span>}
                        {props.free && <span className="badge-free">Free</span>} */}
                        {/* {props.priority && <><Tooltip target={priorityBadgeRef} content="Priorité"></Tooltip><span ref={priorityBadgeRef} className="badge-priority">{props.priority}</span></>}
                        {props.status && <><Tooltip target={statusBadgeRef} content="Statut"></Tooltip><span ref={statusBadgeRef} className="badge-status">{props.status}</span></>} */}
                    </span>
                    <div className="block-actions">
                        {props.priority && <><Tooltip target={priorityBadgeRef} content="Priorité"></Tooltip><span ref={priorityBadgeRef} className="badge-priority">{props.priority}</span></>}
                        {props.status && <><Tooltip target={statusBadgeRef} content="Statut"></Tooltip><span ref={statusBadgeRef} className="badge-status">{props.status}</span></>}
                        {/* <button tabIndex={0} className={classNames('p-link', { 'block-action-active': blockView === 'Editer' })} onClick={() => setBlockView('Editer')}>
                            <span>Editer</span>
                        </button> */}
                        {/* {props.code && <><button className={classNames('p-link', { 'block-action-active': blockView === 'CODE' })} onClick={() => setBlockView('CODE')}>
                            <span>Code</span>
                        </button>
                            <button ref={actionCopyRef} tabIndex={0} className="p-link block-action-copy" onClick={copyCode}>
                                <i className="pi pi-copy"></i>
                            </button>
                            <Tooltip target={actionCopyRef as any} position="bottom" content="Copied to clipboard" event="focus" />
                        </>} */}
                    </div>
                </div>
                <div className="block-content">
                    {blockView === 'Editer' && (
                        <div className={props.containerClassName} style={props.editStyle}>
                            {props.children}
                        </div>
                    )}

                    {blockView === 'CODE' && (
                        <pre className="app-code">
                            <code>{props.code}</code>
                        </pre>
                    )}
                </div>
            </div>
        </div>
    );
};

export default BlockViewer;
