import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/arbitrated-themes/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid arbitrated theme ID' }, { status: 400 })
    }

    const arbitratedTheme = await prisma.arbitratedTheme.findUnique({
      where: { id },
      include: {
        arbitration: {
          include: {
            plan: {
              select: {
                id: true,
                title: true,
                exercise: true,
                type: true,
              }
            },
            team: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
              }
            }
          }
        },
        theme: {
          include: {
            domain: true,
            process: true,
            proposingStructures: true,
            concernedStructures: true,
            risks: true,
            goals: true,
          }
        },
        missions: {
          select: {
            id: true,
            code: true,
            title: true,
            type: true,
            etat: true,
            startDate: true,
            endDate: true,
          }
        }
      },
    })

    if (!arbitratedTheme) {
      return NextResponse.json({ error: 'Arbitrated theme not found' }, { status: 404 })
    }

    return NextResponse.json({ data: arbitratedTheme })
  } catch (error) {
    console.error('Error fetching arbitrated theme:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/arbitrated-themes/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can update arbitrated themes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid arbitrated theme ID' }, { status: 400 })
    }

    const body = await request.json()
    const { note } = body

    // Check if arbitrated theme exists
    const existingArbitratedTheme = await prisma.arbitratedTheme.findUnique({
      where: { id },
    })

    if (!existingArbitratedTheme) {
      return NextResponse.json({ error: 'Arbitrated theme not found' }, { status: 404 })
    }

    // Update arbitrated theme
    const arbitratedTheme = await prisma.arbitratedTheme.update({
      where: { id },
      data: {
        ...(note !== undefined && { note }),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        arbitration: {
          include: {
            plan: {
              select: {
                id: true,
                title: true,
                exercise: true,
                type: true,
              }
            }
          }
        },
        theme: {
          select: {
            id: true,
            title: true,
            code: true,
            validated: true,
            proposedBy: true,
          }
        },
        missions: {
          select: {
            id: true,
            code: true,
            title: true,
            type: true,
            etat: true,
          }
        }
      },
    })

    return NextResponse.json({ data: arbitratedTheme })
  } catch (error) {
    console.error('Error updating arbitrated theme:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/arbitrated-themes/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can delete arbitrated themes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid arbitrated theme ID' }, { status: 400 })
    }

    // Check if arbitrated theme exists
    const existingArbitratedTheme = await prisma.arbitratedTheme.findUnique({
      where: { id },
      include: {
        missions: true,
      },
    })

    if (!existingArbitratedTheme) {
      return NextResponse.json({ error: 'Arbitrated theme not found' }, { status: 404 })
    }

    // Check if arbitrated theme has related missions
    if (existingArbitratedTheme.missions.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete arbitrated theme with existing missions' },
        { status: 400 }
      )
    }

    // Delete arbitrated theme
    await prisma.arbitratedTheme.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Arbitrated theme deleted successfully' })
  } catch (error) {
    console.error('Error deleting arbitrated theme:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
