'use client';

import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import GenericTable from './(components)/GenericTAble';
import { Process } from '@prisma/client';
import { useApiProcessList } from '@/hooks/useNextApi';
import { $Process } from '@/lib/schemas';

const ProcessesTable = () => {
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex:0,
        pageSize: 30, //customize the default page size
    });
    const { data: processes, isLoading:isLoading ,error:error} = useApiProcessList();

    
    if( isLoading )  return (<div></div>)
    return   (        
        <div className="grid">
            <div className="col-12">
                <GenericTable<Process> data_={processes} isLoading={isLoading} error={error} data_type={$Process} pagination={{"set":setPagination,"pagi":pagination}}></GenericTable>
            </div>
        </div>
    );
};

export default ProcessesTable;
