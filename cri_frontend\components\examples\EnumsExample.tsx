/**
 * Example component demonstrating how to use all the enums
 * This shows different ways to use the enum values in dropdowns and forms
 */

import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Tag } from 'primereact/tag';
import { 
  PlanType, PlanTypeOptions, PlanTypeLabels,
  MissionType, MissionTypeOptions, MissionTypeLabels,
  MissionEtat, MissionEtatOptions, MissionEtatLabels,
  ThemeProposingEntity, ThemeProposingEntityOptions, ThemeProposingEntityLabels,
  RecommendationPriority, RecommendationPriorityOptions, RecommendationPriorityLabels,
  RecommendationEtat, RecommendationEtatOptions, RecommendationEtatLabels,
  ActionEtat, ActionEtatOptions, ActionEtatLabels,
  DocumentType, DocumentTypeOptions, DocumentTypeLabels,
  ValidationEnum, ValidationEnumOptions, ValidationEnumLabels,
  RecommendationActionType, RecommendationActionTypeOptions, RecommendationActionTypeLabels,
  $ProposedByEnum
} from '@/lib/enums';

export default function EnumsExample() {
  const [selectedPlanType, setSelectedPlanType] = useState<PlanType | null>(null);
  const [selectedMissionType, setSelectedMissionType] = useState<MissionType | null>(null);
  const [selectedMissionEtat, setSelectedMissionEtat] = useState<MissionEtat | null>(null);
  const [selectedProposingEntity, setSelectedProposingEntity] = useState<ThemeProposingEntity | null>(null);
  const [selectedRecommendationPriority, setSelectedRecommendationPriority] = useState<RecommendationPriority | null>(null);
  const [selectedRecommendationEtat, setSelectedRecommendationEtat] = useState<RecommendationEtat | null>(null);
  const [selectedActionEtat, setSelectedActionEtat] = useState<ActionEtat | null>(null);
  const [selectedDocumentType, setSelectedDocumentType] = useState<DocumentType | null>(null);
  const [selectedValidation, setSelectedValidation] = useState<ValidationEnum | null>(null);
  const [selectedActionType, setSelectedActionType] = useState<RecommendationActionType | null>(null);

  const resetSelections = () => {
    setSelectedPlanType(null);
    setSelectedMissionType(null);
    setSelectedMissionEtat(null);
    setSelectedProposingEntity(null);
    setSelectedRecommendationPriority(null);
    setSelectedRecommendationEtat(null);
    setSelectedActionEtat(null);
    setSelectedDocumentType(null);
    setSelectedValidation(null);
    setSelectedActionType(null);
  };

  const getSelectedValues = () => {
    return {
      planType: selectedPlanType,
      missionType: selectedMissionType,
      missionEtat: selectedMissionEtat,
      proposingEntity: selectedProposingEntity,
      recommendationPriority: selectedRecommendationPriority,
      recommendationEtat: selectedRecommendationEtat,
      actionEtat: selectedActionEtat,
      documentType: selectedDocumentType,
      validation: selectedValidation,
      actionType: selectedActionType,
    };
  };

  return (
    <div className="enums-example">
      <Card title="Exemples d'utilisation des énumérations" className="mb-4">
        <p>
          Cette page démontre comment utiliser toutes les énumérations définies dans le système.
          Chaque dropdown utilise les valeurs d'énumération appropriées avec leurs libellés français.
        </p>
      </Card>

      <div className="grid">
        {/* Plan Type */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="Type de Plan" className="h-full">
            <div className="field">
              <label htmlFor="planType">Type de Plan:</label>
              <Dropdown
                id="planType"
                value={selectedPlanType}
                options={PlanTypeOptions}
                onChange={(e) => setSelectedPlanType(e.value)}
                optionLabel="label"
                placeholder="Sélectionner un type"
                className="w-full"
              />
              {selectedPlanType && (
                <div className="mt-2">
                  <Tag value={PlanTypeLabels[selectedPlanType]} severity="info" />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Mission Type */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="Type de Mission" className="h-full">
            <div className="field">
              <label htmlFor="missionType">Type de Mission:</label>
              <Dropdown
                id="missionType"
                value={selectedMissionType}
                options={MissionTypeOptions}
                onChange={(e) => setSelectedMissionType(e.value)}
                optionLabel="label"
                placeholder="Sélectionner un type"
                className="w-full"
              />
              {selectedMissionType && (
                <div className="mt-2">
                  <Tag value={MissionTypeLabels[selectedMissionType]} severity="success" />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Mission État */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="État de Mission" className="h-full">
            <div className="field">
              <label htmlFor="missionEtat">État de Mission:</label>
              <Dropdown
                id="missionEtat"
                value={selectedMissionEtat}
                options={MissionEtatOptions}
                onChange={(e) => setSelectedMissionEtat(e.value)}
                optionLabel="label"
                placeholder="Sélectionner un état"
                className="w-full"
              />
              {selectedMissionEtat && (
                <div className="mt-2">
                  <Tag 
                    value={MissionEtatLabels[selectedMissionEtat]} 
                    severity={selectedMissionEtat === MissionEtat.Closed ? 'danger' : 
                             selectedMissionEtat === MissionEtat.InProgress ? 'warning' : 'secondary'} 
                  />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Theme Proposing Entity */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="Entité Proposante" className="h-full">
            <div className="field">
              <label htmlFor="proposingEntity">Entité Proposante:</label>
              <Dropdown
                id="proposingEntity"
                value={selectedProposingEntity}
                options={ThemeProposingEntityOptions}
                onChange={(e) => setSelectedProposingEntity(e.value)}
                optionLabel="label"
                placeholder="Sélectionner une entité"
                className="w-full"
              />
              {selectedProposingEntity && (
                <div className="mt-2">
                  <Tag value={ThemeProposingEntityLabels[selectedProposingEntity]} severity="info" />
                </div>
              )}
            </div>
            <div className="mt-3">
              <small className="text-muted">
                Utilise aussi $ProposedByEnum pour compatibilité: {$ProposedByEnum.enum.join(', ')}
              </small>
            </div>
          </Card>
        </div>

        {/* Recommendation Priority */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="Priorité de Recommandation" className="h-full">
            <div className="field">
              <label htmlFor="recommendationPriority">Priorité:</label>
              <Dropdown
                id="recommendationPriority"
                value={selectedRecommendationPriority}
                options={RecommendationPriorityOptions}
                onChange={(e) => setSelectedRecommendationPriority(e.value)}
                optionLabel="label"
                placeholder="Sélectionner une priorité"
                className="w-full"
              />
              {selectedRecommendationPriority && (
                <div className="mt-2">
                  <Tag 
                    value={RecommendationPriorityLabels[selectedRecommendationPriority]} 
                    severity={selectedRecommendationPriority === RecommendationPriority.HIGH ? 'danger' : 
                             selectedRecommendationPriority === RecommendationPriority.NORMAL ? 'warning' : 'success'} 
                  />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Recommendation État */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="État de Recommandation" className="h-full">
            <div className="field">
              <label htmlFor="recommendationEtat">État:</label>
              <Dropdown
                id="recommendationEtat"
                value={selectedRecommendationEtat}
                options={RecommendationEtatOptions}
                onChange={(e) => setSelectedRecommendationEtat(e.value)}
                optionLabel="label"
                placeholder="Sélectionner un état"
                className="w-full"
              />
              {selectedRecommendationEtat && (
                <div className="mt-2">
                  <Tag 
                    value={RecommendationEtatLabels[selectedRecommendationEtat]} 
                    severity={selectedRecommendationEtat === RecommendationEtat.Accomplished ? 'success' : 
                             selectedRecommendationEtat === RecommendationEtat.InProgress ? 'warning' : 'danger'} 
                  />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Action État */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="État d'Action" className="h-full">
            <div className="field">
              <label htmlFor="actionEtat">État d'Action:</label>
              <Dropdown
                id="actionEtat"
                value={selectedActionEtat}
                options={ActionEtatOptions}
                onChange={(e) => setSelectedActionEtat(e.value)}
                optionLabel="label"
                placeholder="Sélectionner un état"
                className="w-full"
              />
              {selectedActionEtat && (
                <div className="mt-2">
                  <Tag 
                    value={ActionEtatLabels[selectedActionEtat]} 
                    severity={selectedActionEtat === ActionEtat.Accomplished ? 'success' : 
                             selectedActionEtat === ActionEtat.InProgress ? 'warning' : 'danger'} 
                  />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Document Type */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="Type de Document" className="h-full">
            <div className="field">
              <label htmlFor="documentType">Type de Document:</label>
              <Dropdown
                id="documentType"
                value={selectedDocumentType}
                options={DocumentTypeOptions}
                onChange={(e) => setSelectedDocumentType(e.value)}
                optionLabel="label"
                placeholder="Sélectionner un type"
                className="w-full"
              />
              {selectedDocumentType && (
                <div className="mt-2">
                  <Tag value={DocumentTypeLabels[selectedDocumentType]} severity="info" />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Validation */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="Validation" className="h-full">
            <div className="field">
              <label htmlFor="validation">Validation:</label>
              <Dropdown
                id="validation"
                value={selectedValidation}
                options={ValidationEnumOptions}
                onChange={(e) => setSelectedValidation(e.value)}
                optionLabel="label"
                placeholder="Sélectionner une validation"
                className="w-full"
              />
              {selectedValidation && (
                <div className="mt-2">
                  <Tag 
                    value={ValidationEnumLabels[selectedValidation]} 
                    severity={selectedValidation === ValidationEnum.YES || selectedValidation === ValidationEnum.AC ? 'success' : 'danger'} 
                  />
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Recommendation Action Type */}
        <div className="col-12 md:col-6 lg:col-4">
          <Card title="Type d'Action de Recommandation" className="h-full">
            <div className="field">
              <label htmlFor="actionType">Type d'Action:</label>
              <Dropdown
                id="actionType"
                value={selectedActionType}
                options={RecommendationActionTypeOptions}
                onChange={(e) => setSelectedActionType(e.value)}
                optionLabel="label"
                placeholder="Sélectionner un type"
                className="w-full"
              />
              {selectedActionType && (
                <div className="mt-2">
                  <Tag 
                    value={RecommendationActionTypeLabels[selectedActionType]} 
                    severity={selectedActionType === RecommendationActionType.accepted ? 'success' : 
                             selectedActionType === RecommendationActionType.not_concerned ? 'warning' : 'danger'} 
                  />
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>

      {/* Actions */}
      <Card title="Actions" className="mt-4">
        <div className="flex gap-2 mb-3">
          <Button 
            label="Réinitialiser toutes les sélections" 
            icon="pi pi-refresh" 
            onClick={resetSelections}
            severity="secondary"
          />
          <Button 
            label="Afficher les valeurs sélectionnées" 
            icon="pi pi-eye" 
            onClick={() => console.log('Valeurs sélectionnées:', getSelectedValues())}
          />
        </div>

        <div className="mt-3">
          <h5>Valeurs actuellement sélectionnées:</h5>
          <pre className="bg-gray-100 p-3 border-round">
            {JSON.stringify(getSelectedValues(), null, 2)}
          </pre>
        </div>
      </Card>
    </div>
  );
}
