services:
  next-app:
    container_name: cri-frontend-prod-app
    build:
      context: ./cri_frontend
      dockerfile: prod.Dockerfile
      # args:
      #   ENV_VARIABLE: ${ENV_VARIABLE}
      #   NEXT_PUBLIC_ENV_VARIABLE: ${NEXT_PUBLIC_ENV_VARIABLE}
    restart: always
    ports:
      - 3000:3000
    networks:
      - cri_network

  # Add more containers below (nginx, postgres, etc.)

# Define a network, which allows containers to communicate
# with each other, by using their container name as a hostname
networks:
  cri_network:
    external: true