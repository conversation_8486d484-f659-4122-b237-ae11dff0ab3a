'use client';

// import { useA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useApi<PERSON><PERSON><PERSON><PERSON>ist, useApiMissionCreate, useApi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useApiMissionList, useApiPlanList, useApiProcessList, useApiRiskList, useApiStructurelqsList, useApiThemeList, useApiUsersList } from '@/services/api/api/api';
import { useEffect, useMemo, useState, type ReactNode } from 'react';
import { $MissionEtatEnum, $MissionSerializerRead, $ProposedByEnum, $Risk, $MissionTypeEnum } from '@/services/openapi_client/schemas.gen';
import { Box, DialogActions, DialogContent, Typography } from '@mui/material';
import { table } from 'console';
import { MRT_EditActionButtons, MRT_TableInstance, type MRT_Row } from 'material-react-table';
import { Sidebar } from 'primereact/sidebar';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { PickList } from 'primereact/picklist';
import { Action, CriStructview, Goal, MissionSerializerRead, ProposedByEnum, Risk, Theme, User } from '@/services/schemas';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import { Calendar } from 'primereact/calendar';
import React from 'react';
import { Button } from 'primereact/button';
import { ProgressSpinner } from 'primereact/progressspinner';
import { addLocale, locale } from 'primereact/api';
import { ToggleButton } from 'primereact/togglebutton';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { getCookie } from 'cookies-next';
import { Card } from 'primereact/card';
import { usebaseData } from '@/utilities/hooks/useBaseData';

interface DropdownItem {
    name: string;
    code: string | number;
}
const ThemeEditForm = (props: {
    internalEditComponents: ReactNode[]; row: MRT_Row<Theme>; table: MRT_TableInstance<Theme>;
}) => {
    ///////////////////////////////////////////////////////////////////////////////
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    const {
        users,
        plans,
        risks,
        themes,
        goals,
        domains,
        processes,
        structureslqs: structures_lqs,
    } = usebaseData()
    ///////////////////////////Stepper functions///////////////////////////////////
    const [activeStep, setActiveStep] = React.useState(0);
    const [skipped, setSkipped] = React.useState(new Set<number>());

    const isStepOptional = (step: number) => {
        return step === 1;
    };

    const isStepSkipped = (step: number) => {
        return skipped.has(step);
    };

    const handleNext = () => {
        let newSkipped = skipped;
        if (isStepSkipped(activeStep)) {
            newSkipped = new Set(newSkipped.values());
            newSkipped.delete(activeStep);
        }

        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        setSkipped(newSkipped);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleSkip = () => {
        if (!isStepOptional(activeStep)) {
            // You probably want to guard against something like this,
            // it should never occur unless someone's actively trying to break something.
            throw new Error("You can't skip a step that isn't optional.");
        }

        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        setSkipped((prevSkipped) => {
            const newSkipped = new Set(prevSkipped.values());
            newSkipped.add(activeStep);
            return newSkipped;
        });
    };

    const handleReset = () => {
        setActiveStep(0);
    };
    ///////////////////////////Stepper functions///////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////////
    const steps = ['Thème', 'Risques', 'Objectifs']; //'Structures Proposantes', 'Structures conernées',

    ///////////////////////////////////////////////////////////////////////////////
    const [theme_data, setThemeData] = useState({
        "domain": props.row.id === 'mrt-row-create' ? null : props.row.original.domain.id,
        "process": props.row.id === 'mrt-row-create' ? null : props.row.original.process.id,
        "risks": props.row.id === 'mrt-row-create' ? [] : props.row.original.risks.map((risk: Risk) => risk.id),
        "goals": props.row.id === 'mrt-row-create' ? [] : props.row.original.goals.map((goal: Goal) => goal.id),
        "proposing_structures": props.row.id === 'mrt-row-create' ? [] : props.row.original.proposing_structures.map((struct: CriStructview) => struct.id),
        "concerned_structures": props.row.id === 'mrt-row-create' ? [] : props.row.original.concerned_structures.map((struct: CriStructview) => struct.id),
        "validated": props.row.id === 'mrt-row-create' ? false : props.row.original.validated,
        "code": props.row.id === 'mrt-row-create' ? "" : props.row.original.code,
        "title": props.row.id === 'mrt-row-create' ? "" : props.row.original.title,
        "proposed_by": props.row.id === 'mrt-row-create' ? null : props.row.original.proposed_by,
        "month_start": props.row.id === 'mrt-row-create' ? null : props.row.original.month_start,
        "month_end": props.row.id === 'mrt-row-create' ? null : props.row.original.month_end,
        "id": props.row.id === 'mrt-row-create' ? null : props.row.original.id
    })
    const handleTheme = (field: string, event: any) => {
        const theme_new = { ...theme_data, ...{ [field]: event } }
        props.row._valuesCache = theme_new
        console.log(theme_new)
        setThemeData(theme_new)
    }
    // const { data: users,            isLoading, error } = useApiUsersList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }});
    // const { data: plans,            isLoading: plan_isLoading, error: plan_error } = useApiPlanList()
    // const { data: risks,            isLoading: risks_isLoading, error: risks_error } = useApiRiskList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})
    // const { data: themes,           isLoading: themes_isLoading, error: themes_error } = useApiThemeList()
    // const { data: goals,            isLoading: goals_isLoading, error: goals_error } = useApiGoalList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})
    // const { data: domains,          isLoading: domains_isLoading, error: domains_error } = useApiDomainList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})
    // const { data: processes,        isLoading: processes_isLoading, error: processes_error } = useApiProcessList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})
    // const { data: structures_lqs,   isLoading: structures_lqs_isLoading, error: structures_lqs_error } = useApiStructurelqsList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})

    const [editDialogVisible, setEditDialogVisible] = useState(true);

    const [picklistSourceValueProposingStructures, setPicklistSourceValueProposingStructures] = useState(structures_lqs?.data?.data.results);
    const [picklistTargetValueProposingStructures, setPicklistTargetValueProposingStructures] = useState(props.row.id === 'mrt-row-create' ? [] : props.row.original.proposing_structures);

    const [picklistSourceValueRisks, setPicklistSourceValueRisks] = useState(risks?.data.results);
    const [picklistTargetValueRisks, setPicklistTargetValueRisks] = useState(props.row.id === 'mrt-row-create' ? [] : props.row.original.risks);

    const [picklistSourceValueGoals, setPicklistSourceValueGoals] = useState(goals?.data.results);
    const [picklistTargetValueGoals, setPicklistTargetValueGoals] = useState(props.row.id === 'mrt-row-create' ? [] : props.row.original.goals);

    const [picklistSourceValueConcernedStructures, setPicklistSourceValueConcernedStructures] = useState(structures_lqs?.data?.data.results);
    const [picklistTargetValueConcernedStructures, setPicklistTargetValueConcernedStructures] = useState(props.row.id === 'mrt-row-create' ? [] : props.row.original.proposing_structures);

    const [dropdownItemDomain, setDropdownItemDomain] = useState<DropdownItem | null>(props.row.id === 'mrt-row-create' ? null : { "name": props.row.original.domain.title, "code": props.row.original.domain.id });

    const [dropdownItemProcess, setDropdownItemProcess] = useState<DropdownItem | null>(props.row.id === 'mrt-row-create' ? null : { "name": props.row.original.process.title, "code": props.row.original.process.id });

    const [dropdownItemProposedBy, setDropdownItemProposedBy] = useState<DropdownItem | null>(props.row.id === 'mrt-row-create' ? null : { "name": props.row.original.proposed_by, "code": props.row.original.proposed_by });

    const [theme_validated, setThemeValidated] = useState(props.row.id === 'mrt-row-create' ? false : props.row.original.validated)

    const [theme_code, setThemeCode] = useState<string | null | undefined>(props.row.id === 'mrt-row-create' ? null : props.row.original.code)

    const [theme_title, setThemeTitle] = useState<string>(props.row.id === 'mrt-row-create' ? '' : props.row.original.title)

    const [theme_end_date, setThemeEndDate] = useState<Date>(props.row.id === 'mrt-row-create' ? new Date() : new Date(props.row.original.month_end))

    const [theme_start_date, setThemeStartDate] = useState<Date>(props.row.id === 'mrt-row-create' ? new Date() : new Date(props.row.original.month_start))

    useEffect(() => {
        setPicklistSourceValueConcernedStructures(props.row.id === 'mrt-row-create' ? structures_lqs?.data?.data.results : structures_lqs?.data?.data.results.filter((struct: CriStructview) => !props.row.original.concerned_structures.map((struct_: CriStructview) => struct_.id).includes(struct.id)));
        setPicklistTargetValueConcernedStructures(props.row.id === 'mrt-row-create' ? [] : props.row.original.concerned_structures);

        setPicklistSourceValueProposingStructures(props.row.id === 'mrt-row-create' ? structures_lqs?.data?.data.results : structures_lqs?.data?.data.results.filter((struct: CriStructview) => !props.row.original.proposing_structures.map((struct_: CriStructview) => struct_.id).includes(struct.id)));
        setPicklistTargetValueProposingStructures(props.row.id === 'mrt-row-create' ? [] : props.row.original.proposing_structures);

        setPicklistSourceValueRisks(props.row.id === 'mrt-row-create' ? risks?.data.data.results : risks?.data?.data.results.filter((risk: Risk) => !props.row.original.risks.map((risk_: Risk) => risk_.id).includes(risk.id)));
        setPicklistTargetValueRisks(props.row.id === 'mrt-row-create' ? [] : props.row.original.risks);

        setPicklistSourceValueGoals(props.row.id === 'mrt-row-create' ? goals?.data.data.results : goals?.data?.data.results.filter((goal: Goal) => !props.row.original.goals.map((goal_: Goal) => goal_.id).includes(goal.id)));
        setPicklistTargetValueGoals(props.row.id === 'mrt-row-create' ? [] : props.row.original.goals);

    }, [structures_lqs])
    useEffect(() => {
        // setDropdownItemDomain(props.row.id === 'mrt-row-create' ? null : { "name": props.row.original.domain.title, "code": props.row.original.domain.id })
        props.row._valuesCache = { ...theme_data }
    }, [])

    if (plans.isLoading && structures_lqs.isLoading && domains.isLoading && processes.isLoading && risks.isLoading && goals.isLoading) return (<ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />)
    if (structures_lqs.error) return (<div>{structures_lqs.error.message}</div>)


    return <>
        <div style={{ zIndex: '1302 !important' }}>
            <Sidebar position='right'
                header={
                    <div className='flex flex-row w-full flex-wrap justify-content-between'>
                        <span className='align-content-center '>{props.row.id === 'mrt-row-create' ? 'Nouveau thème' : 'Editer théme :'} {props.row.original?.code}</span>
                        {props.row._valuesCache.error?.data?.["non_field_errors"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["non_field_errors"][0]}</small>}
                        <DialogActions>
                            <MRT_EditActionButtons variant="text" table={props.table} row={props.row} />
                        </DialogActions>
                    </div>
                }
                visible={editDialogVisible}
                onHide={() => {
                    //  props.table.setEditingRow(null); setEditDialogVisible(false) 
                }}
                className="w-full md:w-9 lg:w-10"
            >
                <DialogContent
                    sx={{ display: 'flex', flexDirection: 'column', gap: '0.7rem' }}
                >
                    <div className="col-12">
                        <Stepper activeStep={activeStep} sx={
                            {
                                paddingY: '0.7rem'
                            }
                        }>
                            {steps.map((label, index) => {
                                const stepProps: { completed?: boolean } = {};
                                const labelProps: {
                                    optional?: React.ReactNode;
                                } = {};
                                if (isStepOptional(index)) {
                                    labelProps.optional = (
                                        <Typography variant="caption">Optional</Typography>
                                    );
                                }
                                if (isStepSkipped(index)) {
                                    stepProps.completed = false;
                                }
                                return (
                                    <Step key={label}
                                        {...stepProps}
                                        sx={{
                                            '& .MuiStepLabel-root .Mui-completed': {
                                                color: 'secondary.dark', // circle color (COMPLETED)
                                            },
                                            '& .MuiStepLabel-label.Mui-completed.MuiStepLabel-alternativeLabel':
                                            {
                                                color: 'white', // Just text label (COMPLETED)
                                            },
                                            '& .MuiStepLabel-root .Mui-active': {
                                                color: 'var(--primary-color)', // circle color (ACTIVE)
                                            },
                                            '& .MuiStepLabel-label.Mui-active.MuiStepLabel-alternativeLabel':
                                            {
                                                color: 'white', // Just text label (ACTIVE)
                                            },
                                            '& .MuiStepLabel-root .Mui-active .MuiStepIcon-text': {
                                                fill: 'white', // circle's number (ACTIVE)
                                            },
                                        }}
                                    >
                                        <StepLabel {...labelProps}>{label}</StepLabel>
                                    </Step>
                                );
                            })}
                        </Stepper>
                        {activeStep === steps.length ? (
                            <React.Fragment>
                                <Typography sx={{ mt: 2, mb: 1 }}>
                                    All steps completed - you&apos;re finished
                                </Typography>
                                <Box sx={{ display: 'flex', flexDirection: 'row', pt: 1 }}>
                                    <Box sx={{ flex: '1 1 auto' }} />
                                    <Button onClick={handleReset}>Reset</Button>
                                </Box>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                {/* <Typography sx={{ mt: 2, mb: 1 }}>Step {activeStep }</Typography> */}
                                {activeStep === 0 && (
                                    <Card >
                                        <div className="p-fluid formgrid grid">
                                            <div className="field col-12 md:col-12">
                                                <label htmlFor="title">Thématique</label>
                                                <InputTextarea className={props.row._valuesCache.error?.data?.["title"] ? 'p-invalid' : ''} id="title" defaultValue={theme_title} onChange={(e) => { handleTheme('title', e.target.value); setThemeTitle(e.target.value) }} />
                                                {props.row._valuesCache.error?.data?.["title"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["title"][0]}</small>}
                                            </div>
                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="code">Code</label>
                                                <InputText className={props.row._valuesCache.error?.data?.["code"] ? 'p-invalid' : ''} id="code" type="text" defaultValue={theme_code!} onChange={(e) => { handleTheme('code', e.target.value); setThemeCode(e.target.value) }} />
                                                {props.row._valuesCache.error?.data?.["code"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["code"][0]}</small>}

                                            </div>
                                            <div className="field col-12 md:col-3">
                                                <label htmlFor="proposed_by">Proposé par</label>
                                                <Dropdown className={props.row._valuesCache.error?.data?.["proposed_by"] ? 'p-invalid' : ''} filter id="proposed_by" value={dropdownItemProposedBy} onChange={(e) => { handleTheme('proposed_by', e.value.name); setDropdownItemProposedBy(e.value) }} options={$ProposedByEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
                                                {props.row._valuesCache.error?.data?.["proposed_by"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["proposed_by"][0]}</small>}
                                            </div>
                                            <div className="field col-12 md:col-3">
                                                <label htmlFor="validated">Validée</label>
                                                <ToggleButton className={props.row._valuesCache.error?.data?.["validated"] ? 'p-invalid' : ''} onLabel='Oui' offLabel='Non' color='green' id="validated" checked={theme_validated} onChange={(e) => { handleTheme('validated', e.value); setThemeValidated(e.value) }} placeholder="Choisir un"></ToggleButton>
                                                {props.row._valuesCache.error?.data?.["validated"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["validated"][0]}</small>}

                                            </div>

                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="domain">Domaine</label>
                                                <Dropdown className={props.row._valuesCache.error?.data?.["domain"] ? 'p-invalid' : ''} filter id="domain" value={dropdownItemDomain} onChange={(e) => { handleTheme('domain', e.value.code); setDropdownItemDomain(e.value) }} options={domains?.data.data.results.map(function (val) { return { "name": val.title, "code": val.id } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
                                                {props.row._valuesCache.error?.data?.["domain"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["domain"][0]}</small>}

                                            </div>
                                            <div className="field col-6">
                                                <label htmlFor="process">Processus</label>
                                                <Dropdown className={props.row._valuesCache.error?.data?.["process"] ? 'p-invalid' : ''} filter id="process" value={dropdownItemProcess} onChange={(e) => { handleTheme('process', e.value.code); setDropdownItemProcess(e.value) }} options={processes?.data.data.results.map(function (val) { return { "name": val.title, "code": val.id } })} optionLabel="name" placeholder="Choisir un"></Dropdown>
                                                {props.row._valuesCache.error?.data?.["process"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["process"][0]}</small>}

                                            </div>
                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="start_date">Date Début</label>
                                                <Calendar className={props.row._valuesCache.error?.data?.["month_start"] ? 'p-invalid' : ''} id="month_start" value={new Date(theme_start_date)} onChange={(e) => { handleTheme('month_start', e.value?.toISOString().split('T')[0]); setThemeStartDate(e.value) }} locale='fr' />
                                                {props.row._valuesCache.error?.data?.["month_start"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["month_start"][0]}</small>}

                                            </div>
                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="end_date">Date Fin</label>
                                                <Calendar className={props.row._valuesCache.error?.data?.["month_end"] ? 'p-invalid' : ''} id="month_end" value={new Date(theme_end_date)} onChange={(e) => { handleTheme('month_end', e.value?.toISOString().split('T')[0]); setThemeEndDate(e.value) }} locale='fr' />
                                                {props.row._valuesCache.error?.data?.["month_end"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["month_end"][0]}</small>}

                                            </div>
                                            <div className="field col-6">
                                                <label htmlFor="picklist_concerned_structrures">Structures Concernées</label>
                                                <div className='card' style={{ borderColor: props.row._valuesCache.error?.data?.["concerned_structrures"] ? '#e24c4c' : '' }} >
                                                    {props.row._valuesCache.error?.data?.["concerned_structrures"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["concerned_structrures"][0]}</small>}
                                                    <PickList
                                                        id='picklist_concerned_structrures'
                                                        source={picklistSourceValueConcernedStructures}
                                                        target={picklistTargetValueConcernedStructures}
                                                        sourceHeader="De"
                                                        targetHeader="A"
                                                        itemTemplate={(item: CriStructview) => <div className='text-sm'>{item.libell_stru} | {item.code_mnemonique}</div>}
                                                        onChange={(e) => {
                                                            setPicklistSourceValueConcernedStructures(e.source);
                                                            setPicklistTargetValueConcernedStructures(e.target);
                                                            handleTheme('concerned_structures', e.target.map((struct: CriStructview) => struct.id))
                                                        }}
                                                        sourceStyle={{ height: '200px' }}
                                                        targetStyle={{ height: '200px' }}
                                                        filter filterBy='libell_stru,code_mnemonique'
                                                        filterMatchMode='contains'
                                                        sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                                        className={props.row._valuesCache.error?.data?.["concerned_structrures"] ? 'p-invalid' : ''}
                                                    >
                                                    </PickList>
                                                    {props.row._valuesCache.error?.data?.["concerned_structrures"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["concerned_structrures"][0]}</small>}

                                                </div>
                                            </div>
                                            <div className="field col-6 text-center	">
                                                <label htmlFor="picklist_proposing_structures">Structures Proposantes</label>
                                                <div className='card' style={{ borderColor: props.row._valuesCache.error?.data?.["proposing_structures"] ? '#e24c4c' : '' }} >
                                                    {props.row._valuesCache.error?.data?.["proposing_structures"] && <small className='p-error w-full text-sm '>{props.row._valuesCache.error?.data?.["proposing_structures"][0]}</small>}

                                                    <PickList
                                                        id='picklist_proposing_structures'
                                                        source={picklistSourceValueProposingStructures}
                                                        target={picklistTargetValueProposingStructures}
                                                        sourceHeader="De"
                                                        targetHeader="A"
                                                        itemTemplate={(item: CriStructview) => <div className='text-sm'>{item.code_mnemonique} | {item.libell_stru}</div>}
                                                        onChange={(e) => {
                                                            setPicklistSourceValueProposingStructures(e.source);
                                                            setPicklistTargetValueProposingStructures(e.target);
                                                            handleTheme('proposing_structures', e.target.map((struct: CriStructview) => struct.id))
                                                        }}
                                                        sourceStyle={{ height: '200px' }}
                                                        targetStyle={{ height: '200px' }}
                                                        filter filterBy='libell_stru,code_mnemonique'
                                                        filterMatchMode='contains'
                                                        sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"

                                                    >
                                                    </PickList>
                                                </div>
                                            </div>
                                        </div>
                                    </Card>
                                )}
                                {/* {activeStep === 1 && (
                                    <div className="card">
                                        <div className="field col-12">
                                            <PickList
                                                id='picklist_proposing_structures'
                                                source={picklistSourceValueProposingStructures}
                                                target={picklistTargetValueProposingStructures}
                                                sourceHeader="De"
                                                targetHeader="A"
                                                itemTemplate={(item) => <div>{item.title} {item.abbrev}</div>}
                                                onChange={(e) => {
                                                    setPicklistSourceValueProposingStructures(e.source);
                                                    setPicklistTargetValueProposingStructures(e.target);
                                                    handleTheme('proposing_structures',e.target.map((struct)=> struct.id))
                                                }}
                                                sourceStyle={{ height: '200px' }}
                                                targetStyle={{ height: '200px' }}
                                                filter filterBy='username,email,first_name,last_name'
                                                filterMatchMode='contains'
                                                sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                            >
                                            </PickList>
                                        </div>
                                    </div>
                                )}
                                {activeStep === 2 && (
                                    <div className="card">
                                        <div className="field col-12">
                                            <PickList
                                                id='picklist_concerned_structrures'
                                                source={picklistSourceValueConcernedStructures}
                                                target={picklistTargetValueConcernedStructures}
                                                sourceHeader="De"
                                                targetHeader="A"
                                                itemTemplate={(item) => <div>{item.title} {item.abbrev}</div>}
                                                onChange={(e) => {
                                                    setPicklistSourceValueConcernedStructures(e.source);
                                                    setPicklistTargetValueConcernedStructures(e.target);
                                                    handleTheme('concerned_structures',e.target.map((struct)=> struct.id))
                                                }}
                                                sourceStyle={{ height: '200px' }}
                                                targetStyle={{ height: '200px' }}
                                                filter filterBy='username,email,first_name,last_name'
                                                filterMatchMode='contains'
                                                sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                            >
                                            </PickList>
                                        </div>
                                    </div>
                                )} */}
                                {activeStep === 1 && (
                                    <div className="card">
                                        <div className="field col-12">
                                            <label htmlFor="picklist_risks">Risques</label>
                                            <div className='card'>
                                                <PickList
                                                    id='picklist_risks'
                                                    source={picklistSourceValueRisks}
                                                    target={picklistTargetValueRisks}
                                                    sourceHeader="Disponibles"
                                                    targetHeader="Séléctionnés"
                                                    itemTemplate={(item) => <div>{item.description}</div>}
                                                    onChange={(e) => {
                                                        setPicklistSourceValueRisks(e.source);
                                                        setPicklistTargetValueRisks(e.target);
                                                        handleTheme('risks', e.target.map((risk: Risk) => risk.id))

                                                    }}
                                                    sourceStyle={{ height: '200px' }}
                                                    targetStyle={{ height: '200px' }}
                                                    filter filterBy='content'
                                                    filterMatchMode='contains'
                                                    sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                                >
                                                </PickList>
                                            </div>
                                        </div>
                                    </div>
                                )}
                                {activeStep === 2 && (
                                    <div className="card">
                                        <div className="field col-12">
                                            <label htmlFor="picklist_goals">Objectifs</label>
                                            <div className='card'>

                                                <PickList
                                                    id='picklist_goals'
                                                    source={picklistSourceValueGoals}
                                                    target={picklistTargetValueGoals}
                                                    sourceHeader="Disponibles"
                                                    targetHeader="Séléctionnés"
                                                    itemTemplate={(item) => <div>{item.description}</div>}
                                                    onChange={(e) => {
                                                        setPicklistSourceValueGoals(e.source);
                                                        setPicklistTargetValueGoals(e.target);
                                                        handleTheme('goals', e.target.map((goal: Goal) => goal.id))
                                                    }}
                                                    sourceStyle={{ height: '200px' }}
                                                    targetStyle={{ height: '200px' }}
                                                    filter filterBy='content'
                                                    filterMatchMode='contains'
                                                    sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                                >
                                                </PickList>
                                            </div>
                                        </div>
                                    </div>
                                )}
                                <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
                                    <Button
                                        color="inherit"
                                        disabled={activeStep === 0}
                                        onClick={handleBack}
                                        sx={{ mr: 1 }}
                                    >
                                        Back
                                    </Button>
                                    <Box sx={{ flex: '1 1 auto' }} />
                                    {isStepOptional(activeStep) && (
                                        <Button color="inherit" onClick={handleSkip}>
                                            Skip
                                        </Button>
                                    )}
                                    <Button onClick={handleNext}>
                                        {activeStep === steps.length - 1 ? 'Finish' : 'Next'}
                                    </Button>
                                </Box>
                            </React.Fragment>
                        )}
                    </div>
                </DialogContent>
            </Sidebar>
        </div >
    </>;
}

export default ThemeEditForm;
