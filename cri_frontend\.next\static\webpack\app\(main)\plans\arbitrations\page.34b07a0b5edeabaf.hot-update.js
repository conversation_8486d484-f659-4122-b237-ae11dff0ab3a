"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx":
/*!*******************************************************************!*\
  !*** ./app/(main)/plans/(components)/GenericTAbleArbitration.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_hooks_useBaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/hooks/useBaseData */ \"(app-client)/./utilities/hooks/useBaseData.tsx\");\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/editor */ \"(app-client)/./node_modules/primereact/editor/editor.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// import { Editor } from '@tinymce/tinymce-react';\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie, _users_data, _users_data1, _plans_data, _arbitrations_data;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { plans, users, arbitrations } = (0,_utilities_hooks_useBaseData__WEBPACK_IMPORTED_MODULE_4__.usebaseData)();\n    const users_data = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _users_data;\n        return (_users_data = users.data) === null || _users_data === void 0 ? void 0 : _users_data.data.results;\n    }, [\n        (_users_data = users.data) === null || _users_data === void 0 ? void 0 : _users_data.data.results\n    ]);\n    const [arbitrationID, setArbitrationID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [picklistTargetValueTeam, setPicklistTargetValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [picklistSourceValueTeam, setPicklistSourceValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(users_data);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { trigger: arbitration_create_trigger, isMutating: isCreateMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__.useApiArbitrationCreate)();\n    const { trigger: arbitration_patch_trigger, isMutating: isPatchMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__.useApiArbitrationPartialUpdate)();\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const accept_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"created\",\n                \"modified\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    // Cell: ({ cell }) => <div>{parse(cell.getValue<string>())}</div>,\n                    // Cell: ({ cell }) => { if ([\"description\", \"content\",\"report\"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Rapport\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_editor__WEBPACK_IMPORTED_MODULE_8__.Editor, {\n                                    // initialValue={row.original[key]}\n                                    // tinymceScriptSrc=\"http://localhost:3000/tinymce/tinymce.min.js\"\n                                    // apiKey='none'\n                                    value: row.original.report,\n                                    // onChange={(e) => { row._valuesCache.report = e.target.getContent() }}\n                                    onTextChange: (e)=>{\n                                        row._valuesCache.report = e.htmlValue;\n                                    },\n                                    style: {\n                                        height: \"320px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (key === \"plan\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"plan\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_9__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            children: cell.getValue().code\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 38\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { row } = param;\n                        var _row__valuesCache_plan, _row__valuesCache_plan1, _plans_data, _plans;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n                                    optionLabel: \"name\",\n                                    placeholder: \"Choisir un plan\",\n                                    onChange: (e)=>{\n                                        var _plans_data, _plans, _plans_data1, _plans1;\n                                        console.log(e);\n                                        setRowTobe({\n                                            ...rowTobe,\n                                            plan: (_plans = plans) === null || _plans === void 0 ? void 0 : (_plans_data = _plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.data.results.find((plan)=>plan.id === e.value.code)\n                                        });\n                                        row._valuesCache = {\n                                            ...row._valuesCache,\n                                            plan: (_plans1 = plans) === null || _plans1 === void 0 ? void 0 : (_plans_data1 = _plans1.data) === null || _plans_data1 === void 0 ? void 0 : _plans_data1.data.results.find((plan)=>plan.id === e.value.code)\n                                        };\n                                    },\n                                    value: {\n                                        code: ((_row__valuesCache_plan = row._valuesCache.plan) === null || _row__valuesCache_plan === void 0 ? void 0 : _row__valuesCache_plan.id) || null,\n                                        name: ((_row__valuesCache_plan1 = row._valuesCache.plan) === null || _row__valuesCache_plan1 === void 0 ? void 0 : _row__valuesCache_plan1.code) || null\n                                    },\n                                    options: (_plans = plans) === null || _plans === void 0 ? void 0 : (_plans_data = _plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.data.results.map((plan)=>{\n                                        return {\n                                            code: plan.id,\n                                            name: plan.code\n                                        };\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            var _data__data_type_properties_key_title3;\n            if (key === \"id\") return {\n                header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                accessorKey: key,\n                id: key,\n                Edit: ()=>null\n            };\n            var _data__data_type_properties_key_title4;\n            if (key === \"team\") return {\n                header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                accessorKey: key,\n                id: key,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: cell.getValue().map((usr)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_5__.getUserFullname)(usr)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    console.log(\"[ARBITRATION]\", row._valuesCache.team);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"font-bold\",\n                                children: \"Membres\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_11__.PickList, {\n                                source: picklistTargetValueTeam.length === 0 ? picklistSourceValueTeam : picklistSourceValueTeam.filter((user)=>picklistTargetValueTeam.map((user)=>user.username).includes(user.username)),\n                                id: \"picklist_team\",\n                                target: picklistTargetValueTeam.length > 0 ? picklistTargetValueTeam : row._valuesCache.team,\n                                sourceHeader: \"De\",\n                                targetHeader: \"A\",\n                                itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            item.first_name,\n                                            \" \",\n                                            item.last_name\n                                        ]\n                                    }, item.username, true, void 0, void 0),\n                                onChange: (e)=>{\n                                    console.log(\"source Team\", e.source);\n                                    setPicklistSourceValueTeam([\n                                        ...e.source\n                                    ]);\n                                    setPicklistTargetValueTeam([\n                                        ...e.target\n                                    ]);\n                                    row._valuesCache.team = e.target;\n                                },\n                                sourceStyle: {\n                                    height: \"200px\"\n                                },\n                                targetStyle: {\n                                    height: \"200px\"\n                                },\n                                filter: true,\n                                filterBy: \"username,email,first_name,last_name\",\n                                filterMatchMode: \"contains\",\n                                sourceFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\",\n                                targetFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true);\n                }\n            };\n            else {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), [\n        (_users_data1 = users.data) === null || _users_data1 === void 0 ? void 0 : _users_data1.data.results,\n        (_plans_data = plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.data.results,\n        (_arbitrations_data = arbitrations.data) === null || _arbitrations_data === void 0 ? void 0 : _arbitrations_data.data.results\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_12__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                report: false,\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_13__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onEditingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_patch_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setEditingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" mis \\xe0 ajour\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onCreatingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_create_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setCreatingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" cr\\xe9\\xe9\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_6__.Can, {\n                        I: \"add\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_6__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"align-content-center \",\n                                children: \"Cr\\xe9ation nouveau arbitrage\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: createVisible,\n                    onHide: ()=>{\n                        table.setCreatingRow(null);\n                        setCreateVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: internalEditComponents\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"align-content-center \",\n                                children: [\n                                    \"Editer l'arbitrage n\\xb0 \",\n                                    row.original.id\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: editVisible,\n                    onHide: ()=>{\n                        table.setEditingRow(null);\n                        setEditVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: [\n                            internalEditComponents,\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, this);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.report);\n        },\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_6__.Can, {\n                        I: \"update\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                setArbitrationID(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true);\n                                console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 542,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_6__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept: accept_row_deletion,\n                                    reject: reject_row_deletion\n                                })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 565,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 569,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_20__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 569,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"wAr9Tg1NS/RKzH3EofJ5RuKSskg=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__.useApiArbitrationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__.useApiArbitrationPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_12__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1jbGllbnQpLy4vYXBwLyhtYWluKS9wbGFucy8oY29tcG9uZW50cykvR2VuZXJpY1RBYmxlQXJiaXRyYXRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3lFO0FBTzNDO0FBQ3dDO0FBQzNCO0FBQzBCO0FBQ3hCO0FBRVI7QUFDSTtBQUNTO0FBQ1o7QUFDdEMsbURBQW1EO0FBQ0o7QUFDQTtBQUNOO0FBQ21CO0FBQ0U7QUFDbkI7QUFDWDtBQUM2RDtBQUU5RSxTQUFTMEIsYUFBb0NDLEtBQThGO1FBQ2hJUixZQUtvQ1MsYUF5THpEQSxjQUEwQkMsYUFBMEJDOztJQTlMdkQsTUFBTUMsT0FBT0MsS0FBS2hCLEtBQUssQ0FBQ0csRUFBQUEsYUFBQUEsdURBQVNBLENBQUMscUJBQVZBLGlDQUFBQSxXQUFtQmMsUUFBUSxPQUFNO0lBRXpELE1BQU1DLFFBQVFwQiw2Q0FBTUEsQ0FBZTtJQUNuQyxNQUFNLEVBQUVlLEtBQUssRUFBRUQsS0FBSyxFQUFFRSxZQUFZLEVBQUUsR0FBR1YseUVBQVdBO0lBRWxELE1BQU1lLGFBQWF0Qiw4Q0FBT0EsQ0FBQztZQUFNZTtnQkFBQUEsY0FBQUEsTUFBTVEsSUFBSSxjQUFWUixrQ0FBQUEsWUFBWVEsSUFBSSxDQUFDQyxPQUFPO09BQUU7U0FBQ1QsY0FBQUEsTUFBTVEsSUFBSSxjQUFWUixrQ0FBQUEsWUFBWVEsSUFBSSxDQUFDQyxPQUFPO0tBQUM7SUFDckYsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3lCLGFBQWFDLGVBQWUsR0FBRzFCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzJCLGVBQWVDLGlCQUFpQixHQUFHNUIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDNkIseUJBQXlCQywyQkFBMkIsR0FBRzlCLCtDQUFRQSxDQUFDLEVBQUU7SUFDekUsTUFBTSxDQUFDK0IseUJBQXlCQywyQkFBMkIsR0FBR2hDLCtDQUFRQSxDQUFDb0I7SUFDdkUsTUFBTSxDQUFDYSxTQUFTQyxXQUFXLEdBQUdsQywrQ0FBUUEsQ0FBQyxDQUFDO0lBQ3hDLE1BQU0sRUFBRW1DLFNBQVNDLDBCQUEwQixFQUFFQyxZQUFZQyxnQkFBZ0IsRUFBRSxHQUFHN0IsMEVBQXVCQTtJQUNyRyxNQUFNLEVBQUUwQixTQUFTSSx5QkFBeUIsRUFBRUYsWUFBWUcsZUFBZSxFQUFFLEdBQUc5QixpRkFBOEJBO0lBQzFHLE1BQU0sQ0FBQytCLFVBQVVDLFlBQVksR0FBRzFDLCtDQUFRQSxDQUFnQjtJQUN4RCxNQUFNLENBQUMyQyxZQUFZQyxjQUFjLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM2QyxVQUFVQyxZQUFZLEdBQUc5QywrQ0FBUUEsQ0FBcUI7SUFDN0QsTUFBTSxDQUFDK0Msa0JBQWtCQyxvQkFBb0IsR0FBR2hELCtDQUFRQSxDQUFDO0lBQ3pELE1BQU1pRCxPQUFPQyxRQUFRTDtJQUVyQixTQUFTTSxtQkFBbUJDLEtBQVU7UUFDcENDLFFBQVFDLEdBQUcsQ0FBQzFDLE1BQU0yQyxVQUFVO1FBQzVCM0MsTUFBTTJDLFVBQVUsQ0FBQ0MsR0FBRyxDQUFDSjtJQUN2QjtJQUNBLFNBQVNLLHNCQUFzQixLQUFrQztZQUFsQyxFQUFFaEIsUUFBUSxFQUF3QixHQUFsQztRQUM3QkMsWUFBWUQ7UUFDWkcsY0FBYztJQUNoQjtJQUNBLFNBQVNjLFdBQVdDLE1BQWM7UUFDaENmLGNBQWNnQixDQUFBQSxpQkFBa0JBLGlCQUFpQkQ7SUFDbkQ7SUFDQSxTQUFTRTtRQUNQSCxXQUFXLENBQUM7SUFDZDtJQUNBLFNBQVNJO1FBQ1BKLFdBQVc7SUFDYjtJQUNBLE1BQU1LLHNCQUFzQjtZQUMxQjVDO1NBQUFBLGlCQUFBQSxNQUFNNkMsT0FBTyxjQUFiN0MscUNBQUFBLGVBQWU4QyxJQUFJLENBQUM7WUFBRUMsVUFBVTtZQUFRQyxTQUFTO1lBQWFDLFFBQVE7WUFBcUJDLE1BQU07UUFBSztJQUN4RztJQUNBLE1BQU1DLHNCQUFzQjtZQUMxQm5EO1NBQUFBLGlCQUFBQSxNQUFNNkMsT0FBTyxjQUFiN0MscUNBQUFBLGVBQWU4QyxJQUFJLENBQUM7WUFBRUMsVUFBVTtZQUFRQyxTQUFTO1lBQVlDLFFBQVE7WUFBcUJDLE1BQU07UUFBSztJQUN2RztJQUVBLE1BQU1FLGNBQWMsQ0FBQ0M7UUFDbkIxQixZQUFZMEIsTUFBTUMsYUFBYTtJQUNqQztJQUVBLE1BQU1DLFVBQVU1RSw4Q0FBT0EsQ0FDckIsSUFDRTZFLE9BQU9DLE9BQU8sQ0FBQ2hFLE1BQU1pRSxTQUFTLENBQUNDLFVBQVUsRUFBRUMsTUFBTSxDQUFDLFFBQWVDO2dCQUFkLENBQUNDLEtBQUtDLE1BQU07bUJBQVksQ0FBQztnQkFBQztnQkFBZTtnQkFBYztnQkFBVzthQUFZLENBQUNDLFFBQVEsQ0FBQ0Y7UUFBRyxHQUFHRyxHQUFHLENBQUMsUUFBZUo7Z0JBQWQsQ0FBQ0MsS0FBS0MsTUFBTTtZQUNoSyxJQUFJO2dCQUFDO2dCQUFVO2dCQUFXO2dCQUFRO2dCQUFTO2dCQUFXO2FBQWMsQ0FBQ0MsUUFBUSxDQUFDRixNQUFNO29CQUV4RXJFO2dCQURWLE9BQU87b0JBQ0x5RSxRQUFRekUsQ0FBQUEsd0NBQUFBLE1BQU1pRSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDMUUsbURBQUFBLHdDQUF5Q3FFO29CQUNqRE0sYUFBYU47b0JBQ2JPLElBQUlQO29CQUNKLG1FQUFtRTtvQkFDbkUsdUpBQXVKO29CQUN2SlEsTUFBTTs0QkFBQyxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsR0FBRyxFQUFFQyxLQUFLLEVBQUU7d0JBQ2pDLHFCQUNFOzs4Q0FDRSw4REFBQ0M7b0NBQU1DLFdBQVU7OENBQVk7Ozs7Ozs4Q0FDN0IsOERBQUN4RixxREFBTUE7b0NBQ0wsbUNBQW1DO29DQUNuQyxrRUFBa0U7b0NBQ2xFLGdCQUFnQjtvQ0FDaEIyRSxPQUFPVSxJQUFJSSxRQUFRLENBQUNDLE1BQU07b0NBQzFCLHdFQUF3RTtvQ0FDeEVDLGNBQWMsQ0FBQ0M7d0NBQVFQLElBQUlRLFlBQVksQ0FBQ0gsTUFBTSxHQUFHRSxFQUFFRSxTQUFTO29DQUFDO29DQUM3REMsT0FBTzt3Q0FBRUMsUUFBUTtvQ0FBUTs7Ozs7Ozs7b0JBa0JqQztnQkFDRjtZQUNGO1lBQ0EsSUFBSXRCLFFBQVEsUUFBUTtvQkFFUnJFO2dCQURWLE9BQU87b0JBQ0x5RSxRQUFRekUsQ0FBQUEseUNBQUFBLE1BQU1pRSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDMUUsb0RBQUFBLHlDQUF5Q3FFO29CQUNqRE0sYUFBYTtvQkFFYmlCLHVCQUF1Qjt3QkFDckJDLE9BQU87b0JBQ1Q7b0JBQ0FDLHVCQUF1Qjt3QkFDckJELE9BQU87b0JBQ1Q7b0JBRUFFLHlCQUF5Qjt3QkFDdkJGLE9BQU87b0JBQ1Q7b0JBQ0FHLE1BQU07NEJBQUMsRUFBRWxCLElBQUksRUFBRUUsR0FBRyxFQUFFOytCQUFLLDhEQUFDaEcsK0NBQUdBOzRCQUFDbUcsV0FBVTtzQ0FBbUJMLEtBQUttQixRQUFRLEdBQVNDLElBQUk7Ozs7OztvQkFBTTtvQkFDM0ZyQixNQUFNOzRCQUFDLEVBQUVHLEdBQUcsRUFBRTs0QkFXT0Esd0JBQXlDQSx5QkFDL0M5RSxhQUFBQTs2Q0FYYjs7OENBQ0UsOERBQUNnRjtvQ0FBTUMsV0FBVTs4Q0FBWTs7Ozs7OzhDQUM3Qiw4REFBQzVGLDBEQUFRQTtvQ0FDUDRHLGFBQVk7b0NBQ1pDLGFBQVk7b0NBQ1pDLFVBQVUsQ0FBQ2Q7NENBRXNCckYsYUFBQUEsUUFDaUJBLGNBQUFBO3dDQUZoRHVDLFFBQVFDLEdBQUcsQ0FBQzZDO3dDQUNaakUsV0FBVzs0Q0FBRSxHQUFHRCxPQUFPOzRDQUFFaUYsSUFBSSxHQUFFcEcsU0FBQUEsbUJBQUFBLDhCQUFBQSxjQUFBQSxPQUFPTyxJQUFJLGNBQVhQLGtDQUFBQSxZQUFhTyxJQUFJLENBQUNDLE9BQU8sQ0FBQzZGLElBQUksQ0FBQyxDQUFDRCxPQUFlQSxLQUFLMUIsRUFBRSxLQUFLVyxFQUFFakIsS0FBSyxDQUFDNEIsSUFBSTt3Q0FBRTt3Q0FDeEdsQixJQUFJUSxZQUFZLEdBQUc7NENBQUUsR0FBR1IsSUFBSVEsWUFBWTs0Q0FBRWMsSUFBSSxHQUFFcEcsVUFBQUEsbUJBQUFBLCtCQUFBQSxlQUFBQSxRQUFPTyxJQUFJLGNBQVhQLG1DQUFBQSxhQUFhTyxJQUFJLENBQUNDLE9BQU8sQ0FBQzZGLElBQUksQ0FBQyxDQUFDRCxPQUFlQSxLQUFLMUIsRUFBRSxLQUFLVyxFQUFFakIsS0FBSyxDQUFDNEIsSUFBSTt3Q0FBRTtvQ0FDM0g7b0NBQ0E1QixPQUFPO3dDQUFFNEIsTUFBTWxCLEVBQUFBLHlCQUFBQSxJQUFJUSxZQUFZLENBQUNjLElBQUksY0FBckJ0Qiw2Q0FBQUEsdUJBQXVCSixFQUFFLEtBQUk7d0NBQU00QixNQUFNeEIsRUFBQUEsMEJBQUFBLElBQUlRLFlBQVksQ0FBQ2MsSUFBSSxjQUFyQnRCLDhDQUFBQSx3QkFBdUJrQixJQUFJLEtBQUk7b0NBQUs7b0NBQzVGTyxPQUFPLEdBQUV2RyxTQUFBQSxtQkFBQUEsOEJBQUFBLGNBQUFBLE9BQU9PLElBQUksY0FBWFAsa0NBQUFBLFlBQWFPLElBQUksQ0FBQ0MsT0FBTyxDQUFDOEQsR0FBRyxDQUFDLENBQUM4Qjt3Q0FBaUIsT0FBTzs0Q0FBRUosTUFBTUksS0FBSzFCLEVBQUU7NENBQUU0QixNQUFNRixLQUFLSixJQUFJO3dDQUFDO29DQUFFOzs7Ozs7Ozs7Z0JBSzNHO1lBQ0Y7WUFDQSxJQUFJbEcsTUFBTWlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNxQyxNQUFNLEtBQUssZUFBZTFHLE1BQU1pRSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDcUMsTUFBTSxLQUFLLFFBQVE7b0JBSXJHMUc7Z0JBRlYsT0FBTztvQkFDTDJHLFlBQVksQ0FBQzNCLE1BQVEsSUFBSTRCLEtBQUt2QyxPQUFPLFlBQVlXLElBQUk2QixPQUFPLEdBQUc3QixJQUFJOEIsUUFBUTtvQkFDM0VyQyxRQUFRekUsQ0FBQUEseUNBQUFBLE1BQU1pRSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDMUUsb0RBQUFBLHlDQUF5Q3FFO29CQUNqRDBDLGVBQWU7b0JBQ2ZDLFVBQVU7b0JBQ1ZDLFdBQVc7b0JBQ1h0QyxhQUFhTjtvQkFDYjJCLE1BQU07NEJBQUMsRUFBRWxCLElBQUksRUFBRTs0QkFBS0E7Z0NBQUFBLGlCQUFBQSxLQUFLbUIsUUFBUSxnQkFBYm5CLHFDQUFBQSxlQUF1Qm9DLGtCQUFrQixDQUFDOztvQkFDOUR0QyxJQUFJUDtvQkFDSlEsTUFBTSxJQUFNO2dCQUNkO1lBQ0Y7Z0JBR1U3RTtZQURWLElBQUlxRSxRQUFRLE1BQU0sT0FBTztnQkFDdkJJLFFBQVF6RSxDQUFBQSx5Q0FBQUEsTUFBTWlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckMxRSxvREFBQUEseUNBQXlDcUU7Z0JBQ2pETSxhQUFhTjtnQkFDYk8sSUFBSVA7Z0JBQ0pRLE1BQU0sSUFBTTtZQUNkO2dCQUVVN0U7WUFEVixJQUFJcUUsUUFBUSxRQUFRLE9BQU87Z0JBQ3pCSSxRQUFRekUsQ0FBQUEseUNBQUFBLE1BQU1pRSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDMUUsb0RBQUFBLHlDQUF5Q3FFO2dCQUNqRE0sYUFBYU47Z0JBQ2JPLElBQUlQO2dCQUNKMkIsTUFBTTt3QkFBQyxFQUFFbEIsSUFBSSxFQUFFRSxHQUFHLEVBQUU7MkJBQUssOERBQUNtQztrQ0FBSXJDLEtBQUttQixRQUFRLEdBQVd6QixHQUFHLENBQUMsQ0FBQzRDLG9CQUFRLDhEQUFDQzswQ0FBSTNILDJFQUFlQSxDQUFDMEg7Ozs7Ozs7Ozs7O2dCQUFnQjtnQkFDeEd2QyxNQUFNO3dCQUFDLEVBQUVHLEdBQUcsRUFBRTtvQkFDWnZDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJzQyxJQUFJUSxZQUFZLENBQUM4QixJQUFJO29CQUNsRCxxQkFBUTs7MENBQ04sOERBQUNwQztnQ0FBTUMsV0FBVTswQ0FBWTs7Ozs7OzBDQUM3Qiw4REFBQzdGLDBEQUFRQTtnQ0FDUGlJLFFBQVF0Ryx3QkFBd0J1RyxNQUFNLEtBQUssSUFBSXJHLDBCQUEwQkEsd0JBQXdCZ0QsTUFBTSxDQUFDL0QsQ0FBQUEsT0FBUWEsd0JBQXdCdUQsR0FBRyxDQUFDcEUsQ0FBQUEsT0FBUUEsS0FBS3FILFFBQVEsRUFBRWxELFFBQVEsQ0FBQ25FLEtBQUtxSCxRQUFRO2dDQUN6TDdDLElBQUc7Z0NBQ0g4QyxRQUFRekcsd0JBQXdCdUcsTUFBTSxHQUFHLElBQUl2RywwQkFBMEIrRCxJQUFJUSxZQUFZLENBQUM4QixJQUFJO2dDQUM1RkssY0FBYTtnQ0FDYkMsY0FBYTtnQ0FDYkMsY0FBYyxDQUFDQyxxQkFBUyw4REFBQ0M7OzRDQUF5QkQsS0FBS0UsVUFBVTs0Q0FBQzs0Q0FBRUYsS0FBS0csU0FBUzs7dUNBQWhESCxLQUFLTCxRQUFRO2dDQUMvQ3BCLFVBQVUsQ0FBQ2Q7b0NBQ1Q5QyxRQUFRQyxHQUFHLENBQUMsZUFBZTZDLEVBQUVnQyxNQUFNO29DQUNuQ25HLDJCQUEyQjsyQ0FBSW1FLEVBQUVnQyxNQUFNO3FDQUFDO29DQUN4Q3JHLDJCQUEyQjsyQ0FBSXFFLEVBQUVtQyxNQUFNO3FDQUFDO29DQUN4QzFDLElBQUlRLFlBQVksQ0FBQzhCLElBQUksR0FBRy9CLEVBQUVtQyxNQUFNO2dDQUNsQztnQ0FDQVEsYUFBYTtvQ0FBRXZDLFFBQVE7Z0NBQVE7Z0NBQy9Cd0MsYUFBYTtvQ0FBRXhDLFFBQVE7Z0NBQVE7Z0NBQy9CeEIsTUFBTTtnQ0FBQ2lFLFVBQVM7Z0NBQ2hCQyxpQkFBZ0I7Z0NBQ2hCQyx5QkFBd0I7Z0NBQThCQyx5QkFBd0I7Ozs7Ozs7O2dCQUtwRjtZQUNGO2lCQUNLO29CQUVPdkk7Z0JBRFYsT0FBTztvQkFDTHlFLFFBQVF6RSxDQUFBQSx5Q0FBQUEsTUFBTWlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckMxRSxvREFBQUEseUNBQXlDcUU7b0JBQ2pETSxhQUFhTjtvQkFDYk8sSUFBSVA7Z0JBRU47WUFFRjtRQUNGLElBRUY7U0FBQ3BFLGVBQUFBLE1BQU1RLElBQUksY0FBVlIsbUNBQUFBLGFBQVlRLElBQUksQ0FBQ0MsT0FBTztTQUFFUixjQUFBQSxNQUFNTyxJQUFJLGNBQVZQLGtDQUFBQSxZQUFZTyxJQUFJLENBQUNDLE9BQU87U0FBRVAscUJBQUFBLGFBQWFNLElBQUksY0FBakJOLHlDQUFBQSxtQkFBbUJNLElBQUksQ0FBQ0MsT0FBTztLQUFDO0lBR3ZGLE1BQU11RSxRQUFRdkcsNEVBQXFCQSxDQUFDO1FBQ2xDb0Y7UUFDQXJELE1BQU1ULE1BQU13SSxLQUFLLEdBQUcsRUFBRSxHQUFHeEksTUFBTUEsS0FBSyxDQUFDUyxJQUFJLENBQUNDLE9BQU8sR0FBR1YsTUFBTUEsS0FBSyxDQUFDUyxJQUFJLENBQUNDLE9BQU8sR0FBRztZQUFDVixNQUFNQSxLQUFLLENBQUNTLElBQUk7U0FBQztRQUNqR2dJLFVBQVV6SSxNQUFNd0ksS0FBSyxHQUFHLElBQUl4SSxNQUFNQSxLQUFLLENBQUNTLElBQUksQ0FBQ0MsT0FBTyxHQUFHVixNQUFNQSxLQUFLLENBQUNTLElBQUksQ0FBQ2lJLEtBQUssR0FBRztRQUNoRkMsb0JBQW9CO1FBQ3BCQyxzQkFBc0I7UUFDdEJDLG9CQUFvQjtRQUNwQkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLGtCQUFrQjtRQUNsQkMsb0JBQW9CO1FBQ3BCQyxvQkFBb0I7UUFDcEJDLHFCQUFxQjtRQUNyQkMsc0JBQXNCO1FBQ3RCQyxrQkFBa0I7UUFDbEJDLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmQyxpQkFBaUI7UUFDakJDLGtCQUFrQjtRQUNsQkMsY0FBYztZQUNaL0csWUFBWTtnQkFBRWdILFVBQVU7Z0JBQUdDLFdBQVc7WUFBRTtZQUN4Q0Msa0JBQWtCO2dCQUFFeEUsUUFBUTtnQkFBT3lFLFlBQVk7Z0JBQU9qRCxTQUFTO2dCQUFPa0QsWUFBWTtnQkFBT2pELFVBQVU7Z0JBQU9rRCxhQUFhO2dCQUFPQyxPQUFPO2dCQUFPQyxZQUFZO2dCQUFPdEYsSUFBSTtnQkFBT3VGLFVBQVU7WUFBTTtZQUMxTEMsU0FBUztZQUNUQyxrQkFBa0I7WUFDbEJDLFNBQVM7Z0JBQUM7b0JBQUUxRixJQUFJO29CQUFNMkYsTUFBTTtnQkFBTTthQUFFO1FBQ3RDO1FBQ0EvSCxPQUFPO1lBQ0xHLFlBQVkzQyxNQUFNMkMsVUFBVSxDQUFDNkgsSUFBSTtZQUNqQ0MsV0FBV3pLLE1BQU15SyxTQUFTO1FBRzVCO1FBQ0FDLGNBQWMvTCxpRkFBbUJBO1FBQ2pDNEQsb0JBQW9CQTtRQUNwQm9JLHlCQUF5QjtZQUN2QixlQUFlO2dCQUNiQyxjQUFjO1lBQ2hCO1lBQ0Esa0JBQWtCO2dCQUNoQkEsY0FBYztZQUNoQjtZQUNBLG1CQUFtQjtnQkFDakIsK0NBQStDO2dCQUMvQ0MsTUFBTTtnQkFDTkQsY0FBYztZQU1oQjtZQUNBLG1CQUFtQjtnQkFDakJBLGNBQWM7WUFDaEI7UUFDRjtRQUNBRSxlQUFlO1lBQ2JDLE1BQU07WUFDTkMsaUJBQWlCO1FBQ25CO1FBQ0FDLG9CQUFvQjtnQkFBQyxFQUFFaEcsS0FBSyxFQUFFO21CQUFNO2dCQUNsQywyQ0FBMkM7Z0JBQzNDLHdCQUF3QjtnQkFDeEJFLFdBQVc7Z0JBQ1grRixTQUFTO29CQUFFQyxNQUFNO2dCQUFxRDtnQkFFdEVDLElBQUk7b0JBQ0Z6RixRQUFTO29CQUNULGlDQUFpQztvQkFDakMscUJBQXFCO29CQUNyQixnQ0FBZ0M7b0JBQ2hDMEYsaUJBQWlCO29CQUNqQkMsWUFBWTtvQkFDWkMscUJBQXFCO29CQUNyQiwrQkFBK0I7d0JBQzdCRixpQkFBaUI7d0JBQ2pCQyxZQUFZO3dCQUNaQyxxQkFBcUI7d0JBQ3JCQyxPQUFPO29CQUNUO29CQUNBLG1CQUFtQjt3QkFDakJILGlCQUFpQjt3QkFDakJDLFlBQVk7d0JBQ1pDLHFCQUFxQjt3QkFDckJDLE9BQU87b0JBQ1Q7Z0JBQ0Y7WUFDRjtRQUFBO1FBQ0FDLHVCQUF1QjtnQkFBQyxFQUFFekcsR0FBRyxFQUFFQyxLQUFLLEVBQUU7bUJBQU07Z0JBQzFDNUMsTUFBTXhCLGVBQWVFO2dCQUNyQnFLLElBQUk7b0JBQ0YscUJBQXFCO3dCQUNuQk0sU0FBUztvQkFDWDtvQkFDQSwwQkFBMEI7d0JBQ3hCQSxTQUFTO29CQUNYO29CQUNBQyxRQUFRO2dCQUVWO1lBQ0Y7UUFBQTtRQUNBQyxpQkFBaUI7UUFDakJDLG1CQUFtQjtRQUNuQkMsa0JBQWtCO2dCQUFDLEVBQUU3RyxLQUFLLEVBQUU4RyxNQUFNLEVBQUUvRyxHQUFHLEVBQUU7Z0JBSTNCK0c7WUFIWnRKLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JxSjtZQUNoQyxNQUFNLEVBQUVuSCxFQUFFLEVBQUUsR0FBR29ILE1BQU0sR0FBR0Q7WUFDeEJDLEtBQUsxRixJQUFJLEdBQUd5RixPQUFPekYsSUFBSSxDQUFDMUIsRUFBRTtZQUMxQm9ILEtBQUsxRSxJQUFJLEdBQUd5RSxFQUFBQSxlQUFBQSxPQUFPekUsSUFBSSxjQUFYeUUsbUNBQUFBLGFBQWF2SCxHQUFHLENBQUNwRSxDQUFBQSxPQUFRQSxLQUFLd0UsRUFBRSxNQUFLLEVBQUU7WUFDbkRqRCwwQkFBMEJxSyxNQUFNO2dCQUM5QkMsWUFBWTtnQkFDWkMsV0FBVztvQkFDVGpILE1BQU1rSCxhQUFhLENBQUMsT0FBTyxvQkFBb0I7b0JBQy9DNUwsTUFBTTZDLE9BQU8sQ0FBRUMsSUFBSSxDQUFDO3dCQUFFQyxVQUFVO3dCQUFXQyxTQUFTO3dCQUFRQyxRQUFRLGFBQThCLE9BQWpCdUksT0FBT3pGLElBQUksQ0FBQ0osSUFBSSxFQUFDO29CQUFjO2dCQUNsSDtnQkFDQWtHLFNBQVMsQ0FBQzVEO3dCQUM2REE7b0JBQXJFakksTUFBTTZDLE9BQU8sQ0FBRUMsSUFBSSxDQUFDO3dCQUFFQyxVQUFVO3dCQUFTQyxTQUFTO3dCQUFRQyxRQUFRLEdBQThCLFFBQTNCZ0Ysa0JBQUFBLE1BQU02RCxRQUFRLGNBQWQ3RCxzQ0FBQUEsZ0JBQWdCOEQsVUFBVTtvQkFBRztvQkFDbEcsa0RBQWtEO29CQUNsRHRILElBQUlRLFlBQVksR0FBRzt3QkFBRWdELE9BQU9BLE1BQU02RCxRQUFRO3dCQUFFLEdBQUdySCxJQUFJUSxZQUFZO29CQUFDO29CQUNoRTtnQkFDRjtZQUNGO1FBQ0Y7UUFDQStHLG9CQUFvQjtnQkFDbEIsNkJBQTZCO1lBQzdCaE07YUFBQUEsaUJBQUFBLE1BQU02QyxPQUFPLGNBQWI3QyxxQ0FBQUEsZUFBZThDLElBQUksQ0FBQztnQkFBRUMsVUFBVTtnQkFBUUMsU0FBUztnQkFBUUMsUUFBUTtZQUFhO1FBQ2hGO1FBQ0FnSixtQkFBbUI7Z0JBQUMsRUFBRXZILEtBQUssRUFBRThHLE1BQU0sRUFBRS9HLEdBQUcsRUFBRTtnQkFJNUIrRztZQUhadEosUUFBUUMsR0FBRyxDQUFDLHFCQUFxQnFKO1lBQ2pDLE1BQU0sRUFBRW5ILEVBQUUsRUFBRSxHQUFHb0gsTUFBTSxHQUFHRDtZQUN4QkMsS0FBSzFGLElBQUksR0FBR3lGLE9BQU96RixJQUFJLENBQUMxQixFQUFFO1lBQzFCb0gsS0FBSzFFLElBQUksR0FBR3lFLEVBQUFBLGVBQUFBLE9BQU96RSxJQUFJLGNBQVh5RSxtQ0FBQUEsYUFBYXZILEdBQUcsQ0FBQ3BFLENBQUFBLE9BQVFBLEtBQUt3RSxFQUFFLE1BQUssRUFBRTtZQUNuRHBELDJCQUEyQndLLE1BQU07Z0JBQy9CQyxZQUFZO2dCQUNaQyxXQUFXO29CQUNUakgsTUFBTXdILGNBQWMsQ0FBQyxPQUFPLG9CQUFvQjtvQkFDaERsTSxNQUFNNkMsT0FBTyxDQUFFQyxJQUFJLENBQUM7d0JBQUVDLFVBQVU7d0JBQVdDLFNBQVM7d0JBQVFDLFFBQVEsYUFBOEIsT0FBakJ1SSxPQUFPekYsSUFBSSxDQUFDSixJQUFJLEVBQUM7b0JBQU87Z0JBQzNHO2dCQUNBa0csU0FBUyxDQUFDNUQ7d0JBQzZEQTtvQkFBckVqSSxNQUFNNkMsT0FBTyxDQUFFQyxJQUFJLENBQUM7d0JBQUVDLFVBQVU7d0JBQVNDLFNBQVM7d0JBQVFDLFFBQVEsR0FBOEIsUUFBM0JnRixrQkFBQUEsTUFBTTZELFFBQVEsY0FBZDdELHNDQUFBQSxnQkFBZ0I4RCxVQUFVO29CQUFHO29CQUNsRyxrREFBa0Q7b0JBQ2xEdEgsSUFBSVEsWUFBWSxHQUFHO3dCQUFFZ0QsT0FBT0EsTUFBTTZELFFBQVE7d0JBQUUsR0FBR3JILElBQUlRLFlBQVk7b0JBQUM7b0JBQ2hFO2dCQUNGO1lBQ0Y7UUFDRjtRQUNBa0gscUJBQXFCO2dCQUNuQm5NO2FBQUFBLGlCQUFBQSxNQUFNNkMsT0FBTyxjQUFiN0MscUNBQUFBLGVBQWU4QyxJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQVFDLFNBQVM7Z0JBQVFDLFFBQVE7WUFBYTtRQUNoRjtRQUNBbUoscUJBQXFCO1lBQ25CeEgsV0FBVztZQUNYaUcsSUFBSTtnQkFDRiwwQkFBMEI7b0JBQ3hCQyxpQkFBaUI7Z0JBQ25CO2dCQUNBQSxpQkFBaUI7Z0JBQ2pCRyxPQUFPO2dCQUNQRixZQUFZO2dCQUNaQyxxQkFBcUI7WUFDdkI7UUFDRjtRQUNBcUIsd0JBQXdCO2dCQUFDLEVBQUUzSCxLQUFLLEVBQUU7Z0JBUWlEQSxtQ0FBc0RBO21CQVJqRztnQkFDdENFLFdBQVc7Z0JBQ1hpRyxJQUFJO29CQUNGRSxZQUFZO29CQUNaQyxxQkFBcUI7b0JBQ3JCLHFCQUFxQjtvQkFDckIsZ0NBQWdDO29CQUNoQ0YsaUJBQWlCO29CQUNqQjFGLFFBQVFWLE1BQU00SCxRQUFRLEdBQUdDLFlBQVksR0FBSSxnQkFBZSwrQkFBdUI3SCxvQ0FBQUEsTUFBTThILElBQUksQ0FBQ0MsYUFBYSxDQUFDNUosT0FBTyxjQUFoQzZCLHdEQUFBQSxrQ0FBa0NnSSxZQUFZLEVBQUMsU0FBeUQsUUFBbERoSSx1Q0FBQUEsTUFBTThILElBQUksQ0FBQ0csZ0JBQWdCLENBQUM5SixPQUFPLGNBQW5DNkIsMkRBQUFBLHFDQUFxQ2dJLFlBQVksRUFBQztnQkFFekw7WUFDRjs7UUFDQUUsb0JBQW9CO1lBQ2xCaEksV0FBVztZQUNYaUcsSUFBSTtnQkFFRixxQkFBcUI7Z0JBQ3JCLGdDQUFnQztnQkFDaENDLGlCQUFpQjtnQkFDakJHLE9BQU87Z0JBQ1BGLFlBQVk7Z0JBQ1pDLHFCQUFxQjtZQUN2QjtRQUNGO1FBQ0EzRix1QkFBdUI7WUFDckJ3RixJQUFJO2dCQUNGLHFCQUFxQjtnQkFDckIsZ0NBQWdDO2dCQUNoQ0MsaUJBQWlCO2dCQUNqQkcsT0FBTztnQkFDUEYsWUFBWTtnQkFDWkMscUJBQXFCO1lBQ3ZCO1FBQ0Y7UUFDQTZCLG9CQUFvQjtZQUNsQmpJLFdBQVc7WUFDWGlHLElBQUk7Z0JBQ0ZFLFlBQVk7Z0JBQ1pDLHFCQUFxQjtnQkFDckIscUJBQXFCO2dCQUNyQixnQ0FBZ0M7Z0JBQ2hDRixpQkFBaUI7Z0JBQ2pCRyxPQUFPO1lBQ1Q7UUFFRjtRQUNBNkIsbUJBQW1CO1lBQ2pCbEksV0FBVztZQUNYaUcsSUFBSTtnQkFDRkUsWUFBWTtnQkFDWkMscUJBQXFCO2dCQUNyQiwrQ0FBK0M7Z0JBQy9DLDhCQUE4QjtvQkFDNUJGLGlCQUFpQjtvQkFDakJHLE9BQU87b0JBQ1BGLFlBQVk7b0JBQ1pDLHFCQUFxQjtnQkFDdkI7Z0JBQ0EsK0JBQStCO29CQUM3QkYsaUJBQWlCO29CQUNqQkcsT0FBTztvQkFDUEYsWUFBWTtvQkFDWkMscUJBQXFCO2dCQUN2QjtZQUNGO1FBQ0Y7UUFDQStCLCtCQUErQjtnQkFBQyxFQUFFckksS0FBSyxFQUFFO21CQUN2Qyw4REFBQzFHLHNEQUFLQTtnQkFBQ2dQLFdBQVc7Z0JBQU9DLFNBQVM7O2tDQUNoQyw4REFBQzVOLHlDQUFHQTt3QkFBQzZOLEdBQUU7d0JBQU1DLEdBQUU7a0NBQ2IsNEVBQUM5TyxzREFBTUE7NEJBQ0wrTyxNQUFLOzRCQUNMQyxPQUFPOzRCQUNQLG9CQUFvQjs0QkFDcEJDLGlCQUFleEwsT0FBTyxlQUFleUw7NEJBQ3JDQyxpQkFBYzs0QkFDZEMsaUJBQWUzTCxPQUFPLFNBQVN5TDs0QkFDL0JHLFNBQVMsQ0FBQ3JLO2dDQUNScUIsTUFBTXdILGNBQWMsQ0FBQztnQ0FBT3pMLGlCQUFpQixPQUFPeUIsUUFBUUMsR0FBRyxDQUFDOzRCQUNsRTs0QkFDQW1JLE1BQUs7Ozs7Ozs7Ozs7O2tDQUlULDhEQUFDakwseUNBQUdBO3dCQUFDNk4sR0FBRTt3QkFBU0MsR0FBRTtrQ0FDaEIsNEVBQUM5TyxzREFBTUE7NEJBQ0xnUCxPQUFPOzRCQUNQTSxVQUFVakosTUFBTWtKLHFCQUFxQjs0QkFDckMsb0JBQW9COzRCQUNwQk4saUJBQWV4TCxPQUFPLGVBQWV5TDs0QkFDckNDLGlCQUFjOzRCQUNkQyxpQkFBZTNMLE9BQU8sU0FBU3lMOzRCQUMvQkcsU0FBU3RLOzRCQUNUZ0ssTUFBSzs0QkFDTCxvRkFBb0Y7NEJBQ3BGOUMsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFHSjtRQUVUdUQscUJBQXFCLElBQU87Z0JBQzFCaEQsSUFBSSxDQUFDaUQsUUFBVzt3QkFDZGhELGlCQUNFZ0QsTUFBTUMsT0FBTyxDQUFDQyxJQUFJLEtBQUssU0FDbkIsMEJBQ0E7b0JBQ1I7WUFDRjtRQUNBQyw4QkFBOEI7Z0JBQUMsRUFBRUMsc0JBQXNCLEVBQUV6SixHQUFHLEVBQUVDLEtBQUssRUFBRTttQkFDbkUsOERBQUM4QztnQkFBSXJDLE9BQU87b0JBQUVpRyxRQUFRO2dCQUFrQjswQkFDdEMsNEVBQUM1TSx3REFBT0E7b0JBQUMyUCxVQUFTO29CQUNoQmpLLHNCQUNFLDhEQUFDc0Q7d0JBQUk1QyxXQUFVOzswQ0FDYiw4REFBQ3dKO2dDQUFLeEosV0FBVTswQ0FBd0I7OzBDQUN4Qyw4REFBQzlHLHNEQUFhQTswQ0FDWiw0RUFBQ0csd0VBQXFCQTtvQ0FBQ29RLFNBQVE7b0NBQU8zSixPQUFPQTtvQ0FBT0QsS0FBS0E7Ozs7O29CQUkvRDZKLFNBQVM5TjtvQkFDVCtOLFFBQVE7d0JBQVE3SixNQUFNd0gsY0FBYyxDQUFDO3dCQUFPekwsaUJBQWlCO29CQUFPO29CQUNwRW1FLFdBQVU7OEJBQ1YsNEVBQUM3RyxzREFBYUE7d0JBQ1o4TSxJQUFJOzRCQUNGTSxTQUFTOzRCQUNUcUQsZUFBZTs0QkFDZkMsS0FBSzt3QkFDUDtrQ0FFQ1A7Ozs7Ozs7Ozs7Ozs7Ozs7UUFHRjtRQUNQUSw0QkFBNEI7Z0JBQUMsRUFBRVIsc0JBQXNCLEVBQUV6SixHQUFHLEVBQUVDLEtBQUssRUFBRTttQkFDakUsOERBQUM4QztnQkFBSXJDLE9BQU87b0JBQUVpRyxRQUFRO2dCQUFrQjswQkFDdEMsNEVBQUM1TSx3REFBT0E7b0JBQUMyUCxVQUFTO29CQUNoQmpLLHNCQUNFLDhEQUFDc0Q7d0JBQUk1QyxXQUFVOzswQ0FDYiw4REFBQytKO2dDQUFHL0osV0FBVTs7b0NBQXdCO29DQUF1QkgsSUFBSUksUUFBUSxDQUFDUixFQUFFOzs7MENBQzVFLDhEQUFDdkcsc0RBQWFBOzBDQUNaLDRFQUFDRyx3RUFBcUJBO29DQUFDb1EsU0FBUTtvQ0FBTzNKLE9BQU9BO29DQUFPRCxLQUFLQTs7Ozs7b0JBSS9ENkosU0FBU2hPO29CQUNUaU8sUUFBUTt3QkFDTjdKLE1BQU1rSCxhQUFhLENBQUM7d0JBQ3BCckwsZUFBZTtvQkFDakI7b0JBQ0FxRSxXQUFVOzhCQUNWLDRFQUFDN0csc0RBQWFBO3dCQUNaOE0sSUFBSTs0QkFBRU0sU0FBUzs0QkFBUXFELGVBQWU7NEJBQVVDLEtBQUs7d0JBQVM7OzRCQUU3RFA7NEJBQXVCOzs7Ozs7Ozs7Ozs7Ozs7OztRQUd6QjtRQUNQVSxtQkFBbUI7Z0JBQUMsRUFBRW5LLEdBQUcsRUFBRTttQkFDekIzRiw2REFBS0EsQ0FBQzJGLElBQUlJLFFBQVEsQ0FBQ0MsTUFBTTtRQUFBO1FBQzNCK0osa0JBQWtCO2dCQUFDLEVBQUV0SyxJQUFJLEVBQUVFLEdBQUcsRUFBRUMsS0FBSyxFQUFFO21CQUNyQyw4REFBQzBKO2dCQUFLeEosV0FBVTs7a0NBQ2QsOERBQUN2Rix5Q0FBR0E7d0JBQUM2TixHQUFFO3dCQUFTQyxHQUFFO2tDQUNoQiw0RUFBQzlPLHNEQUFNQTs0QkFBQ2lNLE1BQUs7NEJBQVE4QyxNQUFLOzRCQUFlTSxTQUFTO2dDQUNoRHJOLGlCQUFpQm9FLElBQUlJLFFBQVEsQ0FBQ1IsRUFBRTtnQ0FDaENLLE1BQU1rSCxhQUFhLENBQUNuSDtnQ0FDcEJsRSxlQUFlO2dDQUNmMkIsUUFBUUMsR0FBRyxDQUFDOzRCQUNkOzRCQUFHa0wsT0FBTzs0QkFBQ3lCLFFBQVE7Ozs7Ozs7Ozs7O2tDQUVyQiw4REFBQ3pQLHlDQUFHQTt3QkFBQzZOLEdBQUU7d0JBQVNDLEdBQUU7a0NBQ2hCLDRFQUFDOU8sc0RBQU1BOzRCQUFDaU0sTUFBSzs0QkFBUThDLE1BQUs7NEJBQWNDLE9BQU87NEJBQUN5QixRQUFROzRCQUN0RHBCLFNBQVMsQ0FBQ3JLLFFBQVU5RSxzRUFBWUEsQ0FBQztvQ0FDL0I0SSxRQUFROUQsTUFBTUMsYUFBYTtvQ0FDM0J5TCxTQUFTO29DQUNUM0IsTUFBTTtvQ0FDTiwwQkFBMEI7b0NBQzFCNEIsaUJBQWlCO29DQUNqQkMsYUFBYTtvQ0FDYkMsYUFBYTtvQ0FDYkMsUUFBUXZNO29DQUNSd00sUUFBUWpNO2dDQUNWOzs7Ozs7Ozs7OztrQ0FHSiw4REFBQzdFLGtFQUFZQTs7Ozs7Ozs7Ozs7UUFDVDtJQUVWO0lBQ0EscUJBQU87OzBCQUFFLDhEQUFDSixxRUFBa0JBO2dCQUFDd0csT0FBT0E7Ozs7OzswQkFBUyw4REFBQ2hHLG9EQUFLQTtnQkFBQzJRLEtBQUtyUDs7Ozs7Ozs7QUFDM0Q7R0E3aEJ3QlI7O1FBYXdERixzRUFBdUJBO1FBQ3pCQyw2RUFBOEJBO1FBb0w1RnBCLHdFQUFxQkE7OztLQWxNYnFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC8obWFpbikvcGxhbnMvKGNvbXBvbmVudHMpL0dlbmVyaWNUQWJsZUFyYml0cmF0aW9uLnRzeD9jZDllIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5pbXBvcnQgeyBCb3gsIERpYWxvZ0FjdGlvbnMsIERpYWxvZ0NvbnRlbnQsIFN0YWNrIH0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XHJcbmltcG9ydCB7XHJcbiAgTVJUX0VkaXRBY3Rpb25CdXR0b25zLFxyXG4gIE1SVF9Sb3dEYXRhLFxyXG4gIE1hdGVyaWFsUmVhY3RUYWJsZSxcclxuICB1c2VNYXRlcmlhbFJlYWN0VGFibGUsXHJcbiAgdHlwZSBNUlRfQ29sdW1uRGVmXHJcbn0gZnJvbSAnbWF0ZXJpYWwtcmVhY3QtdGFibGUnO1xyXG5pbXBvcnQgeyBNUlRfTG9jYWxpemF0aW9uX0ZSIH0gZnJvbSAnbWF0ZXJpYWwtcmVhY3QtdGFibGUvbG9jYWxlcy9mcic7XHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ3ByaW1lcmVhY3QvYnV0dG9uJztcclxuaW1wb3J0IHsgQ29uZmlybVBvcHVwLCBjb25maXJtUG9wdXAgfSBmcm9tICdwcmltZXJlYWN0L2NvbmZpcm1wb3B1cCc7XHJcbmltcG9ydCB7IFNpZGViYXIgfSBmcm9tICdwcmltZXJlYWN0L3NpZGViYXInO1xyXG5pbXBvcnQgeyBUYWJQYW5lbCwgVGFiVmlldyB9IGZyb20gJ3ByaW1lcmVhY3QvdGFidmlldyc7XHJcbmltcG9ydCB7IFRhZyB9IGZyb20gJ3ByaW1lcmVhY3QvdGFnJztcclxuaW1wb3J0IHsgVG9hc3QgfSBmcm9tICdwcmltZXJlYWN0L3RvYXN0JztcclxuaW1wb3J0IHsgdXNlTWVtbywgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHBhcnNlIGZyb20gJ2h0bWwtcmVhY3QtcGFyc2VyJztcclxuLy8gaW1wb3J0IHsgRWRpdG9yIH0gZnJvbSAnQHRpbnltY2UvdGlueW1jZS1yZWFjdCc7XHJcbmltcG9ydCB7IFBpY2tMaXN0IH0gZnJvbSAncHJpbWVyZWFjdC9waWNrbGlzdCc7XHJcbmltcG9ydCB7IERyb3Bkb3duIH0gZnJvbSAncHJpbWVyZWFjdC9kcm9wZG93bic7XHJcbmltcG9ydCB7IGdldENvb2tpZSB9IGZyb20gJ2Nvb2tpZXMtbmV4dCc7XHJcbmltcG9ydCB7IHVzZWJhc2VEYXRhIH0gZnJvbSAnQC91dGlsaXRpZXMvaG9va3MvdXNlQmFzZURhdGEnO1xyXG5pbXBvcnQgeyBnZXRVc2VyRnVsbG5hbWUgfSBmcm9tICdAL3V0aWxpdGllcy9mdW5jdGlvbnMvdXRpbHMnO1xyXG5pbXBvcnQgeyBFZGl0b3IgfSBmcm9tICdwcmltZXJlYWN0L2VkaXRvcic7XHJcbmltcG9ydCB7IENhbiB9IGZyb20gJ0AvYXBwL0Nhbic7XHJcbmltcG9ydCB7IHVzZUFwaUFyYml0cmF0aW9uQ3JlYXRlLCB1c2VBcGlBcmJpdHJhdGlvblBhcnRpYWxVcGRhdGUgfSBmcm9tICdAL2hvb2tzL3VzZU5leHRBcGknO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gR2VuZXJpY1RhYmxlPFQgZXh0ZW5kcyBNUlRfUm93RGF0YT4oZGF0YV86IHsgaXNMb2FkaW5nOiBhbnk7IGRhdGFfOiBhbnksIGVycm9yOiBhbnksIGRhdGFfdHlwZTogYW55IHwgdW5kZWZpbmVkLCBwYWdpbmF0aW9uOiBhbnkgfSkge1xyXG4gIGNvbnN0IHVzZXIgPSBKU09OLnBhcnNlKGdldENvb2tpZSgndXNlcicpPy50b1N0cmluZygpIHx8ICd7fScpXHJcblxyXG4gIGNvbnN0IHRvYXN0ID0gdXNlUmVmPFRvYXN0IHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgeyBwbGFucywgdXNlcnMsIGFyYml0cmF0aW9ucyB9ID0gdXNlYmFzZURhdGEoKVxyXG5cclxuICBjb25zdCB1c2Vyc19kYXRhID0gdXNlTWVtbygoKSA9PiB1c2Vycy5kYXRhPy5kYXRhLnJlc3VsdHMsIFt1c2Vycy5kYXRhPy5kYXRhLnJlc3VsdHNdKVxyXG4gIGNvbnN0IFthcmJpdHJhdGlvbklELCBzZXRBcmJpdHJhdGlvbklEXSA9IHVzZVN0YXRlKDApXHJcbiAgY29uc3QgW2VkaXRWaXNpYmxlLCBzZXRFZGl0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2NyZWF0ZVZpc2libGUsIHNldENyZWF0ZVZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwaWNrbGlzdFRhcmdldFZhbHVlVGVhbSwgc2V0UGlja2xpc3RUYXJnZXRWYWx1ZVRlYW1dID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtwaWNrbGlzdFNvdXJjZVZhbHVlVGVhbSwgc2V0UGlja2xpc3RTb3VyY2VWYWx1ZVRlYW1dID0gdXNlU3RhdGUodXNlcnNfZGF0YSk7XHJcbiAgY29uc3QgW3Jvd1RvYmUsIHNldFJvd1RvYmVdID0gdXNlU3RhdGUoe30pO1xyXG4gIGNvbnN0IHsgdHJpZ2dlcjogYXJiaXRyYXRpb25fY3JlYXRlX3RyaWdnZXIsIGlzTXV0YXRpbmc6IGlzQ3JlYXRlTXV0YXRpbmcgfSA9IHVzZUFwaUFyYml0cmF0aW9uQ3JlYXRlKClcclxuICBjb25zdCB7IHRyaWdnZXI6IGFyYml0cmF0aW9uX3BhdGNoX3RyaWdnZXIsIGlzTXV0YXRpbmc6IGlzUGF0Y2hNdXRhdGluZyB9ID0gdXNlQXBpQXJiaXRyYXRpb25QYXJ0aWFsVXBkYXRlKClcclxuICBjb25zdCBbbnVtUGFnZXMsIHNldE51bVBhZ2VzXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtwYWdlTnVtYmVyLCBzZXRQYWdlTnVtYmVyXSA9IHVzZVN0YXRlKDEpO1xyXG4gIGNvbnN0IFthbmNob3JFbCwgc2V0QW5jaG9yRWxdID0gdXNlU3RhdGU8bnVsbCB8IEhUTUxFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBbcm93QWN0aW9uRW5hYmxlZCwgc2V0Um93QWN0aW9uRW5hYmxlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3Qgb3BlbiA9IEJvb2xlYW4oYW5jaG9yRWwpO1xyXG5cclxuICBmdW5jdGlvbiBvblBhZ2luYXRpb25DaGFuZ2Uoc3RhdGU6IGFueSkge1xyXG4gICAgY29uc29sZS5sb2coZGF0YV8ucGFnaW5hdGlvbik7XHJcbiAgICBkYXRhXy5wYWdpbmF0aW9uLnNldChzdGF0ZSlcclxuICB9O1xyXG4gIGZ1bmN0aW9uIG9uRG9jdW1lbnRMb2FkU3VjY2Vzcyh7IG51bVBhZ2VzIH06IHsgbnVtUGFnZXM6IG51bWJlciB9KSB7XHJcbiAgICBzZXROdW1QYWdlcyhudW1QYWdlcyk7XHJcbiAgICBzZXRQYWdlTnVtYmVyKDEpO1xyXG4gIH1cclxuICBmdW5jdGlvbiBjaGFuZ2VQYWdlKG9mZnNldDogbnVtYmVyKSB7XHJcbiAgICBzZXRQYWdlTnVtYmVyKHByZXZQYWdlTnVtYmVyID0+IHByZXZQYWdlTnVtYmVyICsgb2Zmc2V0KTtcclxuICB9XHJcbiAgZnVuY3Rpb24gcHJldmlvdXNQYWdlKCkge1xyXG4gICAgY2hhbmdlUGFnZSgtMSk7XHJcbiAgfVxyXG4gIGZ1bmN0aW9uIG5leHRQYWdlKCkge1xyXG4gICAgY2hhbmdlUGFnZSgxKTtcclxuICB9XHJcbiAgY29uc3QgYWNjZXB0X3Jvd19kZWxldGlvbiA9ICgpID0+IHtcclxuICAgIHRvYXN0LmN1cnJlbnQ/LnNob3coeyBzZXZlcml0eTogJ2luZm8nLCBzdW1tYXJ5OiAnQ29uZmlybWVkJywgZGV0YWlsOiAnWW91IGhhdmUgYWNjZXB0ZWQnLCBsaWZlOiAzMDAwIH0pO1xyXG4gIH07XHJcbiAgY29uc3QgcmVqZWN0X3Jvd19kZWxldGlvbiA9ICgpID0+IHtcclxuICAgIHRvYXN0LmN1cnJlbnQ/LnNob3coeyBzZXZlcml0eTogJ3dhcm4nLCBzdW1tYXJ5OiAnUmVqZWN0ZWQnLCBkZXRhaWw6ICdZb3UgaGF2ZSByZWplY3RlZCcsIGxpZmU6IDMwMDAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQ8SFRNTEJ1dHRvbkVsZW1lbnQ+KSA9PiB7XHJcbiAgICBzZXRBbmNob3JFbChldmVudC5jdXJyZW50VGFyZ2V0KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBjb2x1bW5zID0gdXNlTWVtbzxNUlRfQ29sdW1uRGVmPFQ+W10+KFxyXG4gICAgKCkgPT5cclxuICAgICAgT2JqZWN0LmVudHJpZXMoZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXMpLmZpbHRlcigoW2tleSwgdmFsdWVdLCBpbmRleCkgPT4gIVsnbW9kaWZpZWRfYnknLCAnY3JlYXRlZF9ieScsICdjcmVhdGVkJywgJ21vZGlmaWVkJyxdLmluY2x1ZGVzKGtleSkpLm1hcCgoW2tleSwgdmFsdWVdLCBpbmRleCkgPT4ge1xyXG4gICAgICAgIGlmIChbJ3JlcG9ydCcsICdjb250ZW50JywgJ25vdGUnLCAnb3JkZXInLCAnY29tbWVudCcsICdkZXNjcmlwdGlvbiddLmluY2x1ZGVzKGtleSkpIHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIC8vIENlbGw6ICh7IGNlbGwgfSkgPT4gPGRpdj57cGFyc2UoY2VsbC5nZXRWYWx1ZTxzdHJpbmc+KCkpfTwvZGl2PixcclxuICAgICAgICAgICAgLy8gQ2VsbDogKHsgY2VsbCB9KSA9PiB7IGlmIChbXCJkZXNjcmlwdGlvblwiLCBcImNvbnRlbnRcIixcInJlcG9ydFwiXS5pbmNsdWRlcyhrZXkpKSByZXR1cm4gbnVsbDsgZWxzZSByZXR1cm4gPGRpdj57cGFyc2UoY2VsbC5nZXRWYWx1ZTxzdHJpbmc+KCkpfTwvZGl2PiB9LFxyXG4gICAgICAgICAgICBFZGl0OiAoeyBjZWxsLCBjb2x1bW4sIHJvdywgdGFibGUgfSkgPT4ge1xyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPSdmb250LWJvbGQnPlJhcHBvcnQ8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICA8RWRpdG9yXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gaW5pdGlhbFZhbHVlPXtyb3cub3JpZ2luYWxba2V5XX1cclxuICAgICAgICAgICAgICAgICAgICAvLyB0aW55bWNlU2NyaXB0U3JjPVwiaHR0cDovL2xvY2FsaG9zdDozMDAwL3RpbnltY2UvdGlueW1jZS5taW4uanNcIlxyXG4gICAgICAgICAgICAgICAgICAgIC8vIGFwaUtleT0nbm9uZSdcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cm93Lm9yaWdpbmFsLnJlcG9ydH1cclxuICAgICAgICAgICAgICAgICAgICAvLyBvbkNoYW5nZT17KGUpID0+IHsgcm93Ll92YWx1ZXNDYWNoZS5yZXBvcnQgPSBlLnRhcmdldC5nZXRDb250ZW50KCkgfX1cclxuICAgICAgICAgICAgICAgICAgICBvblRleHRDaGFuZ2U9eyhlKSA9PiB7IHJvdy5fdmFsdWVzQ2FjaGUucmVwb3J0ID0gZS5odG1sVmFsdWUgfX1cclxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBoZWlnaHQ6ICczMjBweCcgfX1cclxuICAgICAgICAgICAgICAgICAgLy8gaW5pdD17e1xyXG4gICAgICAgICAgICAgICAgICAvLyAgIGxpY2Vuc2VLZXk6J2dwbCcsXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgaGVpZ2h0OiA1MDAsXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgbWVudWJhcjogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgLy8gICBwbHVnaW5zOiBbXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgICAnYWR2bGlzdCcsICdhdXRvbGluaycsICdsaXN0cycsICdsaW5rJywgJ2ltYWdlJywgJ2NoYXJtYXAnLCAncHJpbnQnLCAncHJldmlldycsICdhbmNob3InLFxyXG4gICAgICAgICAgICAgICAgICAvLyAgICAgJ3NlYXJjaHJlcGxhY2UnLCAndmlzdWFsYmxvY2tzJywgJ2NvZGUnLCAnZnVsbHNjcmVlbicsXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgICAnaW5zZXJ0ZGF0ZXRpbWUnLCAnbWVkaWEnLCAndGFibGUnLCAncGFzdGUnLCAnY29kZScsICdoZWxwJywgJ3dvcmRjb3VudCdcclxuICAgICAgICAgICAgICAgICAgLy8gICBdLFxyXG4gICAgICAgICAgICAgICAgICAvLyAgIHRvb2xiYXI6XHJcbiAgICAgICAgICAgICAgICAgIC8vICAgICAndW5kbyByZWRvIHwgZm9ybWF0c2VsZWN0IHwgYm9sZCBpdGFsaWMgYmFja2NvbG9yIHwgXFxcclxuICAgICAgICAgICAgICAgICAgLy8gICAgIGFsaWdubGVmdCBhbGlnbmNlbnRlciBhbGlnbnJpZ2h0IGFsaWduanVzdGlmeSB8IFxcXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgICBidWxsaXN0IG51bWxpc3Qgb3V0ZGVudCBpbmRlbnQgfCByZW1vdmVmb3JtYXQgfCBoZWxwIHx2aXN1YWxibG9ja3MgY29kZSBmdWxsc2NyZWVuJ1xyXG4gICAgICAgICAgICAgICAgICAvLyB9fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGtleSA9PT0gXCJwbGFuXCIpIHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAncGxhbicsXHJcblxyXG4gICAgICAgICAgICBtdWlUYWJsZUhlYWRDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2xlZnQnLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUJvZHlDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2xlZnQnLFxyXG4gICAgICAgICAgICB9LFxyXG5cclxuICAgICAgICAgICAgbXVpVGFibGVGb290ZXJDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwsIHJvdyB9KSA9PiA8VGFnIGNsYXNzTmFtZT0ndy0xMXJlbSB0ZXh0LXNtJz57Y2VsbC5nZXRWYWx1ZTxQbGFuPigpLmNvZGV9PC9UYWc+LFxyXG4gICAgICAgICAgICBFZGl0OiAoeyByb3cgfSkgPT4gKFxyXG4gICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPSdmb250LWJvbGQnPlBsYW48L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgPERyb3Bkb3duXHJcbiAgICAgICAgICAgICAgICAgIG9wdGlvbkxhYmVsPVwibmFtZVwiXHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQ2hvaXNpciB1biBwbGFuXCJcclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Um93VG9iZSh7IC4uLnJvd1RvYmUsIHBsYW46IHBsYW5zPy5kYXRhPy5kYXRhLnJlc3VsdHMuZmluZCgocGxhbjogUGxhbikgPT4gcGxhbi5pZCA9PT0gZS52YWx1ZS5jb2RlKSB9KTtcclxuICAgICAgICAgICAgICAgICAgICByb3cuX3ZhbHVlc0NhY2hlID0geyAuLi5yb3cuX3ZhbHVlc0NhY2hlLCBwbGFuOiBwbGFucz8uZGF0YT8uZGF0YS5yZXN1bHRzLmZpbmQoKHBsYW46IFBsYW4pID0+IHBsYW4uaWQgPT09IGUudmFsdWUuY29kZSkgfVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17eyBjb2RlOiByb3cuX3ZhbHVlc0NhY2hlLnBsYW4/LmlkIHx8IG51bGwsIG5hbWU6IHJvdy5fdmFsdWVzQ2FjaGUucGxhbj8uY29kZSB8fCBudWxsIH19XHJcbiAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3BsYW5zPy5kYXRhPy5kYXRhLnJlc3VsdHMubWFwKChwbGFuOiBQbGFuKSA9PiB7IHJldHVybiB7IGNvZGU6IHBsYW4uaWQsIG5hbWU6IHBsYW4uY29kZSB9IH0pfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPC9Ecm9wZG93bj5cclxuICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS5mb3JtYXQgPT09ICdkYXRlLXRpbWUnIHx8IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0uZm9ybWF0ID09PSAnZGF0ZScpIHtcclxuXHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBhY2Nlc3NvckZuOiAocm93KSA9PiBuZXcgRGF0ZShrZXkgPT0gXCJjcmVhdGVkXCIgPyByb3cuY3JlYXRlZCA6IHJvdy5tb2RpZmllZCksXHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGZpbHRlclZhcmlhbnQ6ICdkYXRlJyxcclxuICAgICAgICAgICAgZmlsdGVyRm46ICdsZXNzVGhhbicsXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogJ2RhdGV0aW1lJyxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgQ2VsbDogKHsgY2VsbCB9KSA9PiBjZWxsLmdldFZhbHVlPERhdGU+KCk/LnRvTG9jYWxlRGF0ZVN0cmluZygnZnInKSxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgICAgRWRpdDogKCkgPT4gbnVsbCxcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChrZXkgPT09IFwiaWRcIikgcmV0dXJuIHtcclxuICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgIEVkaXQ6ICgpID0+IG51bGwsXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChrZXkgPT09IFwidGVhbVwiKSByZXR1cm4ge1xyXG4gICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgQ2VsbDogKHsgY2VsbCwgcm93IH0pID0+IDx1bD57Y2VsbC5nZXRWYWx1ZTxVc2VyW10+KCkubWFwKCh1c3IpID0+IDxsaT57Z2V0VXNlckZ1bGxuYW1lKHVzcil9PC9saT4pfTwvdWw+LFxyXG4gICAgICAgICAgRWRpdDogKHsgcm93IH0pID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1tBUkJJVFJBVElPTl0nLCByb3cuX3ZhbHVlc0NhY2hlLnRlYW0pXHJcbiAgICAgICAgICAgIHJldHVybiAoPD5cclxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPSdmb250LWJvbGQnPk1lbWJyZXM8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxQaWNrTGlzdFxyXG4gICAgICAgICAgICAgICAgc291cmNlPXtwaWNrbGlzdFRhcmdldFZhbHVlVGVhbS5sZW5ndGggPT09IDAgPyBwaWNrbGlzdFNvdXJjZVZhbHVlVGVhbSA6IHBpY2tsaXN0U291cmNlVmFsdWVUZWFtLmZpbHRlcih1c2VyID0+IHBpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtLm1hcCh1c2VyID0+IHVzZXIudXNlcm5hbWUpLmluY2x1ZGVzKHVzZXIudXNlcm5hbWUpKX1cclxuICAgICAgICAgICAgICAgIGlkPSdwaWNrbGlzdF90ZWFtJ1xyXG4gICAgICAgICAgICAgICAgdGFyZ2V0PXtwaWNrbGlzdFRhcmdldFZhbHVlVGVhbS5sZW5ndGggPiAwID8gcGlja2xpc3RUYXJnZXRWYWx1ZVRlYW0gOiByb3cuX3ZhbHVlc0NhY2hlLnRlYW19XHJcbiAgICAgICAgICAgICAgICBzb3VyY2VIZWFkZXI9XCJEZVwiXHJcbiAgICAgICAgICAgICAgICB0YXJnZXRIZWFkZXI9XCJBXCJcclxuICAgICAgICAgICAgICAgIGl0ZW1UZW1wbGF0ZT17KGl0ZW0pID0+IDxkaXYga2V5PXtpdGVtLnVzZXJuYW1lfT57aXRlbS5maXJzdF9uYW1lfSB7aXRlbS5sYXN0X25hbWV9PC9kaXY+fVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdzb3VyY2UgVGVhbScsIGUuc291cmNlKVxyXG4gICAgICAgICAgICAgICAgICBzZXRQaWNrbGlzdFNvdXJjZVZhbHVlVGVhbShbLi4uZS5zb3VyY2VdKVxyXG4gICAgICAgICAgICAgICAgICBzZXRQaWNrbGlzdFRhcmdldFZhbHVlVGVhbShbLi4uZS50YXJnZXRdKVxyXG4gICAgICAgICAgICAgICAgICByb3cuX3ZhbHVlc0NhY2hlLnRlYW0gPSBlLnRhcmdldFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIHNvdXJjZVN0eWxlPXt7IGhlaWdodDogJzIwMHB4JyB9fVxyXG4gICAgICAgICAgICAgICAgdGFyZ2V0U3R5bGU9e3sgaGVpZ2h0OiAnMjAwcHgnIH19XHJcbiAgICAgICAgICAgICAgICBmaWx0ZXIgZmlsdGVyQnk9J3VzZXJuYW1lLGVtYWlsLGZpcnN0X25hbWUsbGFzdF9uYW1lJ1xyXG4gICAgICAgICAgICAgICAgZmlsdGVyTWF0Y2hNb2RlPSdjb250YWlucydcclxuICAgICAgICAgICAgICAgIHNvdXJjZUZpbHRlclBsYWNlaG9sZGVyPVwiUmVjaGVyY2hlciBwYXIgbm9tICYgcHLDqW5vbVwiIHRhcmdldEZpbHRlclBsYWNlaG9sZGVyPVwiUmVjaGVyY2hlciBwYXIgbm9tICYgcHLDqW5vbVwiXHJcbiAgICAgICAgICAgICAgPlxyXG5cclxuICAgICAgICAgICAgICA8L1BpY2tMaXN0PlxyXG4gICAgICAgICAgICA8Lz4pXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgICAgLy8gRWRpdDogKCkgPT4gbnVsbCxcclxuICAgICAgICAgIH07XHJcblxyXG4gICAgICAgIH1cclxuICAgICAgfSlcclxuICAgICxcclxuICAgIFt1c2Vycy5kYXRhPy5kYXRhLnJlc3VsdHMsIHBsYW5zLmRhdGE/LmRhdGEucmVzdWx0cywgYXJiaXRyYXRpb25zLmRhdGE/LmRhdGEucmVzdWx0c10sXHJcbiAgKTtcclxuXHJcbiAgY29uc3QgdGFibGUgPSB1c2VNYXRlcmlhbFJlYWN0VGFibGUoe1xyXG4gICAgY29sdW1ucyxcclxuICAgIGRhdGE6IGRhdGFfLmVycm9yID8gW10gOiBkYXRhXy5kYXRhXy5kYXRhLnJlc3VsdHMgPyBkYXRhXy5kYXRhXy5kYXRhLnJlc3VsdHMgOiBbZGF0YV8uZGF0YV8uZGF0YV0sIC8vbXVzdCBiZSBtZW1vaXplZCBvciBzdGFibGUgKHVzZVN0YXRlLCB1c2VNZW1vLCBkZWZpbmVkIG91dHNpZGUgb2YgdGhpcyBjb21wb25lbnQsIGV0Yy4pXHJcbiAgICByb3dDb3VudDogZGF0YV8uZXJyb3IgPyAwIDogZGF0YV8uZGF0YV8uZGF0YS5yZXN1bHRzID8gZGF0YV8uZGF0YV8uZGF0YS5jb3VudCA6IDEsXHJcbiAgICBlbmFibGVSb3dTZWxlY3Rpb246IHRydWUsIC8vZW5hYmxlIHNvbWUgZmVhdHVyZXNcclxuICAgIGVuYWJsZUNvbHVtbk9yZGVyaW5nOiB0cnVlLCAvL2VuYWJsZSBhIGZlYXR1cmUgZm9yIGFsbCBjb2x1bW5zXHJcbiAgICBlbmFibGVHbG9iYWxGaWx0ZXI6IHRydWUsIC8vdHVybiBvZmYgYSBmZWF0dXJlXHJcbiAgICBlbmFibGVHcm91cGluZzogdHJ1ZSxcclxuICAgIGVuYWJsZVJvd0FjdGlvbnM6IHRydWUsXHJcbiAgICBlbmFibGVSb3dQaW5uaW5nOiB0cnVlLFxyXG4gICAgZW5hYmxlU3RpY2t5SGVhZGVyOiB0cnVlLFxyXG4gICAgZW5hYmxlU3RpY2t5Rm9vdGVyOiB0cnVlLFxyXG4gICAgZW5hYmxlQ29sdW1uUGlubmluZzogdHJ1ZSxcclxuICAgIGVuYWJsZUNvbHVtblJlc2l6aW5nOiB0cnVlLFxyXG4gICAgZW5hYmxlUm93TnVtYmVyczogdHJ1ZSxcclxuICAgIGVuYWJsZUV4cGFuZEFsbDogdHJ1ZSxcclxuICAgIGVuYWJsZUVkaXRpbmc6IHRydWUsXHJcbiAgICBlbmFibGVFeHBhbmRpbmc6IHRydWUsXHJcbiAgICBtYW51YWxQYWdpbmF0aW9uOiB0cnVlLFxyXG4gICAgaW5pdGlhbFN0YXRlOiB7XHJcbiAgICAgIHBhZ2luYXRpb246IHsgcGFnZVNpemU6IDUsIHBhZ2VJbmRleDogMSB9LFxyXG4gICAgICBjb2x1bW5WaXNpYmlsaXR5OiB7IHJlcG9ydDogZmFsc2UsIGNyZWF0ZWRfYnk6IGZhbHNlLCBjcmVhdGVkOiBmYWxzZSwgbW9kZmllZF9ieTogZmFsc2UsIG1vZGlmaWVkOiBmYWxzZSwgbW9kaWZpZWRfYnk6IGZhbHNlLCBzdGFmZjogZmFsc2UsIGFzc2lzdGFudHM6IGZhbHNlLCBpZDogZmFsc2UsIGRvY3VtZW50OiBmYWxzZSB9LFxyXG4gICAgICBkZW5zaXR5OiAnY29tcGFjdCcsXHJcbiAgICAgIHNob3dHbG9iYWxGaWx0ZXI6IHRydWUsXHJcbiAgICAgIHNvcnRpbmc6IFt7IGlkOiAnaWQnLCBkZXNjOiBmYWxzZSB9XSxcclxuICAgIH0sXHJcbiAgICBzdGF0ZToge1xyXG4gICAgICBwYWdpbmF0aW9uOiBkYXRhXy5wYWdpbmF0aW9uLnBhZ2ksXHJcbiAgICAgIGlzTG9hZGluZzogZGF0YV8uaXNMb2FkaW5nLCAvL2NlbGwgc2tlbGV0b25zIGFuZCBsb2FkaW5nIG92ZXJsYXlcclxuICAgICAgLy9zaG93UHJvZ3Jlc3NCYXJzOiBpc0xvYWRpbmcsIC8vcHJvZ3Jlc3MgYmFycyB3aGlsZSByZWZldGNoaW5nXHJcbiAgICAgIC8vIGlzU2F2aW5nOiBpc1NhdmluZ1RvZG9zLCAvL3Byb2dyZXNzIGJhcnMgYW5kIHNhdmUgYnV0dG9uIHNwaW5uZXJzXHJcbiAgICB9LFxyXG4gICAgbG9jYWxpemF0aW9uOiBNUlRfTG9jYWxpemF0aW9uX0ZSLFxyXG4gICAgb25QYWdpbmF0aW9uQ2hhbmdlOiBvblBhZ2luYXRpb25DaGFuZ2UsXHJcbiAgICBkaXNwbGF5Q29sdW1uRGVmT3B0aW9uczoge1xyXG4gICAgICAnbXJ0LXJvdy1waW4nOiB7XHJcbiAgICAgICAgZW5hYmxlSGlkaW5nOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICAnbXJ0LXJvdy1leHBhbmQnOiB7XHJcbiAgICAgICAgZW5hYmxlSGlkaW5nOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICAnbXJ0LXJvdy1hY3Rpb25zJzoge1xyXG4gICAgICAgIC8vIGhlYWRlcjogJ0VkaXQnLCAvL2NoYW5nZSBcIkFjdGlvbnNcIiB0byBcIkVkaXRcIlxyXG4gICAgICAgIHNpemU6IDEwMCxcclxuICAgICAgICBlbmFibGVIaWRpbmc6IHRydWUsXHJcblxyXG4gICAgICAgIC8vdXNlIGEgdGV4dCBidXR0b24gaW5zdGVhZCBvZiBhIGljb24gYnV0dG9uXHJcbiAgICAgICAgLy8gQ2VsbDogKHsgcm93LCB0YWJsZSB9KSA9PiAoXHJcbiAgICAgICAgLy8gICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHRhYmxlLnNldEVkaXRpbmdSb3cocm93KX0+RWRpdCBDdXN0b21lcjwvQnV0dG9uPlxyXG4gICAgICAgIC8vICksXHJcbiAgICAgIH0sXHJcbiAgICAgICdtcnQtcm93LW51bWJlcnMnOiB7XHJcbiAgICAgICAgZW5hYmxlSGlkaW5nOiB0cnVlLCAvL25vdyByb3cgbnVtYmVycyBhcmUgaGlkYWJsZSB0b29cclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBkZWZhdWx0Q29sdW1uOiB7XHJcbiAgICAgIGdyb3c6IHRydWUsXHJcbiAgICAgIGVuYWJsZU11bHRpU29ydDogdHJ1ZSxcclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZVBhcGVyUHJvcHM6ICh7IHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIC8vZWxldmF0aW9uOiAwLCAvL2NoYW5nZSB0aGUgbXVpIGJveCBzaGFkb3dcclxuICAgICAgLy9jdXN0b21pemUgcGFwZXIgc3R5bGVzXHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBjbGFzc2VzOiB7IHJvb3Q6ICdwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bCcgfSxcclxuXHJcbiAgICAgIHN4OiB7XHJcbiAgICAgICAgaGVpZ2h0OiBgY2FsYygxMDB2aCAtIDlyZW0pYCxcclxuICAgICAgICAvLyBoZWlnaHQ6IGBjYWxjKDEwMHZoIC0gMjAwcHgpYCxcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIFwiJiAuTXVpVGFibGVQYWdpbmF0aW9uLXJvb3QgXCI6IHtcclxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIFwiJiAuTXVpQm94LXJvb3QgXCI6IHtcclxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfSksXHJcbiAgICBtdWlFZGl0Um93RGlhbG9nUHJvcHM6ICh7IHJvdywgdGFibGUgfSkgPT4gKHtcclxuICAgICAgb3BlbjogZWRpdFZpc2libGUgfHwgY3JlYXRlVmlzaWJsZSxcclxuICAgICAgc3g6IHtcclxuICAgICAgICAnJiAuTXVpRGlhbG9nLXJvb3QnOiB7XHJcbiAgICAgICAgICBkaXNwbGF5OiAnbm9uZSdcclxuICAgICAgICB9LFxyXG4gICAgICAgICcmIC5NdWlEaWFsb2ctY29udGFpbmVyJzoge1xyXG4gICAgICAgICAgZGlzcGxheTogJ25vbmUnXHJcbiAgICAgICAgfSxcclxuICAgICAgICB6SW5kZXg6IDExMDBcclxuXHJcbiAgICAgIH1cclxuICAgIH0pLFxyXG4gICAgZWRpdERpc3BsYXlNb2RlOiAnbW9kYWwnLFxyXG4gICAgY3JlYXRlRGlzcGxheU1vZGU6ICdtb2RhbCcsXHJcbiAgICBvbkVkaXRpbmdSb3dTYXZlOiAoeyB0YWJsZSwgdmFsdWVzLCByb3cgfSkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIm9uRWRpdGluZ1Jvd1NhdmVcIiwgdmFsdWVzKVxyXG4gICAgICBjb25zdCB7IGlkLCAuLi5yZXN0IH0gPSB2YWx1ZXNcclxuICAgICAgcmVzdC5wbGFuID0gdmFsdWVzLnBsYW4uaWRcclxuICAgICAgcmVzdC50ZWFtID0gdmFsdWVzLnRlYW0/Lm1hcCh1c2VyID0+IHVzZXIuaWQpIHx8IFtdXHJcbiAgICAgIGFyYml0cmF0aW9uX3BhdGNoX3RyaWdnZXIocmVzdCwge1xyXG4gICAgICAgIHJldmFsaWRhdGU6IHRydWUsXHJcbiAgICAgICAgb25TdWNjZXNzOiAoKSA9PiB7XHJcbiAgICAgICAgICB0YWJsZS5zZXRFZGl0aW5nUm93KG51bGwpOyAvL2V4aXQgY3JlYXRpbmcgbW9kZVxyXG4gICAgICAgICAgdG9hc3QuY3VycmVudCEuc2hvdyh7IHNldmVyaXR5OiAnc3VjY2VzcycsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiBgQXJiaXRyYWdlICR7dmFsdWVzLnBsYW4uY29kZX0gbWlzIMOgIGFqb3VyYCB9KTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgdG9hc3QuY3VycmVudCEuc2hvdyh7IHNldmVyaXR5OiAnZXJyb3InLCBzdW1tYXJ5OiAnSW5mbycsIGRldGFpbDogYCR7ZXJyb3IucmVzcG9uc2U/LnN0YXR1c1RleHR9YCB9KTtcclxuICAgICAgICAgIC8vY29uc29sZS5sb2coXCJvbkVkaXRpbmdSb3dTYXZlXCIsIGVycm9yLnJlc3BvbnNlKTtcclxuICAgICAgICAgIHJvdy5fdmFsdWVzQ2FjaGUgPSB7IGVycm9yOiBlcnJvci5yZXNwb25zZSwgLi4ucm93Ll92YWx1ZXNDYWNoZSB9O1xyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuICAgICAgfSlcclxuICAgIH0sXHJcbiAgICBvbkVkaXRpbmdSb3dDYW5jZWw6ICgpID0+IHtcclxuICAgICAgLy9jbGVhciBhbnkgdmFsaWRhdGlvbiBlcnJvcnNcclxuICAgICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnaW5mbycsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiAnQW5udWxhdGlvbicgfSk7XHJcbiAgICB9LFxyXG4gICAgb25DcmVhdGluZ1Jvd1NhdmU6ICh7IHRhYmxlLCB2YWx1ZXMsIHJvdyB9KSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwib25DcmVhdGluZ1Jvd1NhdmVcIiwgdmFsdWVzKVxyXG4gICAgICBjb25zdCB7IGlkLCAuLi5yZXN0IH0gPSB2YWx1ZXNcclxuICAgICAgcmVzdC5wbGFuID0gdmFsdWVzLnBsYW4uaWRcclxuICAgICAgcmVzdC50ZWFtID0gdmFsdWVzLnRlYW0/Lm1hcCh1c2VyID0+IHVzZXIuaWQpIHx8IFtdXHJcbiAgICAgIGFyYml0cmF0aW9uX2NyZWF0ZV90cmlnZ2VyKHJlc3QsIHtcclxuICAgICAgICByZXZhbGlkYXRlOiB0cnVlLFxyXG4gICAgICAgIG9uU3VjY2VzczogKCkgPT4ge1xyXG4gICAgICAgICAgdGFibGUuc2V0Q3JlYXRpbmdSb3cobnVsbCk7IC8vZXhpdCBjcmVhdGluZyBtb2RlXHJcbiAgICAgICAgICB0b2FzdC5jdXJyZW50IS5zaG93KHsgc2V2ZXJpdHk6ICdzdWNjZXNzJywgc3VtbWFyeTogJ0luZm8nLCBkZXRhaWw6IGBBcmJpdHJhZ2UgJHt2YWx1ZXMucGxhbi5jb2RlfSBjcsOpw6lgIH0pO1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICB0b2FzdC5jdXJyZW50IS5zaG93KHsgc2V2ZXJpdHk6ICdlcnJvcicsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiBgJHtlcnJvci5yZXNwb25zZT8uc3RhdHVzVGV4dH1gIH0pO1xyXG4gICAgICAgICAgLy9jb25zb2xlLmxvZyhcIm9uRWRpdGluZ1Jvd1NhdmVcIiwgZXJyb3IucmVzcG9uc2UpO1xyXG4gICAgICAgICAgcm93Ll92YWx1ZXNDYWNoZSA9IHsgZXJyb3I6IGVycm9yLnJlc3BvbnNlLCAuLi5yb3cuX3ZhbHVlc0NhY2hlIH07XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICB9KVxyXG4gICAgfSxcclxuICAgIG9uQ3JlYXRpbmdSb3dDYW5jZWw6ICgpID0+IHtcclxuICAgICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnaW5mbycsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiAnQW5udWxhdGlvbicgfSk7XHJcbiAgICB9LFxyXG4gICAgbXVpVGFibGVGb290ZXJQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBcIiYgLk11aVRhYmxlRm9vdGVyLXJvb3RcIjoge1xyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIG11aVRhYmxlQ29udGFpbmVyUHJvcHM6ICh7IHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgaGVpZ2h0OiB0YWJsZS5nZXRTdGF0ZSgpLmlzRnVsbFNjcmVlbiA/IGBjYWxjKDEwMHZoKWAgOiBgY2FsYygxMDB2aCAtIDlyZW0gLSAke3RhYmxlLnJlZnMudG9wVG9vbGJhclJlZi5jdXJyZW50Py5vZmZzZXRIZWlnaHR9cHggLSAke3RhYmxlLnJlZnMuYm90dG9tVG9vbGJhclJlZi5jdXJyZW50Py5vZmZzZXRIZWlnaHR9cHgpYFxyXG5cclxuICAgICAgfSxcclxuICAgIH0pLFxyXG4gICAgbXVpUGFnaW5hdGlvblByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG5cclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICBjb2xvcjogXCJ2YXIoLS1zdXJmYWNlLTkwMCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZUhlYWRDZWxsUHJvcHM6IHtcclxuICAgICAgc3g6IHtcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgbXVpVG9wVG9vbGJhclByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIlxyXG4gICAgICB9LFxyXG5cclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZUJvZHlQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgICAgLy9zdHJpcGUgdGhlIHJvd3MsIG1ha2Ugb2RkIHJvd3MgYSBkYXJrZXIgY29sb3JcclxuICAgICAgICAnJiB0cjpudGgtb2YtdHlwZShvZGQpID4gdGQnOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1zdXJmYWNlLWNhcmQpJyxcclxuICAgICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgICcmIHRyOm50aC1vZi10eXBlKGV2ZW4pID4gdGQnOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1zdXJmYWNlLWJvcmRlciknLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgcmVuZGVyVG9wVG9vbGJhckN1c3RvbUFjdGlvbnM6ICh7IHRhYmxlIH0pID0+IChcclxuICAgICAgPFN0YWNrIGRpcmVjdGlvbj17XCJyb3dcIn0gc3BhY2luZz17MX0+XHJcbiAgICAgICAgPENhbiBJPVwiYWRkXCIgYT0nYXJiaXRyYXRpb24nPlxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICBpY29uPVwicGkgcGktcGx1c1wiXHJcbiAgICAgICAgICAgIHJvdW5kZWRcclxuICAgICAgICAgICAgLy8gaWQ9XCJiYXNpYy1idXR0b25cIlxyXG4gICAgICAgICAgICBhcmlhLWNvbnRyb2xzPXtvcGVuID8gJ2Jhc2ljLW1lbnUnIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICBhcmlhLWhhc3BvcHVwPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgIGFyaWEtZXhwYW5kZWQ9e29wZW4gPyAndHJ1ZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eyhldmVudCkgPT4ge1xyXG4gICAgICAgICAgICAgIHRhYmxlLnNldENyZWF0aW5nUm93KHRydWUpOyBzZXRDcmVhdGVWaXNpYmxlKHRydWUpLCBjb25zb2xlLmxvZyhcImNyZWF0aW5nIHJvdyAuLi5cIik7XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICA8L0Nhbj5cclxuICAgICAgICA8Q2FuIEk9XCJkZWxldGVcIiBhPSdhcmJpdHJhdGlvbic+XHJcbiAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgIHJvdW5kZWRcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3RhYmxlLmdldElzU29tZVJvd3NTZWxlY3RlZCgpfVxyXG4gICAgICAgICAgICAvLyBpZD1cImJhc2ljLWJ1dHRvblwiXHJcbiAgICAgICAgICAgIGFyaWEtY29udHJvbHM9e29wZW4gPyAnYmFzaWMtbWVudScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgIGFyaWEtaGFzcG9wdXA9XCJ0cnVlXCJcclxuICAgICAgICAgICAgYXJpYS1leHBhbmRlZD17b3BlbiA/ICd0cnVlJyA6IHVuZGVmaW5lZH1cclxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2xpY2t9XHJcbiAgICAgICAgICAgIGljb249XCJwaSBwaS10cmFzaFwiXHJcbiAgICAgICAgICAgIC8vIHN0eWxlPXt7ICBib3JkZXJSYWRpdXM6ICcwJywgYm94U2hhZG93OiAnbm9uZScsIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JyB9fVxyXG4gICAgICAgICAgICBzaXplPVwic21hbGxcIlxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0Nhbj5cclxuICAgICAgPC9TdGFjaz5cclxuICAgICksXHJcbiAgICBtdWlEZXRhaWxQYW5lbFByb3BzOiAoKSA9PiAoe1xyXG4gICAgICBzeDogKHRoZW1lKSA9PiAoe1xyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjpcclxuICAgICAgICAgIHRoZW1lLnBhbGV0dGUubW9kZSA9PT0gJ2RhcmsnXHJcbiAgICAgICAgICAgID8gJ3JnYmEoMjU1LDIxMCwyNDQsMC4xKSdcclxuICAgICAgICAgICAgOiAncmdiYSgwLDAsMCwwLjEpJyxcclxuICAgICAgfSksXHJcbiAgICB9KSxcclxuICAgIHJlbmRlckNyZWF0ZVJvd0RpYWxvZ0NvbnRlbnQ6ICh7IGludGVybmFsRWRpdENvbXBvbmVudHMsIHJvdywgdGFibGUgfSkgPT5cclxuICAgICAgPGRpdiBzdHlsZT17eyB6SW5kZXg6ICcxMzAyICFpbXBvcnRhbnQnIH19PlxyXG4gICAgICAgIDxTaWRlYmFyIHBvc2l0aW9uPSdyaWdodCdcclxuICAgICAgICAgIGhlYWRlcj17XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtcm93IHctZnVsbCBmbGV4LXdyYXAganVzdGlmeS1jb250ZW50LWJldHdlZW4nPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nYWxpZ24tY29udGVudC1jZW50ZXIgJz5DcsOpYXRpb24gbm91dmVhdSBhcmJpdHJhZ2U8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPERpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgICAgICA8TVJUX0VkaXRBY3Rpb25CdXR0b25zIHZhcmlhbnQ9XCJ0ZXh0XCIgdGFibGU9e3RhYmxlfSByb3c9e3Jvd30gLz5cclxuICAgICAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdmlzaWJsZT17Y3JlYXRlVmlzaWJsZX1cclxuICAgICAgICAgIG9uSGlkZT17KCkgPT4geyB0YWJsZS5zZXRDcmVhdGluZ1JvdyhudWxsKTsgc2V0Q3JlYXRlVmlzaWJsZShmYWxzZSkgfX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTkgbGc6dy04XCI+XHJcbiAgICAgICAgICA8RGlhbG9nQ29udGVudFxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcclxuICAgICAgICAgICAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcclxuICAgICAgICAgICAgICBnYXA6ICcxLjVyZW0nXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHtpbnRlcm5hbEVkaXRDb21wb25lbnRzfVxyXG4gICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICAgIDwvU2lkZWJhcj5cclxuICAgICAgPC9kaXY+LFxyXG4gICAgcmVuZGVyRWRpdFJvd0RpYWxvZ0NvbnRlbnQ6ICh7IGludGVybmFsRWRpdENvbXBvbmVudHMsIHJvdywgdGFibGUgfSkgPT5cclxuICAgICAgPGRpdiBzdHlsZT17eyB6SW5kZXg6ICcxMzAyICFpbXBvcnRhbnQnIH19PlxyXG4gICAgICAgIDxTaWRlYmFyIHBvc2l0aW9uPSdyaWdodCdcclxuICAgICAgICAgIGhlYWRlcj17XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtcm93IHctZnVsbCBmbGV4LXdyYXAganVzdGlmeS1jb250ZW50LWJldHdlZW4nPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J2FsaWduLWNvbnRlbnQtY2VudGVyICc+RWRpdGVyIGwnYXJiaXRyYWdlIG7CsCB7cm93Lm9yaWdpbmFsLmlkfTwvaDM+XHJcbiAgICAgICAgICAgICAgPERpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgICAgICA8TVJUX0VkaXRBY3Rpb25CdXR0b25zIHZhcmlhbnQ9XCJ0ZXh0XCIgdGFibGU9e3RhYmxlfSByb3c9e3Jvd30gLz5cclxuICAgICAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdmlzaWJsZT17ZWRpdFZpc2libGV9XHJcbiAgICAgICAgICBvbkhpZGU9eygpID0+IHtcclxuICAgICAgICAgICAgdGFibGUuc2V0RWRpdGluZ1JvdyhudWxsKTtcclxuICAgICAgICAgICAgc2V0RWRpdFZpc2libGUoZmFsc2UpXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctOSBsZzp3LThcIj5cclxuICAgICAgICAgIDxEaWFsb2dDb250ZW50XHJcbiAgICAgICAgICAgIHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsIGdhcDogJzEuNXJlbScgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAge2ludGVybmFsRWRpdENvbXBvbmVudHN9IHsvKiBvciByZW5kZXIgY3VzdG9tIGVkaXQgY29tcG9uZW50cyBoZXJlICovfVxyXG4gICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICAgIDwvU2lkZWJhcj5cclxuICAgICAgPC9kaXY+LFxyXG4gICAgcmVuZGVyRGV0YWlsUGFuZWw6ICh7IHJvdyB9KSA9PlxyXG4gICAgICBwYXJzZShyb3cub3JpZ2luYWwucmVwb3J0KSxcclxuICAgIHJlbmRlclJvd0FjdGlvbnM6ICh7IGNlbGwsIHJvdywgdGFibGUgfSkgPT4gKFxyXG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJwLWJ1dHRvbnNldCBmbGV4IHAtMVwiPlxyXG4gICAgICAgIDxDYW4gST1cInVwZGF0ZVwiIGE9J2FyYml0cmF0aW9uJz5cclxuICAgICAgICAgIDxCdXR0b24gc2l6ZT0nc21hbGwnIGljb249XCJwaSBwaS1wZW5jaWxcIiBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIHNldEFyYml0cmF0aW9uSUQocm93Lm9yaWdpbmFsLmlkKTtcclxuICAgICAgICAgICAgdGFibGUuc2V0RWRpdGluZ1Jvdyhyb3cpO1xyXG4gICAgICAgICAgICBzZXRFZGl0VmlzaWJsZSh0cnVlKTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJlZGl0aW5nIHJvdyAuLi5cIik7XHJcbiAgICAgICAgICB9fSByb3VuZGVkIG91dGxpbmVkIC8+XHJcbiAgICAgICAgPC9DYW4+XHJcbiAgICAgICAgPENhbiBJPVwiZGVsZXRlXCIgYT0nYXJiaXRyYXRpb24nPlxyXG4gICAgICAgICAgPEJ1dHRvbiBzaXplPSdzbWFsbCcgaWNvbj1cInBpIHBpLXRyYXNoXCIgcm91bmRlZCBvdXRsaW5lZFxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoZXZlbnQpID0+IGNvbmZpcm1Qb3B1cCh7XHJcbiAgICAgICAgICAgICAgdGFyZ2V0OiBldmVudC5jdXJyZW50VGFyZ2V0LFxyXG4gICAgICAgICAgICAgIG1lc3NhZ2U6ICdWb3VsZXotdm91cyBzdXBwcmltZXIgY2V0dGUgbGlnbmU/JyxcclxuICAgICAgICAgICAgICBpY29uOiAncGkgcGktaW5mby1jaXJjbGUnLFxyXG4gICAgICAgICAgICAgIC8vIGRlZmF1bHRGb2N1czogJ3JlamVjdCcsXHJcbiAgICAgICAgICAgICAgYWNjZXB0Q2xhc3NOYW1lOiAncC1idXR0b24tZGFuZ2VyJyxcclxuICAgICAgICAgICAgICBhY2NlcHRMYWJlbDogJ091aScsXHJcbiAgICAgICAgICAgICAgcmVqZWN0TGFiZWw6ICdOb24nLFxyXG4gICAgICAgICAgICAgIGFjY2VwdDogYWNjZXB0X3Jvd19kZWxldGlvbixcclxuICAgICAgICAgICAgICByZWplY3Q6IHJlamVjdF9yb3dfZGVsZXRpb25cclxuICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvQ2FuPlxyXG4gICAgICAgIDxDb25maXJtUG9wdXAgLz5cclxuICAgICAgPC9zcGFuPlxyXG4gICAgKSxcclxuICB9KTtcclxuICByZXR1cm4gPD48TWF0ZXJpYWxSZWFjdFRhYmxlIHRhYmxlPXt0YWJsZX0gLz48VG9hc3QgcmVmPXt0b2FzdH0gLz48Lz47XHJcbn1cclxuIl0sIm5hbWVzIjpbIkRpYWxvZ0FjdGlvbnMiLCJEaWFsb2dDb250ZW50IiwiU3RhY2siLCJNUlRfRWRpdEFjdGlvbkJ1dHRvbnMiLCJNYXRlcmlhbFJlYWN0VGFibGUiLCJ1c2VNYXRlcmlhbFJlYWN0VGFibGUiLCJNUlRfTG9jYWxpemF0aW9uX0ZSIiwiQnV0dG9uIiwiQ29uZmlybVBvcHVwIiwiY29uZmlybVBvcHVwIiwiU2lkZWJhciIsIlRhZyIsIlRvYXN0IiwidXNlTWVtbyIsInVzZVJlZiIsInVzZVN0YXRlIiwicGFyc2UiLCJQaWNrTGlzdCIsIkRyb3Bkb3duIiwiZ2V0Q29va2llIiwidXNlYmFzZURhdGEiLCJnZXRVc2VyRnVsbG5hbWUiLCJFZGl0b3IiLCJDYW4iLCJ1c2VBcGlBcmJpdHJhdGlvbkNyZWF0ZSIsInVzZUFwaUFyYml0cmF0aW9uUGFydGlhbFVwZGF0ZSIsIkdlbmVyaWNUYWJsZSIsImRhdGFfIiwidXNlcnMiLCJwbGFucyIsImFyYml0cmF0aW9ucyIsInVzZXIiLCJKU09OIiwidG9TdHJpbmciLCJ0b2FzdCIsInVzZXJzX2RhdGEiLCJkYXRhIiwicmVzdWx0cyIsImFyYml0cmF0aW9uSUQiLCJzZXRBcmJpdHJhdGlvbklEIiwiZWRpdFZpc2libGUiLCJzZXRFZGl0VmlzaWJsZSIsImNyZWF0ZVZpc2libGUiLCJzZXRDcmVhdGVWaXNpYmxlIiwicGlja2xpc3RUYXJnZXRWYWx1ZVRlYW0iLCJzZXRQaWNrbGlzdFRhcmdldFZhbHVlVGVhbSIsInBpY2tsaXN0U291cmNlVmFsdWVUZWFtIiwic2V0UGlja2xpc3RTb3VyY2VWYWx1ZVRlYW0iLCJyb3dUb2JlIiwic2V0Um93VG9iZSIsInRyaWdnZXIiLCJhcmJpdHJhdGlvbl9jcmVhdGVfdHJpZ2dlciIsImlzTXV0YXRpbmciLCJpc0NyZWF0ZU11dGF0aW5nIiwiYXJiaXRyYXRpb25fcGF0Y2hfdHJpZ2dlciIsImlzUGF0Y2hNdXRhdGluZyIsIm51bVBhZ2VzIiwic2V0TnVtUGFnZXMiLCJwYWdlTnVtYmVyIiwic2V0UGFnZU51bWJlciIsImFuY2hvckVsIiwic2V0QW5jaG9yRWwiLCJyb3dBY3Rpb25FbmFibGVkIiwic2V0Um93QWN0aW9uRW5hYmxlZCIsIm9wZW4iLCJCb29sZWFuIiwib25QYWdpbmF0aW9uQ2hhbmdlIiwic3RhdGUiLCJjb25zb2xlIiwibG9nIiwicGFnaW5hdGlvbiIsInNldCIsIm9uRG9jdW1lbnRMb2FkU3VjY2VzcyIsImNoYW5nZVBhZ2UiLCJvZmZzZXQiLCJwcmV2UGFnZU51bWJlciIsInByZXZpb3VzUGFnZSIsIm5leHRQYWdlIiwiYWNjZXB0X3Jvd19kZWxldGlvbiIsImN1cnJlbnQiLCJzaG93Iiwic2V2ZXJpdHkiLCJzdW1tYXJ5IiwiZGV0YWlsIiwibGlmZSIsInJlamVjdF9yb3dfZGVsZXRpb24iLCJoYW5kbGVDbGljayIsImV2ZW50IiwiY3VycmVudFRhcmdldCIsImNvbHVtbnMiLCJPYmplY3QiLCJlbnRyaWVzIiwiZGF0YV90eXBlIiwicHJvcGVydGllcyIsImZpbHRlciIsImluZGV4Iiwia2V5IiwidmFsdWUiLCJpbmNsdWRlcyIsIm1hcCIsImhlYWRlciIsInRpdGxlIiwiYWNjZXNzb3JLZXkiLCJpZCIsIkVkaXQiLCJjZWxsIiwiY29sdW1uIiwicm93IiwidGFibGUiLCJsYWJlbCIsImNsYXNzTmFtZSIsIm9yaWdpbmFsIiwicmVwb3J0Iiwib25UZXh0Q2hhbmdlIiwiZSIsIl92YWx1ZXNDYWNoZSIsImh0bWxWYWx1ZSIsInN0eWxlIiwiaGVpZ2h0IiwibXVpVGFibGVIZWFkQ2VsbFByb3BzIiwiYWxpZ24iLCJtdWlUYWJsZUJvZHlDZWxsUHJvcHMiLCJtdWlUYWJsZUZvb3RlckNlbGxQcm9wcyIsIkNlbGwiLCJnZXRWYWx1ZSIsImNvZGUiLCJvcHRpb25MYWJlbCIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJwbGFuIiwiZmluZCIsIm5hbWUiLCJvcHRpb25zIiwiZm9ybWF0IiwiYWNjZXNzb3JGbiIsIkRhdGUiLCJjcmVhdGVkIiwibW9kaWZpZWQiLCJmaWx0ZXJWYXJpYW50IiwiZmlsdGVyRm4iLCJzb3J0aW5nRm4iLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ1bCIsInVzciIsImxpIiwidGVhbSIsInNvdXJjZSIsImxlbmd0aCIsInVzZXJuYW1lIiwidGFyZ2V0Iiwic291cmNlSGVhZGVyIiwidGFyZ2V0SGVhZGVyIiwiaXRlbVRlbXBsYXRlIiwiaXRlbSIsImRpdiIsImZpcnN0X25hbWUiLCJsYXN0X25hbWUiLCJzb3VyY2VTdHlsZSIsInRhcmdldFN0eWxlIiwiZmlsdGVyQnkiLCJmaWx0ZXJNYXRjaE1vZGUiLCJzb3VyY2VGaWx0ZXJQbGFjZWhvbGRlciIsInRhcmdldEZpbHRlclBsYWNlaG9sZGVyIiwiZXJyb3IiLCJyb3dDb3VudCIsImNvdW50IiwiZW5hYmxlUm93U2VsZWN0aW9uIiwiZW5hYmxlQ29sdW1uT3JkZXJpbmciLCJlbmFibGVHbG9iYWxGaWx0ZXIiLCJlbmFibGVHcm91cGluZyIsImVuYWJsZVJvd0FjdGlvbnMiLCJlbmFibGVSb3dQaW5uaW5nIiwiZW5hYmxlU3RpY2t5SGVhZGVyIiwiZW5hYmxlU3RpY2t5Rm9vdGVyIiwiZW5hYmxlQ29sdW1uUGlubmluZyIsImVuYWJsZUNvbHVtblJlc2l6aW5nIiwiZW5hYmxlUm93TnVtYmVycyIsImVuYWJsZUV4cGFuZEFsbCIsImVuYWJsZUVkaXRpbmciLCJlbmFibGVFeHBhbmRpbmciLCJtYW51YWxQYWdpbmF0aW9uIiwiaW5pdGlhbFN0YXRlIiwicGFnZVNpemUiLCJwYWdlSW5kZXgiLCJjb2x1bW5WaXNpYmlsaXR5IiwiY3JlYXRlZF9ieSIsIm1vZGZpZWRfYnkiLCJtb2RpZmllZF9ieSIsInN0YWZmIiwiYXNzaXN0YW50cyIsImRvY3VtZW50IiwiZGVuc2l0eSIsInNob3dHbG9iYWxGaWx0ZXIiLCJzb3J0aW5nIiwiZGVzYyIsInBhZ2kiLCJpc0xvYWRpbmciLCJsb2NhbGl6YXRpb24iLCJkaXNwbGF5Q29sdW1uRGVmT3B0aW9ucyIsImVuYWJsZUhpZGluZyIsInNpemUiLCJkZWZhdWx0Q29sdW1uIiwiZ3JvdyIsImVuYWJsZU11bHRpU29ydCIsIm11aVRhYmxlUGFwZXJQcm9wcyIsImNsYXNzZXMiLCJyb290Iiwic3giLCJiYWNrZ3JvdW5kQ29sb3IiLCJmb250RmFtaWx5IiwiZm9udEZlYXR1cmVTZXR0aW5ncyIsImNvbG9yIiwibXVpRWRpdFJvd0RpYWxvZ1Byb3BzIiwiZGlzcGxheSIsInpJbmRleCIsImVkaXREaXNwbGF5TW9kZSIsImNyZWF0ZURpc3BsYXlNb2RlIiwib25FZGl0aW5nUm93U2F2ZSIsInZhbHVlcyIsInJlc3QiLCJyZXZhbGlkYXRlIiwib25TdWNjZXNzIiwic2V0RWRpdGluZ1JvdyIsIm9uRXJyb3IiLCJyZXNwb25zZSIsInN0YXR1c1RleHQiLCJvbkVkaXRpbmdSb3dDYW5jZWwiLCJvbkNyZWF0aW5nUm93U2F2ZSIsInNldENyZWF0aW5nUm93Iiwib25DcmVhdGluZ1Jvd0NhbmNlbCIsIm11aVRhYmxlRm9vdGVyUHJvcHMiLCJtdWlUYWJsZUNvbnRhaW5lclByb3BzIiwiZ2V0U3RhdGUiLCJpc0Z1bGxTY3JlZW4iLCJyZWZzIiwidG9wVG9vbGJhclJlZiIsIm9mZnNldEhlaWdodCIsImJvdHRvbVRvb2xiYXJSZWYiLCJtdWlQYWdpbmF0aW9uUHJvcHMiLCJtdWlUb3BUb29sYmFyUHJvcHMiLCJtdWlUYWJsZUJvZHlQcm9wcyIsInJlbmRlclRvcFRvb2xiYXJDdXN0b21BY3Rpb25zIiwiZGlyZWN0aW9uIiwic3BhY2luZyIsIkkiLCJhIiwiaWNvbiIsInJvdW5kZWQiLCJhcmlhLWNvbnRyb2xzIiwidW5kZWZpbmVkIiwiYXJpYS1oYXNwb3B1cCIsImFyaWEtZXhwYW5kZWQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJnZXRJc1NvbWVSb3dzU2VsZWN0ZWQiLCJtdWlEZXRhaWxQYW5lbFByb3BzIiwidGhlbWUiLCJwYWxldHRlIiwibW9kZSIsInJlbmRlckNyZWF0ZVJvd0RpYWxvZ0NvbnRlbnQiLCJpbnRlcm5hbEVkaXRDb21wb25lbnRzIiwicG9zaXRpb24iLCJzcGFuIiwidmFyaWFudCIsInZpc2libGUiLCJvbkhpZGUiLCJmbGV4RGlyZWN0aW9uIiwiZ2FwIiwicmVuZGVyRWRpdFJvd0RpYWxvZ0NvbnRlbnQiLCJoMyIsInJlbmRlckRldGFpbFBhbmVsIiwicmVuZGVyUm93QWN0aW9ucyIsIm91dGxpbmVkIiwibWVzc2FnZSIsImFjY2VwdENsYXNzTmFtZSIsImFjY2VwdExhYmVsIiwicmVqZWN0TGFiZWwiLCJhY2NlcHQiLCJyZWplY3QiLCJyZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx\n"));

/***/ })

});