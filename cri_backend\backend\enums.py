from django.db import models
import datetime
from django.utils.translation import gettext_lazy as _
def year_choices():
    return [
        (r, r)
        for r in range(datetime.date.today().year, datetime.date.today().year + 100)
    ]


def current_year():
    return datetime.date.today().year


# Choices
class DegreeImpact(models.TextChoices):
    MODERATE = "Modéré"
    CRITICAL = "Critique"
    LOW = "Faible"
    HIGH = "Elevé"


class OccurProbImpact(models.TextChoices):
    RARE = "Rare"
    OCCAS = "Occasionel"
    FREQ = "Fréquent"
    IMPROB = "Imp"


class PlanType(models.TextChoices):
    AUDIT_INTERN = "Audit Interne"
    CTRL_INTERN = "Contrôle Interne"
    HORS_PLAN = "Hors Plan"


class DocumentTypeEnum(models.TextChoices):
    MISSION = "MISSION"
    ACTION = "ACTION"
    RECOMMENDATION = "RECOMMENDATION"


class RecommendationPriorityChoice(models.TextChoices):
    LOW = "FAIBLE"
    NORMAL = "NORMALE"
    HIGH = "ELEVEE"


# class COMMENT_SUBJECT(models.TextChoices):
#     problem  =  "Problème"
#     fact =    "Faît"
#     causes =    "Causes"
#     consequeces =    "Conséquences"
#     recommendations = "Recommendations"


class MissionType(models.TextChoices):
    COMMANDED = "Commandée"
    PLANIFIED = "Planifiée"
    AVIS_CONSEIL = "Avis & Conseils"


class MissionEtat(models.TextChoices):
    NotSarted = "Non Lancée"
    Suspended = "Suspendue"
    InProgress = "En cours"
    Closed = "Clôturée"


class ActionEtat(models.TextChoices):
    Accomplished = "Réalisée"
    Notaccomplished = "Non Réalisée"
    InProgress = "En cours"


class RecommendationEtat(models.TextChoices):
    Accomplished = "Réalisée"
    Notaccomplished = "Non Réalisée"
    InProgress = "En cours"


class RecommendationEtat2(models.TextChoices):
    RT = "Retenue"
    NRT = "Non Retenue"
    NCRN = "Non Concerné"


# class SH_lQS_LOCATION(models.TextChoices):
#     ORN = "ORN","Oran"
#     ARZ = "ARZ","Arzew"
#     SKD = "SKD","Skikda"


class THEME_PROPOSING_ENTITY(models.TextChoices):
    VP = ("Vice Président", _("Vice Président"))
    CI = ("Contrôle Interne", _("Contrôle Interne"))
    AI = ("Audit Interne", _("Audit Interne"))
    STRUCT = ("Structures", _("Structures"))

class ActionStateEnum(models.TextChoices):
    Valide = "Validé"
    nonValide = "Non Validé"
    Accepte = "Accepté"
    nonAccepte="Non Accepté"

class ActionTypeEnum(models.TextChoices):
    Validation = "Validation"
    Approbation = "Approbation"
    Acceptation = "Acceptation"

class RecommendationActionTypeEnum(models.TextChoices):
    accepted = "Retenue"
    not_accepted = "Non Retenue"
    not_concerned = "Non Concerné"

class ValidationEnum(models.TextChoices):
    YES ="Validé"
    NO = "Non Validé"
    AC = "Accepté"
    NA = "Non Accepté"
    