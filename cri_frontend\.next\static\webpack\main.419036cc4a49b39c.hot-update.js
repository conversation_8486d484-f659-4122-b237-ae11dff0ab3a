"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return Router;\n    },\n    matchesMiddleware: function() {\n        return matchesMiddleware;\n    },\n    createKey: function() {\n        return createKey;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"./node_modules/next/dist/client/route-loader.js\");\nconst _script = __webpack_require__(/*! ../../../client/script */ \"./node_modules/next/dist/client/script.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nconst _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"./node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../mitt */ \"./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _utils = __webpack_require__(/*! ../utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nconst _resolverewrites = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"?506d\"));\nconst _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nconst _formaturl = __webpack_require__(/*! ./utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"./node_modules/next/dist/client/detect-domain-locale.js\");\nconst _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"./node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nconst _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"./node_modules/next/dist/client/remove-locale.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"./node_modules/next/dist/client/remove-base-path.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nconst _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"./node_modules/next/dist/lib/is-api-route.js\");\nconst _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nconst _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"./node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nconst _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"./node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nconst _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _isbot = __webpack_require__(/*! ./utils/is-bot */ \"./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _omit = __webpack_require__(/*! ./utils/omit */ \"./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _resolvehref = __webpack_require__(/*! ./utils/resolve-href */ \"./node_modules/next/dist/shared/lib/router/utils/resolve-href.js\");\nconst _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nfunction buildCancellationError() {\n    return Object.assign(new Error(\"Route Cancelled\"), {\n        cancelled: true\n    });\n}\nasync function matchesMiddleware(options) {\n    const matchers = await Promise.resolve(options.router.pageLoader.getMiddleware());\n    if (!matchers) return false;\n    const { pathname: asPathname } = (0, _parsepath.parsePath)(options.asPath);\n    // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n    const cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n    const asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n    // Check only path match on client. Matching \"has\" should be done on server\n    // where we can access more info such as headers, HttpOnly cookie, etc.\n    return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils.getLocationOrigin)();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, url, true);\n    const origin = (0, _utils.getLocationOrigin)();\n    const hrefWasAbsolute = resolvedHref.startsWith(origin);\n    const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n    const preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n    if (cleanPathname === \"/404\" || cleanPathname === \"/_error\") {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get(\"x-nextjs-rewrite\");\n    let rewriteTarget = rewriteHeader || response.headers.get(\"x-nextjs-matched-path\");\n    const matchedPath = response.headers.get(\"x-matched-path\");\n    if (matchedPath && !rewriteTarget && !matchedPath.includes(\"__next_data_catchall\") && !matchedPath.includes(\"/_error\") && !matchedPath.includes(\"/404\")) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith(\"/\") || undefined) {\n            const parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n            const pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)()\n            ]).then((param)=>{\n                let [pages, { __rewrites: rewrites }] = param;\n                let as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n                        nextConfig:  false ? 0 : nextConfig,\n                        parseData: true\n                    });\n                    as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (false) {} else if (!pages.includes(fsPathname)) {\n                    const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n                    if (resolvedPathname !== fsPathname) {\n                        fsPathname = resolvedPathname;\n                    }\n                }\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n                    const matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: \"rewrite\",\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsepath.parsePath)(source);\n        const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n            ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                nextConfig,\n                parseData: true\n            }),\n            defaultLocale: options.router.defaultLocale,\n            buildId: \"\"\n        });\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: \"\" + pathname + src.query + src.hash\n        });\n    }\n    const redirectTarget = response.headers.get(\"x-nextjs-redirect\");\n    if (redirectTarget) {\n        if (redirectTarget.startsWith(\"/\")) {\n            const src = (0, _parsepath.parsePath)(redirectTarget);\n            const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n                ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                    nextConfig,\n                    parseData: true\n                }),\n                defaultLocale: options.router.defaultLocale,\n                buildId: \"\"\n            });\n            return Promise.resolve({\n                type: \"redirect-internal\",\n                newAs: \"\" + pathname + src.query + src.hash,\n                newUrl: \"\" + pathname + src.query + src.hash\n            });\n        }\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: \"next\"\n    });\n}\nasync function withMiddlewareEffects(options) {\n    const matches = await matchesMiddleware(options);\n    if (!matches || !options.fetchData) {\n        return null;\n    }\n    try {\n        const data = await options.fetchData();\n        const effect = await getMiddlewareData(data.dataHref, data.response, options);\n        return {\n            dataHref: data.dataHref,\n            json: data.json,\n            response: data.response,\n            text: data.text,\n            cacheKey: data.cacheKey,\n            effect\n        };\n    } catch (e) {\n        /**\n     * TODO: Revisit this in the future.\n     * For now we will not consider middleware data errors to be fatal.\n     * maybe we should revisit in the future.\n     */ return null;\n    }\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol(\"SSG_DATA_NOT_FOUND\");\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: \"same-origin\",\n        method: options.method || \"GET\",\n        headers: Object.assign({}, options.headers, {\n            \"x-nextjs-data\": \"1\"\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    let { dataHref, inflightCache, isPrefetch, hasMiddleware, isServerRender, parseJSON, persistCache, isBackground, unstable_skipClientCache } = param;\n    const { href: cacheKey } = new URL(dataHref, window.location.href);\n    var _params_method;\n    const getData = (params)=>{\n        return fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: \"prefetch\"\n            } : {}, isPrefetch && hasMiddleware ? {\n                \"x-middleware-prefetch\": \"1\"\n            } : {}),\n            method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : \"GET\"\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === \"HEAD\") {\n                return {\n                    dataHref,\n                    response,\n                    text: \"\",\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var _tryToParseAsJSON;\n                        if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = new Error(\"Failed to load static props\");\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeloader.markAssetError)(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== \"production\" || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === \"Failed to fetch\" || // firefox\n            err.message === \"NetworkError when attempting to fetch resource.\" || // safari\n            err.message === \"Load failed\") {\n                (0, _routeloader.markAssetError)(err);\n            }\n            throw err;\n        });\n    };\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            inflightCache[cacheKey] = Promise.resolve(data);\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: \"HEAD\"\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    let { url, router } = param;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n        throw new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href);\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = (param)=>{\n    let { route, router } = param;\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = new Error('Abort fetching component for route: \"' + route + '\"');\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Go forward in history\n   */ forward() {\n        window.history.forward();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as, options) {\n        if (options === void 0) options = {};\n        if (false) {}\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"pushState\", url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as, options) {\n        if (options === void 0) options = {};\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change(\"replaceState\", url, as, options);\n    }\n    async _bfl(as, resolvedAs, locale, skipNavigate) {\n        if (true) {\n            let matchesBflStatic = false;\n            let matchesBflDynamic = false;\n            for (const curAs of [\n                as,\n                resolvedAs\n            ]){\n                if (curAs) {\n                    const asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, \"http://n\").pathname);\n                    const asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || this.locale));\n                    if (asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(this.asPath, \"http://n\").pathname)) {\n                        var _this__bfl_s, _this__bfl_s1;\n                        matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n                        for (const normalizedAS of [\n                            asNoSlash,\n                            asNoSlashLocale\n                        ]){\n                            // if any sub-path of as matches a dynamic filter path\n                            // it should be hard navigated\n                            const curAsParts = normalizedAS.split(\"/\");\n                            for(let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                var _this__bfl_d;\n                                const currentPart = curAsParts.slice(0, i).join(\"/\");\n                                if (currentPart && ((_this__bfl_d = this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                                    matchesBflDynamic = true;\n                                    break;\n                                }\n                            }\n                        }\n                        // if the client router filter is matched then we trigger\n                        // a hard navigation\n                        if (matchesBflStatic || matchesBflDynamic) {\n                            if (skipNavigate) {\n                                return true;\n                            }\n                            handleHardNavigation({\n                                url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                                router: this\n                            });\n                            return new Promise(()=>{});\n                        }\n                    }\n                }\n            }\n        }\n        return false;\n    }\n    async change(method, url, as, options, forcedScroll) {\n        var _this_components_pathname;\n        if (!(0, _islocalurl.isLocalURL)(url)) {\n            handleHardNavigation({\n                url,\n                router: this\n            });\n            return false;\n        }\n        // WARNING: `_h` is an internal option for handing Next.js client-side\n        // hydration. Your app should _never_ use this property. It may change at\n        // any time without notice.\n        const isQueryUpdating = options._h === 1;\n        if (!isQueryUpdating && !options.shallow) {\n            await this._bfl(as, undefined, options.locale);\n        }\n        let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n        const nextState = {\n            ...this.state\n        };\n        // for static pages with query params in the URL we delay\n        // marking the router ready until after the query is updated\n        // or a navigation has occurred\n        const readyStateChange = this.isReady !== true;\n        this.isReady = true;\n        const isSsr = this.isSsr;\n        if (!isQueryUpdating) {\n            this.isSsr = false;\n        }\n        // if a route transition is already in progress before\n        // the query updating is triggered ignore query updating\n        if (isQueryUpdating && this.clc) {\n            return false;\n        }\n        const prevLocale = nextState.locale;\n        if (false) { var _this_locales; }\n        // marking route changes as a navigation start entry\n        if (_utils.ST) {\n            performance.mark(\"routeChange\");\n        }\n        const { shallow = false, scroll = true } = options;\n        const routeProps = {\n            shallow\n        };\n        if (this._inFlightRoute && this.clc) {\n            if (!isSsr) {\n                Router.events.emit(\"routeChangeError\", buildCancellationError(), this._inFlightRoute, routeProps);\n            }\n            this.clc();\n            this.clc = null;\n        }\n        as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, this.defaultLocale));\n        const cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n        this._inFlightRoute = as;\n        const localeChange = prevLocale !== nextState.locale;\n        // If the url change is only related to a hash change\n        // We should not proceed. We should only change the state.\n        if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n            nextState.asPath = cleanedAs;\n            Router.events.emit(\"hashChangeStart\", as, routeProps);\n            // TODO: do we need the resolved href when only a hash change?\n            this.changeState(method, url, as, {\n                ...options,\n                scroll: false\n            });\n            if (scroll) {\n                this.scrollToHash(cleanedAs);\n            }\n            try {\n                await this.set(nextState, this.components[nextState.route], null);\n            } catch (err) {\n                if ((0, _iserror.default)(err) && err.cancelled) {\n                    Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                }\n                throw err;\n            }\n            Router.events.emit(\"hashChangeComplete\", as, routeProps);\n            return true;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        let { pathname, query } = parsed;\n        // if we detected the path as app route during prefetching\n        // trigger hard navigation\n        if ((_this_components_pathname = this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return new Promise(()=>{});\n        }\n        // The build manifest needs to be loaded before auto-static dynamic pages\n        // get their query parameters to allow ensuring they can be parsed properly\n        // when rewritten to\n        let pages, rewrites;\n        try {\n            [pages, { __rewrites: rewrites }] = await Promise.all([\n                this.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)(),\n                this.pageLoader.getMiddleware()\n            ]);\n        } catch (err) {\n            // If we fail to resolve the page list or client-build manifest, we must\n            // do a server-side transition:\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        // If asked to change the current URL we should reload the current page\n        // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n        // We also need to set the method = replaceState always\n        // as this should not go into the history (That's how browsers work)\n        // We should compare the new asPath to the current asPath, not the url\n        if (!this.urlIsNew(cleanedAs) && !localeChange) {\n            method = \"replaceState\";\n        }\n        // we need to resolve the as value using rewrites for dynamic SSG\n        // pages to allow building the data URL correctly\n        let resolvedAs = as;\n        // url and as should always be prefixed with basePath by this\n        // point by either next/link or router.push/replace so strip the\n        // basePath from the pathname to match the pages dir 1-to-1\n        pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n        let route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        const parsedAsPathname = as.startsWith(\"/\") && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n        const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n        // we don't attempt resolve asPath when we need to execute\n        // middleware as the resolving will occur server-side\n        const isMiddlewareMatch = !options.shallow && await matchesMiddleware({\n            asPath: as,\n            locale: nextState.locale,\n            router: this\n        });\n        if (isQueryUpdating && isMiddlewareMatch) {\n            shouldResolveHref = false;\n        }\n        if (shouldResolveHref && pathname !== \"/_error\") {\n            options._shouldResolveHref = true;\n            if (false) {} else {\n                parsed.pathname = resolveDynamicRoute(pathname, pages);\n                if (parsed.pathname !== pathname) {\n                    pathname = parsed.pathname;\n                    parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                    if (!isMiddlewareMatch) {\n                        url = (0, _formaturl.formatWithValidation)(parsed);\n                    }\n                }\n            }\n        }\n        if (!(0, _islocalurl.isLocalURL)(as)) {\n            if (true) {\n                throw new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\");\n            }\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n        route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        let routeMatch = false;\n        if ((0, _isdynamic.isDynamicRoute)(route)) {\n            const parsedAs = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n            const asPathname = parsedAs.pathname;\n            const routeRegex = (0, _routeregex.getRouteRegex)(route);\n            routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n            const shouldInterpolate = route === asPathname;\n            const interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n            if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param] && !routeRegex.groups[param].optional);\n                if (missingParams.length > 0 && !isMiddlewareMatch) {\n                    if (true) {\n                        console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(\", \") + \" in the `href`'s `query`\"));\n                    }\n                    throw new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(\", \") + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? \"href-interpolation-failed\" : \"incompatible-href-as\")));\n                }\n            } else if (shouldInterpolate) {\n                as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs, {\n                    pathname: interpolatedAs.result,\n                    query: (0, _omit.omit)(query, interpolatedAs.params)\n                }));\n            } else {\n                // Merge params into `query`, overwriting any specified in search\n                Object.assign(query, routeMatch);\n            }\n        }\n        if (!isQueryUpdating) {\n            Router.events.emit(\"routeChangeStart\", as, routeProps);\n        }\n        const isErrorRoute = this.pathname === \"/404\" || this.pathname === \"/_error\";\n        try {\n            var _self___NEXT_DATA___props, _self___NEXT_DATA___props_pageProps, _routeInfo_props;\n            let routeInfo = await this.getRouteInfo({\n                route,\n                pathname,\n                query,\n                as,\n                resolvedAs,\n                routeProps,\n                locale: nextState.locale,\n                isPreview: nextState.isPreview,\n                hasMiddleware: isMiddlewareMatch,\n                unstable_skipClientCache: options.unstable_skipClientCache,\n                isQueryUpdating: isQueryUpdating && !this.isFallback,\n                isMiddlewareRewrite\n            });\n            if (!isQueryUpdating && !options.shallow) {\n                await this._bfl(as, \"resolvedAs\" in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n            }\n            if (\"route\" in routeInfo && isMiddlewareMatch) {\n                pathname = routeInfo.route || route;\n                route = pathname;\n                if (!routeProps.shallow) {\n                    query = Object.assign({}, routeInfo.query || {}, query);\n                }\n                const cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n                if (routeMatch && pathname !== cleanedParsedPathname) {\n                    Object.keys(routeMatch).forEach((key)=>{\n                        if (routeMatch && query[key] === routeMatch[key]) {\n                            delete query[key];\n                        }\n                    });\n                }\n                if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n                    const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n                    let rewriteAs = prefixedAs;\n                    if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n                        rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n                    }\n                    if (false) {}\n                    const routeRegex = (0, _routeregex.getRouteRegex)(pathname);\n                    const curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(new URL(rewriteAs, location.href).pathname);\n                    if (curRouteMatch) {\n                        Object.assign(query, curRouteMatch);\n                    }\n                }\n            }\n            // If the routeInfo brings a redirect we simply apply it.\n            if (\"type\" in routeInfo) {\n                if (routeInfo.type === \"redirect-internal\") {\n                    return this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                } else {\n                    handleHardNavigation({\n                        url: routeInfo.destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n            }\n            const component = routeInfo.Component;\n            if (component && component.unstable_scriptLoader) {\n                const scripts = [].concat(component.unstable_scriptLoader());\n                scripts.forEach((script)=>{\n                    (0, _script.handleClientScriptLoad)(script.props);\n                });\n            }\n            // handle redirect on client-transition\n            if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n                if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                    // Use the destination from redirect without adding locale\n                    options.locale = false;\n                    const destination = routeInfo.props.pageProps.__N_REDIRECT;\n                    // check if destination is internal (resolves to a page) and attempt\n                    // client-navigation if it is falling back to hard navigation if\n                    // it's not\n                    if (destination.startsWith(\"/\") && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                        const parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n                        parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                        const { url: newUrl, as: newAs } = prepareUrlAs(this, destination, destination);\n                        return this.change(method, newUrl, newAs, options);\n                    }\n                    handleHardNavigation({\n                        url: destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                // handle SSG data 404\n                if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n                    let notFoundRoute;\n                    try {\n                        await this.fetchComponent(\"/404\");\n                        notFoundRoute = \"/404\";\n                    } catch (_) {\n                        notFoundRoute = \"/_error\";\n                    }\n                    routeInfo = await this.getRouteInfo({\n                        route: notFoundRoute,\n                        pathname: notFoundRoute,\n                        query,\n                        as,\n                        resolvedAs,\n                        routeProps: {\n                            shallow: false\n                        },\n                        locale: nextState.locale,\n                        isPreview: nextState.isPreview,\n                        isNotFound: true\n                    });\n                    if (\"type\" in routeInfo) {\n                        throw new Error(\"Unexpected middleware effect on /404\");\n                    }\n                }\n            }\n            if (isQueryUpdating && this.pathname === \"/_error\" && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n                // ensure statusCode is still correct for static 500 page\n                // when updating query information\n                routeInfo.props.pageProps.statusCode = 500;\n            }\n            var _routeInfo_route;\n            // shallow routing is only allowed for same page URL changes.\n            const isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n            var _options_scroll;\n            const shouldScroll = (_options_scroll = options.scroll) != null ? _options_scroll : !isQueryUpdating && !isValidShallowRoute;\n            const resetScroll = shouldScroll ? {\n                x: 0,\n                y: 0\n            } : null;\n            const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n            // the new state that the router gonna set\n            const upcomingRouterState = {\n                ...nextState,\n                route,\n                pathname,\n                query,\n                asPath: cleanedAs,\n                isFallback: false\n            };\n            // When the page being rendered is the 404 page, we should only update the\n            // query parameters. Route changes here might add the basePath when it\n            // wasn't originally present. This is also why this block is before the\n            // below `changeState` call which updates the browser's history (changing\n            // the URL).\n            if (isQueryUpdating && isErrorRoute) {\n                var _self___NEXT_DATA___props1, _self___NEXT_DATA___props_pageProps1, _routeInfo_props1;\n                routeInfo = await this.getRouteInfo({\n                    route: this.pathname,\n                    pathname: this.pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps: {\n                        shallow: false\n                    },\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    isQueryUpdating: isQueryUpdating && !this.isFallback\n                });\n                if (\"type\" in routeInfo) {\n                    throw new Error(\"Unexpected middleware effect on \" + this.pathname);\n                }\n                if (this.pathname === \"/_error\" && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    routeInfo.props.pageProps.statusCode = 500;\n                }\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (err) {\n                    if ((0, _iserror.default)(err) && err.cancelled) {\n                        Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                return true;\n            }\n            Router.events.emit(\"beforeHistoryChange\", as, routeProps);\n            this.changeState(method, url, as, options);\n            // for query updates we can skip it if the state is unchanged and we don't\n            // need to scroll\n            // https://github.com/vercel/next.js/issues/37139\n            const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, this.state);\n            if (!canSkipUpdating) {\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (e) {\n                    if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                    else throw e;\n                }\n                if (routeInfo.error) {\n                    if (!isQueryUpdating) {\n                        Router.events.emit(\"routeChangeError\", routeInfo.error, cleanedAs, routeProps);\n                    }\n                    throw routeInfo.error;\n                }\n                if (false) {}\n                if (!isQueryUpdating) {\n                    Router.events.emit(\"routeChangeComplete\", as, routeProps);\n                }\n                // A hash mark # is the optional last part of a URL\n                const hashRegex = /#.+$/;\n                if (shouldScroll && hashRegex.test(as)) {\n                    this.scrollToHash(as);\n                }\n            }\n            return true;\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.cancelled) {\n                return false;\n            }\n            throw err;\n        }\n    }\n    changeState(method, url, as, options) {\n        if (options === void 0) options = {};\n        if (true) {\n            if (typeof window.history === \"undefined\") {\n                console.error(\"Warning: window.history is not available.\");\n                return;\n            }\n            if (typeof window.history[method] === \"undefined\") {\n                console.error(\"Warning: window.history.\" + method + \" is not available\");\n                return;\n            }\n        }\n        if (method !== \"pushState\" || (0, _utils.getURL)() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== \"pushState\" ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/en-US/docs/Web/API/History/replaceState\n            \"\", as);\n        }\n    }\n    async handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        console.error(err);\n        if (err.cancelled) {\n            // bubble up cancellation errors\n            throw err;\n        }\n        if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n            Router.events.emit(\"routeChangeError\", err, as, routeProps);\n            // If we can't load the page it could be one of following reasons\n            //  1. Page doesn't exists\n            //  2. Page does exist in a different zone\n            //  3. Internal error while loading the page\n            // So, doing a hard reload is the proper way to deal with this.\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            // Changing the URL doesn't block executing the current code path.\n            // So let's throw a cancellation error stop the routing logic.\n            throw buildCancellationError();\n        }\n        try {\n            let props;\n            const { page: Component, styleSheets } = await this.fetchComponent(\"/_error\");\n            const routeInfo = {\n                props,\n                Component,\n                styleSheets,\n                err,\n                error: err\n            };\n            if (!routeInfo.props) {\n                try {\n                    routeInfo.props = await this.getInitialProps(Component, {\n                        err,\n                        pathname,\n                        query\n                    });\n                } catch (gipErr) {\n                    console.error(\"Error in error page `getInitialProps`: \", gipErr);\n                    routeInfo.props = {};\n                }\n            }\n            return routeInfo;\n        } catch (routeInfoErr) {\n            return this.handleRouteInfoError((0, _iserror.default)(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + \"\"), pathname, query, as, routeProps, true);\n        }\n    }\n    async getRouteInfo(param) {\n        let { route: requestedRoute, pathname, query, as, resolvedAs, routeProps, locale, hasMiddleware, isPreview, unstable_skipClientCache, isQueryUpdating, isMiddlewareRewrite, isNotFound } = param;\n        /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n        try {\n            var _data_effect, _data_effect1, _data_effect2, _data_response;\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: this\n            });\n            let existingInfo = this.components[route];\n            if (routeProps.shallow && existingInfo && this.route === route) {\n                return existingInfo;\n            }\n            if (hasMiddleware) {\n                existingInfo = undefined;\n            }\n            let cachedRouteInfo = existingInfo && !(\"initial\" in existingInfo) && \"development\" !== \"development\" ? 0 : undefined;\n            const isBackground = isQueryUpdating;\n            const fetchNextDataParams = {\n                dataHref: this.pageLoader.getDataHref({\n                    href: (0, _formaturl.formatWithValidation)({\n                        pathname,\n                        query\n                    }),\n                    skipInterpolation: true,\n                    asPath: isNotFound ? \"/404\" : resolvedAs,\n                    locale\n                }),\n                hasMiddleware: true,\n                isServerRender: this.isSsr,\n                parseJSON: true,\n                inflightCache: isBackground ? this.sbc : this.sdc,\n                persistCache: !isPreview,\n                isPrefetch: false,\n                unstable_skipClientCache,\n                isBackground\n            };\n            let data = isQueryUpdating && !isMiddlewareRewrite ? null : await withMiddlewareEffects({\n                fetchData: ()=>fetchNextData(fetchNextDataParams),\n                asPath: isNotFound ? \"/404\" : resolvedAs,\n                locale: locale,\n                router: this\n            }).catch((err)=>{\n                // we don't hard error during query updating\n                // as it's un-necessary and doesn't need to be fatal\n                // unless it is a fallback route and the props can't\n                // be loaded\n                if (isQueryUpdating) {\n                    return null;\n                }\n                throw err;\n            });\n            // when rendering error routes we don't apply middleware\n            // effects\n            if (data && (pathname === \"/_error\" || pathname === \"/404\")) {\n                data.effect = undefined;\n            }\n            if (isQueryUpdating) {\n                if (!data) {\n                    data = {\n                        json: self.__NEXT_DATA__.props\n                    };\n                } else {\n                    data.json = self.__NEXT_DATA__.props;\n                }\n            }\n            handleCancelled();\n            if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === \"redirect-internal\" || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === \"redirect-external\") {\n                return data.effect;\n            }\n            if ((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === \"rewrite\") {\n                const resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n                const pages = await this.pageLoader.getPageList();\n                // during query updating the page must match although during\n                // client-transition a redirect that doesn't match a page\n                // can be returned and this should trigger a hard navigation\n                // which is valid for incremental migration\n                if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                    route = resolvedRoute;\n                    pathname = data.effect.resolvedHref;\n                    query = {\n                        ...query,\n                        ...data.effect.parsedAs.query\n                    };\n                    resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, this.locales).pathname);\n                    // Check again the cache with the new destination.\n                    existingInfo = this.components[route];\n                    if (routeProps.shallow && existingInfo && this.route === route && !hasMiddleware) {\n                        // If we have a match with the current route due to rewrite,\n                        // we can copy the existing information to the rewritten one.\n                        // Then, we return the information along with the matched route.\n                        return {\n                            ...existingInfo,\n                            route\n                        };\n                    }\n                }\n            }\n            if ((0, _isapiroute.isAPIRoute)(route)) {\n                handleHardNavigation({\n                    url: as,\n                    router: this\n                });\n                return new Promise(()=>{});\n            }\n            const routeInfo = cachedRouteInfo || await this.fetchComponent(route).then((res)=>({\n                    Component: res.page,\n                    styleSheets: res.styleSheets,\n                    __N_SSG: res.mod.__N_SSG,\n                    __N_SSP: res.mod.__N_SSP\n                }));\n            if (true) {\n                const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"./node_modules/next/dist/compiled/react-is/index.js\");\n                if (!isValidElementType(routeInfo.Component)) {\n                    throw new Error('The default export is not a React Component in page: \"' + pathname + '\"');\n                }\n            }\n            const wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get(\"x-middleware-skip\");\n            const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n            // For non-SSG prefetches that bailed before sending data\n            // we clear the cache to fetch full response\n            if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                delete this.sdc[data.dataHref];\n            }\n            const { props, cacheKey } = await this._getData(async ()=>{\n                if (shouldFetchData) {\n                    if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                        return {\n                            cacheKey: data.cacheKey,\n                            props: data.json\n                        };\n                    }\n                    const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname,\n                            query\n                        }),\n                        asPath: resolvedAs,\n                        locale\n                    });\n                    const fetched = await fetchNextData({\n                        dataHref,\n                        isServerRender: this.isSsr,\n                        parseJSON: true,\n                        inflightCache: wasBailedPrefetch ? {} : this.sdc,\n                        persistCache: !isPreview,\n                        isPrefetch: false,\n                        unstable_skipClientCache\n                    });\n                    return {\n                        cacheKey: fetched.cacheKey,\n                        props: fetched.json || {}\n                    };\n                }\n                return {\n                    headers: {},\n                    props: await this.getInitialProps(routeInfo.Component, {\n                        pathname,\n                        query,\n                        asPath: as,\n                        locale,\n                        locales: this.locales,\n                        defaultLocale: this.defaultLocale\n                    })\n                };\n            });\n            // Only bust the data cache for SSP routes although\n            // middleware can skip cache per request with\n            // x-middleware-cache: no-cache as well\n            if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                delete this.sdc[cacheKey];\n            }\n            // we kick off a HEAD request in the background\n            // when a non-prefetch request is made to signal revalidation\n            if (!this.isPreview && routeInfo.__N_SSG && \"development\" !== \"development\" && 0) {}\n            props.pageProps = Object.assign({}, props.pageProps);\n            routeInfo.props = props;\n            routeInfo.route = route;\n            routeInfo.query = query;\n            routeInfo.resolvedAs = resolvedAs;\n            this.components[route] = routeInfo;\n            return routeInfo;\n        } catch (err) {\n            return this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps);\n        }\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components[\"/_app\"].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split(\"#\");\n        const [newUrlNoHash, newHash] = as.split(\"#\");\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = \"\"] = as.split(\"#\");\n        // Scroll to top if the hash is just `#` with no value or `#top`\n        // To mirror browsers\n        if (hash === \"\" || hash === \"top\") {\n            (0, _handlesmoothscroll.handleSmoothScroll)(()=>window.scrollTo(0, 0));\n            return;\n        }\n        // Decode hash to make non-latin anchor works.\n        const rawHash = decodeURIComponent(hash);\n        // First we check if the element by id is found\n        const idEl = document.getElementById(rawHash);\n        if (idEl) {\n            (0, _handlesmoothscroll.handleSmoothScroll)(()=>idEl.scrollIntoView());\n            return;\n        }\n        // If there's no element with the id, we check the `name` property\n        // To mirror browsers\n        const nameEl = document.getElementsByName(rawHash)[0];\n        if (nameEl) {\n            (0, _handlesmoothscroll.handleSmoothScroll)(()=>nameEl.scrollIntoView());\n        }\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ async prefetch(url, asPath, options) {\n        if (asPath === void 0) asPath = url;\n        if (options === void 0) options = {};\n        // Prefetch is not supported in development mode because it would trigger on-demand-entries\n        if (true) {\n            return;\n        }\n        if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n            // No prefetches for bots that render the link since they are typically navigating\n            // links via the equivalent of a hard navigation and hence never utilize these\n            // prefetches.\n            return;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        const urlPathname = parsed.pathname;\n        let { pathname, query } = parsed;\n        const originalPathname = pathname;\n        if (false) {}\n        const pages = await this.pageLoader.getPageList();\n        let resolvedAs = asPath;\n        const locale = typeof options.locale !== \"undefined\" ? options.locale || undefined : this.locale;\n        const isMiddlewareMatch = await matchesMiddleware({\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        if (false) {}\n        parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n        if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n            pathname = parsed.pathname;\n            parsed.pathname = pathname;\n            Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n            if (!isMiddlewareMatch) {\n                url = (0, _formaturl.formatWithValidation)(parsed);\n            }\n        }\n        const data =  false ? 0 : await withMiddlewareEffects({\n            fetchData: ()=>fetchNextData({\n                    dataHref: this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname: originalPathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: this.isSsr,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true\n                }),\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === \"rewrite\") {\n            parsed.pathname = data.effect.resolvedHref;\n            pathname = data.effect.resolvedHref;\n            query = {\n                ...query,\n                ...data.effect.parsedAs.query\n            };\n            resolvedAs = data.effect.parsedAs.pathname;\n            url = (0, _formaturl.formatWithValidation)(parsed);\n        }\n        /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === \"redirect-external\") {\n            return;\n        }\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n            this.components[urlPathname] = {\n                __appRouter: true\n            };\n        }\n        await Promise.all([\n            this.pageLoader._isSsg(route).then((isSsg)=>{\n                return isSsg ? fetchNextData({\n                    dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : this.pageLoader.getDataHref({\n                        href: url,\n                        asPath: resolvedAs,\n                        locale: locale\n                    }),\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true,\n                    unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                }).then(()=>false).catch(()=>false) : false;\n            }),\n            this.pageLoader[options.priority ? \"loadPage\" : \"prefetch\"](route)\n        ]);\n    }\n    async fetchComponent(route) {\n        const handleCancelled = getCancelledHandler({\n            route,\n            router: this\n        });\n        try {\n            const componentResult = await this.pageLoader.loadPage(route);\n            handleCancelled();\n            return componentResult;\n        } catch (err) {\n            handleCancelled();\n            throw err;\n        }\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = new Error(\"Loading initial props cancelled\");\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    _getFlightData(dataHref) {\n        // Do not cache RSC flight response since it's not a static resource\n        return fetchNextData({\n            dataHref,\n            isServerRender: true,\n            parseJSON: false,\n            inflightCache: this.sdc,\n            persistCache: false,\n            isPrefetch: false\n        }).then((param)=>{\n            let { text } = param;\n            return {\n                data: text\n            };\n        });\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App } = this.components[\"/_app\"];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils.loadGetInitialProps)(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname, query, as, { initialProps, pageLoader, App, wrapApp, Component, err, subscription, isFallback, locale, locales, defaultLocale, domainLocales, isPreview }){\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname, query } = this;\n                this.changeState(\"replaceState\", (0, _formaturl.formatWithValidation)({\n                    pathname: (0, _addbasepath.addBasePath)(pathname),\n                    query\n                }), (0, _utils.getURL)());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url, as, options, key } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname } = (0, _parserelativeurl.parseRelativeUrl)(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addbasepath.addBasePath)(this.asPath) && pathname === (0, _addbasepath.addBasePath)(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change(\"replaceState\", url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname !== \"/_error\") {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components[\"/_app\"] = {\n            Component: App,\n            styleSheets: []\n        };\n        if (true) {\n            const { BloomFilter } = __webpack_require__(/*! ../../lib/bloom-filter */ \"./node_modules/next/dist/shared/lib/bloom-filter.js\");\n            const staticFilterData = {\"numItems\":46,\"errorRate\":0.01,\"numBits\":441,\"numHashes\":7,\"bitArray\":[1,1,0,1,1,0,0,1,1,0,1,1,0,1,1,1,0,0,1,1,1,1,0,0,1,0,0,0,1,0,0,0,0,0,1,0,0,1,1,0,0,1,1,1,1,0,0,1,0,1,0,0,1,1,0,0,0,0,1,0,0,0,1,1,0,0,0,1,0,1,0,1,1,0,0,1,0,1,1,1,1,0,1,0,1,0,1,1,0,1,1,0,0,1,0,0,1,0,1,1,0,1,1,0,0,1,0,1,1,1,0,0,1,0,0,0,0,1,0,0,1,1,0,0,1,0,0,1,1,1,0,1,1,0,1,0,0,1,0,0,1,1,0,1,1,1,0,1,0,1,1,1,1,1,0,1,1,0,0,0,0,1,1,1,0,1,1,1,0,0,0,0,0,1,1,1,0,1,1,0,0,1,1,0,1,1,1,1,1,1,1,0,0,1,0,1,0,0,1,0,0,0,1,0,1,0,1,1,1,0,0,1,0,1,0,1,0,0,1,1,1,1,0,1,0,1,0,1,1,1,0,0,0,1,0,1,0,0,1,0,1,0,1,0,1,0,0,1,0,1,1,0,1,0,1,1,0,0,0,0,1,1,0,0,0,0,1,1,0,1,1,1,1,1,1,1,0,0,1,1,0,0,1,1,1,1,1,0,1,0,1,1,1,0,1,1,1,0,0,1,0,0,1,1,1,1,1,1,1,1,1,0,0,1,0,1,1,1,1,1,0,1,0,0,0,0,0,0,0,1,0,0,1,1,0,0,1,0,1,1,0,0,1,1,0,0,1,0,1,1,0,0,0,0,1,1,0,0,1,0,1,1,0,0,0,0,0,1,1,1,1,0,0,1,1,1,0,0,1,1,1,0,1,1,0,1,1,0,0,1,1,1,1,0,1,1,1,1,0,0,1,0,0,1,0,1,1,1,1,1,1,0,1,1,0,1,0,0,0,1,0,0,0,1,0,1,1,0,1,1,1,0,0,1,1,1,0,1,0,0,0]};\n            const dynamicFilterData = {\"numItems\":18,\"errorRate\":0.01,\"numBits\":173,\"numHashes\":7,\"bitArray\":[1,0,0,1,1,1,1,0,0,1,0,1,1,0,0,1,1,0,1,0,0,1,0,0,1,0,0,1,0,1,0,1,0,1,1,1,1,1,1,1,0,1,0,0,1,0,0,0,0,1,1,0,0,0,0,1,0,1,1,1,1,0,1,0,0,1,1,1,1,0,1,1,1,1,1,0,1,1,0,0,0,1,0,1,1,0,1,0,0,0,1,0,1,1,0,0,1,1,1,0,1,0,0,0,0,1,1,0,0,1,0,1,0,0,0,1,1,1,1,0,1,1,0,0,1,0,1,1,0,0,1,1,0,0,0,1,1,0,1,0,1,0,1,1,1,1,0,0,1,1,0,1,1,1,0,1,1,1,0,1,1,0,0,1,0,1,1,0,0,1,0,1,1]};\n            if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n                this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n                this._bfl_s.import(staticFilterData);\n            }\n            if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n                this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n                this._bfl_d.import(dynamicFilterData);\n            }\n        }\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || \"\";\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n        if (false) {}\n        this.state = {\n            route,\n            pathname,\n            query,\n            asPath: autoExportDynamic ? pathname : as,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as.startsWith(\"//\")) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                const options = {\n                    locale\n                };\n                const asPath = (0, _utils.getURL)();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale,\n                    asPath\n                }).then((matches)=>{\n                    options._shouldResolveHref = as !== pathname;\n                    this.changeState(\"replaceState\", matches ? asPath : (0, _formaturl.formatWithValidation)({\n                        pathname: (0, _addbasepath.addBasePath)(pathname),\n                        query\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener(\"popstate\", this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n}\n(()=>{\n    Router.events = (0, _mitt.default)();\n})(); //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});