"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx":
/*!*******************************************************************!*\
  !*** ./app/(main)/plans/(components)/GenericTAbleArbitration.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_editor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/editor */ \"(app-client)/./node_modules/primereact/editor/editor.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// import { Editor } from '@tinymce/tinymce-react';\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie, _users, _users1, _plans, _arbitrations;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: plans, isLoading: plans_isLoading, error: plans_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList)();\n    const { data: arbitrations, isLoading: arbitrations_isLoading, error: arbitrations_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList)();\n    const { data: users, isLoading: users_isLoading, error: users_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList)();\n    const users_data = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _users;\n        return ((_users = users) === null || _users === void 0 ? void 0 : _users.data) || [];\n    }, [\n        (_users = users) === null || _users === void 0 ? void 0 : _users.data\n    ]);\n    const [arbitrationID, setArbitrationID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [picklistTargetValueTeam, setPicklistTargetValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [picklistSourceValueTeam, setPicklistSourceValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(users_data);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { mutate: arbitration_create_trigger, isPending: isCreateMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate)();\n    const { mutate: arbitration_patch_trigger, isPending: isPatchMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate)();\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const accept_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"created\",\n                \"modified\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    // Cell: ({ cell }) => <div>{parse(cell.getValue<string>())}</div>,\n                    // Cell: ({ cell }) => { if ([\"description\", \"content\",\"report\"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Rapport\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_editor__WEBPACK_IMPORTED_MODULE_7__.Editor, {\n                                    // initialValue={row.original[key]}\n                                    // tinymceScriptSrc=\"http://localhost:3000/tinymce/tinymce.min.js\"\n                                    // apiKey='none'\n                                    value: row.original.report,\n                                    // onChange={(e) => { row._valuesCache.report = e.target.getContent() }}\n                                    onTextChange: (e)=>{\n                                        row._valuesCache.report = e.htmlValue;\n                                    },\n                                    style: {\n                                        height: \"320px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (key === \"plan\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"plan\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_8__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            children: cell.getValue().code\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 38\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { row } = param;\n                        var _row__valuesCache_plan, _row__valuesCache_plan1, _plans_data, _plans;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_9__.Dropdown, {\n                                    optionLabel: \"name\",\n                                    placeholder: \"Choisir un plan\",\n                                    onChange: (e)=>{\n                                        var _plans, _plans1;\n                                        console.log(e);\n                                        setRowTobe({\n                                            ...rowTobe,\n                                            plan: (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data.find((plan)=>plan.id === e.value.code)\n                                        });\n                                        row._valuesCache = {\n                                            ...row._valuesCache,\n                                            plan: (_plans1 = plans) === null || _plans1 === void 0 ? void 0 : _plans1.find((plan)=>plan.id === e.value.code)\n                                        };\n                                    },\n                                    value: {\n                                        code: ((_row__valuesCache_plan = row._valuesCache.plan) === null || _row__valuesCache_plan === void 0 ? void 0 : _row__valuesCache_plan.id) || null,\n                                        name: ((_row__valuesCache_plan1 = row._valuesCache.plan) === null || _row__valuesCache_plan1 === void 0 ? void 0 : _row__valuesCache_plan1.code) || null\n                                    },\n                                    options: (_plans = plans) === null || _plans === void 0 ? void 0 : (_plans_data = _plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.map((plan)=>{\n                                        return {\n                                            code: plan.id,\n                                            name: plan.type\n                                        };\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            var _data__data_type_properties_key_title3;\n            if (key === \"id\") return {\n                header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                accessorKey: key,\n                id: key,\n                Edit: ()=>null\n            };\n            var _data__data_type_properties_key_title4;\n            if (key === \"team\") return {\n                header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                accessorKey: key,\n                id: key,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: cell.getValue().map((usr)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__.getUserFullname)(usr)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    console.log(\"[ARBITRATION]\", row._valuesCache.team);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"font-bold\",\n                                children: \"Membres\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_10__.PickList, {\n                                source: picklistTargetValueTeam.length === 0 ? picklistSourceValueTeam : picklistSourceValueTeam.filter((user)=>picklistTargetValueTeam.map((user)=>user.username).includes(user.username)),\n                                id: \"picklist_team\",\n                                target: picklistTargetValueTeam.length > 0 ? picklistTargetValueTeam : row._valuesCache.team,\n                                sourceHeader: \"De\",\n                                targetHeader: \"A\",\n                                itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            item.first_name,\n                                            \" \",\n                                            item.last_name\n                                        ]\n                                    }, item.username, true, void 0, void 0),\n                                onChange: (e)=>{\n                                    console.log(\"source Team\", e.source);\n                                    setPicklistSourceValueTeam([\n                                        ...e.source\n                                    ]);\n                                    setPicklistTargetValueTeam([\n                                        ...e.target\n                                    ]);\n                                    row._valuesCache.team = e.target;\n                                },\n                                sourceStyle: {\n                                    height: \"200px\"\n                                },\n                                targetStyle: {\n                                    height: \"200px\"\n                                },\n                                filter: true,\n                                filterBy: \"username,email,first_name,last_name\",\n                                filterMatchMode: \"contains\",\n                                sourceFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\",\n                                targetFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true);\n                }\n            };\n            else {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), [\n        (_users1 = users) === null || _users1 === void 0 ? void 0 : _users1.data,\n        (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data,\n        (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_11__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data ? data_.data_.data : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data ? data_.data_.data.length : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                report: false,\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_12__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onEditingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_patch_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setEditingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" mis \\xe0 ajour\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onCreatingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_create_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setCreatingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" cr\\xe9\\xe9\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"add\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"align-content-center \",\n                                children: \"Cr\\xe9ation nouveau arbitrage\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: createVisible,\n                    onHide: ()=>{\n                        table.setCreatingRow(null);\n                        setCreateVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: internalEditComponents\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 493,\n                columnNumber: 7\n            }, this);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"align-content-center \",\n                                children: [\n                                    \"Editer l'arbitrage n\\xb0 \",\n                                    row.original.id\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: editVisible,\n                    onHide: ()=>{\n                        table.setEditingRow(null);\n                        setEditVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: [\n                            internalEditComponents,\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, this);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.report);\n        },\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"update\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                setArbitrationID(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true);\n                                console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept: accept_row_deletion,\n                                    reject: reject_row_deletion\n                                })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 572,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_19__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 572,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"8DYJ/6Yf89tsHPECLvw8buq1Cu0=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_11__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx\n"));

/***/ })

});