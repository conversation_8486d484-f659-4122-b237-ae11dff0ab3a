"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx":
/*!*******************************************************************!*\
  !*** ./app/(main)/plans/(components)/GenericTAbleArbitration.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_editor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/editor */ \"(app-client)/./node_modules/primereact/editor/editor.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// import { Editor } from '@tinymce/tinymce-react';\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie, _users, _users_data, _plans_data, _arbitrations_data;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: plans, isLoading: plans_isLoading, error: plans_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList)();\n    const { data: arbitrations, isLoading: arbitrations_isLoading, error: arbitrations_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList)();\n    const { data: users, isLoading: users_isLoading, error: users_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList)();\n    const users_data = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>users.data, [\n        (_users = users) === null || _users === void 0 ? void 0 : _users.data\n    ]);\n    const [arbitrationID, setArbitrationID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [picklistTargetValueTeam, setPicklistTargetValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [picklistSourceValueTeam, setPicklistSourceValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(users_data);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { mutate: arbitration_create_trigger, isPending: isCreateMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate)();\n    const { mutate: arbitration_patch_trigger, isPending: isPatchMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate)();\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const accept_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"created\",\n                \"modified\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    // Cell: ({ cell }) => <div>{parse(cell.getValue<string>())}</div>,\n                    // Cell: ({ cell }) => { if ([\"description\", \"content\",\"report\"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Rapport\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_editor__WEBPACK_IMPORTED_MODULE_7__.Editor, {\n                                    // initialValue={row.original[key]}\n                                    // tinymceScriptSrc=\"http://localhost:3000/tinymce/tinymce.min.js\"\n                                    // apiKey='none'\n                                    value: row.original.report,\n                                    // onChange={(e) => { row._valuesCache.report = e.target.getContent() }}\n                                    onTextChange: (e)=>{\n                                        row._valuesCache.report = e.htmlValue;\n                                    },\n                                    style: {\n                                        height: \"320px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (key === \"plan\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"plan\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_8__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            children: cell.getValue().code\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 38\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { row } = param;\n                        var _row__valuesCache_plan, _row__valuesCache_plan1, _plans;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_9__.Dropdown, {\n                                    optionLabel: \"name\",\n                                    placeholder: \"Choisir un plan\",\n                                    onChange: (e)=>{\n                                        var _plans, _plans1;\n                                        console.log(e);\n                                        setRowTobe({\n                                            ...rowTobe,\n                                            plan: (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data.find((plan)=>plan.id === e.value.code)\n                                        });\n                                        row._valuesCache = {\n                                            ...row._valuesCache,\n                                            plan: (_plans1 = plans) === null || _plans1 === void 0 ? void 0 : _plans1.find((plan)=>plan.id === e.value.code)\n                                        };\n                                    },\n                                    value: {\n                                        code: ((_row__valuesCache_plan = row._valuesCache.plan) === null || _row__valuesCache_plan === void 0 ? void 0 : _row__valuesCache_plan.id) || null,\n                                        name: ((_row__valuesCache_plan1 = row._valuesCache.plan) === null || _row__valuesCache_plan1 === void 0 ? void 0 : _row__valuesCache_plan1.code) || null\n                                    },\n                                    options: (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data.map((plan)=>{\n                                        return {\n                                            code: plan.id,\n                                            name: plan.type\n                                        };\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            var _data__data_type_properties_key_title3;\n            if (key === \"id\") return {\n                header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                accessorKey: key,\n                id: key,\n                Edit: ()=>null\n            };\n            var _data__data_type_properties_key_title4;\n            if (key === \"team\") return {\n                header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                accessorKey: key,\n                id: key,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: cell.getValue().map((usr)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__.getUserFullname)(usr)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    console.log(\"[ARBITRATION]\", row._valuesCache.team);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"font-bold\",\n                                children: \"Membres\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_10__.PickList, {\n                                source: picklistTargetValueTeam.length === 0 ? picklistSourceValueTeam : picklistSourceValueTeam.filter((user)=>picklistTargetValueTeam.map((user)=>user.username).includes(user.username)),\n                                id: \"picklist_team\",\n                                target: picklistTargetValueTeam.length > 0 ? picklistTargetValueTeam : row._valuesCache.team,\n                                sourceHeader: \"De\",\n                                targetHeader: \"A\",\n                                itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            item.first_name,\n                                            \" \",\n                                            item.last_name\n                                        ]\n                                    }, item.username, true, void 0, void 0),\n                                onChange: (e)=>{\n                                    console.log(\"source Team\", e.source);\n                                    setPicklistSourceValueTeam([\n                                        ...e.source\n                                    ]);\n                                    setPicklistTargetValueTeam([\n                                        ...e.target\n                                    ]);\n                                    row._valuesCache.team = e.target;\n                                },\n                                sourceStyle: {\n                                    height: \"200px\"\n                                },\n                                targetStyle: {\n                                    height: \"200px\"\n                                },\n                                filter: true,\n                                filterBy: \"username,email,first_name,last_name\",\n                                filterMatchMode: \"contains\",\n                                sourceFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\",\n                                targetFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true);\n                }\n            };\n            else {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), [\n        (_users_data = users.data) === null || _users_data === void 0 ? void 0 : _users_data.data,\n        (_plans_data = plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.data,\n        (_arbitrations_data = arbitrations.data) === null || _arbitrations_data === void 0 ? void 0 : _arbitrations_data.data\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_11__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data ? data_.data_.data : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data ? data_.data_.data.length : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                report: false,\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_12__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onEditingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_patch_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setEditingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" mis \\xe0 ajour\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onCreatingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_create_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setCreatingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" cr\\xe9\\xe9\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"add\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"align-content-center \",\n                                children: \"Cr\\xe9ation nouveau arbitrage\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: createVisible,\n                    onHide: ()=>{\n                        table.setCreatingRow(null);\n                        setCreateVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: internalEditComponents\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 493,\n                columnNumber: 7\n            }, this);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_15__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"align-content-center \",\n                                children: [\n                                    \"Editer l'arbitrage n\\xb0 \",\n                                    row.original.id\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: editVisible,\n                    onHide: ()=>{\n                        table.setEditingRow(null);\n                        setEditVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: [\n                            internalEditComponents,\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, this);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.report);\n        },\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"update\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                setArbitrationID(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true);\n                                console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept: accept_row_deletion,\n                                    reject: reject_row_deletion\n                                })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_11__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 572,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_19__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 572,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"8DYJ/6Yf89tsHPECLvw8buq1Cu0=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_11__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx\n"));

/***/ })

});