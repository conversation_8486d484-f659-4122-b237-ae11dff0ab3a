from rest_framework.routers import DefaultRouter
from backend import views
from django.urls import include, path,re_path

router = DefaultRouter()

router.register(r'userprofile', views.UserProfileViewSet)
router.register(r'structurelqscorrespondents', views.StructureLQSCorrespondentsViewSet)
router.register(r'structurelqs', views.CriStructviewViewSet)
router.register(r'structurelqsinterim', views.StructureLQSInterimViewSet)
router.register(r'comment', views.CommentViewSet,'comment')
# router.register(r'comments', views.CommentListViewSet,'comments')
router.register(r'notification', views.NotificationViewSet)
router.register(r'theme', views.ThemeViewSet)
router.register(r'domain', views.DomainViewSet)
router.register(r'process', views.ProcessViewSet)
router.register(r'risk', views.RiskViewSet)
router.register(r'riskimpact', views.RiskImpactViewSet)
router.register(r'fact', views.FACTViewSet)
router.register(r'goal', views.GoalViewSet)
router.register(r'arbitration', views.ArbitrationViewSet)
router.register(r'arbitratedtheme', views.ArbitratedThemeViewSet)
router.register(r'plan', views.PlanViewSet)
router.register(r'mission', views.MissionViewSet)
router.register(r'docs', views.MissionDocumentViewSet)
router.register(r'action', views.ActionViewSet)
router.register(r'recommendation', views.RecommendationViewSet)
router.register(r'constat', views.ConstatViewSet)
router.register(r'cause', views.CauseViewSet)
router.register(r'consequence', views.ConsequenceViewSet)
router.register(r'users', views.UserViewSet)
router.register(r'Actionresponsable', views.ActResponsableViewSet,'validation Responsable')
# router.register(r'userpermission', views.UserPermissionsView)


urlpatterns = router.urls
urlpatterns += [
    path('api-token-auth/', views.CustomAuthToken.as_view()),
    path('userpermission/', views.UserPermissionsView.as_view())
    # ,
    # path('comment/bulk', views.CommentBulk)
]