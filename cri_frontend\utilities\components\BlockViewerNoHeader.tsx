import { Tooltip } from 'primereact/tooltip';
import { classNames } from 'primereact/utils';
import React, { useRef, useState } from 'react';

interface BlockViewerProps {
    header: string | React.ReactNode;
    code?: string;
    new?: boolean;
    free?: boolean;
    priority?: string;
    status?: string;
    containerClassName?: string;
    editStyle?: React.CSSProperties;
    children: React.ReactNode;
}

const BlockViewerNoHeader = (props: BlockViewerProps) => {
    const [blockView, setBlockView] = useState('Editer');
    const actionCopyRef = useRef(null);
    const statusBadgeRef = useRef(null);
    const priorityBadgeRef = useRef(null);

    const copyCode = async (event: React.MouseEvent<HTMLButtonElement>) => {
        await navigator.clipboard.writeText(props.code!);
        event.preventDefault();
    };

    return (
        <div className="block-viewer">
            <div className="block-section">
                <div className="block-noheader">
                   
                </div>
                <div className="block-content">
                    {blockView === 'Editer' && (
                        <div className={props.containerClassName} style={props.editStyle}>
                            {props.children}
                        </div>
                    )}

                    {blockView === 'CODE' && (
                        <pre className="app-code">
                            <code>{props.code}</code>
                        </pre>
                    )}
                </div>
            </div>
        </div>
    );
};

export default BlockViewerNoHeader;
