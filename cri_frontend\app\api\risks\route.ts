import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/risks
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const validated = searchParams.get('validated')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' as const } },
        { description: { contains: search, mode: 'insensitive' as const } },
      ]
    }
    
    if (validated !== null && validated !== undefined) {
      where.validated = validated === 'true'
    }
    
    const [risks, total] = await Promise.all([
      prisma.risk.findMany({
        where,
        skip,
        take: limit,
        include: {
          themes: {
            select: {
              id: true,
              title: true,
              validated: true,
              code: true,
            }
          }
        },
        orderBy: { id: 'desc' },
      }),
      prisma.risk.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: risks,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching risks:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/risks
export async function POST(request: NextRequest) {
  try {
    // Check authentication - only staff can create risks
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const body = await request.json()
    
    const {
      title,
      description,
      validated = false,
    } = body
    
    // Validate required fields
    if (!title) {
      return NextResponse.json(
        { error: 'title is required' },
        { status: 400 }
      )
    }

    const risk = await prisma.risk.create({
      data: {
        title,
        description,
        validated,
        createdBy: session.user.id.toString(),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
          }
        }
      },
    })

    return NextResponse.json({ data: risk }, { status: 201 })
  } catch (error) {
    console.error('Error creating risk:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
