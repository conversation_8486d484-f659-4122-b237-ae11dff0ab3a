'use client'
import { useRef, useState } from 'react';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { Column, ColumnEditorOptions, ColumnEvent } from 'primereact/column';
import { Button } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Tag } from 'primereact/tag';
import { Sidebar } from 'primereact/sidebar';
import { TabView, TabPanel } from 'primereact/tabview';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { Dropdown } from 'primereact/dropdown';
import { Editor } from '@tinymce/tinymce-react';
import parse from 'html-react-parser';
import { getCookie } from 'cookies-next';

interface GenericDataTableProps<T> {
  data_: {
    isLoading: boolean;
    data_: any;
    error: any;
    data_type: any | undefined;
    pagination: any;
  };
}

export default function GenericDataTable<T>(props: GenericDataTableProps<T>) {
  const toast = useRef<Toast | null>(null);
  const user = JSON.parse(getCookie('user')?.toString() || '{}');
  const [selectedRows, setSelectedRows] = useState<T[]>([]);
  const [filters, setFilters] = useState<DataTableFilterMeta>({});
  const [globalFilterValue, setGlobalFilterValue] = useState<string>('');
  const [expandedRows, setExpandedRows] = useState<any>(null);
  const [sidebarVisible, setSidebarVisible] = useState<boolean>(false);
  const [editingRow, setEditingRow] = useState<any>(null);
  const [groupedColumns, setGroupedColumns] = useState<string[]>([]);
  
  // Extract data from props
  const { data_, isLoading, error, data_type, pagination } = props.data_;
  const data = error ? [] : data_?.data.results ? data_?.data.results : [data_?.data];
  const rowCount = error ? 0 : data_?.data.results ? data_?.data.count : 1;
  
  // Dynamically generate columns based on the first data item
  const generateColumns = () => {
    if (!data || data.length === 0) return [];
    
    const firstItem = data[0];
    return Object.keys(firstItem).map(key => {
      // Skip complex objects or arrays for simple display
      if (typeof firstItem[key] === 'object' && firstItem[key] !== null) {
        return {
          field: key,
          header: key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' '),
          body: (rowData: any) => {
            if (Array.isArray(rowData[key])) {
              return rowData[key].map((item: any, index: number) => (
                <Tag key={index} value={item.id || item.name || JSON.stringify(item)} />
              ));
            }
            return JSON.stringify(rowData[key]);
          }
        };
      }
      
      return {
        field: key,
        header: key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' '),
        sortable: true,
        filter: true
      };
    });
  };
  
  const columns = generateColumns();
  
  // Global filter
  const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setGlobalFilterValue(value);
    
    const _filters = { ...filters };
    _filters['global'] = { value, matchMode: 'contains' };
    setFilters(_filters);
  };
  
  const renderHeader = () => {
    return (
      <div className="flex justify-content-between">
        <Button 
          type="button" 
          icon="pi pi-filter-slash" 
          label="Clear" 
          outlined 
          onClick={clearFilter} 
        />
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText 
            value={globalFilterValue} 
            onChange={onGlobalFilterChange} 
            placeholder="Keyword Search" 
          />
        </span>
      </div>
    );
  };
  
  const clearFilter = () => {
    setFilters({});
    setGlobalFilterValue('');
  };
  
  // Row expansion template
  const rowExpansionTemplate = (data: any) => {
    return (
      <div className="p-3">
        <h5>Details for {data.id || data.name || 'Item'}</h5>
        <div className="grid">
          {Object.entries(data).map(([key, value]) => (
            <div className="col-12 md:col-6" key={key}>
              <div className="p-2">
                <strong>{key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ')}:</strong> {
                  typeof value === 'object' && value !== null 
                    ? JSON.stringify(value) 
                    : String(value)
                }
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  // Column selection for grouping
  const columnSelectionTemplate = () => {
    return (
      <MultiSelect 
        value={groupedColumns} 
        options={columns.map(col => ({ label: col.header, value: col.field }))} 
        onChange={(e) => setGroupedColumns(e.value)} 
        placeholder="Select Columns to Group" 
        className="w-full md:w-20rem" 
      />
    );
  };
  
  const header = renderHeader();
  
  return (
    <>
      <Toast ref={toast} />
      <ConfirmPopup />
      
      <div className="card">
        <DataTable 
          value={data} 
          paginator 
          rows={10} 
          rowsPerPageOptions={[5, 10, 25, 50]} 
          tableStyle={{ minWidth: '50rem' }}
          selectionMode="multiple" 
          selection={selectedRows} 
          onSelectionChange={(e) => setSelectedRows(e.value)}
          dataKey="id"
          filters={filters}
          filterDisplay="menu"
          loading={isLoading}
          responsiveLayout="scroll"
          globalFilterFields={columns.map(col => col.field)}
          header={header}
          emptyMessage="No data found."
          expandedRows={expandedRows}
          onRowToggle={(e) => setExpandedRows(e.data)}
          rowExpansionTemplate={rowExpansionTemplate}
          resizableColumns 
          columnResizeMode="fit"
          showGridlines
          stripedRows
          size="small"
          scrollable
          scrollHeight="400px"
          groupRowsBy={groupedColumns.length > 0 ? groupedColumns[0] : undefined}
        >
          <Column expander style={{ width: '3em' }} />
          <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} />
          
          {columns.map((col) => (
            <Column 
              key={col.field} 
              field={col.field} 
              header={col.header} 
              sortable={col.sortable} 
              filter={col.filter}
              body={col.body}
              style={{ minWidth: '12rem' }}
            />
          ))}
          
          <Column 
            body={(data) => (
              <div className="flex gap-2">
                <Button 
                  icon="pi pi-pencil" 
                  rounded 
                  outlined 
                  severity="success" 
                  onClick={() => {
                    setEditingRow(data);
                    setSidebarVisible(true);
                  }} 
                />
                <Button 
                  icon="pi pi-trash" 
                  rounded 
                  outlined 
                  severity="danger" 
                  onClick={(e) => {
                    confirmPopup({
                      target: e.currentTarget,
                      message: 'Are you sure you want to delete this record?',
                      icon: 'pi pi-exclamation-triangle',
                      accept: () => {
                        // Delete logic here
                        toast.current?.show({ severity: 'success', summary: 'Success', detail: 'Record Deleted', life: 3000 });
                      }
                    });
                  }} 
                />
              </div>
            )} 
            headerStyle={{ width: '8rem', textAlign: 'center' }}
            bodyStyle={{ textAlign: 'center', overflow: 'visible' }}
          />
        </DataTable>
      </div>
      
      <Sidebar visible={sidebarVisible} onHide={() => setSidebarVisible(false)} position="right" className="p-sidebar-md">
        <h3>Edit Record</h3>
        {editingRow && (
          <div className="p-fluid">
            {Object.entries(editingRow).map(([key, value]) => (
              <div className="field" key={key}>
                <label htmlFor={key}>{key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ')}</label>
                <InputText id={key} value={String(value)} onChange={(e) => {
                  setEditingRow({...editingRow, [key]: e.target.value});
                }} />
              </div>
            ))}
            <Button label="Save" icon="pi pi-check" onClick={() => {
              // Save logic here
              toast.current?.show({ severity: 'success', summary: 'Success', detail: 'Record Updated', life: 3000 });
              setSidebarVisible(false);
            }} />
          </div>
        )}
      </Sidebar>
    </>
  );
}
