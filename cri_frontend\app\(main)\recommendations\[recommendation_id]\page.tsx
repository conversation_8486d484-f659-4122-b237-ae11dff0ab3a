'use client';

import GenericTable from '@/utilities/components/GenericTAble';
import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import React from 'react';
import BlockViewer from '@/utilities/components/BlockViewer';
import { Stack } from '@mui/material';
import { Viewer } from '@react-pdf-viewer/core';
import { parse } from 'path';
import { Button } from 'primereact/button';
import { Chip } from 'primereact/chip';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Sidebar } from 'primereact/sidebar';
import { TabView, TabPanel } from 'primereact/tabview';
import { useParams, useSearchParams } from 'next/navigation';
import { useApiRecommendationList } from '@/hooks/useNextApi';

const RecommendationRefDetails = ({ params }: { params: { recommendation: Recommendation } }) => {
    let rec_ :Recommendation ;
    const searchParams = useParams()
    const { recommendation } = params
    const { data: recommendations } = useApiRecommendationList({ limit: 100 })
    console.log("search params", searchParams)
    if (!recommendation) {
        rec_ = Array.isArray(recommendations) ?
            recommendations.find((rec: any) => rec.id === searchParams.recommandation_id) :
            null
    } else {
        rec_ = recommendation
    }
    const generateRecommandationActionsColumns = () => {
        let columns = [];
        for (const [key, value] of Object.entries($Action.properties).filter(([key, value], index) => !['created_by', 'dependencies', 'modified_by', 'created', 'modified', 'id'].includes(key))) {

            if (key === 'description') {
                columns.push(<Column field={key} body={(data) => data.description} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (key === 'job_leader') {
                columns.push(<Column field={key} body={(data) => `${data.job_leader?.last_name} ${data.job_leader?.first_name}`} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (['start_date', 'end_date'].includes(key)) {
                columns.push(<Column field={key} body={(data) => new Date(data[key]).toLocaleDateString()} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            // else if (['validated', 'accepted'].includes(key)) {
            //     columns.push(<Column field={key}  body={(data) => data[key] ? <i className="pi pi-check-circle" style={{ color: 'green' }}></i> : <i className="pi pi-times-circle" style={{ color: 'red' }}></i>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            // }
            else if (['progress'].includes(key)) {
                columns.push(<Column field={key} body={(data) => <ProgressBar value={data[key]}></ProgressBar>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (['status'].includes(key)) {
                columns.push(<Column field={key} body={(data) => <Tag value={data[key]}></Tag>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '45%' }} />)
            }
            else if (['proof'].includes(key)) {
                columns.push(<Column field={key} body={(data) => <Button severity='warning' icon='pi pi-paperclip' onClick={attachementViewProofClick}></Button>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '45%' }} />)
            }


        }
        columns.push(<Column align={'center'} field={'comment'} body={(data) => <Button rounded outlined severity='info' icon='pi pi-comments' onClick={(e) => { console.log(data); addCommentClick(e, data.id, data.recommendation) }}><Badge value={data.comments.length ?? 0} severity="danger"></Badge></Button>} header={'Commentaires'} sortable style={{ width: '25%' }} />)


        return columns;
    }

    return (
        <div className="grid">
            <div className="col-12">
                <BlockViewer
                    header={`Mission ${recommendation?.id}`}
                    containerClassName="surface-0 px-4 py-4 md:px-6 lg:px-8"
                    status={recommendation?.accepted ? 'Acceptée' : 'Non-Acceptée'}
                    priority={recommendation?.priority}
                >
                    <div className="surface-0">

                        <ul className="list-none p-0 m-0">
                            <li className="flex align-items-center py-3 px-2 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">{recommendation?.recommendation ? 'Plan' : 'Exercice'}</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">
                                    {/* <Chip label={recommendation?.plan || recommendation?.exercise} className="mr-2" />                                */}
                                </div>
                            </li>
                            <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Thématique</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">{rec_?.responsible}</div>
                                {/* <div className="w-6 md:w-2 flex justify-content-end">
                                <Button label="Edit" icon="pi pi-pencil" className="p-button-text" />
                            </div> */}
                            </li>
                            <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Structures concernées</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">
                                    <Stack direction={'row'} spacing={1}><Chip label={recommendation?.concerned_structure.code_mnemonique}></Chip></Stack>
                                </div>
                                {/* <div className="w-6 md:w-2 flex justify-content-end">
                                    <Button label="Edit" icon="pi pi-pencil" className="p-button-text" />
                                </div> */}
                            </li>
                            <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap w-full">
                                <TabView className='w-full'>


                                    <TabPanel header={"Commtaires"} rightIcon="pi pi-thumbs-up ml-2" className='align-content-center	align-items-center	justify-content-center	'>
                                        <DataTable<Comment[]> resizableColumns value={recommendation?.comments ?? []} size='small' stripedRows rows={5} paginator emptyMessage={'Pas de commentaires.'}>
                                            <Column field="id" header="N°" sortable />
                                            <Column field="created_by" header="Constats" sortable style={{ width: '35%' }} body={(data) => data.constats?.map((constat: Constat) => constat.id).join(",")} />
                                            <Column field="created_" header="Structure Concerné" sortable body={(data) => data.concerned_structure.code_mnemonique} />
                                            <Column field="comment" header="Priorité" sortable style={{ width: '35%' }} />
                                            <Column
                                                header="Voir"
                                                style={{ width: '15%' }}
                                                body={() => (
                                                    <>
                                                        <Button icon="pi pi-eye" text />
                                                    </>
                                                )}
                                            />
                                        </DataTable>
                                    </TabPanel>
                                    <TabPanel header="Actions" leftIcon="pi pi-file-word mr-2" rightIcon="pi pi-file-pdf ml-2">
                                        <DataTable<Action[]> tableStyle={{ maxWidth: '70vw' }} value={recommendation?.actions ?? []} rows={5} paginator resizableColumns responsiveLayout="scroll">
                                            {/* {generateRecommandationActionsColumns()} */}
                                        </DataTable>
                                    </TabPanel>
                                </TabView>
                            </li>
                            {/* <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <CommentTimeLine data={recommendation?.comments}/>
                            </li> */}
                        </ul>
                    </div>
                </BlockViewer>
            </div>
        </div>
    );
};

export default RecommendationRefDetails;
