# Development Stage
FROM node:20-alpine AS development

WORKDIR /

COPY package*.json ./

RUN npm ci

COPY . .

EXPOSE 3000


CMD ["npm", "run", "dev"]

# Builder Stage
FROM node:20-alpine AS builder

WORKDIR /app

COPY package*.json ./

RUN npm ci

COPY . .

RUN npm run build

# Production Stage 

FROM node:20-alpine AS production

WORKDIR /app

# Copy the built artifacts from the builder stage
COPY --from=builder /.next/standalone ./
COPY --from=builder /.next/static ./.next/static

# Set the environment variables (if needed)
ENV NODE_ENV=production

EXPOSE 3000

CMD ["node", "server.js"]


# FROM node:18-alpine AS base
# FROM base AS deps
# RUN apk add --no-cache libc6-compat
# WORKDIR /
# COPY package.json package-lock.json* ./
# RUN npm install
# FROM base AS builder
# WORKDIR /
# COPY --from=deps /node_modules ./node_modules
# COPY . .

# ENV NEXT_TELEMETRY_DISABLED=1
# RUN npm run build
# FROM base AS runner
# WORKDIR /

# ENV NODE_ENV=development
# ENV NEXT_TELEMETRY_DISABLED=1

# RUN addgroup --system --gid 1001 nodejs
# RUN adduser --system --uid 1001 nextjs

# COPY --from=builder /app/public ./public
# RUN mkdir .next
# RUN chown nextjs:nodejs .next

# COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
# COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# USER nextjs
# EXPOSE 3000
# ENV PORT=3000
# ENV HOSTNAME="0.0.0.0"
# CMD ["node", "server.js"]