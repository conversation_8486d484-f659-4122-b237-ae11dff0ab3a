"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/opportunity_sheet/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx":
/*!*************************************************************************!*\
  !*** ./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTableOpportunitySheet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-client)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_dialog__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/dialog */ \"(app-client)/./node_modules/primereact/dialog/dialog.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _editForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./editForm */ \"(app-client)/./app/(main)/themes/(components)/editForm.tsx\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _app_ability__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ability */ \"(app-client)/./app/ability.ts\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-client)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"(app-client)/./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTableOpportunitySheet(data_) {\n    var _getCookie;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_6__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const [theme_id, setThemeId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const { push } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeCreate)();\n    const { data: data_modify, error: error_modify, isPending: isMutating_modify, mutate: trigger_modify } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeUpdate)();\n    const { data: data_delete, error: error_delete, isPending: isMutating_delete, mutate: trigger_delete } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeDestroy)();\n    const [detailsDialogVisible, setDetailsDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(\"PAGINATION\", data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const getSeverity = (str)=>{\n        switch(str){\n            case \"Vice Pr\\xe9sident\":\n                return \"success\";\n            case \"Contr\\xf4le Interne\":\n                return \"warning\";\n            case \"Audit Interne\":\n                return \"warning\";\n            case \"Structures\":\n                return \"danger\";\n            default:\n                return null;\n        }\n    };\n    const accept = ()=>{\n        trigger_delete({}, {\n            onSuccess: ()=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"Enregistrement supprim\\xe9\"\n                });\n            },\n            onError: (error)=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"\".concat(error.code)\n                });\n            }\n        });\n    };\n    const reject = ()=>{\n        toast.current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        console.log(\"theme_id\", theme_id);\n    }, [\n        theme_id\n    ]);\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"risks\",\n                \"goals\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__.Editor, {\n                            onChange: (e)=>{\n                                row._valuesCache.content = e.target.getContent();\n                            },\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    accessorFn: (row)=>new Date(row[key]),\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        return new Date(cell.getValue()).toLocaleDateString(\"fr\");\n                    },\n                    id: key\n                };\n            }\n            if (key === \"concerned_structures\") {\n                var _data__data_type_properties_key_title2, _struct_code_mnemonique;\n                return {\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 1,\n                            children: cell.getValue().map((struct)=>{\n                                var _struct, _struct1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        backgroundColor: \"var(--pink-300)\",\n                                        color: \"black\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    label: (_struct_code_mnemonique = (_struct = struct) === null || _struct === void 0 ? void 0 : _struct.code_mnemonique) !== null && _struct_code_mnemonique !== void 0 ? _struct_code_mnemonique : (_struct1 = struct) === null || _struct1 === void 0 ? void 0 : _struct1.code_stru\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 186\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 84\n                        }, this) : \"/\";\n                    }\n                };\n            }\n            if (key === \"proposing_structures\") {\n                var _data__data_type_properties_key_title3, _struct_code_mnemonique1;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            direction: \"row\",\n                            spacing: 1,\n                            children: cell.getValue().map((struct)=>{\n                                var _struct, _struct1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        backgroundColor: \"var(--cyan-300)\",\n                                        color: \"black\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    label: (_struct_code_mnemonique1 = (_struct = struct) === null || _struct === void 0 ? void 0 : _struct.code_mnemonique) !== null && _struct_code_mnemonique1 !== void 0 ? _struct_code_mnemonique1 : (_struct1 = struct) === null || _struct1 === void 0 ? void 0 : _struct1.code_stru\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 186\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 84\n                        }, this) : \"/\";\n                    }\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            severity: getSeverity(cell.getValue()),\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"validated\") {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    size: 120,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                            className: \"text-green-500\",\n                            size: \"2x\",\n                            icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faCheckSquare\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 65\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_8__.FontAwesomeIcon, {\n                            className: \"text-red-500\",\n                            size: \"2x\",\n                            icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmarkCircle\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 145\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: cell.getValue() === \"Non lanc\\xe9e\" ? \"danger\" : cell.getValue() === \"En cours\" ? \"success\" : \"info\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"type\") {\n                var _data__data_type_properties_key_title7;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: cell.getValue() === \"Command\\xe9e\" ? \"danger\" : cell.getValue() === \"Planifi\\xe9e\" ? \"warning\" : cell.getValue() === \"AI\" ? \"info\" : \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title8;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_13__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"title\") {\n                var _data__data_type_properties_key_title9;\n                return {\n                    header: (_data__data_type_properties_key_title9 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title9 !== void 0 ? _data__data_type_properties_key_title9 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum , \n                    muiEditTextFieldProps: {\n                        // select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"white-space-normal\",\n                            children: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"domain\") {\n                var _data__data_type_properties_key_title10, _cell_getValue_title;\n                return {\n                    header: (_data__data_type_properties_key_title10 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title10 !== void 0 ? _data__data_type_properties_key_title10 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue_title = (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.title) !== null && _cell_getValue_title !== void 0 ? _cell_getValue_title : \"/\";\n                    }\n                };\n            }\n            if (key === \"process\") {\n                var _data__data_type_properties_key_title11, _cell_getValue_title1;\n                return {\n                    header: (_data__data_type_properties_key_title11 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title11 !== void 0 ? _data__data_type_properties_key_title11 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue_title1 = (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.title) !== null && _cell_getValue_title1 !== void 0 ? _cell_getValue_title1 : \"/\";\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title12, _data__data_type_properties_key_title13;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title12 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title12 !== void 0 ? _data__data_type_properties_key_title12 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title13 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title13 !== void 0 ? _data__data_type_properties_key_title13 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_15__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.results ? data_.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.results ? data_.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: false,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading,\n            //showProgressBars: isLoading, //progress bars while refetching\n            isSaving: isMutating_create\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_16__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                size: 140,\n                enableHiding: true,\n                grow: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiSvgIcon-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiInputBase-input\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiTableSortLabel-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        // editDisplayMode: 'modal',\n        // createDisplayMode: 'modal',\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            console.log(\"onEditingRowSave\", values);\n            const { created, modified, id, ...rest } = values;\n            setThemeId(id);\n            trigger_modify({\n                id: id,\n                data: rest\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    table.setEditingRow(null); //exit editing mode\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"success\",\n                        summary: \"Modification\",\n                        detail: \"Enregistrement modifi\\xe9\"\n                    });\n                },\n                onError: (error)=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        summary: \"Modification\",\n                        detail: \"\".concat(error.message)\n                    });\n                    console.log(\"onEditingRowSave\", error.message);\n                    row._valuesCache = {\n                        error: error.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            console.log(\"onCreatingRowSave\", values);\n            const { created, modified, id, ...rest } = values;\n            trigger_create(rest, {\n                revalidate: true,\n                populateCache: true,\n                onSuccess: ()=>{\n                    var _toast_current;\n                    table.setCreatingRow(null);\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"success\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                },\n                onError: (err)=>{\n                    var _err_response, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.statusText)\n                    });\n                    console.log(\"onCreatingRowSave\", err.response);\n                    row._valuesCache = {\n                        error: err.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                    },\n                    \"& .MuiDialog-container\": {\n                        //  display: 'none',\n                        \"& .MuiPaper-root\": {\n                            maxWidth: \"60vw\"\n                        }\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"add\",\n                        a: \"theme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"delete\",\n                        a: \"theme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_9__.Can, {\n                        I: \"add\",\n                        a: \"arbitratedtheme\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            icon: \"pi pi-bell\",\n                            rounded: true,\n                            color: rowActionEnabled ? \"secondary\" : \"primary\",\n                            size: \"small\",\n                            \"aria-label\": \"edit\",\n                            onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                            lineNumber: 700,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 699,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 670,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        renderEditRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        // renderDetailPanel: ({ row }) =>\n        //   row.original.staff ?\n        //     (\n        //       <Box\n        //         sx={{\n        //           display: 'grid',\n        //           margin: 'auto',\n        //           //gridTemplateColumns: '1fr 1fr',\n        //           width: '100vw',\n        //         }}\n        //       >\n        //         <TabView>\n        //           <TabPanel header={data_.data_type.properties[\"staff\"].title} leftIcon=\"pi pi-user mr-2\">\n        //             <ul>{row.original.staff.staff.map((user, idx) => <a key={user.email + row.original.code} href={\"mailto:\" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>\n        //           </TabPanel>\n        //           <TabPanel header={\"Assistants\"} rightIcon=\"pi pi-user ml-2\">\n        //             <ul>{row.original.staff.assistants.map((user, idx) => <a key={user.email + row.original.code} href={\"mailto:\" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>\n        //           </TabPanel>\n        //           <TabPanel header=\"Lettre\" leftIcon=\"pi pi-file-word mr-2\" rightIcon=\"pi pi-file-pdf ml-2\">\n        //             <Button icon=\"pi pi-check\" rounded onClick={() => setVisible(true)} disabled={row.original.document === null} />\n        //             <Sidebar key={row.original.id} header={<h2>Lettre de mission : {row.original.code}</h2>} visible={visible} onHide={() => setVisible(false)} className=\"w-full md:w-9 lg:w-8\">\n        //               <div className=\"flex flex-column\talign-items-center justify-content-center gap-1\">\n        //                 {row.original.document !== null ?\n        //                   <Document\n        //                     file={row.original.document}\n        //                     onLoadSuccess={onDocumentLoadSuccess}\n        //                   >\n        //                     <Page pageNumber={pageNumber} />\n        //                   </Document> : <p>No Document</p>}\n        //                 <div className='flex flex-column\talign-items-center justify-content-center gap-1' >\n        //                   <p>\n        //                     Page {pageNumber || (numPages ? 1 : '--')} of {numPages || '--'}\n        //                   </p>\n        //                   <div className='flex flex-row\talign-items-center justify-content-center gap-1' >\n        //                     <Button\n        //                       type=\"button\"\n        //                       disabled={pageNumber <= 1}\n        //                       onClick={previousPage}\n        //                     >\n        //                       Previous\n        //                     </Button>\n        //                     <Button\n        //                       type=\"button\"\n        //                       disabled={pageNumber >= numPages}\n        //                       onClick={nextPage}\n        //                     >\n        //                       Next\n        //                     </Button>\n        //                   </div>\n        //                 </div>\n        //               </div>\n        //             </Sidebar>\n        //           </TabPanel>          </TabView>\n        //       </Box >\n        //     ) : <></>,\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-eye\",\n                        onClick: ()=>{\n                            setThemeId(row.original.id);\n                            setDetailsDialogVisible(true);\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 777,\n                        columnNumber: 9\n                    }, this),\n                    _app_ability__WEBPACK_IMPORTED_MODULE_7__[\"default\"].can(\"change\", \"theme\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            setThemeId(row.original.id);\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 9\n                    }, this),\n                    _app_ability__WEBPACK_IMPORTED_MODULE_7__[\"default\"].can(\"delete\", \"theme\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>{\n                            setThemeId(row.original.id);\n                            (0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            });\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 781,\n                        columnNumber: 43\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_18__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                        lineNumber: 796,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 776,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_15__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 808,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_19__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 809,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dialog__WEBPACK_IMPORTED_MODULE_20__.Dialog, {\n                maximizable: true,\n                header: \"THEME ID : \".concat(theme_id),\n                visible: detailsDialogVisible,\n                style: {\n                    width: \"90vw\"\n                },\n                onHide: ()=>{\n                    if (!detailsDialogVisible) return;\n                    setDetailsDialogVisible(false);\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAbleOpportunitySheet.tsx\",\n                lineNumber: 810,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTableOpportunitySheet, \"Q0KDj6Olwz2kzfOmmcPt2mUIdjY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeUpdate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_10__.useApiThemeDestroy,\n        material_react_table__WEBPACK_IMPORTED_MODULE_15__.useMaterialReactTable\n    ];\n});\n_c = GenericTableOpportunitySheet;\nvar _c;\n$RefreshReg$(_c, \"GenericTableOpportunitySheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1jbGllbnQpLy4vYXBwLyhtYWluKS90aGVtZXMvKGNvbXBvbmVudHMpL0dlbmVyaWNUQWJsZU9wcG9ydHVuaXR5U2hlZXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNpRDtBQUNEO0FBQ1Y7QUFNUjtBQUN3QztBQUMxQjtBQUNEO0FBQzBCO0FBQzFCO0FBR047QUFDSTtBQUNvQjtBQUViO0FBQ1A7QUFDTDtBQUM2QjtBQUNrQjtBQUNuRDtBQUM4RDtBQUcvRSxTQUFTNEIsNkJBQW9EQyxLQUE4RjtRQUNoSlY7O0lBQXhCLE1BQU1XLE9BQU9DLEtBQUs1QixLQUFLLENBQUNnQixFQUFBQSxhQUFBQSx1REFBU0EsQ0FBQyxxQkFBVkEsaUNBQUFBLFdBQW1CYSxRQUFRLE9BQU07SUFDekQsTUFBTUMsUUFBUWpCLDZDQUFNQSxDQUFNO0lBQzFCLE1BQU0sQ0FBQ2tCLFVBQVVDLFdBQVcsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQ3hDLE1BQU0sRUFBRW1CLElBQUksRUFBRSxHQUFHN0IsMERBQVNBO0lBRTFCLE1BQU0sQ0FBQzhCLFNBQVNDLFdBQVcsR0FBR3JCLCtDQUFRQSxDQUFDO0lBRXZDLE1BQU0sRUFBRXNCLE1BQU1DLFdBQVcsRUFBRUMsT0FBT0MsWUFBWSxFQUFFQyxXQUFXQyxpQkFBaUIsRUFBRUMsUUFBUUMsY0FBYyxFQUFFLEdBQUdyQixxRUFBaUJBO0lBQzFILE1BQU0sRUFBRWMsTUFBTVEsV0FBVyxFQUFFTixPQUFPTyxZQUFZLEVBQUVMLFdBQVdNLGlCQUFpQixFQUFFSixRQUFRSyxjQUFjLEVBQUUsR0FBR3ZCLHFFQUFpQkE7SUFDMUgsTUFBTSxFQUFFWSxNQUFNWSxXQUFXLEVBQUVWLE9BQU9XLFlBQVksRUFBRVQsV0FBV1UsaUJBQWlCLEVBQUVSLFFBQVFTLGNBQWMsRUFBRSxHQUFHNUIsc0VBQWtCQTtJQUMzSCxNQUFNLENBQUM2QixzQkFBc0JDLHdCQUF3QixHQUFHdkMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDd0MsYUFBYUMsZUFBZSxHQUFHekMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDMEMsZUFBZUMsaUJBQWlCLEdBQUczQywrQ0FBUUEsQ0FBQztJQUNuRCxTQUFTNEMsbUJBQW1CQyxLQUFVO1FBQ3BDQyxRQUFRQyxHQUFHLENBQUMsY0FBY25DLE1BQU1vQyxVQUFVO1FBQzFDcEMsTUFBTW9DLFVBQVUsQ0FBQ0MsR0FBRyxDQUFDSjtJQUN2QjtJQUNBLE1BQU0sQ0FBQ0ssVUFBVUMsWUFBWSxHQUFHbkQsK0NBQVFBLENBQVM7SUFDakQsTUFBTSxDQUFDb0QsWUFBWUMsY0FBYyxHQUFHckQsK0NBQVFBLENBQUM7SUFFN0MsTUFBTXNELGNBQWMsQ0FBQ0M7UUFDbkIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFFVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFFVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUNBLE1BQU1DLFNBQVM7UUFDYm5CLGVBQ0UsQ0FBQyxHQUNEO1lBRUVvQixXQUFXO29CQUFNekM7d0JBQUFBLGlCQUFBQSxNQUFNMEMsT0FBTyxjQUFiMUMscUNBQUFBLGVBQWUyQyxJQUFJLENBQUM7b0JBQUVDLFVBQVU7b0JBQVFDLFNBQVM7b0JBQWVDLFFBQVE7Z0JBQTBCOztZQUNuSEMsU0FBUyxDQUFDdkM7b0JBQVVSO3dCQUFBQSxpQkFBQUEsTUFBTTBDLE9BQU8sY0FBYjFDLHFDQUFBQSxlQUFlMkMsSUFBSSxDQUFDO29CQUFFQyxVQUFVO29CQUFRQyxTQUFTO29CQUFlQyxRQUFRLEdBQWMsT0FBWHRDLE1BQU13QyxJQUFJO2dCQUFHOztRQUM5RztJQUVKO0lBRUEsTUFBTUMsU0FBUztRQUNiakQsTUFBTTBDLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDO1lBQUVDLFVBQVU7WUFBUUMsU0FBUztZQUFZQyxRQUFRO1lBQXFCSSxNQUFNO1FBQUs7SUFDdEc7SUFFQSxTQUFTQyxzQkFBc0IsS0FBa0M7WUFBbEMsRUFBRWpCLFFBQVEsRUFBd0IsR0FBbEM7UUFDN0JDLFlBQVlEO1FBQ1pHLGNBQWM7SUFDaEI7SUFDQSxTQUFTZSxXQUFXQyxNQUFjO1FBQ2hDaEIsY0FBY2lCLENBQUFBLGlCQUFrQkEsaUJBQWlCRDtJQUNuRDtJQUVBLFNBQVNFO1FBQ1BILFdBQVcsQ0FBQztJQUNkO0lBRUEsU0FBU0k7UUFDUEosV0FBVztJQUNiO0lBQ0EsTUFBTSxDQUFDSyxVQUFVQyxZQUFZLEdBQUcxRSwrQ0FBUUEsQ0FBcUI7SUFDN0QsTUFBTSxDQUFDMkUsa0JBQWtCQyxvQkFBb0IsR0FBRzVFLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU02RSxPQUFPQyxRQUFRTDtJQUNyQixNQUFNTSxjQUFjLENBQUNDO1FBQ25CTixZQUFZTSxNQUFNQyxhQUFhO0lBQ2pDO0lBQ0FwRixnREFBU0EsQ0FBQztRQUNSaUQsUUFBUUMsR0FBRyxDQUFDLFlBQVk5QjtJQUMxQixHQUFHO1FBQUNBO0tBQVM7SUFDYixNQUFNaUUsVUFBVXBGLDhDQUFPQSxDQUNyQixJQUNFcUYsT0FBT0MsT0FBTyxDQUFDeEUsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxFQUFFQyxNQUFNLENBQUMsUUFBZUM7Z0JBQWQsQ0FBQ0MsS0FBS0MsTUFBTTttQkFBWSxDQUFDO2dCQUFDO2dCQUFlO2dCQUFjO2dCQUFTO2FBQVEsQ0FBQ0MsUUFBUSxDQUFDRjtRQUFHLEdBQUdHLEdBQUcsQ0FBQyxRQUFlSjtnQkFBZCxDQUFDQyxLQUFLQyxNQUFNO1lBQzFKLElBQUk7Z0JBQUM7Z0JBQVU7Z0JBQVc7Z0JBQVE7Z0JBQVM7Z0JBQVc7YUFBYyxDQUFDQyxRQUFRLENBQUNGLE1BQU07b0JBRXhFN0U7Z0JBRFYsT0FBTztvQkFDTGlGLFFBQVFqRixDQUFBQSx3Q0FBQUEsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckNsRixtREFBQUEsd0NBQXlDNkU7b0JBQ2pETSxhQUFhTjtvQkFDYk8sSUFBSVA7b0JBQ0pRLE1BQU07NEJBQUMsRUFBRUMsSUFBSSxFQUFFO3dCQUFPLElBQUk7NEJBQUM7NEJBQWU7NEJBQVc7eUJBQVMsQ0FBQ1AsUUFBUSxDQUFDRixNQUFNLE9BQU87NkJBQVcscUJBQU8sOERBQUNVO3NDQUFLakgsNkRBQUtBLENBQUNnSCxLQUFLRSxRQUFROzs7Ozs7b0JBQW1CO29CQUNuSkMsTUFBTTs0QkFBQyxFQUFFSCxJQUFJLEVBQUVJLE1BQU0sRUFBRUMsR0FBRyxFQUFFQyxLQUFLLEVBQUU7d0JBQ2pDLHFCQUFPLDhEQUFDdkgsMERBQU1BOzRCQUNad0gsVUFBVSxDQUFDQztnQ0FBUUgsSUFBSUksWUFBWSxDQUFDQyxPQUFPLEdBQUdGLEVBQUVHLE1BQU0sQ0FBQ0MsVUFBVTs0QkFBRzs0QkFDcEVDLGNBQWNSLElBQUlTLFFBQVEsQ0FBQ3ZCLElBQUk7NEJBQy9Cd0Isa0JBQWlCOzRCQUNqQkMsUUFBTzs0QkFDUEMsTUFBTTtnQ0FDSkMsUUFBUTtnQ0FDUkMsU0FBUztnQ0FDVEMsU0FBUztvQ0FDUDtvQ0FBVztvQ0FBWTtvQ0FBUztvQ0FBUTtvQ0FBUztvQ0FBVztvQ0FBUztvQ0FBVztvQ0FDaEY7b0NBQWlCO29DQUFnQjtvQ0FBUTtvQ0FDekM7b0NBQWtCO29DQUFTO29DQUFTO29DQUFTO29DQUFRO29DQUFRO2lDQUM5RDtnQ0FDREMsU0FDRTs0QkFNSjs7Ozs7O29CQUdKO2dCQUNGO1lBQ0Y7WUFDQSxJQUFJM0csTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUMrQixNQUFNLEtBQUssZUFBZTVHLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDK0IsTUFBTSxLQUFLLFFBQVE7b0JBR3JHNUc7Z0JBRlYsT0FBTztvQkFDTDZHLFlBQVksQ0FBQ2xCLE1BQVEsSUFBSW1CLEtBQUtuQixHQUFHLENBQUNkLElBQUk7b0JBQ3RDSSxRQUFRakYsQ0FBQUEseUNBQUFBLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDbEYsb0RBQUFBLHlDQUF5QzZFO29CQUNqRGtDLGVBQWU7b0JBQ2ZDLFVBQVU7b0JBQ1ZDLFdBQVc7b0JBQ1g5QixhQUFhTjtvQkFDYlEsTUFBTTs0QkFBQyxFQUFFQyxJQUFJLEVBQUU7K0JBQUssSUFBS3dCLEtBQUt4QixLQUFLRSxRQUFRLElBQWEwQixrQkFBa0IsQ0FBQztvQkFBSTtvQkFDL0U5QixJQUFJUDtnQkFDTjtZQUNGO1lBQ0EsSUFBSUEsUUFBUSx3QkFBd0I7b0JBR3hCN0Usd0NBTXFRbUg7Z0JBUC9RLE9BQU87b0JBQ0xsQyxRQUFRakYsQ0FBQUEseUNBQUFBLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDbEYsb0RBQUFBLHlDQUF5QzZFO29CQUNqRE0sYUFBYU47b0JBQ2J1Qyx5QkFBeUI7d0JBQ3ZCQyxPQUFPO29CQUNUO29CQUNBakMsSUFBSVA7b0JBQ0pRLE1BQU07NEJBQUMsRUFBRUMsSUFBSSxFQUFFSyxHQUFHLEVBQUU7K0JBQUtMLEtBQUtFLFFBQVEsR0FBb0I4QixNQUFNLEdBQUcsa0JBQUksOERBQUNsSixzREFBS0E7NEJBQUNtSixXQUFXOzRCQUFPQyxTQUFTO3NDQUFJbEMsS0FBS0UsUUFBUSxHQUFvQlIsR0FBRyxDQUFDLENBQUNtQztvQ0FBMEhBLFNBQTJCQTtxREFBM0gsOERBQUNoSixzREFBSUE7b0NBQUNzSixPQUFPO3dDQUFFQyxpQkFBaUI7d0NBQW1CQyxPQUFPO3dDQUFTQyxZQUFZO29DQUFPO29DQUFHQyxPQUFPVixDQUFBQSwyQkFBQUEsVUFBQUEsb0JBQUFBLDhCQUFBQSxRQUFRVyxlQUFlLGNBQXZCWCxxQ0FBQUEsMkJBQTJCQSxXQUFBQSxvQkFBQUEsK0JBQUFBLFNBQVFZLFNBQVM7Ozs7Ozs7Ozs7O21DQUFzQjtvQkFBRTtnQkFFblY7WUFDRjtZQUVBLElBQUlsRCxRQUFRLHdCQUF3QjtvQkFHeEI3RSx3Q0FNcVFtSDtnQkFQL1EsT0FBTztvQkFDTGxDLFFBQVFqRixDQUFBQSx5Q0FBQUEsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckNsRixvREFBQUEseUNBQXlDNkU7b0JBQ2pETSxhQUFhTjtvQkFDYnVDLHlCQUF5Qjt3QkFDdkJDLE9BQU87b0JBQ1Q7b0JBQ0FqQyxJQUFJUDtvQkFDSlEsTUFBTTs0QkFBQyxFQUFFQyxJQUFJLEVBQUVLLEdBQUcsRUFBRTsrQkFBS0wsS0FBS0UsUUFBUSxHQUFvQjhCLE1BQU0sR0FBRyxrQkFBSSw4REFBQ2xKLHNEQUFLQTs0QkFBQ21KLFdBQVc7NEJBQU9DLFNBQVM7c0NBQUlsQyxLQUFLRSxRQUFRLEdBQW9CUixHQUFHLENBQUMsQ0FBQ21DO29DQUEwSEEsU0FBMkJBO3FEQUEzSCw4REFBQ2hKLHNEQUFJQTtvQ0FBQ3NKLE9BQU87d0NBQUVDLGlCQUFpQjt3Q0FBbUJDLE9BQU87d0NBQVNDLFlBQVk7b0NBQU87b0NBQUdDLE9BQU9WLENBQUFBLDRCQUFBQSxVQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVFXLGVBQWUsY0FBdkJYLHNDQUFBQSw0QkFBMkJBLFdBQUFBLG9CQUFBQSwrQkFBQUEsU0FBUVksU0FBUzs7Ozs7Ozs7Ozs7bUNBQXNCO29CQUFFO2dCQUduVjtZQUNGO1lBQ0EsSUFBSWxELFFBQVEsZUFBZTtvQkFHZjdFO2dCQURWLE9BQU87b0JBQ0xpRixRQUFRakYsQ0FBQUEseUNBQUFBLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDbEYsb0RBQUFBLHlDQUF5QzZFO29CQUNqRE0sYUFBYU47b0JBQ2JtRCx1QkFBdUI7d0JBQ3JCWCxPQUFPO29CQUNUO29CQUNBWSx1QkFBdUI7d0JBQ3JCWixPQUFPO29CQUNUO29CQUNBRCx5QkFBeUI7d0JBQ3ZCQyxPQUFPO29CQUNUO29CQUNBakMsSUFBSVA7b0JBQ0pRLE1BQU07NEJBQUMsRUFBRUMsSUFBSSxFQUFFSyxHQUFHLEVBQUU7K0JBQUssOERBQUM1RyxnREFBR0E7NEJBQzNCbUosV0FBVTs0QkFFVmxGLFVBQVVOLFlBQVk0QyxLQUFLRSxRQUFROzRCQUNuQ1YsT0FBT1EsS0FBS0UsUUFBUTsyQkFGZkcsSUFBSVMsUUFBUSxDQUFDaEQsSUFBSSxHQUFHdUMsSUFBSVMsUUFBUSxDQUFDK0IsT0FBTzs7Ozs7b0JBSzFDO2dCQUNQO1lBQ0Y7WUFDQSxJQUFJdEQsUUFBUSxhQUFhO29CQUViN0U7Z0JBRFYsT0FBTztvQkFDTGlGLFFBQVFqRixDQUFBQSx5Q0FBQUEsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckNsRixvREFBQUEseUNBQXlDNkU7b0JBQ2pETSxhQUFhTjtvQkFDYnVDLHlCQUF5Qjt3QkFDdkJDLE9BQU87b0JBQ1Q7b0JBQ0FZLHVCQUF1Qjt3QkFDckJaLE9BQU87b0JBQ1Q7b0JBQ0FlLE1BQU07b0JBQ05oRCxJQUFJUDtvQkFDSlEsTUFBTTs0QkFBQyxFQUFFQyxJQUFJLEVBQUVLLEdBQUcsRUFBRTsrQkFBS0wsS0FBS0UsUUFBUSxtQkFBYyw4REFBQ2hHLDJFQUFlQTs0QkFBQzBJLFdBQVU7NEJBQWlCRSxNQUFLOzRCQUFLQyxNQUFNNUksK0VBQWFBOzs7OztpREFBTyw4REFBQ0QsMkVBQWVBOzRCQUFDMEksV0FBVTs0QkFBZUUsTUFBSzs0QkFBS0MsTUFBTTNJLCtFQUFhQTs7Ozs7O29CQUFHO2dCQUVoTjtZQUNGO1lBQ0EsSUFBSW1GLFFBQVEsUUFBUTtvQkFFUjdFO2dCQURWLE9BQU87b0JBQ0xpRixRQUFRakYsQ0FBQUEseUNBQUFBLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDbEYsb0RBQUFBLHlDQUF5QzZFO29CQUNqRE0sYUFBYU47b0JBQ2JtRCx1QkFBdUI7d0JBQ3JCWCxPQUFPO29CQUNUO29CQUNBWSx1QkFBdUI7d0JBQ3JCWixPQUFPO29CQUNUO29CQUNBRCx5QkFBeUI7d0JBQ3ZCQyxPQUFPO29CQUNUO29CQUNBLG9FQUFvRTtvQkFDcEVpQix1QkFBdUI7d0JBQ3JCQyxRQUFRO3dCQUNSQyxTQUFTO3dCQUNULDBEQUEwRDt3QkFDMURDLGFBQWE7d0JBSWI7b0JBR0Y7b0JBQ0FyRCxJQUFJUDtvQkFDSlEsTUFBTTs0QkFBQyxFQUFFQyxJQUFJLEVBQUVLLEdBQUcsRUFBRTsrQkFBSyw4REFBQzVHLGdEQUFHQTs0QkFFM0JpRSxVQUFVc0MsS0FBS0UsUUFBUSxPQUFlLGtCQUNwQyxXQUFXRixLQUFLRSxRQUFRLE9BQWUsYUFBYSxZQUFZOzRCQUVsRVYsT0FBT1EsS0FBS0UsUUFBUTsyQkFKZkcsSUFBSVMsUUFBUSxDQUFDaEQsSUFBSSxHQUFHdUMsSUFBSVMsUUFBUSxDQUFDK0IsT0FBTzs7Ozs7b0JBTzFDO2dCQUNQO1lBQ0Y7WUFDQSxJQUFJdEQsUUFBUSxRQUFRO29CQUdSN0U7Z0JBRlYscUVBQXFFO2dCQUNyRSxPQUFPO29CQUNMaUYsUUFBUWpGLENBQUFBLHlDQUFBQSxNQUFNeUUsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ0ssS0FBSyxjQUFyQ2xGLG9EQUFBQSx5Q0FBeUM2RTtvQkFDakRNLGFBQWFOO29CQUNibUQsdUJBQXVCO3dCQUNyQlgsT0FBTztvQkFDVDtvQkFDQVksdUJBQXVCO3dCQUNyQlosT0FBTztvQkFDVDtvQkFFQXFCLG1CQUFtQjFJLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDLE9BQU8sQ0FBQzhELElBQUk7b0JBQzdETCx1QkFBdUI7d0JBQ3ZCQyxRQUFRO3dCQUNSQyxTQUFTO3dCQUVUQyxhQUFhOzRCQUNYRyxVQUFVO3dCQUdaO29CQUlGO29CQUNBeEIseUJBQXlCO3dCQUN2QkMsT0FBTztvQkFDVDtvQkFDQWpDLElBQUlQO29CQUNKUSxNQUFNOzRCQUFDLEVBQUVDLElBQUksRUFBRUssR0FBRyxFQUFFOytCQUNsQiw4REFBQzVHLGdEQUFHQTs0QkFFRmlFLFVBQ0VzQyxLQUFLRSxRQUFRLE9BQWUsaUJBQzFCLFdBQVdGLEtBQUtFLFFBQVEsT0FBZSxpQkFBYyxZQUFZRixLQUFLRSxRQUFRLE9BQWUsT0FBTyxTQUFTOzRCQUVqSFYsT0FBT1EsS0FBS0UsUUFBUTsyQkFMZkcsSUFBSVMsUUFBUSxDQUFDaEQsSUFBSSxHQUFHdUMsSUFBSVMsUUFBUSxDQUFDK0IsT0FBTzs7Ozs7b0JBUTFDO2dCQUNUO1lBRUY7WUFDQSxJQUFJdEQsUUFBUSxZQUFZO29CQUdaN0U7Z0JBRlYscUVBQXFFO2dCQUNyRSxPQUFPO29CQUNMaUYsUUFBUWpGLENBQUFBLHlDQUFBQSxNQUFNeUUsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ0ssS0FBSyxjQUFyQ2xGLG9EQUFBQSx5Q0FBeUM2RTtvQkFDakRNLGFBQWFOO29CQUNibUQsdUJBQXVCO3dCQUNyQlgsT0FBTztvQkFDVDtvQkFDQVksdUJBQXVCO3dCQUNyQlosT0FBTztvQkFDVDtvQkFFQXFCLG1CQUFtQjFJLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDLE9BQU8sQ0FBQzhELElBQUk7b0JBQzdETCx1QkFBdUI7d0JBQ3ZCQyxRQUFRO3dCQUNSQyxTQUFTO3dCQUVUQyxhQUFhOzRCQUNYRyxVQUFVO3dCQUdaO29CQUlGO29CQUNBeEIseUJBQXlCO3dCQUN2QkMsT0FBTztvQkFDVDtvQkFDQWpDLElBQUlQO29CQUNKUSxNQUFNOzRCQUFDLEVBQUVDLElBQUksRUFBRUssR0FBRyxFQUFFOytCQUNsQiw4REFBQzVHLGdEQUFHQTs0QkFFRmlFLFVBQ0U7NEJBRUY4QixPQUFPUSxLQUFLRSxRQUFROzJCQUpmRyxJQUFJUyxRQUFRLENBQUNoRCxJQUFJLEdBQUd1QyxJQUFJUyxRQUFRLENBQUMrQixPQUFPOzs7OztvQkFPMUM7Z0JBQ1Q7WUFFRjtZQUNBLElBQUl0RCxRQUFRLFNBQVM7b0JBRVQ3RTtnQkFEVixPQUFPO29CQUNMaUYsUUFBUWpGLENBQUFBLHlDQUFBQSxNQUFNeUUsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ0ssS0FBSyxjQUFyQ2xGLG9EQUFBQSx5Q0FBeUM2RTtvQkFDakRNLGFBQWFOO29CQUNibUQsdUJBQXVCO3dCQUNyQlgsT0FBTztvQkFDVDtvQkFDQVksdUJBQXVCO3dCQUNyQlosT0FBTztvQkFDVDtvQkFDQSxxRUFBcUU7b0JBQ3JFaUIsdUJBQXVCO3dCQUNyQixnQkFBZ0I7d0JBQ2hCRSxTQUFTO3dCQUVUQyxhQUFhOzRCQUNYRyxVQUFVO3dCQUdaO29CQUlGO29CQUNBeEIseUJBQXlCO3dCQUN2QkMsT0FBTztvQkFDVDtvQkFDQWpDLElBQUlQO29CQUNKUSxNQUFNOzRCQUFDLEVBQUVDLElBQUksRUFBRUssR0FBRyxFQUFFOytCQUFLLDhEQUFDSjs0QkFBSTJDLFdBQVU7c0NBQXNCNUMsS0FBS0UsUUFBUTs7Ozs7O29CQUFRO2dCQUVyRjtZQUNGO1lBQ0EsSUFBSVgsUUFBUSxVQUFVO29CQUVWN0UseUNBU2lCc0Y7Z0JBVjNCLE9BQU87b0JBQ0xMLFFBQVFqRixDQUFBQSwwQ0FBQUEsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckNsRixxREFBQUEsMENBQXlDNkU7b0JBQ2pETSxhQUFhTjtvQkFDYnVDLHlCQUF5Qjt3QkFDdkJDLE9BQU87b0JBQ1Q7b0JBQ0FZLHVCQUF1Qjt3QkFDckJaLE9BQU87b0JBQ1Q7b0JBQ0FqQyxJQUFJUDtvQkFDSlEsTUFBTTs0QkFBQyxFQUFFQyxJQUFJLEVBQUVLLEdBQUcsRUFBRTs0QkFBS0w7K0JBQUFBLENBQUFBLHdCQUFBQSxpQkFBQUEsS0FBS0UsUUFBUSxnQkFBYkYscUNBQUFBLGVBQXlCSixLQUFLLGNBQTlCSSxrQ0FBQUEsdUJBQWtDOztnQkFDN0Q7WUFDRjtZQUNBLElBQUlULFFBQVEsV0FBVztvQkFFWDdFLHlDQVNpQnNGO2dCQVYzQixPQUFPO29CQUNMTCxRQUFRakYsQ0FBQUEsMENBQUFBLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDbEYscURBQUFBLDBDQUF5QzZFO29CQUNqRE0sYUFBYU47b0JBQ2J1Qyx5QkFBeUI7d0JBQ3ZCQyxPQUFPO29CQUNUO29CQUNBWSx1QkFBdUI7d0JBQ3JCWixPQUFPO29CQUNUO29CQUNBakMsSUFBSVA7b0JBQ0pRLE1BQU07NEJBQUMsRUFBRUMsSUFBSSxFQUFFSyxHQUFHLEVBQUU7NEJBQUtMOytCQUFBQSxDQUFBQSx5QkFBQUEsaUJBQUFBLEtBQUtFLFFBQVEsZ0JBQWJGLHFDQUFBQSxlQUEwQkosS0FBSyxjQUEvQkksbUNBQUFBLHdCQUFtQzs7Z0JBQzlEO1lBQ0YsT0FDSztvQkFFT3RGLHlDQU1FQTtnQkFQWixJQUFJNkUsUUFBUSxNQUFNLE9BQU87b0JBQ3ZCSSxRQUFRakYsQ0FBQUEsMENBQUFBLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDbEYscURBQUFBLDBDQUF5QzZFO29CQUNqRE0sYUFBYU47b0JBQ2JPLElBQUlQO29CQUNKWSxNQUFNLElBQU07Z0JBQ2Q7cUJBQ0UsT0FBTztvQkFDTFIsUUFBUWpGLENBQUFBLDBDQUFBQSxNQUFNeUUsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ0ssS0FBSyxjQUFyQ2xGLHFEQUFBQSwwQ0FBeUM2RTtvQkFDakRNLGFBQWFOO29CQUNiTyxJQUFJUDtnQkFFTjtZQUVKO1FBQ0YsSUFDRixFQUFFO0lBR0osTUFBTWUsUUFBUXBILDRFQUFxQkEsQ0FBSTtRQUNyQzhGO1FBQ0E1RCxNQUFNVixNQUFNWSxLQUFLLEdBQUcsRUFBRSxHQUFHWixNQUFNNkksT0FBTyxHQUFHN0ksTUFBTTZJLE9BQU8sR0FBRztZQUFDN0ksTUFBTUEsS0FBSyxDQUFDVSxJQUFJO1NBQUM7UUFDM0VvSSxVQUFVOUksTUFBTVksS0FBSyxHQUFHLElBQUlaLE1BQU02SSxPQUFPLEdBQUc3SSxNQUFNK0ksS0FBSyxHQUFHO1FBQzFEQyxvQkFBb0I7UUFDcEJDLHNCQUFzQjtRQUN0QkMsb0JBQW9CO1FBQ3BCQyxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtRQUNsQkMsa0JBQWtCO1FBQ2xCQyxvQkFBb0I7UUFDcEJDLG9CQUFvQjtRQUNwQkMscUJBQXFCO1FBQ3JCQyxzQkFBc0I7UUFDdEJDLGtCQUFrQjtRQUNsQkMsaUJBQWlCO1FBQ2pCQyxlQUFlO1FBQ2ZDLGlCQUFpQjtRQUNqQkMsa0JBQWtCO1FBQ2xCQyxjQUFjO1lBQ1ozSCxZQUFZO2dCQUFFNEgsVUFBVTtnQkFBR0MsV0FBVztZQUFFO1lBQ3hDQyxrQkFBa0I7Z0JBQUVDLFlBQVk7Z0JBQU9oQyxTQUFTO2dCQUFPaUMsWUFBWTtnQkFBT0MsVUFBVTtnQkFBT0MsYUFBYTtnQkFBT0MsT0FBTztnQkFBT0MsWUFBWTtnQkFBT3BGLElBQUk7Z0JBQU9xRixVQUFVO1lBQU07WUFDM0tDLFNBQVM7WUFDVEMsa0JBQWtCO1lBQ2xCQyxTQUFTO2dCQUFDO29CQUFFeEYsSUFBSTtvQkFBTXlGLE1BQU07Z0JBQU07YUFBRTtRQUN0QztRQUNBNUksT0FBTztZQUNMRyxZQUFZcEMsTUFBTW9DLFVBQVUsQ0FBQzBJLElBQUk7WUFDakNDLFdBQVcvSyxNQUFNK0ssU0FBUztZQUMxQiwrREFBK0Q7WUFDL0RDLFVBQVVqSztRQUNaO1FBQ0FrSyxjQUFjeE0saUZBQW1CQTtRQUNqQ3VELG9CQUFvQkE7UUFDcEJrSix5QkFBeUI7WUFDdkIsZUFBZTtnQkFDYkMsY0FBYztZQUNoQjtZQUNBLGtCQUFrQjtnQkFDaEJBLGNBQWM7WUFDaEI7WUFDQSxtQkFBbUI7Z0JBQ2pCL0MsTUFBTTtnQkFDTitDLGNBQWM7Z0JBQ2RDLE1BQU07WUFDUjtZQUNBLG1CQUFtQjtnQkFDakJELGNBQWM7WUFDaEI7UUFDRjtRQUNBRSxlQUFlO1lBQ2JELE1BQU07WUFDTkUsaUJBQWlCO1FBQ25CO1FBQ0FDLG9CQUFvQjtnQkFBQyxFQUFFM0YsS0FBSyxFQUFFO21CQUFNO2dCQUNsQywyQ0FBMkM7Z0JBQzNDLHdCQUF3QjtnQkFDeEJzQyxXQUFXO2dCQUNYc0QsU0FBUztvQkFBRUMsTUFBTTtnQkFBcUQ7Z0JBQ3RFQyxJQUFJO29CQUNGbEYsUUFBUztvQkFDVCxpQ0FBaUM7b0JBQ2pDLHFCQUFxQjtvQkFDckIsZ0NBQWdDO29CQUNoQ2tCLGlCQUFpQjtvQkFDakJpRSxZQUFZO29CQUNaQyxxQkFBcUI7b0JBQ3JCLCtCQUErQjt3QkFDN0JsRSxpQkFBaUI7d0JBQ2pCaUUsWUFBWTt3QkFDWkMscUJBQXFCO3dCQUNyQmpFLE9BQU87b0JBQ1Q7b0JBQ0Esc0JBQXNCO3dCQUNwQkEsT0FBTztvQkFDVDtvQkFDQSx5QkFBeUI7d0JBQ3ZCQSxPQUFPO29CQUNUO29CQUNBLDZCQUE2Qjt3QkFDM0JBLE9BQU87b0JBQ1Q7b0JBQ0EsbUJBQW1CO3dCQUNqQkQsaUJBQWlCO3dCQUNqQmlFLFlBQVk7d0JBQ1pDLHFCQUFxQjt3QkFDckJqRSxPQUFPO29CQUNUO2dCQUNGO1lBQ0Y7UUFBQTtRQUNBLDRCQUE0QjtRQUM1Qiw4QkFBOEI7UUFDOUJrRSxrQkFBa0I7Z0JBQUMsRUFBRWpHLEtBQUssRUFBRUQsR0FBRyxFQUFFbUcsTUFBTSxFQUFFO1lBQ3ZDNUosUUFBUUMsR0FBRyxDQUFDLG9CQUFvQjJKO1lBQ2hDLE1BQU0sRUFBRTNELE9BQU8sRUFBRWtDLFFBQVEsRUFBRWpGLEVBQUUsRUFBRSxHQUFHMkcsTUFBTSxHQUFHRDtZQUMzQ3hMLFdBQVc4RTtZQUNYL0QsZUFDQztnQkFBQytELElBQUdBO2dCQUFJMUUsTUFBS3FMO1lBQUksR0FDaEI7Z0JBRUVsSixXQUFXO3dCQUVUekM7b0JBREF3RixNQUFNb0csYUFBYSxDQUFDLE9BQU8sbUJBQW1CO3FCQUM5QzVMLGlCQUFBQSxNQUFNMEMsT0FBTyxjQUFiMUMscUNBQUFBLGVBQWUyQyxJQUFJLENBQUM7d0JBQUVDLFVBQVU7d0JBQVdDLFNBQVM7d0JBQWdCQyxRQUFRO29CQUF5QjtnQkFDdkc7Z0JBQ0FDLFNBQVMsQ0FBQ3ZDO3dCQUNSUjtxQkFBQUEsaUJBQUFBLE1BQU0wQyxPQUFPLGNBQWIxQyxxQ0FBQUEsZUFBZTJDLElBQUksQ0FBQzt3QkFBRUMsVUFBVTt3QkFBU0MsU0FBUzt3QkFBZ0JDLFFBQVEsR0FBaUIsT0FBZHRDLE1BQU1xTCxPQUFPO29CQUFHO29CQUM3Ri9KLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0J2QixNQUFNcUwsT0FBTztvQkFDN0N0RyxJQUFJSSxZQUFZLEdBQUc7d0JBQUVuRixPQUFPQSxNQUFNcUwsT0FBTzt3QkFBRSxHQUFHdEcsSUFBSUksWUFBWTtvQkFBQztvQkFDL0Q7Z0JBQ0Y7WUFDRjtRQUdKO1FBQ0FtRyxvQkFBb0I7Z0JBQ2xCLDZCQUE2QjtZQUM3QjlMO2FBQUFBLGlCQUFBQSxNQUFNMEMsT0FBTyxjQUFiMUMscUNBQUFBLGVBQWUyQyxJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQVFDLFNBQVM7Z0JBQVFDLFFBQVE7WUFBYTtRQUNoRjtRQUNBaUosbUJBQW1CO2dCQUFDLEVBQUV2RyxLQUFLLEVBQUVrRyxNQUFNLEVBQUVuRyxHQUFHLEVBQUU7WUFDeEN6RCxRQUFRQyxHQUFHLENBQUMscUJBQXFCMko7WUFDakMsTUFBTSxFQUFFM0QsT0FBTyxFQUFFa0MsUUFBUSxFQUFFakYsRUFBRSxFQUFFLEdBQUcyRyxNQUFNLEdBQUdEO1lBQzNDN0ssZUFDRThLLE1BQ0E7Z0JBQ0VLLFlBQVk7Z0JBQ1pDLGVBQWU7Z0JBQ2Z4SixXQUFXO3dCQUVUekM7b0JBREF3RixNQUFNMEcsY0FBYyxDQUFDO3FCQUNyQmxNLGlCQUFBQSxNQUFNMEMsT0FBTyxjQUFiMUMscUNBQUFBLGVBQWUyQyxJQUFJLENBQUM7d0JBQUVDLFVBQVU7d0JBQVdDLFNBQVM7d0JBQVlDLFFBQVE7b0JBQXNCO2dCQUNoRztnQkFDQUMsU0FBUyxDQUFDb0o7d0JBQ2lFQSxlQUF6RW5NO3FCQUFBQSxpQkFBQUEsTUFBTTBDLE9BQU8sY0FBYjFDLHFDQUFBQSxlQUFlMkMsSUFBSSxDQUFDO3dCQUFFQyxVQUFVO3dCQUFTQyxTQUFTO3dCQUFZQyxRQUFRLEdBQTRCLFFBQXpCcUosZ0JBQUFBLElBQUlDLFFBQVEsY0FBWkQsb0NBQUFBLGNBQWNFLFVBQVU7b0JBQUc7b0JBQ3BHdkssUUFBUUMsR0FBRyxDQUFDLHFCQUFxQm9LLElBQUlDLFFBQVE7b0JBQzdDN0csSUFBSUksWUFBWSxHQUFHO3dCQUFFbkYsT0FBTzJMLElBQUlDLFFBQVE7d0JBQUUsR0FBRzdHLElBQUlJLFlBQVk7b0JBQUM7b0JBQzlEO2dCQUNGO1lBQ0Y7UUFFSjtRQUNBMkcscUJBQXFCO2dCQUNuQiw2QkFBNkI7WUFDN0J0TTthQUFBQSxpQkFBQUEsTUFBTTBDLE9BQU8sY0FBYjFDLHFDQUFBQSxlQUFlMkMsSUFBSSxDQUFDO2dCQUFFQyxVQUFVO2dCQUFRQyxTQUFTO2dCQUFRQyxRQUFRO1lBQWE7UUFDaEY7UUFDQXlKLHVCQUF1QjtnQkFBQyxFQUFFaEgsR0FBRyxFQUFFQyxLQUFLLEVBQUU7bUJBQU07Z0JBQzFDLGlDQUFpQztnQkFDakMsc0JBQXNCO2dCQUN0QjNCLE1BQU1yQyxlQUFlRTtnQkFDckI0SixJQUFJO29CQUNGLHFCQUFxQjtvQkFHckI7b0JBQ0EsMEJBQTBCO3dCQUN4QixvQkFBb0I7d0JBQ3BCLG9CQUFvQjs0QkFDbEJrQixVQUFVO3dCQUNaO29CQUVGO29CQUNBQyxRQUFRO2dCQUVWO1lBQ0Y7UUFBQTtRQUNBQyxxQkFBcUI7WUFDbkI1RSxXQUFXO1lBQ1h3RCxJQUFJO2dCQUNGLDBCQUEwQjtvQkFDeEJoRSxpQkFBaUI7Z0JBQ25CO2dCQUNBQSxpQkFBaUI7Z0JBQ2pCQyxPQUFPO2dCQUNQZ0UsWUFBWTtnQkFDWkMscUJBQXFCO1lBQ3ZCO1FBQ0Y7UUFDQW1CLHdCQUF3QjtnQkFBQyxFQUFFbkgsS0FBSyxFQUFFO2dCQVFpREEsbUNBQXNEQTttQkFSakc7Z0JBQ3RDc0MsV0FBVztnQkFDWHdELElBQUk7b0JBQ0ZDLFlBQVk7b0JBQ1pDLHFCQUFxQjtvQkFDckIscUJBQXFCO29CQUNyQixnQ0FBZ0M7b0JBQ2hDbEUsaUJBQWlCO29CQUNqQmxCLFFBQVFaLE1BQU1vSCxRQUFRLEdBQUdDLFlBQVksR0FBSSxnQkFBZSwrQkFBdUJySCxvQ0FBQUEsTUFBTXNILElBQUksQ0FBQ0MsYUFBYSxDQUFDckssT0FBTyxjQUFoQzhDLHdEQUFBQSxrQ0FBa0N3SCxZQUFZLEVBQUMsU0FBeUQsUUFBbER4SCx1Q0FBQUEsTUFBTXNILElBQUksQ0FBQ0csZ0JBQWdCLENBQUN2SyxPQUFPLGNBQW5DOEMsMkRBQUFBLHFDQUFxQ3dILFlBQVksRUFBQztnQkFFekw7WUFDRjs7UUFDQUUsb0JBQW9CO1lBQ2xCcEYsV0FBVztZQUNYd0QsSUFBSTtnQkFFRixxQkFBcUI7Z0JBQ3JCLGdDQUFnQztnQkFDaENoRSxpQkFBaUI7Z0JBQ2pCQyxPQUFPO2dCQUNQZ0UsWUFBWTtnQkFDWkMscUJBQXFCO1lBQ3ZCO1FBQ0Y7UUFDQTVELHVCQUF1QjtZQUNyQjBELElBQUk7Z0JBQ0YscUJBQXFCO2dCQUNyQixnQ0FBZ0M7Z0JBQ2hDaEUsaUJBQWlCO2dCQUNqQkMsT0FBTztnQkFDUGdFLFlBQVk7Z0JBQ1pDLHFCQUFxQjtZQUN2QjtRQUNGO1FBQ0EyQixvQkFBb0I7WUFDbEJyRixXQUFXO1lBQ1h3RCxJQUFJO2dCQUNGQyxZQUFZO2dCQUNaQyxxQkFBcUI7Z0JBQ3JCLHFCQUFxQjtnQkFDckIsZ0NBQWdDO2dCQUNoQ2xFLGlCQUFpQjtnQkFDakJDLE9BQU87WUFDVDtRQUVGO1FBQ0E2RixtQkFBbUI7WUFDakJ0RixXQUFXO1lBQ1h3RCxJQUFJO2dCQUNGQyxZQUFZO2dCQUNaQyxxQkFBcUI7Z0JBQ3JCLCtDQUErQztnQkFDL0MsOEJBQThCO29CQUM1QmxFLGlCQUFpQjtvQkFDakJDLE9BQU87b0JBQ1BnRSxZQUFZO29CQUNaQyxxQkFBcUI7Z0JBQ3ZCO2dCQUNBLCtCQUErQjtvQkFDN0JsRSxpQkFBaUI7b0JBQ2pCQyxPQUFPO29CQUNQZ0UsWUFBWTtvQkFDWkMscUJBQXFCO2dCQUN2QjtZQUNGO1FBQ0Y7UUFDQTZCLCtCQUErQjtnQkFBQyxFQUFFN0gsS0FBSyxFQUFFO21CQUN2Qyw4REFBQ3hILHNEQUFLQTtnQkFBQ21KLFdBQVc7Z0JBQU9DLFNBQVM7O2tDQUNoQyw4REFBQzdILHlDQUFHQTt3QkFBQytOLEdBQUU7d0JBQU1DLEdBQUU7a0NBQ2IsNEVBQUNoUCxzREFBTUE7NEJBQ0wwSixNQUFLOzRCQUNMdUYsT0FBTzs0QkFDUCxvQkFBb0I7NEJBQ3BCQyxpQkFBZTVKLE9BQU8sZUFBZTZKOzRCQUNyQ0MsaUJBQWM7NEJBQ2RDLGlCQUFlL0osT0FBTyxTQUFTNko7NEJBQy9CRyxTQUFTLENBQUM3SjtnQ0FDUndCLE1BQU0wRyxjQUFjLENBQUM7Z0NBQU92SyxpQkFBaUIsT0FBT0csUUFBUUMsR0FBRyxDQUFDOzRCQUNsRTs0QkFDQWlHLE1BQUs7Ozs7Ozs7Ozs7O2tDQUdULDhEQUFDekkseUNBQUdBO3dCQUFDK04sR0FBRTt3QkFBU0MsR0FBRTtrQ0FDaEIsNEVBQUNoUCxzREFBTUE7NEJBQ0xpUCxPQUFPOzRCQUNQTSxVQUFVdEksTUFBTXVJLHFCQUFxQjs0QkFDckMsb0JBQW9COzRCQUNwQk4saUJBQWU1SixPQUFPLGVBQWU2Sjs0QkFDckNDLGlCQUFjOzRCQUNkQyxpQkFBZS9KLE9BQU8sU0FBUzZKOzRCQUMvQkcsU0FBUzlKOzRCQUNUa0UsTUFBSzs0QkFDTCxvRkFBb0Y7NEJBQ3BGRCxNQUFLOzs7Ozs7Ozs7OztrQ0FHVCw4REFBQ3pJLHlDQUFHQTt3QkFBQytOLEdBQUU7d0JBQU1DLEdBQUU7a0NBQ2IsNEVBQUNoUCxzREFBTUE7NEJBQUMwSixNQUFLOzRCQUFhdUYsT0FBTzs0QkFBQ2pHLE9BQU81RCxtQkFBbUIsY0FBYzs0QkFBV3FFLE1BQUs7NEJBQ3hGZ0csY0FBVzs0QkFBT0gsU0FBUyxJQUFNakssb0JBQW9CLENBQUNEOzs7Ozs7Ozs7Ozs7Ozs7OztRQUVyRDtRQUVUc0sscUJBQXFCLElBQU87Z0JBQzFCM0MsSUFBSSxDQUFDNEMsUUFBVzt3QkFDZDVHLGlCQUNFNEcsTUFBTUMsT0FBTyxDQUFDQyxJQUFJLEtBQUssU0FDbkIsMEJBQ0E7b0JBQ1I7WUFDRjtRQUNBQyw4QkFBOEJwUCxpREFBc0JBO1FBQ3BEcVAsNEJBQTRCclAsaURBQXNCQTtRQUNsRCxrQ0FBa0M7UUFDbEMseUJBQXlCO1FBQ3pCLFFBQVE7UUFDUixhQUFhO1FBQ2IsZ0JBQWdCO1FBQ2hCLDZCQUE2QjtRQUM3Qiw0QkFBNEI7UUFDNUIsOENBQThDO1FBQzlDLDRCQUE0QjtRQUM1QixhQUFhO1FBQ2IsVUFBVTtRQUNWLG9CQUFvQjtRQUVwQixxR0FBcUc7UUFFckcsNExBQTRMO1FBQzVMLHdCQUF3QjtRQUN4Qix5RUFBeUU7UUFFekUsaU1BQWlNO1FBRWpNLHdCQUF3QjtRQUN4Qix1R0FBdUc7UUFDdkcsK0hBQStIO1FBQy9ILDRMQUE0TDtRQUU1TCxtR0FBbUc7UUFDbkcsb0RBQW9EO1FBQ3BELDhCQUE4QjtRQUM5QixtREFBbUQ7UUFDbkQsNERBQTREO1FBQzVELHNCQUFzQjtRQUN0Qix1REFBdUQ7UUFDdkQsc0RBQXNEO1FBQ3RELHNHQUFzRztRQUN0Ryx3QkFBd0I7UUFDeEIsdUZBQXVGO1FBQ3ZGLHlCQUF5QjtRQUN6QixxR0FBcUc7UUFDckcsOEJBQThCO1FBQzlCLHNDQUFzQztRQUN0QyxtREFBbUQ7UUFDbkQsK0NBQStDO1FBQy9DLHdCQUF3QjtRQUN4QixpQ0FBaUM7UUFDakMsZ0NBQWdDO1FBQ2hDLDhCQUE4QjtRQUM5QixzQ0FBc0M7UUFDdEMsMERBQTBEO1FBQzFELDJDQUEyQztRQUMzQyx3QkFBd0I7UUFDeEIsNkJBQTZCO1FBQzdCLGdDQUFnQztRQUNoQywyQkFBMkI7UUFDM0IseUJBQXlCO1FBQ3pCLHVCQUF1QjtRQUN2Qix5QkFBeUI7UUFDekIsNENBQTRDO1FBQzVDLGdCQUFnQjtRQUNoQixpQkFBaUI7UUFDakJzUCxrQkFBa0I7Z0JBQUMsRUFBRXJKLElBQUksRUFBRUssR0FBRyxFQUFFQyxLQUFLLEVBQUU7bUJBQ3JDLDhEQUFDZ0o7Z0JBQUsxRyxXQUFVOztrQ0FDZCw4REFBQ3ZKLHNEQUFNQTt3QkFBQ3lKLE1BQUs7d0JBQVFDLE1BQUs7d0JBQVk0RixTQUFTOzRCQUFRM04sV0FBV3FGLElBQUlTLFFBQVEsQ0FBQ2hCLEVBQUU7NEJBQUd6RCx3QkFBd0I7d0JBQU07d0JBQUdpTSxPQUFPO3dCQUFDaUIsUUFBUTs7Ozs7O29CQUNwSXRQLG9EQUFPQSxDQUFDdVAsR0FBRyxDQUFDLFVBQVMsMEJBQ3RCLDhEQUFDblEsc0RBQU1BO3dCQUFDeUosTUFBSzt3QkFBUUMsTUFBSzt3QkFBZTRGLFNBQVM7NEJBQVEzTixXQUFXcUYsSUFBSVMsUUFBUSxDQUFDaEIsRUFBRTs0QkFBR1EsTUFBTW9HLGFBQWEsQ0FBQ3JHOzRCQUFNOUQsZUFBZSxPQUFPSyxRQUFRQyxHQUFHLENBQUM7d0JBQW9CO3dCQUFHeUwsT0FBTzt3QkFBQ2lCLFFBQVE7Ozs7OztvQkFFekx0UCxvREFBT0EsQ0FBQ3VQLEdBQUcsQ0FBQyxVQUFTLDBCQUFZLDhEQUFDblEsc0RBQU1BO3dCQUFDeUosTUFBSzt3QkFBUUMsTUFBSzt3QkFBY3VGLE9BQU87d0JBQUNpQixRQUFRO3dCQUN4RlosU0FBUyxDQUFDN0o7NEJBQ1I5RCxXQUFXcUYsSUFBSVMsUUFBUSxDQUFDaEIsRUFBRTs0QkFBR3ZHLHNFQUFZQSxDQUFDO2dDQUN4Q29ILFFBQVE3QixNQUFNQyxhQUFhO2dDQUMzQjRILFNBQVM7Z0NBQ1Q1RCxNQUFNO2dDQUNOLDBCQUEwQjtnQ0FDMUIwRyxpQkFBaUI7Z0NBQ2pCQyxhQUFhO2dDQUNiQyxhQUFhO2dDQUNick07Z0NBQ0FTOzRCQUNGO3dCQUNGOzs7Ozs7a0NBRUYsOERBQUN6RSxrRUFBWUE7Ozs7Ozs7Ozs7O1FBQ1Q7SUFRVjtJQUVBLHFCQUFROzswQkFDTiw4REFBQ0wscUVBQWtCQTtnQkFBQ3FILE9BQU9BOzs7Ozs7MEJBQzNCLDhEQUFDNUcsb0RBQUtBO2dCQUFDa1EsS0FBSzlPOzs7Ozs7MEJBQ1osOERBQUN0QixzREFBTUE7Z0JBQUNxUSxXQUFXO2dCQUFDbEssUUFBUSxjQUF1QixPQUFUNUU7Z0JBQVlHLFNBQVNrQjtnQkFBc0IrRixPQUFPO29CQUFFMkgsT0FBTztnQkFBTztnQkFBR0MsUUFBUTtvQkFBUSxJQUFJLENBQUMzTixzQkFBc0I7b0JBQVFDLHdCQUF3QjtnQkFBUTs7Ozs7Ozs7QUFJdE07R0Evd0J3QjVCOztRQUlMckIsc0RBQVNBO1FBSStFa0IsaUVBQWlCQTtRQUNqQkUsaUVBQWlCQTtRQUNqQkQsa0VBQWtCQTtRQW1ZN0dyQix3RUFBcUJBOzs7S0E3WWJ1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvKG1haW4pL3RoZW1lcy8oY29tcG9uZW50cykvR2VuZXJpY1RBYmxlT3Bwb3J0dW5pdHlTaGVldC50c3g/NDRiOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IHsgQm94LCBDaGlwLCBTdGFjayB9IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xyXG5pbXBvcnQgeyBFZGl0b3IgfSBmcm9tICdAdGlueW1jZS90aW55bWNlLXJlYWN0JztcclxuaW1wb3J0IHBhcnNlIGZyb20gJ2h0bWwtcmVhY3QtcGFyc2VyJztcclxuaW1wb3J0IHtcclxuICBNUlRfUm93RGF0YSxcclxuICBNYXRlcmlhbFJlYWN0VGFibGUsXHJcbiAgdXNlTWF0ZXJpYWxSZWFjdFRhYmxlLFxyXG4gIHR5cGUgTVJUX0NvbHVtbkRlZlxyXG59IGZyb20gJ21hdGVyaWFsLXJlYWN0LXRhYmxlJztcclxuaW1wb3J0IHsgTVJUX0xvY2FsaXphdGlvbl9GUiB9IGZyb20gJ21hdGVyaWFsLXJlYWN0LXRhYmxlL2xvY2FsZXMvZnInO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdwcmltZXJlYWN0L2J1dHRvbic7XHJcbmltcG9ydCB7IENvbmZpcm1Qb3B1cCwgY29uZmlybVBvcHVwIH0gZnJvbSAncHJpbWVyZWFjdC9jb25maXJtcG9wdXAnO1xyXG5pbXBvcnQgeyBEaWFsb2cgfSBmcm9tICdwcmltZXJlYWN0L2RpYWxvZyc7XHJcbmltcG9ydCB7IFNpZGViYXIgfSBmcm9tICdwcmltZXJlYWN0L3NpZGViYXInO1xyXG5pbXBvcnQgeyBUYWJQYW5lbCwgVGFiVmlldyB9IGZyb20gJ3ByaW1lcmVhY3QvdGFidmlldyc7XHJcbmltcG9ydCB7IFRhZyB9IGZyb20gJ3ByaW1lcmVhY3QvdGFnJztcclxuaW1wb3J0IHsgVG9hc3QgfSBmcm9tICdwcmltZXJlYWN0L3RvYXN0JztcclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VNZW1vLCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5cclxuaW1wb3J0IFJlY29tbWVuZGF0aW9uRWRpdEZvcm0gZnJvbSAnLi9lZGl0Rm9ybSc7XHJcbmltcG9ydCB7IGdldENvb2tpZSB9IGZyb20gJ2Nvb2tpZXMtbmV4dCc7XHJcbmltcG9ydCBhYmlsaXR5IGZyb20gJ0AvYXBwL2FiaWxpdHknO1xyXG5pbXBvcnQgeyBGb250QXdlc29tZUljb24gfSBmcm9tICdAZm9ydGF3ZXNvbWUvcmVhY3QtZm9udGF3ZXNvbWUnO1xyXG5pbXBvcnQgeyBmYUNoZWNrU3F1YXJlLCBmYVhtYXJrQ2lyY2xlIH0gZnJvbSAnQGZvcnRhd2Vzb21lL2ZyZWUtcmVndWxhci1zdmctaWNvbnMnO1xyXG5pbXBvcnQgeyBDYW4gfSBmcm9tICdAL2FwcC9DYW4nO1xyXG5pbXBvcnQgeyB1c2VBcGlUaGVtZUNyZWF0ZSwgdXNlQXBpVGhlbWVEZXN0cm95LCB1c2VBcGlUaGVtZVVwZGF0ZSB9IGZyb20gJ0AvaG9va3MvdXNlTmV4dEFwaSc7XHJcbmltcG9ydCB7IENyaVN0cnVjdHZpZXcsIERvbWFpbiwgUHJvY2VzcyB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdlbmVyaWNUYWJsZU9wcG9ydHVuaXR5U2hlZXQ8VCBleHRlbmRzIE1SVF9Sb3dEYXRhPihkYXRhXzogeyBpc0xvYWRpbmc6IGFueTsgZGF0YV86IGFueSwgZXJyb3I6IGFueSwgZGF0YV90eXBlOiBhbnkgfCB1bmRlZmluZWQsIHBhZ2luYXRpb246IGFueSB9KSB7XHJcbiAgY29uc3QgdXNlciA9IEpTT04ucGFyc2UoZ2V0Q29va2llKCd1c2VyJyk/LnRvU3RyaW5nKCkgfHwgJ3t9JylcclxuICBjb25zdCB0b2FzdCA9IHVzZVJlZjxhbnk+KG51bGwpO1xyXG4gIGNvbnN0IFt0aGVtZV9pZCwgc2V0VGhlbWVJZF0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCB7IHB1c2ggfSA9IHVzZVJvdXRlcigpO1xyXG5cclxuICBjb25zdCBbdmlzaWJsZSwgc2V0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIGNvbnN0IHsgZGF0YTogZGF0YV9jcmVhdGUsIGVycm9yOiBlcnJvcl9jcmVhdGUsIGlzUGVuZGluZzogaXNNdXRhdGluZ19jcmVhdGUsIG11dGF0ZTogdHJpZ2dlcl9jcmVhdGUgfSA9IHVzZUFwaVRoZW1lQ3JlYXRlKClcclxuICBjb25zdCB7IGRhdGE6IGRhdGFfbW9kaWZ5LCBlcnJvcjogZXJyb3JfbW9kaWZ5LCBpc1BlbmRpbmc6IGlzTXV0YXRpbmdfbW9kaWZ5LCBtdXRhdGU6IHRyaWdnZXJfbW9kaWZ5IH0gPSB1c2VBcGlUaGVtZVVwZGF0ZSgpXHJcbiAgY29uc3QgeyBkYXRhOiBkYXRhX2RlbGV0ZSwgZXJyb3I6IGVycm9yX2RlbGV0ZSwgaXNQZW5kaW5nOiBpc011dGF0aW5nX2RlbGV0ZSwgbXV0YXRlOiB0cmlnZ2VyX2RlbGV0ZSB9ID0gdXNlQXBpVGhlbWVEZXN0cm95KClcclxuICBjb25zdCBbZGV0YWlsc0RpYWxvZ1Zpc2libGUsIHNldERldGFpbHNEaWFsb2dWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbZWRpdFZpc2libGUsIHNldEVkaXRWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY3JlYXRlVmlzaWJsZSwgc2V0Q3JlYXRlVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgZnVuY3Rpb24gb25QYWdpbmF0aW9uQ2hhbmdlKHN0YXRlOiBhbnkpIHtcclxuICAgIGNvbnNvbGUubG9nKFwiUEFHSU5BVElPTlwiLCBkYXRhXy5wYWdpbmF0aW9uKTtcclxuICAgIGRhdGFfLnBhZ2luYXRpb24uc2V0KHN0YXRlKVxyXG4gIH07XHJcbiAgY29uc3QgW251bVBhZ2VzLCBzZXROdW1QYWdlc10gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xyXG4gIGNvbnN0IFtwYWdlTnVtYmVyLCBzZXRQYWdlTnVtYmVyXSA9IHVzZVN0YXRlKDEpO1xyXG5cclxuICBjb25zdCBnZXRTZXZlcml0eSA9IChzdHI6IHN0cmluZykgPT4ge1xyXG4gICAgc3dpdGNoIChzdHIpIHtcclxuICAgICAgY2FzZSAnVmljZSBQcsOpc2lkZW50JzpcclxuICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnO1xyXG5cclxuICAgICAgY2FzZSAnQ29udHLDtGxlIEludGVybmUnOlxyXG4gICAgICAgIHJldHVybiAnd2FybmluZyc7XHJcbiAgICAgIGNhc2UgJ0F1ZGl0IEludGVybmUnOlxyXG4gICAgICAgIHJldHVybiAnd2FybmluZyc7XHJcbiAgICAgIGNhc2UgJ1N0cnVjdHVyZXMnOlxyXG4gICAgICAgIHJldHVybiAnZGFuZ2VyJztcclxuXHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbiAgfTtcclxuICBjb25zdCBhY2NlcHQgPSAoKSA9PiB7XHJcbiAgICB0cmlnZ2VyX2RlbGV0ZShcclxuICAgICAge30sXHJcbiAgICAgIHtcclxuXHJcbiAgICAgICAgb25TdWNjZXNzOiAoKSA9PiB0b2FzdC5jdXJyZW50Py5zaG93KHsgc2V2ZXJpdHk6ICdpbmZvJywgc3VtbWFyeTogJ1N1cHByZXNzaW9uJywgZGV0YWlsOiAnRW5yZWdpc3RyZW1lbnQgc3VwcHJpbcOpJyB9KSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3IpID0+IHRvYXN0LmN1cnJlbnQ/LnNob3coeyBzZXZlcml0eTogJ2luZm8nLCBzdW1tYXJ5OiAnU3VwcHJlc3Npb24nLCBkZXRhaWw6IGAke2Vycm9yLmNvZGV9YCB9KSxcclxuICAgICAgfVxyXG4gICAgKVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHJlamVjdCA9ICgpID0+IHtcclxuICAgIHRvYXN0LmN1cnJlbnQuc2hvdyh7IHNldmVyaXR5OiAnd2FybicsIHN1bW1hcnk6ICdSZWplY3RlZCcsIGRldGFpbDogJ1lvdSBoYXZlIHJlamVjdGVkJywgbGlmZTogMzAwMCB9KTtcclxuICB9O1xyXG5cclxuICBmdW5jdGlvbiBvbkRvY3VtZW50TG9hZFN1Y2Nlc3MoeyBudW1QYWdlcyB9OiB7IG51bVBhZ2VzOiBudW1iZXIgfSkge1xyXG4gICAgc2V0TnVtUGFnZXMobnVtUGFnZXMpO1xyXG4gICAgc2V0UGFnZU51bWJlcigxKTtcclxuICB9XHJcbiAgZnVuY3Rpb24gY2hhbmdlUGFnZShvZmZzZXQ6IG51bWJlcikge1xyXG4gICAgc2V0UGFnZU51bWJlcihwcmV2UGFnZU51bWJlciA9PiBwcmV2UGFnZU51bWJlciArIG9mZnNldCk7XHJcbiAgfVxyXG5cclxuICBmdW5jdGlvbiBwcmV2aW91c1BhZ2UoKSB7XHJcbiAgICBjaGFuZ2VQYWdlKC0xKTtcclxuICB9XHJcblxyXG4gIGZ1bmN0aW9uIG5leHRQYWdlKCkge1xyXG4gICAgY2hhbmdlUGFnZSgxKTtcclxuICB9XHJcbiAgY29uc3QgW2FuY2hvckVsLCBzZXRBbmNob3JFbF0gPSB1c2VTdGF0ZTxudWxsIHwgSFRNTEVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IFtyb3dBY3Rpb25FbmFibGVkLCBzZXRSb3dBY3Rpb25FbmFibGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBvcGVuID0gQm9vbGVhbihhbmNob3JFbCk7XHJcbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQ8SFRNTEJ1dHRvbkVsZW1lbnQ+KSA9PiB7XHJcbiAgICBzZXRBbmNob3JFbChldmVudC5jdXJyZW50VGFyZ2V0KTtcclxuICB9O1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcInRoZW1lX2lkXCIsIHRoZW1lX2lkKTtcclxuICB9LCBbdGhlbWVfaWRdKTtcclxuICBjb25zdCBjb2x1bW5zID0gdXNlTWVtbzxNUlRfQ29sdW1uRGVmPFQ+W10+KFxyXG4gICAgKCkgPT5cclxuICAgICAgT2JqZWN0LmVudHJpZXMoZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXMpLmZpbHRlcigoW2tleSwgdmFsdWVdLCBpbmRleCkgPT4gIVsnbW9kaWZpZWRfYnknLCAnY3JlYXRlZF9ieScsICdyaXNrcycsICdnb2FscyddLmluY2x1ZGVzKGtleSkpLm1hcCgoW2tleSwgdmFsdWVdLCBpbmRleCkgPT4ge1xyXG4gICAgICAgIGlmIChbJ3JlcG9ydCcsICdjb250ZW50JywgJ25vdGUnLCAnb3JkZXInLCAnY29tbWVudCcsICdkZXNjcmlwdGlvbiddLmluY2x1ZGVzKGtleSkpIHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwgfSkgPT4geyBpZiAoW1wiZGVzY3JpcHRpb25cIiwgXCJjb250ZW50XCIsIFwicmVwb3J0XCJdLmluY2x1ZGVzKGtleSkpIHJldHVybiBudWxsOyBlbHNlIHJldHVybiA8ZGl2PntwYXJzZShjZWxsLmdldFZhbHVlPHN0cmluZz4oKSl9PC9kaXY+IH0sXHJcbiAgICAgICAgICAgIEVkaXQ6ICh7IGNlbGwsIGNvbHVtbiwgcm93LCB0YWJsZSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIDxFZGl0b3JcclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4geyByb3cuX3ZhbHVlc0NhY2hlLmNvbnRlbnQgPSBlLnRhcmdldC5nZXRDb250ZW50KCkgfX1cclxuICAgICAgICAgICAgICAgIGluaXRpYWxWYWx1ZT17cm93Lm9yaWdpbmFsW2tleV19XHJcbiAgICAgICAgICAgICAgICB0aW55bWNlU2NyaXB0U3JjPVwiaHR0cDovL2xvY2FsaG9zdDozMDAwL3RpbnltY2UvdGlueW1jZS5taW4uanNcIlxyXG4gICAgICAgICAgICAgICAgYXBpS2V5PSdub25lJ1xyXG4gICAgICAgICAgICAgICAgaW5pdD17e1xyXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6IDUwMCxcclxuICAgICAgICAgICAgICAgICAgbWVudWJhcjogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgcGx1Z2luczogW1xyXG4gICAgICAgICAgICAgICAgICAgICdhZHZsaXN0JywgJ2F1dG9saW5rJywgJ2xpc3RzJywgJ2xpbmsnLCAnaW1hZ2UnLCAnY2hhcm1hcCcsICdwcmludCcsICdwcmV2aWV3JywgJ2FuY2hvcicsXHJcbiAgICAgICAgICAgICAgICAgICAgJ3NlYXJjaHJlcGxhY2UnLCAndmlzdWFsYmxvY2tzJywgJ2NvZGUnLCAnZnVsbHNjcmVlbicsXHJcbiAgICAgICAgICAgICAgICAgICAgJ2luc2VydGRhdGV0aW1lJywgJ21lZGlhJywgJ3RhYmxlJywgJ3Bhc3RlJywgJ2NvZGUnLCAnaGVscCcsICd3b3JkY291bnQnXHJcbiAgICAgICAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgICAgICAgIHRvb2xiYXI6XHJcbiAgICAgICAgICAgICAgICAgICAgJ3VuZG8gcmVkbyB8IGZvcm1hdHNlbGVjdCB8IGJvbGQgaXRhbGljIGJhY2tjb2xvciB8IFxcXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWdubGVmdCBhbGlnbmNlbnRlciBhbGlnbnJpZ2h0IGFsaWduanVzdGlmeSB8IFxcXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJ1bGxpc3QgbnVtbGlzdCBvdXRkZW50IGluZGVudCB8IHJlbW92ZWZvcm1hdCB8IGhlbHAgfHZpc3VhbGJsb2NrcyBjb2RlIGZ1bGxzY3JlZW4nXHJcbiAgICAgICAgICAgICAgICAgICxcclxuXHJcblxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgO1xyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS5mb3JtYXQgPT09ICdkYXRlLXRpbWUnIHx8IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0uZm9ybWF0ID09PSAnZGF0ZScpIHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yRm46IChyb3cpID0+IG5ldyBEYXRlKHJvd1trZXldKSxcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgZmlsdGVyVmFyaWFudDogJ2RhdGUnLFxyXG4gICAgICAgICAgICBmaWx0ZXJGbjogJ2xlc3NUaGFuJyxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAnZGF0ZXRpbWUnLFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsIH0pID0+IChuZXcgRGF0ZShjZWxsLmdldFZhbHVlPHN0cmluZz4oKSkpLnRvTG9jYWxlRGF0ZVN0cmluZygnZnInKSxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGtleSA9PT0gXCJjb25jZXJuZWRfc3RydWN0dXJlc1wiKSB7XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgbXVpVGFibGVGb290ZXJDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwsIHJvdyB9KSA9PiBjZWxsLmdldFZhbHVlPENyaVN0cnVjdHZpZXdbXT4oKS5sZW5ndGggPiAwID8gPFN0YWNrIGRpcmVjdGlvbj17J3Jvdyd9IHNwYWNpbmc9ezF9PntjZWxsLmdldFZhbHVlPENyaVN0cnVjdHZpZXdbXT4oKS5tYXAoKHN0cnVjdDogQ3JpU3RydWN0dmlldykgPT4gPENoaXAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAndmFyKC0tcGluay0zMDApJywgY29sb3I6ICdibGFjaycsIGZvbnRXZWlnaHQ6ICdib2xkJyB9fSBsYWJlbD17c3RydWN0Py5jb2RlX21uZW1vbmlxdWUgPz8gc3RydWN0Py5jb2RlX3N0cnV9PjwvQ2hpcD4pfTwvU3RhY2s+IDogJy8nXHJcblxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKGtleSA9PT0gXCJwcm9wb3Npbmdfc3RydWN0dXJlc1wiKSB7XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgbXVpVGFibGVGb290ZXJDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwsIHJvdyB9KSA9PiBjZWxsLmdldFZhbHVlPENyaVN0cnVjdHZpZXdbXT4oKS5sZW5ndGggPiAwID8gPFN0YWNrIGRpcmVjdGlvbj17J3Jvdyd9IHNwYWNpbmc9ezF9PntjZWxsLmdldFZhbHVlPENyaVN0cnVjdHZpZXdbXT4oKS5tYXAoKHN0cnVjdDogQ3JpU3RydWN0dmlldykgPT4gPENoaXAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAndmFyKC0tY3lhbi0zMDApJywgY29sb3I6ICdibGFjaycsIGZvbnRXZWlnaHQ6ICdib2xkJyB9fSBsYWJlbD17c3RydWN0Py5jb2RlX21uZW1vbmlxdWUgPz8gc3RydWN0Py5jb2RlX3N0cnV9PjwvQ2hpcD4pfTwvU3RhY2s+IDogJy8nXHJcblxyXG5cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGtleSA9PT0gXCJwcm9wb3NlZF9ieVwiKSB7XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgbXVpVGFibGVIZWFkQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUJvZHlDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG11aVRhYmxlRm9vdGVyQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsLCByb3cgfSkgPT4gPFRhZ1xyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT0ndy0xMXJlbSB0ZXh0LXNtJ1xyXG4gICAgICAgICAgICAgIGtleT17cm93Lm9yaWdpbmFsLmNvZGUgKyByb3cub3JpZ2luYWwuY3JlYXRlZH1cclxuICAgICAgICAgICAgICBzZXZlcml0eT17Z2V0U2V2ZXJpdHkoY2VsbC5nZXRWYWx1ZTxzdHJpbmc+KCkpfVxyXG4gICAgICAgICAgICAgIHZhbHVlPXtjZWxsLmdldFZhbHVlPHN0cmluZz4oKX1cclxuICAgICAgICAgICAgPlxyXG5cclxuICAgICAgICAgICAgPC9UYWc+XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChrZXkgPT09IFwidmFsaWRhdGVkXCIpIHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIG11aVRhYmxlRm9vdGVyQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUJvZHlDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNpemU6IDEyMCxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgICAgQ2VsbDogKHsgY2VsbCwgcm93IH0pID0+IGNlbGwuZ2V0VmFsdWU8Ym9vbGVhbj4oKSA/IDxGb250QXdlc29tZUljb24gY2xhc3NOYW1lPSd0ZXh0LWdyZWVuLTUwMCcgc2l6ZT0nMngnIGljb249e2ZhQ2hlY2tTcXVhcmV9IC8+IDogPEZvbnRBd2Vzb21lSWNvbiBjbGFzc05hbWU9J3RleHQtcmVkLTUwMCcgc2l6ZT0nMngnIGljb249e2ZhWG1hcmtDaXJjbGV9IC8+XHJcblxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoa2V5ID09PSBcImV0YXRcIikge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgbXVpVGFibGVIZWFkQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUJvZHlDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG11aVRhYmxlRm9vdGVyQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAvLyBlZGl0U2VsZWN0T3B0aW9uczogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XVsnJHJlZiddLmVudW0gLFxyXG4gICAgICAgICAgICBtdWlFZGl0VGV4dEZpZWxkUHJvcHM6IHtcclxuICAgICAgICAgICAgICBzZWxlY3Q6IHRydWUsXHJcbiAgICAgICAgICAgICAgdmFyaWFudDogJ291dGxpbmVkJyxcclxuICAgICAgICAgICAgICAvLyBjaGlsZHJlbjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XVsnJHJlZiddLmVudW0sXHJcbiAgICAgICAgICAgICAgU2VsZWN0UHJvcHM6IHtcclxuICAgICAgICAgICAgICAgIC8vIG11bHRpcGxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgLy8gdmFsdWU6IGZvcm1TdGF0ZS51c2VyUm9sZXMsXHJcbiAgICAgICAgICAgICAgICAvLyBvbkNoYW5nZTogaGFuZGxlRmllbGRDaGFuZ2VcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgLy8gZXJyb3I6ICEhdmFsaWRhdGlvbkVycm9ycz8uc3RhdGUsXHJcbiAgICAgICAgICAgICAgLy8gaGVscGVyVGV4dDogdmFsaWRhdGlvbkVycm9ycz8uc3RhdGUsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwsIHJvdyB9KSA9PiA8VGFnXHJcbiAgICAgICAgICAgICAga2V5PXtyb3cub3JpZ2luYWwuY29kZSArIHJvdy5vcmlnaW5hbC5jcmVhdGVkfVxyXG4gICAgICAgICAgICAgIHNldmVyaXR5PXtjZWxsLmdldFZhbHVlPHN0cmluZz4oKSA9PT0gXCJOb24gbGFuY8OpZVwiID9cclxuICAgICAgICAgICAgICAgIFwiZGFuZ2VyXCIgOiBjZWxsLmdldFZhbHVlPHN0cmluZz4oKSA9PT0gXCJFbiBjb3Vyc1wiID8gXCJzdWNjZXNzXCIgOiBcImluZm9cIlxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB2YWx1ZT17Y2VsbC5nZXRWYWx1ZTxzdHJpbmc+KCl9XHJcbiAgICAgICAgICAgID5cclxuXHJcbiAgICAgICAgICAgIDwvVGFnPlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoa2V5ID09PSBcInR5cGVcIikge1xyXG4gICAgICAgICAgLy8gY29uc29sZS5sb2coZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS5hbGxPZlswXVsnJHJlZiddLmVudW0pXHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUhlYWRDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG11aVRhYmxlQm9keUNlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfSxcclxuXHJcbiAgICAgICAgICAgIGVkaXRTZWxlY3RPcHRpb25zOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldWyckcmVmJ10uZW51bVxyXG4gICAgICAgICAgICAsIG11aUVkaXRUZXh0RmllbGRQcm9wczoge1xyXG4gICAgICAgICAgICAgIHNlbGVjdDogdHJ1ZSxcclxuICAgICAgICAgICAgICB2YXJpYW50OiAnb3V0bGluZWQnLFxyXG5cclxuICAgICAgICAgICAgICBTZWxlY3RQcm9wczoge1xyXG4gICAgICAgICAgICAgICAgbXVsdGlwbGU6IGZhbHNlLFxyXG4gICAgICAgICAgICAgICAgLy8gdmFsdWU6IGZvcm1TdGF0ZS51c2VyUm9sZXMsXHJcbiAgICAgICAgICAgICAgICAvLyBvbkNoYW5nZTogaGFuZGxlRmllbGRDaGFuZ2VcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC8vIGVycm9yOiAhIXZhbGlkYXRpb25FcnJvcnM/LnN0YXRlLFxyXG4gICAgICAgICAgICAgIC8vIGhlbHBlclRleHQ6IHZhbGlkYXRpb25FcnJvcnM/LnN0YXRlLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUZvb3RlckNlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgICAgQ2VsbDogKHsgY2VsbCwgcm93IH0pID0+XHJcbiAgICAgICAgICAgICAgPFRhZ1xyXG4gICAgICAgICAgICAgICAga2V5PXtyb3cub3JpZ2luYWwuY29kZSArIHJvdy5vcmlnaW5hbC5jcmVhdGVkfVxyXG4gICAgICAgICAgICAgICAgc2V2ZXJpdHk9e1xyXG4gICAgICAgICAgICAgICAgICBjZWxsLmdldFZhbHVlPHN0cmluZz4oKSA9PT0gXCJDb21tYW5kw6llXCIgP1xyXG4gICAgICAgICAgICAgICAgICAgIFwiZGFuZ2VyXCIgOiBjZWxsLmdldFZhbHVlPHN0cmluZz4oKSA9PT0gXCJQbGFuaWZpw6llXCIgPyBcIndhcm5pbmdcIiA6IGNlbGwuZ2V0VmFsdWU8c3RyaW5nPigpID09PSAnQUknID8gXCJpbmZvXCIgOiAnc3VjY2VzcydcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXtjZWxsLmdldFZhbHVlPHN0cmluZz4oKX1cclxuICAgICAgICAgICAgICA+XHJcblxyXG4gICAgICAgICAgICAgIDwvVGFnPlxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGtleSA9PT0gXCJleGVyY2lzZVwiKSB7XHJcbiAgICAgICAgICAvLyBjb25zb2xlLmxvZyhkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLmFsbE9mWzBdWyckcmVmJ10uZW51bSlcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIG11aVRhYmxlSGVhZENlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgbXVpVGFibGVCb2R5Q2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG5cclxuICAgICAgICAgICAgZWRpdFNlbGVjdE9wdGlvbnM6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV1bJyRyZWYnXS5lbnVtXHJcbiAgICAgICAgICAgICwgbXVpRWRpdFRleHRGaWVsZFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgc2VsZWN0OiB0cnVlLFxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ6ICdvdXRsaW5lZCcsXHJcblxyXG4gICAgICAgICAgICAgIFNlbGVjdFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgICBtdWx0aXBsZTogZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAvLyB2YWx1ZTogZm9ybVN0YXRlLnVzZXJSb2xlcyxcclxuICAgICAgICAgICAgICAgIC8vIG9uQ2hhbmdlOiBoYW5kbGVGaWVsZENoYW5nZVxyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgLy8gZXJyb3I6ICEhdmFsaWRhdGlvbkVycm9ycz8uc3RhdGUsXHJcbiAgICAgICAgICAgICAgLy8gaGVscGVyVGV4dDogdmFsaWRhdGlvbkVycm9ycz8uc3RhdGUsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG11aVRhYmxlRm9vdGVyQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsLCByb3cgfSkgPT5cclxuICAgICAgICAgICAgICA8VGFnXHJcbiAgICAgICAgICAgICAgICBrZXk9e3Jvdy5vcmlnaW5hbC5jb2RlICsgcm93Lm9yaWdpbmFsLmNyZWF0ZWR9XHJcbiAgICAgICAgICAgICAgICBzZXZlcml0eT17XHJcbiAgICAgICAgICAgICAgICAgICdzdWNjZXNzJ1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e2NlbGwuZ2V0VmFsdWU8c3RyaW5nPigpfVxyXG4gICAgICAgICAgICAgID5cclxuXHJcbiAgICAgICAgICAgICAgPC9UYWc+XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoa2V5ID09PSBcInRpdGxlXCIpIHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIG11aVRhYmxlSGVhZENlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnbGVmdCcsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG11aVRhYmxlQm9keUNlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnbGVmdCcsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIC8vIGVkaXRTZWxlY3RPcHRpb25zOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldWyckcmVmJ10uZW51bSAsIFxyXG4gICAgICAgICAgICBtdWlFZGl0VGV4dEZpZWxkUHJvcHM6IHtcclxuICAgICAgICAgICAgICAvLyBzZWxlY3Q6IHRydWUsXHJcbiAgICAgICAgICAgICAgdmFyaWFudDogJ291dGxpbmVkJyxcclxuXHJcbiAgICAgICAgICAgICAgU2VsZWN0UHJvcHM6IHtcclxuICAgICAgICAgICAgICAgIG11bHRpcGxlOiBmYWxzZSxcclxuICAgICAgICAgICAgICAgIC8vIHZhbHVlOiBmb3JtU3RhdGUudXNlclJvbGVzLFxyXG4gICAgICAgICAgICAgICAgLy8gb25DaGFuZ2U6IGhhbmRsZUZpZWxkQ2hhbmdlXHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAvLyBlcnJvcjogISF2YWxpZGF0aW9uRXJyb3JzPy5zdGF0ZSxcclxuICAgICAgICAgICAgICAvLyBoZWxwZXJUZXh0OiB2YWxpZGF0aW9uRXJyb3JzPy5zdGF0ZSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgbXVpVGFibGVGb290ZXJDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwsIHJvdyB9KSA9PiA8ZGl2IGNsYXNzTmFtZT0nd2hpdGUtc3BhY2Utbm9ybWFsJz57Y2VsbC5nZXRWYWx1ZSgpfTwvZGl2PlxyXG5cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGtleSA9PT0gJ2RvbWFpbicpIHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIG11aVRhYmxlRm9vdGVyQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUJvZHlDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwsIHJvdyB9KSA9PiBjZWxsLmdldFZhbHVlPERvbWFpbj4oKT8udGl0bGUgPz8gJy8nXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChrZXkgPT09ICdwcm9jZXNzJykge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgbXVpVGFibGVGb290ZXJDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG11aVRhYmxlQm9keUNlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgICAgQ2VsbDogKHsgY2VsbCwgcm93IH0pID0+IGNlbGwuZ2V0VmFsdWU8UHJvY2Vzcz4oKT8udGl0bGUgPz8gJy8nXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgaWYgKGtleSA9PT0gXCJpZFwiKSByZXR1cm4ge1xyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBFZGl0OiAoKSA9PiBudWxsLFxyXG4gICAgICAgICAgfTsgZWxzZVxyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICAgIC8vIEVkaXQ6ICgpID0+IG51bGwsXHJcbiAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgIH1cclxuICAgICAgfSksXHJcbiAgICBbXSxcclxuICApO1xyXG5cclxuICBjb25zdCB0YWJsZSA9IHVzZU1hdGVyaWFsUmVhY3RUYWJsZTxUPih7XHJcbiAgICBjb2x1bW5zLFxyXG4gICAgZGF0YTogZGF0YV8uZXJyb3IgPyBbXSA6IGRhdGFfLnJlc3VsdHMgPyBkYXRhXy5yZXN1bHRzIDogW2RhdGFfLmRhdGFfLmRhdGFdLCAvL211c3QgYmUgbWVtb2l6ZWQgb3Igc3RhYmxlICh1c2VTdGF0ZSwgdXNlTWVtbywgZGVmaW5lZCBvdXRzaWRlIG9mIHRoaXMgY29tcG9uZW50LCBldGMuKVxyXG4gICAgcm93Q291bnQ6IGRhdGFfLmVycm9yID8gMCA6IGRhdGFfLnJlc3VsdHMgPyBkYXRhXy5jb3VudCA6IDEsXHJcbiAgICBlbmFibGVSb3dTZWxlY3Rpb246IHRydWUsIC8vZW5hYmxlIHNvbWUgZmVhdHVyZXNcclxuICAgIGVuYWJsZUNvbHVtbk9yZGVyaW5nOiB0cnVlLCAvL2VuYWJsZSBhIGZlYXR1cmUgZm9yIGFsbCBjb2x1bW5zXHJcbiAgICBlbmFibGVHbG9iYWxGaWx0ZXI6IHRydWUsIC8vdHVybiBvZmYgYSBmZWF0dXJlXHJcbiAgICBlbmFibGVHcm91cGluZzogdHJ1ZSxcclxuICAgIGVuYWJsZVJvd0FjdGlvbnM6IHRydWUsXHJcbiAgICBlbmFibGVSb3dQaW5uaW5nOiB0cnVlLFxyXG4gICAgZW5hYmxlU3RpY2t5SGVhZGVyOiB0cnVlLFxyXG4gICAgZW5hYmxlU3RpY2t5Rm9vdGVyOiB0cnVlLFxyXG4gICAgZW5hYmxlQ29sdW1uUGlubmluZzogdHJ1ZSxcclxuICAgIGVuYWJsZUNvbHVtblJlc2l6aW5nOiB0cnVlLFxyXG4gICAgZW5hYmxlUm93TnVtYmVyczogdHJ1ZSxcclxuICAgIGVuYWJsZUV4cGFuZEFsbDogdHJ1ZSxcclxuICAgIGVuYWJsZUVkaXRpbmc6IHRydWUsXHJcbiAgICBlbmFibGVFeHBhbmRpbmc6IGZhbHNlLFxyXG4gICAgbWFudWFsUGFnaW5hdGlvbjogdHJ1ZSxcclxuICAgIGluaXRpYWxTdGF0ZToge1xyXG4gICAgICBwYWdpbmF0aW9uOiB7IHBhZ2VTaXplOiA1LCBwYWdlSW5kZXg6IDEgfSxcclxuICAgICAgY29sdW1uVmlzaWJpbGl0eTogeyBjcmVhdGVkX2J5OiBmYWxzZSwgY3JlYXRlZDogZmFsc2UsIG1vZGZpZWRfYnk6IGZhbHNlLCBtb2RpZmllZDogZmFsc2UsIG1vZGlmaWVkX2J5OiBmYWxzZSwgc3RhZmY6IGZhbHNlLCBhc3Npc3RhbnRzOiBmYWxzZSwgaWQ6IGZhbHNlLCBkb2N1bWVudDogZmFsc2UgfSxcclxuICAgICAgZGVuc2l0eTogJ2NvbXBhY3QnLFxyXG4gICAgICBzaG93R2xvYmFsRmlsdGVyOiB0cnVlLFxyXG4gICAgICBzb3J0aW5nOiBbeyBpZDogJ2lkJywgZGVzYzogZmFsc2UgfV0sXHJcbiAgICB9LFxyXG4gICAgc3RhdGU6IHtcclxuICAgICAgcGFnaW5hdGlvbjogZGF0YV8ucGFnaW5hdGlvbi5wYWdpLFxyXG4gICAgICBpc0xvYWRpbmc6IGRhdGFfLmlzTG9hZGluZywgLy9jZWxsIHNrZWxldG9ucyBhbmQgbG9hZGluZyBvdmVybGF5XHJcbiAgICAgIC8vc2hvd1Byb2dyZXNzQmFyczogaXNMb2FkaW5nLCAvL3Byb2dyZXNzIGJhcnMgd2hpbGUgcmVmZXRjaGluZ1xyXG4gICAgICBpc1NhdmluZzogaXNNdXRhdGluZ19jcmVhdGUsIC8vcHJvZ3Jlc3MgYmFycyBhbmQgc2F2ZSBidXR0b24gc3Bpbm5lcnNcclxuICAgIH0sXHJcbiAgICBsb2NhbGl6YXRpb246IE1SVF9Mb2NhbGl6YXRpb25fRlIsXHJcbiAgICBvblBhZ2luYXRpb25DaGFuZ2U6IG9uUGFnaW5hdGlvbkNoYW5nZSxcclxuICAgIGRpc3BsYXlDb2x1bW5EZWZPcHRpb25zOiB7XHJcbiAgICAgICdtcnQtcm93LXBpbic6IHtcclxuICAgICAgICBlbmFibGVIaWRpbmc6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgICdtcnQtcm93LWV4cGFuZCc6IHtcclxuICAgICAgICBlbmFibGVIaWRpbmc6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgICdtcnQtcm93LWFjdGlvbnMnOiB7XHJcbiAgICAgICAgc2l6ZTogMTQwLFxyXG4gICAgICAgIGVuYWJsZUhpZGluZzogdHJ1ZSxcclxuICAgICAgICBncm93OiB0cnVlXHJcbiAgICAgIH0sXHJcbiAgICAgICdtcnQtcm93LW51bWJlcnMnOiB7XHJcbiAgICAgICAgZW5hYmxlSGlkaW5nOiB0cnVlLCAvL25vdyByb3cgbnVtYmVycyBhcmUgaGlkYWJsZSB0b29cclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBkZWZhdWx0Q29sdW1uOiB7XHJcbiAgICAgIGdyb3c6IHRydWUsXHJcbiAgICAgIGVuYWJsZU11bHRpU29ydDogdHJ1ZSxcclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZVBhcGVyUHJvcHM6ICh7IHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIC8vZWxldmF0aW9uOiAwLCAvL2NoYW5nZSB0aGUgbXVpIGJveCBzaGFkb3dcclxuICAgICAgLy9jdXN0b21pemUgcGFwZXIgc3R5bGVzXHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBjbGFzc2VzOiB7IHJvb3Q6ICdwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bCcgfSxcclxuICAgICAgc3g6IHtcclxuICAgICAgICBoZWlnaHQ6IGBjYWxjKDEwMHZoIC0gOXJlbSlgLFxyXG4gICAgICAgIC8vIGhlaWdodDogYGNhbGMoMTAwdmggLSAyMDBweClgLFxyXG4gICAgICAgIC8vIGJvcmRlclJhZGl1czogJzAnLFxyXG4gICAgICAgIC8vIGJvcmRlcjogJzFweCBkYXNoZWQgI2UwZTBlMCcsXHJcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZClcIixcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgICAgXCImIC5NdWlUYWJsZVBhZ2luYXRpb24tcm9vdCBcIjoge1xyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgICAgICBjb2xvcjogXCJ2YXIoLS1zdXJmYWNlLTkwMCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgXCImIC5NdWlTdmdJY29uLXJvb3RcIjoge1xyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIFwiJiAuTXVpSW5wdXRCYXNlLWlucHV0XCI6IHtcclxuICAgICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBcIiYgLk11aVRhYmxlU29ydExhYmVsLXJvb3RcIjoge1xyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIFwiJiAuTXVpQm94LXJvb3QgXCI6IHtcclxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfSksXHJcbiAgICAvLyBlZGl0RGlzcGxheU1vZGU6ICdtb2RhbCcsXHJcbiAgICAvLyBjcmVhdGVEaXNwbGF5TW9kZTogJ21vZGFsJyxcclxuICAgIG9uRWRpdGluZ1Jvd1NhdmU6ICh7IHRhYmxlLCByb3csIHZhbHVlcyB9KSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwib25FZGl0aW5nUm93U2F2ZVwiLCB2YWx1ZXMpXHJcbiAgICAgIGNvbnN0IHsgY3JlYXRlZCwgbW9kaWZpZWQsIGlkLCAuLi5yZXN0IH0gPSB2YWx1ZXM7XHJcbiAgICAgIHNldFRoZW1lSWQoaWQpXHJcbiAgICAgIHRyaWdnZXJfbW9kaWZ5KFxyXG4gICAgICAge2lkOmlkLCBkYXRhOnJlc3R9LFxyXG4gICAgICAgIHtcclxuXHJcbiAgICAgICAgICBvblN1Y2Nlc3M6ICgpID0+IHtcclxuICAgICAgICAgICAgdGFibGUuc2V0RWRpdGluZ1JvdyhudWxsKTsgLy9leGl0IGVkaXRpbmcgbW9kZVxyXG4gICAgICAgICAgICB0b2FzdC5jdXJyZW50Py5zaG93KHsgc2V2ZXJpdHk6ICdzdWNjZXNzJywgc3VtbWFyeTogJ01vZGlmaWNhdGlvbicsIGRldGFpbDogJ0VucmVnaXN0cmVtZW50IG1vZGlmacOpJyB9KTtcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcclxuICAgICAgICAgICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnZXJyb3InLCBzdW1tYXJ5OiAnTW9kaWZpY2F0aW9uJywgZGV0YWlsOiBgJHtlcnJvci5tZXNzYWdlfWAgfSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwib25FZGl0aW5nUm93U2F2ZVwiLCBlcnJvci5tZXNzYWdlKTtcclxuICAgICAgICAgICAgcm93Ll92YWx1ZXNDYWNoZSA9IHsgZXJyb3I6IGVycm9yLm1lc3NhZ2UsIC4uLnJvdy5fdmFsdWVzQ2FjaGUgfTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9XHJcbiAgICAgIClcclxuXHJcbiAgICB9LFxyXG4gICAgb25FZGl0aW5nUm93Q2FuY2VsOiAoKSA9PiB7XHJcbiAgICAgIC8vY2xlYXIgYW55IHZhbGlkYXRpb24gZXJyb3JzXHJcbiAgICAgIHRvYXN0LmN1cnJlbnQ/LnNob3coeyBzZXZlcml0eTogJ2luZm8nLCBzdW1tYXJ5OiAnSW5mbycsIGRldGFpbDogJ0FubnVsYXRpb24nIH0pO1xyXG4gICAgfSxcclxuICAgIG9uQ3JlYXRpbmdSb3dTYXZlOiAoeyB0YWJsZSwgdmFsdWVzLCByb3cgfSkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIm9uQ3JlYXRpbmdSb3dTYXZlXCIsIHZhbHVlcylcclxuICAgICAgY29uc3QgeyBjcmVhdGVkLCBtb2RpZmllZCwgaWQsIC4uLnJlc3QgfSA9IHZhbHVlcztcclxuICAgICAgdHJpZ2dlcl9jcmVhdGUoXHJcbiAgICAgICAgcmVzdCxcclxuICAgICAgICB7XHJcbiAgICAgICAgICByZXZhbGlkYXRlOiB0cnVlLFxyXG4gICAgICAgICAgcG9wdWxhdGVDYWNoZTogdHJ1ZSxcclxuICAgICAgICAgIG9uU3VjY2VzczogKCkgPT4ge1xyXG4gICAgICAgICAgICB0YWJsZS5zZXRDcmVhdGluZ1JvdyhudWxsKTtcclxuICAgICAgICAgICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnc3VjY2VzcycsIHN1bW1hcnk6ICdDcsOpYXRpb24nLCBkZXRhaWw6ICdFbnJlZ2lzdHJlbWVudCBjcsOpw6knIH0pO1xyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIG9uRXJyb3I6IChlcnIpID0+IHtcclxuICAgICAgICAgICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnZXJyb3InLCBzdW1tYXJ5OiAnQ3LDqWF0aW9uJywgZGV0YWlsOiBgJHtlcnIucmVzcG9uc2U/LnN0YXR1c1RleHR9YCB9KTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJvbkNyZWF0aW5nUm93U2F2ZVwiLCBlcnIucmVzcG9uc2UpO1xyXG4gICAgICAgICAgICByb3cuX3ZhbHVlc0NhY2hlID0geyBlcnJvcjogZXJyLnJlc3BvbnNlLCAuLi5yb3cuX3ZhbHVlc0NhY2hlIH07XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfVxyXG4gICAgICApXHJcbiAgICB9LFxyXG4gICAgb25DcmVhdGluZ1Jvd0NhbmNlbDogKCkgPT4ge1xyXG4gICAgICAvL2NsZWFyIGFueSB2YWxpZGF0aW9uIGVycm9yc1xyXG4gICAgICB0b2FzdC5jdXJyZW50Py5zaG93KHsgc2V2ZXJpdHk6ICdpbmZvJywgc3VtbWFyeTogJ0luZm8nLCBkZXRhaWw6ICdBbm51bGF0aW9uJyB9KTtcclxuICAgIH0sXHJcbiAgICBtdWlFZGl0Um93RGlhbG9nUHJvcHM6ICh7IHJvdywgdGFibGUgfSkgPT4gKHtcclxuICAgICAgLy9vcHRpb25hbGx5IGN1c3RvbWl6ZSB0aGUgZGlhbG9nXHJcbiAgICAgIC8vIGFib3V0OlwiZWRpdCBtb2RhbFwiLFxyXG4gICAgICBvcGVuOiBlZGl0VmlzaWJsZSB8fCBjcmVhdGVWaXNpYmxlLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgICcmIC5NdWlEaWFsb2ctcm9vdCc6IHtcclxuICAgICAgICAgIC8vIGRpc3BsYXk6ICdub25lJ1xyXG5cclxuICAgICAgICB9LFxyXG4gICAgICAgICcmIC5NdWlEaWFsb2ctY29udGFpbmVyJzoge1xyXG4gICAgICAgICAgLy8gIGRpc3BsYXk6ICdub25lJyxcclxuICAgICAgICAgIFwiJiAuTXVpUGFwZXItcm9vdFwiOiB7XHJcbiAgICAgICAgICAgIG1heFdpZHRoOiAnNjB2dydcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgfSxcclxuICAgICAgICB6SW5kZXg6IDExMDBcclxuXHJcbiAgICAgIH1cclxuICAgIH0pLFxyXG4gICAgbXVpVGFibGVGb290ZXJQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBcIiYgLk11aVRhYmxlRm9vdGVyLXJvb3RcIjoge1xyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIG11aVRhYmxlQ29udGFpbmVyUHJvcHM6ICh7IHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgaGVpZ2h0OiB0YWJsZS5nZXRTdGF0ZSgpLmlzRnVsbFNjcmVlbiA/IGBjYWxjKDEwMHZoKWAgOiBgY2FsYygxMDB2aCAtIDlyZW0gLSAke3RhYmxlLnJlZnMudG9wVG9vbGJhclJlZi5jdXJyZW50Py5vZmZzZXRIZWlnaHR9cHggLSAke3RhYmxlLnJlZnMuYm90dG9tVG9vbGJhclJlZi5jdXJyZW50Py5vZmZzZXRIZWlnaHR9cHgpYFxyXG5cclxuICAgICAgfSxcclxuICAgIH0pLFxyXG4gICAgbXVpUGFnaW5hdGlvblByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG5cclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICBjb2xvcjogXCJ2YXIoLS1zdXJmYWNlLTkwMCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZUhlYWRDZWxsUHJvcHM6IHtcclxuICAgICAgc3g6IHtcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgbXVpVG9wVG9vbGJhclByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIlxyXG4gICAgICB9LFxyXG5cclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZUJvZHlQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgICAgLy9zdHJpcGUgdGhlIHJvd3MsIG1ha2Ugb2RkIHJvd3MgYSBkYXJrZXIgY29sb3JcclxuICAgICAgICAnJiB0cjpudGgtb2YtdHlwZShvZGQpID4gdGQnOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1zdXJmYWNlLWNhcmQpJyxcclxuICAgICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgICcmIHRyOm50aC1vZi10eXBlKGV2ZW4pID4gdGQnOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1zdXJmYWNlLWJvcmRlciknLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgcmVuZGVyVG9wVG9vbGJhckN1c3RvbUFjdGlvbnM6ICh7IHRhYmxlIH0pID0+IChcclxuICAgICAgPFN0YWNrIGRpcmVjdGlvbj17XCJyb3dcIn0gc3BhY2luZz17MX0+XHJcbiAgICAgICAgPENhbiBJPSdhZGQnIGE9J3RoZW1lJz5cclxuICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgaWNvbj1cInBpIHBpLXBsdXNcIlxyXG4gICAgICAgICAgICByb3VuZGVkXHJcbiAgICAgICAgICAgIC8vIGlkPVwiYmFzaWMtYnV0dG9uXCJcclxuICAgICAgICAgICAgYXJpYS1jb250cm9scz17b3BlbiA/ICdiYXNpYy1tZW51JyA6IHVuZGVmaW5lZH1cclxuICAgICAgICAgICAgYXJpYS1oYXNwb3B1cD1cInRydWVcIlxyXG4gICAgICAgICAgICBhcmlhLWV4cGFuZGVkPXtvcGVuID8gJ3RydWUnIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgICB0YWJsZS5zZXRDcmVhdGluZ1Jvdyh0cnVlKTsgc2V0Q3JlYXRlVmlzaWJsZSh0cnVlKSwgY29uc29sZS5sb2coXCJjcmVhdGluZyByb3cgLi4uXCIpO1xyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBzaXplPVwic21hbGxcIlxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0Nhbj5cclxuICAgICAgICA8Q2FuIEk9J2RlbGV0ZScgYT0ndGhlbWUnPlxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICByb3VuZGVkXHJcbiAgICAgICAgICAgIGRpc2FibGVkPXt0YWJsZS5nZXRJc1NvbWVSb3dzU2VsZWN0ZWQoKX1cclxuICAgICAgICAgICAgLy8gaWQ9XCJiYXNpYy1idXR0b25cIlxyXG4gICAgICAgICAgICBhcmlhLWNvbnRyb2xzPXtvcGVuID8gJ2Jhc2ljLW1lbnUnIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICBhcmlhLWhhc3BvcHVwPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgIGFyaWEtZXhwYW5kZWQ9e29wZW4gPyAndHJ1ZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxyXG4gICAgICAgICAgICBpY29uPVwicGkgcGktdHJhc2hcIlxyXG4gICAgICAgICAgICAvLyBzdHlsZT17eyAgYm9yZGVyUmFkaXVzOiAnMCcsIGJveFNoYWRvdzogJ25vbmUnLCBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcgfX1cclxuICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9DYW4+XHJcbiAgICAgICAgPENhbiBJPSdhZGQnIGE9J2FyYml0cmF0ZWR0aGVtZSc+XHJcbiAgICAgICAgICA8QnV0dG9uIGljb249XCJwaSBwaS1iZWxsXCIgcm91bmRlZCBjb2xvcj17cm93QWN0aW9uRW5hYmxlZCA/IFwic2Vjb25kYXJ5XCIgOiBcInByaW1hcnlcIn0gc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICAgICAgYXJpYS1sYWJlbD1cImVkaXRcIiBvbkNsaWNrPXsoKSA9PiBzZXRSb3dBY3Rpb25FbmFibGVkKCFyb3dBY3Rpb25FbmFibGVkKX0gLz5cclxuICAgICAgICA8L0Nhbj5cclxuICAgICAgPC9TdGFjaz5cclxuICAgICksXHJcbiAgICBtdWlEZXRhaWxQYW5lbFByb3BzOiAoKSA9PiAoe1xyXG4gICAgICBzeDogKHRoZW1lKSA9PiAoe1xyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjpcclxuICAgICAgICAgIHRoZW1lLnBhbGV0dGUubW9kZSA9PT0gJ2RhcmsnXHJcbiAgICAgICAgICAgID8gJ3JnYmEoMjU1LDIxMCwyNDQsMC4xKSdcclxuICAgICAgICAgICAgOiAncmdiYSgwLDAsMCwwLjEpJyxcclxuICAgICAgfSksXHJcbiAgICB9KSxcclxuICAgIHJlbmRlckNyZWF0ZVJvd0RpYWxvZ0NvbnRlbnQ6IFJlY29tbWVuZGF0aW9uRWRpdEZvcm0sXHJcbiAgICByZW5kZXJFZGl0Um93RGlhbG9nQ29udGVudDogUmVjb21tZW5kYXRpb25FZGl0Rm9ybSxcclxuICAgIC8vIHJlbmRlckRldGFpbFBhbmVsOiAoeyByb3cgfSkgPT5cclxuICAgIC8vICAgcm93Lm9yaWdpbmFsLnN0YWZmID9cclxuICAgIC8vICAgICAoXHJcbiAgICAvLyAgICAgICA8Qm94XHJcbiAgICAvLyAgICAgICAgIHN4PXt7XHJcbiAgICAvLyAgICAgICAgICAgZGlzcGxheTogJ2dyaWQnLFxyXG4gICAgLy8gICAgICAgICAgIG1hcmdpbjogJ2F1dG8nLFxyXG4gICAgLy8gICAgICAgICAgIC8vZ3JpZFRlbXBsYXRlQ29sdW1uczogJzFmciAxZnInLFxyXG4gICAgLy8gICAgICAgICAgIHdpZHRoOiAnMTAwdncnLFxyXG4gICAgLy8gICAgICAgICB9fVxyXG4gICAgLy8gICAgICAgPlxyXG4gICAgLy8gICAgICAgICA8VGFiVmlldz5cclxuXHJcbiAgICAvLyAgICAgICAgICAgPFRhYlBhbmVsIGhlYWRlcj17ZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNbXCJzdGFmZlwiXS50aXRsZX0gbGVmdEljb249XCJwaSBwaS11c2VyIG1yLTJcIj5cclxuXHJcbiAgICAvLyAgICAgICAgICAgICA8dWw+e3Jvdy5vcmlnaW5hbC5zdGFmZi5zdGFmZi5tYXAoKHVzZXIsIGlkeCkgPT4gPGEga2V5PXt1c2VyLmVtYWlsICsgcm93Lm9yaWdpbmFsLmNvZGV9IGhyZWY9e1wibWFpbHRvOlwiICsgdXNlci5lbWFpbH0+PGxpPnt1c2VyLmxhc3RfbmFtZX0ge3VzZXIuZmlyc3RfbmFtZX08L2xpPjwvYT4pfTwvdWw+XHJcbiAgICAvLyAgICAgICAgICAgPC9UYWJQYW5lbD5cclxuICAgIC8vICAgICAgICAgICA8VGFiUGFuZWwgaGVhZGVyPXtcIkFzc2lzdGFudHNcIn0gcmlnaHRJY29uPVwicGkgcGktdXNlciBtbC0yXCI+XHJcblxyXG4gICAgLy8gICAgICAgICAgICAgPHVsPntyb3cub3JpZ2luYWwuc3RhZmYuYXNzaXN0YW50cy5tYXAoKHVzZXIsIGlkeCkgPT4gPGEga2V5PXt1c2VyLmVtYWlsICsgcm93Lm9yaWdpbmFsLmNvZGV9IGhyZWY9e1wibWFpbHRvOlwiICsgdXNlci5lbWFpbH0+PGxpPnt1c2VyLmxhc3RfbmFtZX0ge3VzZXIuZmlyc3RfbmFtZX08L2xpPjwvYT4pfTwvdWw+XHJcblxyXG4gICAgLy8gICAgICAgICAgIDwvVGFiUGFuZWw+XHJcbiAgICAvLyAgICAgICAgICAgPFRhYlBhbmVsIGhlYWRlcj1cIkxldHRyZVwiIGxlZnRJY29uPVwicGkgcGktZmlsZS13b3JkIG1yLTJcIiByaWdodEljb249XCJwaSBwaS1maWxlLXBkZiBtbC0yXCI+XHJcbiAgICAvLyAgICAgICAgICAgICA8QnV0dG9uIGljb249XCJwaSBwaS1jaGVja1wiIHJvdW5kZWQgb25DbGljaz17KCkgPT4gc2V0VmlzaWJsZSh0cnVlKX0gZGlzYWJsZWQ9e3Jvdy5vcmlnaW5hbC5kb2N1bWVudCA9PT0gbnVsbH0gLz5cclxuICAgIC8vICAgICAgICAgICAgIDxTaWRlYmFyIGtleT17cm93Lm9yaWdpbmFsLmlkfSBoZWFkZXI9ezxoMj5MZXR0cmUgZGUgbWlzc2lvbiA6IHtyb3cub3JpZ2luYWwuY29kZX08L2gyPn0gdmlzaWJsZT17dmlzaWJsZX0gb25IaWRlPXsoKSA9PiBzZXRWaXNpYmxlKGZhbHNlKX0gY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctOSBsZzp3LThcIj5cclxuXHJcbiAgICAvLyAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbHVtblx0YWxpZ24taXRlbXMtY2VudGVyIGp1c3RpZnktY29udGVudC1jZW50ZXIgZ2FwLTFcIj5cclxuICAgIC8vICAgICAgICAgICAgICAgICB7cm93Lm9yaWdpbmFsLmRvY3VtZW50ICE9PSBudWxsID9cclxuICAgIC8vICAgICAgICAgICAgICAgICAgIDxEb2N1bWVudFxyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgICBmaWxlPXtyb3cub3JpZ2luYWwuZG9jdW1lbnR9XHJcbiAgICAvLyAgICAgICAgICAgICAgICAgICAgIG9uTG9hZFN1Y2Nlc3M9e29uRG9jdW1lbnRMb2FkU3VjY2Vzc31cclxuICAgIC8vICAgICAgICAgICAgICAgICAgID5cclxuICAgIC8vICAgICAgICAgICAgICAgICAgICAgPFBhZ2UgcGFnZU51bWJlcj17cGFnZU51bWJlcn0gLz5cclxuICAgIC8vICAgICAgICAgICAgICAgICAgIDwvRG9jdW1lbnQ+IDogPHA+Tm8gRG9jdW1lbnQ8L3A+fVxyXG4gICAgLy8gICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtY29sdW1uXHRhbGlnbi1pdGVtcy1jZW50ZXIganVzdGlmeS1jb250ZW50LWNlbnRlciBnYXAtMScgPlxyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgPHA+XHJcbiAgICAvLyAgICAgICAgICAgICAgICAgICAgIFBhZ2Uge3BhZ2VOdW1iZXIgfHwgKG51bVBhZ2VzID8gMSA6ICctLScpfSBvZiB7bnVtUGFnZXMgfHwgJy0tJ31cclxuICAgIC8vICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgIC8vICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtcm93XHRhbGlnbi1pdGVtcy1jZW50ZXIganVzdGlmeS1jb250ZW50LWNlbnRlciBnYXAtMScgPlxyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAvLyAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAvLyAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3BhZ2VOdW1iZXIgPD0gMX1cclxuICAgIC8vICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtwcmV2aW91c1BhZ2V9XHJcbiAgICAvLyAgICAgICAgICAgICAgICAgICAgID5cclxuICAgIC8vICAgICAgICAgICAgICAgICAgICAgICBQcmV2aW91c1xyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgIC8vICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtwYWdlTnVtYmVyID49IG51bVBhZ2VzfVxyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e25leHRQYWdlfVxyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAvLyAgICAgICAgICAgICAgICAgICAgICAgTmV4dFxyXG4gICAgLy8gICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgIC8vICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgLy8gICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgLy8gICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgIC8vICAgICAgICAgICAgIDwvU2lkZWJhcj5cclxuICAgIC8vICAgICAgICAgICA8L1RhYlBhbmVsPiAgICAgICAgICA8L1RhYlZpZXc+XHJcbiAgICAvLyAgICAgICA8L0JveCA+XHJcbiAgICAvLyAgICAgKSA6IDw+PC8+LFxyXG4gICAgcmVuZGVyUm93QWN0aW9uczogKHsgY2VsbCwgcm93LCB0YWJsZSB9KSA9PiAoXHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInAtYnV0dG9uc2V0IGZsZXggcC0xXCI+XHJcbiAgICAgICAgPEJ1dHRvbiBzaXplPSdzbWFsbCcgaWNvbj1cInBpIHBpLWV5ZVwiIG9uQ2xpY2s9eygpID0+IHsgc2V0VGhlbWVJZChyb3cub3JpZ2luYWwuaWQpOyBzZXREZXRhaWxzRGlhbG9nVmlzaWJsZSh0cnVlKSB9fSByb3VuZGVkIG91dGxpbmVkIC8+XHJcbiAgICAgICAge2FiaWxpdHkuY2FuKCdjaGFuZ2UnLCd0aGVtZScpICYmIFxyXG4gICAgICAgIDxCdXR0b24gc2l6ZT0nc21hbGwnIGljb249XCJwaSBwaS1wZW5jaWxcIiBvbkNsaWNrPXsoKSA9PiB7IHNldFRoZW1lSWQocm93Lm9yaWdpbmFsLmlkKTsgdGFibGUuc2V0RWRpdGluZ1Jvdyhyb3cpOyBzZXRFZGl0VmlzaWJsZSh0cnVlKSwgY29uc29sZS5sb2coXCJlZGl0aW5nIHJvdyAuLi5cIik7IH19IHJvdW5kZWQgb3V0bGluZWQgLz5cclxuICAgICAgICAgfSBcclxuICAgICAgICB7YWJpbGl0eS5jYW4oJ2RlbGV0ZScsJ3RoZW1lJykgJiYgPEJ1dHRvbiBzaXplPSdzbWFsbCcgaWNvbj1cInBpIHBpLXRyYXNoXCIgcm91bmRlZCBvdXRsaW5lZFxyXG4gICAgICAgICAgb25DbGljaz17KGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgIHNldFRoZW1lSWQocm93Lm9yaWdpbmFsLmlkKTsgY29uZmlybVBvcHVwKHtcclxuICAgICAgICAgICAgICB0YXJnZXQ6IGV2ZW50LmN1cnJlbnRUYXJnZXQsXHJcbiAgICAgICAgICAgICAgbWVzc2FnZTogJ1ZvdWxlei12b3VzIHN1cHByaW1lciBjZXR0ZSBsaWduZT8nLFxyXG4gICAgICAgICAgICAgIGljb246ICdwaSBwaS1pbmZvLWNpcmNsZScsXHJcbiAgICAgICAgICAgICAgLy8gZGVmYXVsdEZvY3VzOiAncmVqZWN0JyxcclxuICAgICAgICAgICAgICBhY2NlcHRDbGFzc05hbWU6ICdwLWJ1dHRvbi1kYW5nZXInLFxyXG4gICAgICAgICAgICAgIGFjY2VwdExhYmVsOiAnT3VpJyxcclxuICAgICAgICAgICAgICByZWplY3RMYWJlbDogJ05vbicsXHJcbiAgICAgICAgICAgICAgYWNjZXB0LFxyXG4gICAgICAgICAgICAgIHJlamVjdFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAvPn1cclxuICAgICAgICA8Q29uZmlybVBvcHVwIC8+XHJcbiAgICAgIDwvc3Bhbj5cclxuICAgICAgLy8gPC9Cb3g+XHJcbiAgICApLFxyXG4gICAgLy8gbXJ0VGhlbWU6ICh0aGVtZSkgPT4gKHtcclxuICAgIC8vICAgYmFzZUJhY2tncm91bmRDb2xvcjogXCIjMWYyOTM3XCIsXHJcbiAgICAvLyAgIGRyYWdnaW5nQm9yZGVyQ29sb3I6IHRoZW1lLnBhbGV0dGUuc2Vjb25kYXJ5Lm1haW4sXHJcbiAgICAvLyB9KSxcclxuXHJcbiAgfSk7XHJcblxyXG4gIHJldHVybiAoPD5cclxuICAgIDxNYXRlcmlhbFJlYWN0VGFibGUgdGFibGU9e3RhYmxlfSAvPlxyXG4gICAgPFRvYXN0IHJlZj17dG9hc3R9IC8+XHJcbiAgICA8RGlhbG9nIG1heGltaXphYmxlIGhlYWRlcj17YFRIRU1FIElEIDogJHt0aGVtZV9pZH1gfSB2aXNpYmxlPXtkZXRhaWxzRGlhbG9nVmlzaWJsZX0gc3R5bGU9e3sgd2lkdGg6ICc5MHZ3JyB9fSBvbkhpZGU9eygpID0+IHsgaWYgKCFkZXRhaWxzRGlhbG9nVmlzaWJsZSkgcmV0dXJuOyBzZXREZXRhaWxzRGlhbG9nVmlzaWJsZShmYWxzZSk7IH19PlxyXG4gICAgICB7LyogPFJlY29tbWVuZGF0aW9uRGV0YWlscyBwYXJhbXM9e3sgcmVjb21tZW5kYXRpb25faWQ6IHJlY29tbWVuZGF0aW9uX2lkIH19IC8+ICovfVxyXG4gICAgPC9EaWFsb2c+XHJcbiAgPC8+KTtcclxufVxyXG4iXSwibmFtZXMiOlsiQ2hpcCIsIlN0YWNrIiwiRWRpdG9yIiwicGFyc2UiLCJNYXRlcmlhbFJlYWN0VGFibGUiLCJ1c2VNYXRlcmlhbFJlYWN0VGFibGUiLCJNUlRfTG9jYWxpemF0aW9uX0ZSIiwidXNlUm91dGVyIiwiQnV0dG9uIiwiQ29uZmlybVBvcHVwIiwiY29uZmlybVBvcHVwIiwiRGlhbG9nIiwiVGFnIiwiVG9hc3QiLCJ1c2VFZmZlY3QiLCJ1c2VNZW1vIiwidXNlUmVmIiwidXNlU3RhdGUiLCJSZWNvbW1lbmRhdGlvbkVkaXRGb3JtIiwiZ2V0Q29va2llIiwiYWJpbGl0eSIsIkZvbnRBd2Vzb21lSWNvbiIsImZhQ2hlY2tTcXVhcmUiLCJmYVhtYXJrQ2lyY2xlIiwiQ2FuIiwidXNlQXBpVGhlbWVDcmVhdGUiLCJ1c2VBcGlUaGVtZURlc3Ryb3kiLCJ1c2VBcGlUaGVtZVVwZGF0ZSIsIkdlbmVyaWNUYWJsZU9wcG9ydHVuaXR5U2hlZXQiLCJkYXRhXyIsInVzZXIiLCJKU09OIiwidG9TdHJpbmciLCJ0b2FzdCIsInRoZW1lX2lkIiwic2V0VGhlbWVJZCIsInB1c2giLCJ2aXNpYmxlIiwic2V0VmlzaWJsZSIsImRhdGEiLCJkYXRhX2NyZWF0ZSIsImVycm9yIiwiZXJyb3JfY3JlYXRlIiwiaXNQZW5kaW5nIiwiaXNNdXRhdGluZ19jcmVhdGUiLCJtdXRhdGUiLCJ0cmlnZ2VyX2NyZWF0ZSIsImRhdGFfbW9kaWZ5IiwiZXJyb3JfbW9kaWZ5IiwiaXNNdXRhdGluZ19tb2RpZnkiLCJ0cmlnZ2VyX21vZGlmeSIsImRhdGFfZGVsZXRlIiwiZXJyb3JfZGVsZXRlIiwiaXNNdXRhdGluZ19kZWxldGUiLCJ0cmlnZ2VyX2RlbGV0ZSIsImRldGFpbHNEaWFsb2dWaXNpYmxlIiwic2V0RGV0YWlsc0RpYWxvZ1Zpc2libGUiLCJlZGl0VmlzaWJsZSIsInNldEVkaXRWaXNpYmxlIiwiY3JlYXRlVmlzaWJsZSIsInNldENyZWF0ZVZpc2libGUiLCJvblBhZ2luYXRpb25DaGFuZ2UiLCJzdGF0ZSIsImNvbnNvbGUiLCJsb2ciLCJwYWdpbmF0aW9uIiwic2V0IiwibnVtUGFnZXMiLCJzZXROdW1QYWdlcyIsInBhZ2VOdW1iZXIiLCJzZXRQYWdlTnVtYmVyIiwiZ2V0U2V2ZXJpdHkiLCJzdHIiLCJhY2NlcHQiLCJvblN1Y2Nlc3MiLCJjdXJyZW50Iiwic2hvdyIsInNldmVyaXR5Iiwic3VtbWFyeSIsImRldGFpbCIsIm9uRXJyb3IiLCJjb2RlIiwicmVqZWN0IiwibGlmZSIsIm9uRG9jdW1lbnRMb2FkU3VjY2VzcyIsImNoYW5nZVBhZ2UiLCJvZmZzZXQiLCJwcmV2UGFnZU51bWJlciIsInByZXZpb3VzUGFnZSIsIm5leHRQYWdlIiwiYW5jaG9yRWwiLCJzZXRBbmNob3JFbCIsInJvd0FjdGlvbkVuYWJsZWQiLCJzZXRSb3dBY3Rpb25FbmFibGVkIiwib3BlbiIsIkJvb2xlYW4iLCJoYW5kbGVDbGljayIsImV2ZW50IiwiY3VycmVudFRhcmdldCIsImNvbHVtbnMiLCJPYmplY3QiLCJlbnRyaWVzIiwiZGF0YV90eXBlIiwicHJvcGVydGllcyIsImZpbHRlciIsImluZGV4Iiwia2V5IiwidmFsdWUiLCJpbmNsdWRlcyIsIm1hcCIsImhlYWRlciIsInRpdGxlIiwiYWNjZXNzb3JLZXkiLCJpZCIsIkNlbGwiLCJjZWxsIiwiZGl2IiwiZ2V0VmFsdWUiLCJFZGl0IiwiY29sdW1uIiwicm93IiwidGFibGUiLCJvbkNoYW5nZSIsImUiLCJfdmFsdWVzQ2FjaGUiLCJjb250ZW50IiwidGFyZ2V0IiwiZ2V0Q29udGVudCIsImluaXRpYWxWYWx1ZSIsIm9yaWdpbmFsIiwidGlueW1jZVNjcmlwdFNyYyIsImFwaUtleSIsImluaXQiLCJoZWlnaHQiLCJtZW51YmFyIiwicGx1Z2lucyIsInRvb2xiYXIiLCJmb3JtYXQiLCJhY2Nlc3NvckZuIiwiRGF0ZSIsImZpbHRlclZhcmlhbnQiLCJmaWx0ZXJGbiIsInNvcnRpbmdGbiIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInN0cnVjdCIsIm11aVRhYmxlRm9vdGVyQ2VsbFByb3BzIiwiYWxpZ24iLCJsZW5ndGgiLCJkaXJlY3Rpb24iLCJzcGFjaW5nIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJjb2xvciIsImZvbnRXZWlnaHQiLCJsYWJlbCIsImNvZGVfbW5lbW9uaXF1ZSIsImNvZGVfc3RydSIsIm11aVRhYmxlSGVhZENlbGxQcm9wcyIsIm11aVRhYmxlQm9keUNlbGxQcm9wcyIsImNsYXNzTmFtZSIsImNyZWF0ZWQiLCJzaXplIiwiaWNvbiIsIm11aUVkaXRUZXh0RmllbGRQcm9wcyIsInNlbGVjdCIsInZhcmlhbnQiLCJTZWxlY3RQcm9wcyIsImVkaXRTZWxlY3RPcHRpb25zIiwiZW51bSIsIm11bHRpcGxlIiwicmVzdWx0cyIsInJvd0NvdW50IiwiY291bnQiLCJlbmFibGVSb3dTZWxlY3Rpb24iLCJlbmFibGVDb2x1bW5PcmRlcmluZyIsImVuYWJsZUdsb2JhbEZpbHRlciIsImVuYWJsZUdyb3VwaW5nIiwiZW5hYmxlUm93QWN0aW9ucyIsImVuYWJsZVJvd1Bpbm5pbmciLCJlbmFibGVTdGlja3lIZWFkZXIiLCJlbmFibGVTdGlja3lGb290ZXIiLCJlbmFibGVDb2x1bW5QaW5uaW5nIiwiZW5hYmxlQ29sdW1uUmVzaXppbmciLCJlbmFibGVSb3dOdW1iZXJzIiwiZW5hYmxlRXhwYW5kQWxsIiwiZW5hYmxlRWRpdGluZyIsImVuYWJsZUV4cGFuZGluZyIsIm1hbnVhbFBhZ2luYXRpb24iLCJpbml0aWFsU3RhdGUiLCJwYWdlU2l6ZSIsInBhZ2VJbmRleCIsImNvbHVtblZpc2liaWxpdHkiLCJjcmVhdGVkX2J5IiwibW9kZmllZF9ieSIsIm1vZGlmaWVkIiwibW9kaWZpZWRfYnkiLCJzdGFmZiIsImFzc2lzdGFudHMiLCJkb2N1bWVudCIsImRlbnNpdHkiLCJzaG93R2xvYmFsRmlsdGVyIiwic29ydGluZyIsImRlc2MiLCJwYWdpIiwiaXNMb2FkaW5nIiwiaXNTYXZpbmciLCJsb2NhbGl6YXRpb24iLCJkaXNwbGF5Q29sdW1uRGVmT3B0aW9ucyIsImVuYWJsZUhpZGluZyIsImdyb3ciLCJkZWZhdWx0Q29sdW1uIiwiZW5hYmxlTXVsdGlTb3J0IiwibXVpVGFibGVQYXBlclByb3BzIiwiY2xhc3NlcyIsInJvb3QiLCJzeCIsImZvbnRGYW1pbHkiLCJmb250RmVhdHVyZVNldHRpbmdzIiwib25FZGl0aW5nUm93U2F2ZSIsInZhbHVlcyIsInJlc3QiLCJzZXRFZGl0aW5nUm93IiwibWVzc2FnZSIsIm9uRWRpdGluZ1Jvd0NhbmNlbCIsIm9uQ3JlYXRpbmdSb3dTYXZlIiwicmV2YWxpZGF0ZSIsInBvcHVsYXRlQ2FjaGUiLCJzZXRDcmVhdGluZ1JvdyIsImVyciIsInJlc3BvbnNlIiwic3RhdHVzVGV4dCIsIm9uQ3JlYXRpbmdSb3dDYW5jZWwiLCJtdWlFZGl0Um93RGlhbG9nUHJvcHMiLCJtYXhXaWR0aCIsInpJbmRleCIsIm11aVRhYmxlRm9vdGVyUHJvcHMiLCJtdWlUYWJsZUNvbnRhaW5lclByb3BzIiwiZ2V0U3RhdGUiLCJpc0Z1bGxTY3JlZW4iLCJyZWZzIiwidG9wVG9vbGJhclJlZiIsIm9mZnNldEhlaWdodCIsImJvdHRvbVRvb2xiYXJSZWYiLCJtdWlQYWdpbmF0aW9uUHJvcHMiLCJtdWlUb3BUb29sYmFyUHJvcHMiLCJtdWlUYWJsZUJvZHlQcm9wcyIsInJlbmRlclRvcFRvb2xiYXJDdXN0b21BY3Rpb25zIiwiSSIsImEiLCJyb3VuZGVkIiwiYXJpYS1jb250cm9scyIsInVuZGVmaW5lZCIsImFyaWEtaGFzcG9wdXAiLCJhcmlhLWV4cGFuZGVkIiwib25DbGljayIsImRpc2FibGVkIiwiZ2V0SXNTb21lUm93c1NlbGVjdGVkIiwiYXJpYS1sYWJlbCIsIm11aURldGFpbFBhbmVsUHJvcHMiLCJ0aGVtZSIsInBhbGV0dGUiLCJtb2RlIiwicmVuZGVyQ3JlYXRlUm93RGlhbG9nQ29udGVudCIsInJlbmRlckVkaXRSb3dEaWFsb2dDb250ZW50IiwicmVuZGVyUm93QWN0aW9ucyIsInNwYW4iLCJvdXRsaW5lZCIsImNhbiIsImFjY2VwdENsYXNzTmFtZSIsImFjY2VwdExhYmVsIiwicmVqZWN0TGFiZWwiLCJyZWYiLCJtYXhpbWl6YWJsZSIsIndpZHRoIiwib25IaWRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/GenericTAbleOpportunitySheet.tsx\n"));

/***/ })

});