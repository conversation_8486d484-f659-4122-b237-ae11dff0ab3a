"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/processes/page",{

/***/ "(app-client)/./app/(main)/processes/(components)/GenericTAble.tsx":
/*!************************************************************!*\
  !*** ./app/(main)/processes/(components)/GenericTAble.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    _s();\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { mutate: createProcess } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessCreate)();\n    const { mutate: updateProcess } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessUpdate)();\n    const { mutate: deleteProcess } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessDestroy)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 115\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_3__.Editor, {\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"parent\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"parent.title\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title3;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0][\"$ref\"] ? data_.data_type.properties[key].allOf[0][\"$ref\"].enum : [],\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"VP\" ? \"danger\" : cell.getValue() === \"STRUCT\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"VP\" ? \"Vice Pr\\xe9sident\" : cell.getValue() === \"STRUCT\" ? \"Structures\" : \"Contr\\xf4le Interne\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"NS\" ? \"danger\" : cell.getValue() === \"EC\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"NS\" ? \"Non lanc\\xe9e\" : cell.getValue() === \"SP\" ? \"Suspendue\" : cell.getValue() === \"EC\" ? \"En cours\" : \"Cl\\xf4tur\\xe9e\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title5;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key][\"$ref\"] && data_.data_type.properties[key][\"$ref\"].enum) {\n                console.log(\"#######enum##########\", key, value);\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+\" ,\"; }, \"\"):\"\",\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.row.original[key];\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        children: data_.data_type.properties[key][\"$ref\"].enum,\n                        SelectProps: {\n                        }\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title7, _data__data_type_properties_key_title8;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    console.log(\"############## data from api \", data_);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values } = param;\n            var _toast_current;\n            //validate data\n            //save data to api\n            table.setEditingRow(null); //exit editing mode\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Sauvegarde en cours\"\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values } = param;\n            //validate data\n            //save data to api\n            table.setCreatingRow(null); //exit creating mode\n        },\n        onCreatingRowCancel: ()=>{\n        //clear any validation errors\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        rounded: true,\n                        disabled: table.getIsSomeRowsSelected(),\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: handleClick,\n                        icon: \"pi pi-trash\",\n                        // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-bell\",\n                        rounded: true,\n                        color: rowActionEnabled ? \"secondary\" : \"primary\",\n                        size: \"small\",\n                        \"aria-label\": \"edit\",\n                        onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 489,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            zIndex: \"1302 !important\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                            position: \"right\",\n                            header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"align-content-center \",\n                                        children: [\n                                            \"Cr\\xe9ation \",\n                                            data_.data_type.name,\n                                            \" \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                            variant: \"text\",\n                                            table: table,\n                                            row: row\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            visible: createVisible,\n                            onHide: ()=>{\n                                table.setCreatingRow(null);\n                                setCreateVisible(false);\n                            },\n                            className: \"w-full md:w-9 lg:w-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: [\n                                    internalEditComponents,\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 7\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        zIndex: \"1302 !important\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        position: \"right\",\n                        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"align-content-center \",\n                                    children: [\n                                        \"Editer \",\n                                        data_.data_type.name,\n                                        \" \",\n                                        row.original.code\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                        variant: \"text\",\n                                        table: table,\n                                        row: row\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        visible: editVisible,\n                        onHide: ()=>{\n                            table.setEditingRow(null);\n                            setEditVisible(false);\n                        },\n                        className: \"w-full md:w-9 lg:w-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                gap: \"1.5rem\"\n                            },\n                            children: [\n                                internalEditComponents,\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 602,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 601,\n                    columnNumber: 79\n                }, this)\n            }, void 0, false);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return row.original.description ? (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.description) : row.original.content ? (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.content) : row.original.staff ? row.original.staff.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    display: \"grid\",\n                    margin: \"auto\",\n                    //gridTemplateColumns: '1fr 1fr',\n                    width: \"100vw\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabView, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"staff\"].title,\n                            leftIcon: \"pi pi-user mr-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.staff.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 134\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 64\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"assistants\"].title,\n                            rightIcon: \"pi pi-user ml-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.assistants.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 139\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 69\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: \"Lettre\",\n                            leftIcon: \"pi pi-file-word mr-2\",\n                            rightIcon: \"pi pi-file-pdf ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    icon: \"pi pi-check\",\n                                    rounded: true,\n                                    onClick: ()=>setVisible(true),\n                                    disabled: row.original.document === null\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: [\n                                            \"Lettre de mission : \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    visible: visible,\n                                    onHide: ()=>setVisible(false),\n                                    className: \"w-full md:w-9 lg:w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                        children: [\n                                            row.original.document !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Document, {\n                                                file: row.original.document,\n                                                onLoadSuccess: onDocumentLoadSuccess,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Page, {\n                                                    pageNumber: pageNumber\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 27\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No Document\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Page \",\n                                                            pageNumber || (numPages ? 1 : \"--\"),\n                                                            \" of \",\n                                                            numPages || \"--\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row align-items-center justify-content-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber <= 1,\n                                                                onClick: previousPage,\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 658,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber >= numPages,\n                                                                onClick: nextPage,\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 23\n                                    }, this)\n                                }, row.original.id, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 19\n                        }, this),\n                        \"          \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 630,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 622,\n                columnNumber: 15\n            }, this) : null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n        },\n        renderRowActions: (param)=>// <Box sx={{ display: 'flex', gap: '1rem' }}>\n        /*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 682,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 683,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    // console.log(data_.isLoading)\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 709,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_16__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 709,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"3RAFQliHDbfbMvEtoep07KyNeFs=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessUpdate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessDestroy,\n        material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/processes/(components)/GenericTAble.tsx\n"));

/***/ })

});