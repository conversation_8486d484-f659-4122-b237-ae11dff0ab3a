'use client';


import { useState } from 'react';
import { MRT_PaginationState } from 'material-react-table';
import GenericTable from './(components)/GenericTAblePrime';
import { getCookie } from 'cookies-next';
import { Mission } from '@prisma/client';
import { useApiMissionList } from '@/hooks/useNextApi';

const TableMission = () => {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    const {data : missions} = useApiMissionList()
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex: 0,
        pageSize: 5, //customize the default page size
    });
    const [selected, setSelected] = useState([]);
    const [selectedMissionFilter, setSelectedMissionFilter] = useState({});


    if (missions.error) return (<div>{missions.error.message}</div>)
    return (
        <div className="grid">
            <div className="col-12">
                <GenericTable<Mission>
                    data_={missions.data} isLoading={missions.isLoading}
                    error={missions.error} data_type={null}
                    mutate ={missions.mutate}
                    pagination={{ "set": setPagination, "pagi": pagination }}>
                </GenericTable>
            </div>
        </div>
    );
};

export default TableMission;
