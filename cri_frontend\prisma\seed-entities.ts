import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedEntities() {
  console.log('🌱 Starting entity seeding...')

  // 1. DOMAINS - Hierarchical structure for audit domains
  console.log('Creating domains...')

  // Root domains
  const financeDomain = await prisma.domain.upsert({
    where: { id: 1 },
    update: {},
    create: {
      id: 1,
      title: 'Gestion Financière',
      shortTitle: 'Finance',
      type: 'ROOT',
      observation: 'Domaine principal couvrant tous les aspects financiers',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  const operationsDomain = await prisma.domain.upsert({
    where: { id: 2 },
    update: {},
    create: {
      id: 2,
      title: 'Opérations et Processus',
      shortTitle: 'Opérations',
      type: 'ROOT',
      observation: 'Domaine couvrant les processus opérationnels',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  const hrDomain = await prisma.domain.upsert({
    where: { id: 3 },
    update: {},
    create: {
      id: 3,
      title: 'Ressources Humaines',
      shortTitle: 'RH',
      type: 'ROOT',
      observation: 'Domaine des ressources humaines et gestion du personnel',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  const itDomain = await prisma.domain.upsert({
    where: { id: 4 },
    update: {},
    create: {
      id: 4,
      title: 'Systèmes d\'Information',
      shortTitle: 'SI',
      type: 'ROOT',
      observation: 'Domaine des technologies et systèmes d\'information',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  // Sub-domains for Finance
  await prisma.domain.upsert({
    where: { id: 5 },
    update: {},
    create: {
      id: 5,
      title: 'Comptabilité Générale',
      shortTitle: 'Compta',
      parentId: financeDomain.id,
      type: 'SUB',
      observation: 'Gestion de la comptabilité générale et des états financiers',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.domain.upsert({
    where: { id: 6 },
    update: {},
    create: {
      id: 6,
      title: 'Contrôle Budgétaire',
      shortTitle: 'Budget',
      parentId: financeDomain.id,
      type: 'SUB',
      observation: 'Suivi et contrôle des budgets',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.domain.upsert({
    where: { id: 7 },
    update: {},
    create: {
      id: 7,
      title: 'Trésorerie',
      shortTitle: 'Tréso',
      parentId: financeDomain.id,
      type: 'SUB',
      observation: 'Gestion de la trésorerie et des flux financiers',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  // Sub-domains for Operations
  await prisma.domain.upsert({
    where: { id: 8 },
    update: {},
    create: {
      id: 8,
      title: 'Gestion des Achats',
      shortTitle: 'Achats',
      parentId: operationsDomain.id,
      type: 'SUB',
      observation: 'Processus d\'achat et approvisionnement',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.domain.upsert({
    where: { id: 9 },
    update: {},
    create: {
      id: 9,
      title: 'Gestion des Stocks',
      shortTitle: 'Stocks',
      parentId: operationsDomain.id,
      type: 'SUB',
      observation: 'Gestion et contrôle des inventaires',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.domain.upsert({
    where: { id: 10 },
    update: {},
    create: {
      id: 10,
      title: 'Qualité et Conformité',
      shortTitle: 'Qualité',
      parentId: operationsDomain.id,
      type: 'SUB',
      observation: 'Assurance qualité et conformité réglementaire',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  // 2. PROCESSES - Hierarchical structure for audit processes
  console.log('Creating processes...')

  // Root processes
  const auditProcess = await prisma.process.upsert({
    where: { id: 1 },
    update: {},
    create: {
      id: 1,
      title: 'Processus d\'Audit',
      shortTitle: 'Audit',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  const riskProcess = await prisma.process.upsert({
    where: { id: 2 },
    update: {},
    create: {
      id: 2,
      title: 'Gestion des Risques',
      shortTitle: 'Risques',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  const complianceProcess = await prisma.process.upsert({
    where: { id: 3 },
    update: {},
    create: {
      id: 3,
      title: 'Conformité et Contrôle',
      shortTitle: 'Conformité',
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  // Sub-processes for Audit
  await prisma.process.upsert({
    where: { id: 4 },
    update: {},
    create: {
      id: 4,
      title: 'Planification d\'Audit',
      shortTitle: 'Plan Audit',
      parentId: auditProcess.id,
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.process.upsert({
    where: { id: 5 },
    update: {},
    create: {
      id: 5,
      title: 'Exécution d\'Audit',
      shortTitle: 'Exec Audit',
      parentId: auditProcess.id,
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.process.upsert({
    where: { id: 6 },
    update: {},
    create: {
      id: 6,
      title: 'Rapport d\'Audit',
      shortTitle: 'Rapport',
      parentId: auditProcess.id,
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.process.upsert({
    where: { id: 7 },
    update: {},
    create: {
      id: 7,
      title: 'Suivi des Recommandations',
      shortTitle: 'Suivi',
      parentId: auditProcess.id,
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  // Sub-processes for Risk Management
  await prisma.process.upsert({
    where: { id: 8 },
    update: {},
    create: {
      id: 8,
      title: 'Identification des Risques',
      shortTitle: 'ID Risques',
      parentId: riskProcess.id,
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.process.upsert({
    where: { id: 9 },
    update: {},
    create: {
      id: 9,
      title: 'Évaluation des Risques',
      shortTitle: 'Eval Risques',
      parentId: riskProcess.id,
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  await prisma.process.upsert({
    where: { id: 10 },
    update: {},
    create: {
      id: 10,
      title: 'Mitigation des Risques',
      shortTitle: 'Mitigation',
      parentId: riskProcess.id,
      createdBy: 'admin-user-id',
      modifiedBy: 'admin-user-id',
    },
  })

  // 3. CRI STRUCTURE VIEW - Organizational structures
  console.log('Creating CRI structure views...')

  const structures = [
    {
      id: BigInt(1),
      codeStru: 'DG',
      libellStru: 'Direction Générale',
      codeUnit: 'DG001',
      codeMnemonique: 'DG',
    },
    {
      id: BigInt(2),
      codeStru: 'DAF',
      libellStru: 'Direction Administrative et Financière',
      codeUnit: 'DAF001',
      codeMnemonique: 'DAF',
    },
    {
      id: BigInt(3),
      codeStru: 'DRH',
      libellStru: 'Direction des Ressources Humaines',
      codeUnit: 'DRH001',
      codeMnemonique: 'DRH',
    },
    {
      id: BigInt(4),
      codeStru: 'DSI',
      libellStru: 'Direction des Systèmes d\'Information',
      codeUnit: 'DSI001',
      codeMnemonique: 'DSI',
    },
    {
      id: BigInt(5),
      codeStru: 'DCOM',
      libellStru: 'Direction Commerciale',
      codeUnit: 'DCOM001',
      codeMnemonique: 'DCOM',
    },
    {
      id: BigInt(6),
      codeStru: 'DPROD',
      libellStru: 'Direction de Production',
      codeUnit: 'DPROD001',
      codeMnemonique: 'DPROD',
    },
    {
      id: BigInt(7),
      codeStru: 'DQUAL',
      libellStru: 'Direction Qualité',
      codeUnit: 'DQUAL001',
      codeMnemonique: 'DQUAL',
    },
    {
      id: BigInt(8),
      codeStru: 'DLOG',
      libellStru: 'Direction Logistique',
      codeUnit: 'DLOG001',
      codeMnemonique: 'DLOG',
    },
    {
      id: BigInt(9),
      codeStru: 'DAUD',
      libellStru: 'Direction Audit Interne',
      codeUnit: 'DAUD001',
      codeMnemonique: 'DAUD',
    },
    {
      id: BigInt(10),
      codeStru: 'DJUR',
      libellStru: 'Direction Juridique',
      codeUnit: 'DJUR001',
      codeMnemonique: 'DJUR',
    },
  ]

  for (const structure of structures) {
    await prisma.criStructview.upsert({
      where: { id: structure.id },
      update: {},
      create: structure,
    })
  }

  // 4. CRI AGENTS - Personnel data
  console.log('Creating CRI agents...')

  const agents = [
    {
      matricAgnt: 'AGT001',
      idStruct: BigInt(1),
      nomAgnt: 'MARTIN',
      prenomAgnt: 'Jean',
      intituFonc: 'Directeur Général',
      libellStru: 'Direction Générale',
      codeUnit: 'DG001',
      codeStru: 'DG',
      codeSer: 'DG-SER001',
      codeMnemonique: 'DG',
    },
    {
      matricAgnt: 'AGT002',
      idStruct: BigInt(2),
      nomAgnt: 'DUBOIS',
      prenomAgnt: 'Marie',
      intituFonc: 'Directrice Administrative et Financière',
      libellStru: 'Direction Administrative et Financière',
      codeUnit: 'DAF001',
      codeStru: 'DAF',
      codeSer: 'DAF-SER001',
      codeMnemonique: 'DAF',
    },
    {
      matricAgnt: 'AGT003',
      idStruct: BigInt(3),
      nomAgnt: 'BERNARD',
      prenomAgnt: 'Pierre',
      intituFonc: 'Directeur des Ressources Humaines',
      libellStru: 'Direction des Ressources Humaines',
      codeUnit: 'DRH001',
      codeStru: 'DRH',
      codeSer: 'DRH-SER001',
      codeMnemonique: 'DRH',
    },
    {
      matricAgnt: 'AGT004',
      idStruct: BigInt(4),
      nomAgnt: 'LEROY',
      prenomAgnt: 'Sophie',
      intituFonc: 'Directrice des Systèmes d\'Information',
      libellStru: 'Direction des Systèmes d\'Information',
      codeUnit: 'DSI001',
      codeStru: 'DSI',
      codeSer: 'DSI-SER001',
      codeMnemonique: 'DSI',
    },
    {
      matricAgnt: 'AGT005',
      idStruct: BigInt(5),
      nomAgnt: 'MOREAU',
      prenomAgnt: 'Luc',
      intituFonc: 'Directeur Commercial',
      libellStru: 'Direction Commerciale',
      codeUnit: 'DCOM001',
      codeStru: 'DCOM',
      codeSer: 'DCOM-SER001',
      codeMnemonique: 'DCOM',
    },
    {
      matricAgnt: 'AGT006',
      idStruct: BigInt(6),
      nomAgnt: 'SIMON',
      prenomAgnt: 'Anne',
      intituFonc: 'Directrice de Production',
      libellStru: 'Direction de Production',
      codeUnit: 'DPROD001',
      codeStru: 'DPROD',
      codeSer: 'DPROD-SER001',
      codeMnemonique: 'DPROD',
    },
    {
      matricAgnt: 'AGT007',
      idStruct: BigInt(7),
      nomAgnt: 'MICHEL',
      prenomAgnt: 'François',
      intituFonc: 'Directeur Qualité',
      libellStru: 'Direction Qualité',
      codeUnit: 'DQUAL001',
      codeStru: 'DQUAL',
      codeSer: 'DQUAL-SER001',
      codeMnemonique: 'DQUAL',
    },
    {
      matricAgnt: 'AGT008',
      idStruct: BigInt(8),
      nomAgnt: 'GARCIA',
      prenomAgnt: 'Carmen',
      intituFonc: 'Directrice Logistique',
      libellStru: 'Direction Logistique',
      codeUnit: 'DLOG001',
      codeStru: 'DLOG',
      codeSer: 'DLOG-SER001',
      codeMnemonique: 'DLOG',
    },
    {
      matricAgnt: 'AGT009',
      idStruct: BigInt(9),
      nomAgnt: 'ROUX',
      prenomAgnt: 'Philippe',
      intituFonc: 'Directeur Audit Interne',
      libellStru: 'Direction Audit Interne',
      codeUnit: 'DAUD001',
      codeStru: 'DAUD',
      codeSer: 'DAUD-SER001',
      codeMnemonique: 'DAUD',
    },
    {
      matricAgnt: 'AGT010',
      idStruct: BigInt(10),
      nomAgnt: 'BLANC',
      prenomAgnt: 'Isabelle',
      intituFonc: 'Directrice Juridique',
      libellStru: 'Direction Juridique',
      codeUnit: 'DJUR001',
      codeStru: 'DJUR',
      codeSer: 'DJUR-SER001',
      codeMnemonique: 'DJUR',
    },
  ]

  for (const agent of agents) {
    await prisma.criAgents.upsert({
      where: { matricAgnt: agent.matricAgnt },
      update: {},
      create: agent,
    })
  }

  console.log('✅ All entities seeded successfully!')
  console.log('📊 Summary:')
  console.log('   - 10 Domains (4 root + 6 sub-domains)')
  console.log('   - 10 Processes (3 root + 7 sub-processes)')
  console.log('   - 10 CRI Structure Views')
  console.log('   - 10 CRI Agents')
}

export default seedEntities
