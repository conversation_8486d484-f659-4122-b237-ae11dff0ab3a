'use client';
import { BaseDataProps, ChildContainerProps } from '@/types';
import React, { createContext, useContext, useState } from 'react';

export const BaseDataContext = createContext({} as BaseDataProps)

export const BaseDataContextProvider = ({ children }: ChildContainerProps) => {

    // Initialize empty data states - these will be populated by individual components as needed
    // Since we've migrated to Prisma and Next.js API routes, data fetching is now done
    // at the component level using our new API routes
    const [baseData] = useState({
        missions: { data: [], isLoading: false, error: null },
        users: { data: [], isLoading: false, error: null },
        domains: { data: [], isLoading: false, error: null },
        processes: { data: [], isLoading: false, error: null },
        plans: { data: [], isLoading: false, error: null },
        recommendations: { data: [], isLoading: false, error: null },
        themes: { data: [], isLoading: false, error: null },
        arbitratedThemes: { data: [], isLoading: false, error: null },
        structureslqs: { data: [], isLoading: false, error: null },
        risks: { data: [], isLoading: false, error: null },
        goals: { data: [], isLoading: false, error: null },
        actions: { data: [], isLoading: false, error: null },
        arbitrations: { data: [], isLoading: false, error: null },
        comments: { data: [], isLoading: false, error: null }
    });

    return (
        <BaseDataContext.Provider value={baseData}>
            {children}
        </BaseDataContext.Provider>
    );
}

export const usebaseData = () => {
    const context = useContext(BaseDataContext);
    if (!context) {
        throw new Error('usebaseData must be used within a BaseDataContextProvider');
    }
    return context;
}