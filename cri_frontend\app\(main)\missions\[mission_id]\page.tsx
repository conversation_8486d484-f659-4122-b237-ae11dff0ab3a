'use client';

import { <PERSON><PERSON> } from 'primereact/button';
import { Chip } from 'primereact/chip';
import { Sidebar } from 'primereact/sidebar';
import { Tab<PERSON><PERSON><PERSON>, <PERSON>bView } from 'primereact/tabview';
import { useContext, useEffect, useState } from 'react';
import { $MissionSerializerRead, Constat, MissionSerializerRead, Recommendation } from '@/services/openapi_client';
import { CriStructview, MissionDocument } from '@/services/schemas';
import BlockViewer from '@/utilities/components/BlockViewer';
import { Stack } from '@mui/material';
import { LocalizationMap, Viewer, Worker } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import parse from 'html-react-parser';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import fr_FR from '@react-pdf-viewer/locales/lib/fr_FR.json';
import { LayoutContext } from '@/layout/context/layoutcontext';
import { usebaseData } from '@/utilities/hooks/useBaseData';
import { useParams } from 'next/navigation';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Message } from 'primereact/message';

const MissionDetails = ({ props }: { props: { mission: MissionSerializerRead, mission_id: number } }) => {
    const defaultLayoutPluginInstance = defaultLayoutPlugin();
    const params_ = useParams()
    const { missions } = usebaseData()
    const [mission_, setMission_] = useState<MissionSerializerRead | null>(props?.mission)
    const [visible, setVisible] = useState(false);
    const [mission_doc, setMissionDoc] = useState<MissionDocument | null>(null);
    const { layoutConfig } = useContext(LayoutContext);


    useEffect(() => {
        setMission_(missions.data?.data.results.find((mission: MissionSerializerRead) => mission.id === parseInt(params_.mission_id!)) ?? props?.mission);
    }, [missions.data?.data.results])
    if (missions.isLoading) return (<div className='flex flex-row h-screen w-full justify-content-center	 align-items-center align-content-center	 justify-content-center '><ProgressSpinner /></div>)
    if (!mission_) return (<div className='flex flex-row h-screen w-full justify-content-center	 align-items-center align-content-center	 justify-content-center '>
        <Message severity="error" text={`Mission avec ID ${params_.mission_id ?? props.mission_id} n'existe pas.`} />
    </div>)
    return (
        <div className="grid">
            <div className="col-12">
                <BlockViewer
                    header={`Mission ${mission_?.code}`}
                    containerClassName="surface-0 px-4 py-4 md:px-6 lg:px-8"
                    status={mission_?.etat}
                    priority={mission_?.type}
                >
                    <div className="surface-0">
                        {/* <div className="font-medium text-3xl text-900 mb-3">Movie Information</div>
                        <div className="text-500 mb-5">{parse(sanitizeHtml(recommendation.recommendation.content))}</div> */}
                        <ul className="list-none p-0 m-0">
                            {/* <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Title</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">Heat</div> */}
                            {/* <div className="w-6 md:w-2 flex justify-content-end">
                                <Button label="Edit" icon="pi pi-pencil" className="p-button-text" />
                            </div> */}
                            {/* </li> */}
                            <li className="flex align-items-center py-3 px-2 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">{mission_?.plan ? 'Plan' : 'Exercice'}</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">
                                    <Chip label={mission_?.plan || mission_?.exercise} className="mr-2" />
                                    {/* <Chip label="Crime" className="mr-2" />
                                <Chip label="Drama" className="mr-2" />
                                <Chip label="Thriller" /> */}
                                </div>
                                {/* <div className="w-6 md:w-2 flex justify-content-end">
                                <Button label="Edit" icon="pi pi-pencil" className="p-button-text" />
                            </div> */}
                            </li>
                            <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Thématique</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">{mission_?.theme?.theme.title}</div>
                                {/* <div className="w-6 md:w-2 flex justify-content-end">
                                <Button label="Edit" icon="pi pi-pencil" className="p-button-text" />
                            </div> */}
                            </li>
                            <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Structures contrôlées</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1">
                                    <Stack direction={'row'} spacing={1}>{mission_.controled_structures?.map((struct: CriStructview) => <Chip label={struct.code_mnemonique}></Chip>)}</Stack>
                                </div>
                                <div className="w-6 md:w-2 flex justify-content-end">
                                    <Button label="Edit" icon="pi pi-pencil" className="p-button-text" />
                                </div>
                            </li>
                            {/* <li className="flex align-items-center py-3 px-2 border-top-1 border-bottom-1 border-300 flex-wrap">
                                <div className="text-500 w-6 md:w-2 font-medium">Contenu</div>
                                <div className="text-900 w-full md:w-8 md:flex-order-0 flex-order-1 line-height-3">{parse(sanitizeHtml(recommendation.recommendation.content))}</div>      
                            </li> */}
                            <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap w-full">
                                <TabView className='w-full'>
                                    <TabPanel header={$MissionSerializerRead.properties["staff"].title} leftIcon="pi pi-user mr-2">
                                        <div className='flex flex-column gap-1'>
                                            <span><b>Chef de mission</b> : <a key={mission_.head?.email + mission_.code} href={"mailto:" + mission_.head?.email}>{mission_.head?.last_name} {mission_.head?.first_name}</a></span>
                                            <span><b>Superviseur</b> : <a key={mission_.supervisor?.email + mission_.code} href={"mailto:" + mission_.supervisor?.email}>{mission_.supervisor?.last_name} {mission_.supervisor?.first_name}</a></span>
                                            <span><b>Equipe</b> :</span>
                                            {mission_.staff.length > 0 ? <ul>{mission_.staff.map((user, idx) => <a key={user.email + mission_.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul> : <span>Aucun membre de mission</span>}
                                        </div>
                                        </TabPanel>
                                    <TabPanel header={"Assistants"} rightIcon="pi pi-user ml-2">
                                    {mission_.assistants.length > 0 ? <ul>{mission_.assistants.map((user, idx) => <a key={user.email + mission_.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul> : <span>Aucun assistant</span>}
                                    </TabPanel>
                                    <TabPanel header={"Recommendations"} rightIcon="pi pi-thumbs-up ml-2" className='align-content-center	align-items-center	justify-content-center	'>
                                        <DataTable<Recommendation[]> resizableColumns value={mission_?.recommendations ?? []} size='small' stripedRows rows={5} paginator emptyMessage={'Pas de recommendations.'}>
                                            <Column field="id" header="N°" sortable />
                                            <Column field="constats" header="Constats" sortable style={{ width: '35%' }} body={(data) => data.constats?.map((constat: Constat) => constat.id).join(",")} />
                                            <Column field="concerned_structure" header="Structure Concerné" sortable body={(data) => data.concerned_structure.code_mnemonique} />
                                            <Column field="priority" header="Priorité" sortable style={{ width: '35%' }} />
                                            <Column field="responsible" header="Responsable" sortable style={{ width: '35%' }} />
                                            <Column field="recommendation" header="Description" sortable style={{ width: '35%' }} body={(data) => parse(data.recommendation)} />
                                            <Column
                                                header="Voir"
                                                style={{ width: '15%' }}
                                                body={() => (
                                                    <>
                                                        <Button icon="pi pi-eye" text />
                                                    </>
                                                )}
                                            />
                                        </DataTable>

                                    </TabPanel>
                                    <TabPanel header="Documents" leftIcon="pi pi-file-word mr-2" rightIcon="pi pi-file-pdf ml-2">
                                        <Stack direction={'column'} spacing={1}>
                                            {mission_?.mission_docs?.map((doc: MissionDocument) => (
                                                <Stack direction={'row'} spacing={2} alignItems={'center'} alignContent={'center'}>
                                                    <Button icon="pi pi-eye" rounded onClick={() => { setMissionDoc(doc); setVisible(true) }} />
                                                    <div>{doc.document.replace('http://10.39.107.98:3001/media/missions/', '')}</div>
                                                    {/* <Button size='small' icon="pi pi-trash" severity='danger' rounded onClick={() => { setMissionDoc(doc); trigger_docs_delete() }}></Button> */}
                                                </Stack>
                                            )
                                            )}
                                        </Stack>
                                        <Sidebar key={mission_?.id}
                                            header={<h3>💼 Mission : {mission_?.code} | 📄 {mission_doc?.document.replace('http://10.39.107.98:3001/media/missions/', '')}</h3>}
                                            visible={visible} onHide={() => setVisible(false)}
                                            className="w-full md:w-9 lg:w-7">
                                            <div className="h-full w-full flex flex-column justify-items-center align-content-center	align-items-center justify-content-center gap-1 pt-3">
                                                {(mission_doc?.document.includes("jpg") || mission_doc?.document.includes("webp")) ? <Image downloadable imageStyle={{ height: 'calc(100vh - 300px)', width: '50vw' }} src={mission_doc?.document}></Image> :
                                                    <Worker workerUrl="/pdfjs/pdf.worker.js">
                                                        <div style={{ height: 'calc(100vh - 100px)', width: 'calc(50vw - 100px)' }}>
                                                            <Viewer
                                                                localization={fr_FR as unknown as LocalizationMap}
                                                                fileUrl={mission_doc?.document}
                                                                theme={layoutConfig.colorScheme}
                                                                plugins={[
                                                                    defaultLayoutPluginInstance,
                                                                ]}
                                                            />
                                                        </div>
                                                    </Worker>}
                                            </div>
                                        </Sidebar>
                                    </TabPanel>
                                </TabView>
                            </li>
                            {/* <li className="flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap">
                                <CommentTimeLine data={recommendation?.comments}/>
                            </li> */}
                        </ul>
                    </div>
                </BlockViewer>
            </div>
        </div>
    );
};

export default MissionDetails;
