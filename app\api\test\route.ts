import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/test - Test database connection and basic functionality
export async function GET(request: NextRequest) {
  try {
    // Test database connection
    await prisma.$connect()
    
    // Test basic queries
    const userCount = await prisma.user.count()
    const missionCount = await prisma.mission.count()
    const recommendationCount = await prisma.recommendation.count()
    
    // Test a simple query with relations
    const recentMissions = await prisma.mission.findMany({
      take: 5,
      include: {
        head: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          },
        },
        recommendations: {
          select: {
            id: true,
            recommendation: true,
            priority: true,
          },
        },
      },
      orderBy: { id: 'desc' },
    })
    
    return NextResponse.json({
      status: 'success',
      message: 'API and database connection working',
      data: {
        counts: {
          users: userCount,
          missions: missionCount,
          recommendations: recommendationCount,
        },
        recentMissions,
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Test API error:', error)
    return NextResponse.json(
      {
        status: 'error',
        message: 'Database connection failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
