import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/arbitrated-themes
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const arbitrationId = searchParams.get('arbitrationId')
    const themeId = searchParams.get('themeId')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { note: { contains: search, mode: 'insensitive' as const } },
        { theme: { title: { contains: search, mode: 'insensitive' as const } } },
        { arbitration: { report: { contains: search, mode: 'insensitive' as const } } },
      ]
    }
    
    if (arbitrationId) {
      where.arbitrationId = parseInt(arbitrationId)
    }
    
    if (themeId) {
      where.themeId = parseInt(themeId)
    }
    
    const [arbitratedThemes, total] = await Promise.all([
      prisma.arbitratedTheme.findMany({
        where,
        skip,
        take: limit,
        include: {
          arbitration: {
            include: {
              plan: {
                select: {
                  id: true,
                  exercise: true,
                  type: true,
                }
              }
            }
          },
          theme: {
            select: {
              id: true,
              title: true,
              code: true,
              validated: true,
              proposedBy: true,
            }
          },
          missions: {
            select: {
              id: true,
              code: true,
              type: true,
              etat: true,
            }
          }
        },
        orderBy: { id: 'desc' },
      }),
      prisma.arbitratedTheme.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: arbitratedThemes,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching arbitrated themes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/arbitrated-themes
export async function POST(request: NextRequest) {
  try {
    // Check authentication - only staff can create arbitrated themes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const body = await request.json()
    
    const {
      arbitrationId,
      themeId,
      note,
    } = body
    
    // Validate required fields
    if (!arbitrationId || !themeId) {
      return NextResponse.json(
        { error: 'arbitrationId and themeId are required' },
        { status: 400 }
      )
    }

    // Check if arbitration exists
    const arbitration = await prisma.arbitration.findUnique({
      where: { id: arbitrationId }
    })
    
    if (!arbitration) {
      return NextResponse.json(
        { error: 'Arbitration not found' },
        { status: 404 }
      )
    }

    // Check if theme exists
    const theme = await prisma.theme.findUnique({
      where: { id: themeId }
    })
    
    if (!theme) {
      return NextResponse.json(
        { error: 'Theme not found' },
        { status: 404 }
      )
    }

    // Check if arbitrated theme already exists
    const existingArbitratedTheme = await prisma.arbitratedTheme.findUnique({
      where: {
        arbitrationId_themeId: {
          arbitrationId,
          themeId,
        }
      }
    })

    if (existingArbitratedTheme) {
      return NextResponse.json(
        { error: 'This theme is already arbitrated for this arbitration' },
        { status: 400 }
      )
    }

    const arbitratedTheme = await prisma.arbitratedTheme.create({
      data: {
        arbitrationId,
        themeId,
        note,
        createdBy: session.user.id.toString(),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        arbitration: {
          include: {
            plan: {
              select: {
                id: true,
                exercise: true,
                type: true,
              }
            }
          }
        },
        theme: {
          select: {
            id: true,
            title: true,
            code: true,
            validated: true,
            proposedBy: true,
          }
        },
        missions: {
          select: {
            id: true,
            code: true,
            type: true,
            etat: true,
          }
        }
      },
    })

    return NextResponse.json({ data: arbitratedTheme }, { status: 201 })
  } catch (error) {
    console.error('Error creating arbitrated theme:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
