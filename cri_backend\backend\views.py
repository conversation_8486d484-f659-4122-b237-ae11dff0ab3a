from backend.paginators import MultiplePaginationMixin, Unpaginated
from backend.schemas import CustomAutoSchema
from rest_framework.viewsets import ModelViewSet
from backend.serializers import * #UserProfileSerializer, ExerciseSerializer, StructureLQSSerializer, PlanThemeSerializer, ThemeSerializer, OpportunitySheetSerializer, DomainSerializer, ProcessSerializer, RiskSerializer, RiskImpactSerializer, GoalSerializer, ArbitrationSerializer, ArbitratedThemeSerializer, PlanSerializer, MissionSerializerWrite, AnnotationSerializer, FRAPSerializer, ActionPlanAnnotationSerializer, ActionPlanRecommendationSerializer, ActionPlanSerializer, ActionSerializer, RecommendationSerializer, SuviRecommendationSerializer, ConstatSerializer, CauseSerializer, ConsequenceSerializer
from backend.models import UserProfile,  StructureLQSCorrespondents,  Theme, Domain, Process, Risk, RiskImpact, Goal, Arbitration, ArbitratedTheme, Plan, Mission,Action, Recommendation, Constat, Cause, Consequence
from rest_framework import mixins
import django
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters,permissions, status
from rest_framework.views import APIView
from drf_rw_serializers import viewsets
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.authtoken.models import Token
from rest_framework.response import Response
from rest_framework import authentication, permissions
from backend.permissions import *
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample,extend_schema_view
from drf_spectacular.types import OpenApiTypes
from rest_framework.exceptions import ValidationError
from rest_framework.parsers import MultiPartParser
from rest_framework.decorators import action
from django.db.models import F   
from django.contrib.auth.models import Permission
from django.db.models import  Q,Prefetch
from django.http import JsonResponse
class EnablePartialUpdateMixin:
    """Enable partial updates

    Override partial kwargs in UpdateModelMixin class
    https://github.com/encode/django-rest-framework/blob/91916a4db14cd6a06aca13fb9a46fc667f6c0682/rest_framework/mixins.py#L64
    """
    def update(self, request, *args, **kwargs):
        print('FILES ##############',request.data)
        kwargs['partial'] = True
        return super().update(request, *args, **kwargs)
    
class UserProfileViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = UserProfile.objects.order_by('pk')
    serializer_class = UserProfileSerializer

class CommentViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Comment.objects.order_by('-created')
    # serializer_class = CommentSerializer_
    read_serializer_class = CommentReadSerializer
    write_serializer_class = CommentWriteSerializer

# class CommentListViewSet(viewsets.ModelViewSet):
#     permission_classes = [permissions.IsAuthenticated]
#     filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
#     queryset = Comment.objects.order_by('-created')
    
#     # list_serializer_class = CommentReadListSerializer
#     read_serializer_class = CommentReadSerializer1
#     write_serializer_class = CommentWriteListSerializer  

class ActResponsableViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    queryset = ActionResponsable.objects.order_by('-created')

    read_serializer_class = ActionresponsableReadSerializer
    write_serializer_class =ActionresponsableWriteSerializer

class NotificationViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Notification.objects.order_by('pk')
    serializer_class = NotificationSerializer
    filterset_fields = ["user","content_type","read"]
    search_fields = ["user","content_type","read"]

# This class defines a view set for discussion comments with filtering, ordering, and searching
# capabilities.
# class DiscussionCommentViewSet(ModelViewSet):
#     filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
#     queryset = DiscussionComment.objects.order_by('pk')
#     serializer_class = DiscussionCommentSerializer

# class ExerciseViewSet(ModelViewSet):
#     filter_backends = [DjangoFilterBackend]
#     queryset = Exercise.objects.order_by('pk')
#     serializer_class = ExerciseSerializer

class StructureLQSCorrespondentsViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    ordering = ["-activity","-division","-direction","-subdirection","-department","-service","-section"]
    queryset = StructureLQSCorrespondents.objects.order_by('pk')
    serializer_class = StructureLQSCorrespondentsSerializer

class CriStructviewViewSet(MultiplePaginationMixin,ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    # ordering = ["-activity","-division","-direction","-subdirection","-department","-service","-section"]
    queryset = CriStructview.objects.order_by('pk')
    serializer_class = CriStructviewSerializer
    def get_pagination_class(self):
        return Unpaginated

class StructureLQSInterimViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = StructureLQSInterim.objects.order_by('pk')
    serializer_class = StructureLQSInterimSerializer

# class PlanThemeViewSet(ModelViewSet):
#     filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
#     queryset = PlanTheme.objects.order_by('pk')
#     serializer_class = PlanThemeSerializer


class ThemeViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Theme.objects.order_by('pk')
    filterset_fields = ["validated","code","title","proposed_by","month_start","month_end"]
    search_fields = ["validated","code","title","proposed_by","month_start","month_end"]
    read_serializer_class = ThemeSerializer
    write_serializer_class = ThemeSerializerWriter

class SubDomainViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Domain.objects.exclude(parent=None).order_by('pk')
    serializer_class = SubDomainSerializer
    def get_pagination_class(self):
        return Unpaginated

class DomainViewSet(MultiplePaginationMixin,ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Domain.objects.order_by('pk')
    serializer_class = DomainSerializer
    def get_pagination_class(self):
        return Unpaginated

class ProcessViewSet(MultiplePaginationMixin,ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Process.objects.order_by('pk')
    serializer_class = ProcessSerializer
    def get_pagination_class(self):
        return Unpaginated

class RiskViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Risk.objects.order_by('pk')
    serializer_class = RiskSerializer
    http_method_names = ["get","put","post","patch","head","options"]
    filterset_fields = ["validated","title","description",]
    search_fields = ["validated","title","description",]

class RiskImpactViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = RiskImpact.objects.order_by('pk')
    serializer_class = RiskImpactSerializer

class GoalViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Goal.objects.order_by('pk')
    serializer_class = GoalSerializer

class ArbitrationViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Arbitration.objects.order_by('pk')
    read_serializer_class = ArbitrationSerializer
    write_serializer_class = ArbitrationWriteSerializer
    http_method_names = ["get","put","post","patch","head","options"]

class ArbitratedThemeViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = ArbitratedTheme.objects.order_by('pk')
    # serializer_class = ArbitratedThemeSerializer
    filterset_fields = ['arbitration', 'mission','note','theme']
    search_fields = ['arbitration', 'mission','note','theme']
    read_serializer_class = ArbitratedThemeSerializer
    write_serializer_class = ArbitratedThemeWriteSerializer

class PlanViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Plan.objects.order_by('pk')
    serializer_class = PlanSerializer
    
class FACTViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = FACT.objects.order_by('pk')
    serializer_class = FACTSerializer

@extend_schema_view(
    create=extend_schema(request={'multipart/form-data': MissionSerializerWrite}),
    update =extend_schema(request={'multipart/form-data': MissionSerializerWrite}),
    partial_update =extend_schema(request={'multipart/form-data': MissionSerializerWrite}),    
)
class MissionViewSet(EnablePartialUpdateMixin,viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Mission.objects.prefetch_related(Prefetch('mission_docs', queryset=MissionDocument.objects.filter(mission=1,context='MISSION'), to_attr='mission_docs_filtered')).order_by('pk')
    # http_method_names = ["get","put","post","patch","head","options","patch"]
    filterset_fields = ['type', 'etat','start_date','code']
    search_fields = ['type', 'etat','code','start_date']
    read_serializer_class = MissionSerializerRead
    write_serializer_class = MissionSerializerWrite
    # def create(self, validated_data):
    #     print(validated_data)
    # def update(self, validated_data):
    #     print(validated_data)
    def get_queryset(self):
        return Mission.objects.active_for_user(self.request.user)
    
@extend_schema_view(
    create=extend_schema(request={'multipart/form-data': MissionDocumentSerializer}),
)
class MissionDocumentViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = MissionDocument.objects.order_by('pk')
    # http_method_names = ["get","put","post","patch","head","options","patch"]
    # filterset_fields = ['type', 'etat','start_date','code']
    # search_fields = ['type', 'etat','code','start_date']
    serializer_class = MissionDocumentSerializer
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        files_list = request.FILES.getlist('document')
        if serializer.is_valid() :
            for item in files_list:
                f = MissionDocument.objects.create(mission=Mission.objects.get(pk=request.data['mission']),document=item,type=item.content_type,size=item.size,name= item.name)
            headers = self.get_success_headers(serializer.data)
            return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    
class ActionViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Action.objects.order_by('pk')
    read_serializer_class = ActionSerializer
    write_serializer_class = ActionWriteSerializer

class RecommendationViewSet(EnablePartialUpdateMixin,viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Recommendation.objects.order_by('pk')
    read_serializer_class = RecommendationSerializer
    write_serializer_class = RecommendationWriteSerializer
    
    def get_queryset(self):
        return Recommendation.objects.active_for_user(self.request.user)

class ConstatViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    # filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Constat.objects.order_by('pk')
    read_serializer_class = ConstatSerializer
    write_serializer_class = ConstatWriteSerializer
    
    http_method_names = ["get","put","post","patch","head","options"]

class CauseViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Cause.objects.order_by('pk')
    read_serializer_class = CauseSerializer
    write_serializer_class = CauseWriteSerializer
    
    http_method_names = ["get","put","post","patch","head","options"]
    def get_pagination_class(self):
        return Unpaginated

class ConsequenceViewSet(ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = Consequence.objects.order_by('pk')
    serializer_class = ConsequenceSerializer

class UserViewSet(MultiplePaginationMixin,ModelViewSet):
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend,filters.OrderingFilter,filters.SearchFilter]
    queryset = User.objects.order_by('first_name','last_name')
    filterset_fields = ['username', 'email','first_name','last_name']
    search_fields = ['username', 'email','first_name','last_name','userprofile']
    serializer_class = UserSerializer
    def get_pagination_class(self):
        return Unpaginated

class CustomAuthToken(ObtainAuthToken):

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data,context={'request': request})
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)
       
        return Response({
            'token': token.key,
            'user' :{
                'id': user.pk,
                'username':user.username,                
                'first_name':user.first_name,  
                'last_name':user.last_name,  
                'email': user.email,
                #'groups' : list(user.groups.all().values('name')),
                #'permissions':list(user.user_permissions.all().values( action=F('codename'), subject=F('content_type__model'))),
                #'permissions':list(casl_rules),
            },
            'crfs':django.middleware.csrf.get_token(request)
        })
        
class UserPermissionsView(APIView):
    # authentication_classes = [authentication.TokenAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    def get(self, request):
      
        user = request.user
        # Récupérer les groupes de l'utilisateur
        user_groups = user.groups.all()
      
        # Récupérer les permissions à partir des groupes
        group_permissions = Permission.objects.filter(group__in=user_groups).distinct()
        user_permissions =        list(user.user_permissions.all().values( action=F('codename'), subject=F('content_type__model')))
        casl_rules = []
        for perm in user_permissions :
            casl_rules.append({"action": perm['action'].split('_')[0], "subject": perm['subject']})
        for perm in group_permissions:
            print(perm.codename)
            action,*rest = perm.codename.split('_')  # Exemple : "add_article"
            print(perm.content_type)
            subject = perm.content_type.model
            casl_rules.append({"action": action, "subject": subject})
        # serializer = CaslRuleSerializer(casl_rules, many=True)
        print({'rules':casl_rules+user_permissions,'roles' : list(user.groups.all().values('name')),})
        return Response(
            {'rules':casl_rules,'roles' : list(user.groups.all().values('name')),}
            )

# from rest_framework.decorators import api_view,permission_classes,authentication_classes,renderer_classes,parser_classes,schema
# from rest_framework.response import Response

# @extend_schema( 
#          parameters=[
#              CommentWriteListSerializer
#          ], 
#          responses=[OpenApiTypes.OBJECT], 
# ) 
# @api_view(http_method_names=['POST','PUT','PATCH'])
# @schema(CustomAutoSchema())
# @permission_classes([permissions.IsAuthenticated])
# def CommentBulk(request):
#     if request.method == 'POST' :
#         serializer = CommentWriteSerializer(data=request.data,many=True)
#         serializer.is_valid(raise_exception=True)
#         serializer.save()
#         print(request)
#         return Response(serializer.data)
#     if request.method == 'PATCH' :
#         return Response(CommentReadSerializer(Comment.objects.filter(action_id=id),many=True).data)
#     if request.method == 'PUT' :
#         return Response(CommentReadSerializer(Comment.objects.filter(action_id=id),many=True).data)
#Mission and Recommendation import
# def upload_file(request):
#     if request.method == 'POST':
#             file = request.FILES['file']
#             df = pd.read_excel(file)
#             for _, row in df.iterrows():
#                 mission, created_mission = Mission.objects.get_or_create(
#                     code=row['Code Mission CI'],
#                     exercise=row['Exercice'],
#                     plan        = row['Code Mission CI'],
#                     exercise    = row['Code Mission CI'],
#                     type        = row['Code Mission CI'],
#                     code        = row['Code Mission CI'],
#                     etat        = row['Code Mission CI'],
#                     start_date  =   row['Code Mission CI'],
#                     end_date    =row['Code Mission CI'],
#                     theme       = row['Code Mission CI'],
#                     head        = row['Code Mission CI'],
#                     supervisor  =row['Code Mission CI'],
#                     staff       = row['Code Mission CI'],
#                     assistants  =row['Code Mission CI'],
#                     publication_date=datetime.strptime(row['Publication Date'], '%Y-%m-%d')
#                 )
#                 recommendation, created_recommendation = Recommendation.objects.get_or_create(
#                     numrecommandation = row['Numéro recommandation'], 
#                     recommendation = row['libellé de la recommandation'],
#                     concerned_structure = row['structure concernée par la recemandation'],
#                     priority = row['Priorité'],
#                     status = row['situation prise en charge recommandation'],
#                     validated =True,
#                     accepted = row['Acceptation de la recommandation'],
#                     responsible = row['structure en charge (signataire de la Fiche )'],
#                     causes = [],
#                     mission = row['Code Mission CI'],
#                 )
#                 if created_recommendation:
#                     message=  f'Successfully imported {book.title}'
#                 else:
#                     message = f'{book.title} already exists'
#             return JsonResponse({message: message})

#     return JsonResponse({'message': 'GET not allowed'})