'use client'

import React, { useState, useEffect, useRef } from 'react'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { Button } from 'primereact/button'
import { InputText } from 'primereact/inputtext'
import { Dialog } from 'primereact/dialog'
import { Dropdown } from 'primereact/dropdown'
import { InputSwitch } from 'primereact/inputswitch'
import { Toast } from 'primereact/toast'
import { Toolbar } from 'primereact/toolbar'
import { Badge } from 'primereact/badge'
import { Tag } from 'primereact/tag'
import { FilterMatchMode } from 'primereact/api'
import { Can } from '@/app/Can'

interface User {
  id: string
  username: string
  email: string
  firstName?: string
  lastName?: string
  isActive: boolean
  isStaff: boolean
  isSuperuser: boolean
  dateJoined: Date
  lastLogin?: Date
  userRoles?: any[]
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [globalFilter, setGlobalFilter] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [userDialog, setUserDialog] = useState(false)
  const [deleteUserDialog, setDeleteUserDialog] = useState(false)
  const [user, setUser] = useState<Partial<User>>({})
  const [filters, setFilters] = useState({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    username: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
    email: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
    isActive: { value: null, matchMode: FilterMatchMode.EQUALS }
  })
  const toast = useRef<Toast>(null)

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      // Mock data for now - replace with actual API call
      setTimeout(() => {
        setUsers([
          {
            id: '1',
            username: 'admin',
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
            isActive: true,
            isStaff: true,
            isSuperuser: true,
            dateJoined: new Date('2024-01-01'),
            lastLogin: new Date(),
            userRoles: [{ role: { name: 'Super Admin' } }]
          },
          {
            id: '2',
            username: 'manager',
            email: '<EMAIL>',
            firstName: 'Mission',
            lastName: 'Manager',
            isActive: true,
            isStaff: true,
            isSuperuser: false,
            dateJoined: new Date('2024-01-15'),
            lastLogin: new Date(),
            userRoles: [{ role: { name: 'Mission Manager' } }]
          }
        ])
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error fetching users:', error)
      setLoading(false)
    }
  }

  const openNew = () => {
    setUser({})
    setUserDialog(true)
  }

  const hideDialog = () => {
    setUserDialog(false)
  }

  const hideDeleteUserDialog = () => {
    setDeleteUserDialog(false)
  }

  const saveUser = () => {
    // Save user logic here
    toast.current?.show({ severity: 'success', summary: 'Successful', detail: 'User saved', life: 3000 })
    setUserDialog(false)
    setUser({})
  }

  const editUser = (user: User) => {
    setUser({ ...user })
    setUserDialog(true)
  }

  const confirmDeleteUser = (user: User) => {
    setSelectedUser(user)
    setDeleteUserDialog(true)
  }

  const deleteUser = () => {
    // Delete user logic here
    toast.current?.show({ severity: 'success', summary: 'Successful', detail: 'User deleted', life: 3000 })
    setDeleteUserDialog(false)
    setSelectedUser(null)
  }

  const onInputChange = (e: any, name: string) => {
    const val = (e.target && e.target.value) || ''
    setUser(prev => ({ ...prev, [name]: val }))
  }

  const onInputSwitchChange = (e: any, name: string) => {
    setUser(prev => ({ ...prev, [name]: e.value }))
  }

  const leftToolbarTemplate = () => {
    return (
      <div className="flex flex-wrap gap-2">
        <Can I="create" a="User">
          <Button label="New" icon="pi pi-plus" severity="success" onClick={openNew} />
        </Can>
      </div>
    )
  }

  const rightToolbarTemplate = () => {
    return (
      <div className="flex align-items-center gap-2">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            type="search"
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search users..."
          />
        </span>
      </div>
    )
  }

  const actionBodyTemplate = (rowData: User) => {
    return (
      <div className="flex gap-2">
        <Can I="update" a="User">
          <Button
            icon="pi pi-pencil"
            rounded
            outlined
            className="mr-2"
            onClick={() => editUser(rowData)}
          />
        </Can>
        <Can I="delete" a="User">
          <Button
            icon="pi pi-trash"
            rounded
            outlined
            severity="danger"
            onClick={() => confirmDeleteUser(rowData)}
          />
        </Can>
      </div>
    )
  }

  const statusBodyTemplate = (rowData: User) => {
    return (
      <Tag
        value={rowData.isActive ? 'Active' : 'Inactive'}
        severity={rowData.isActive ? 'success' : 'danger'}
      />
    )
  }

  const roleBodyTemplate = (rowData: User) => {
    if (rowData.isSuperuser) {
      return <Badge value="Super Admin" severity="danger" />
    }
    if (rowData.isStaff) {
      return <Badge value="Staff" severity="warning" />
    }
    return <Badge value="User" severity="info" />
  }

  const dateBodyTemplate = (rowData: User) => {
    return new Date(rowData.dateJoined).toLocaleDateString()
  }

  const userDialogFooter = (
    <div>
      <Button label="Cancel" icon="pi pi-times" outlined onClick={hideDialog} />
      <Button label="Save" icon="pi pi-check" onClick={saveUser} />
    </div>
  )

  const deleteUserDialogFooter = (
    <div>
      <Button label="No" icon="pi pi-times" outlined onClick={hideDeleteUserDialog} />
      <Button label="Yes" icon="pi pi-check" severity="danger" onClick={deleteUser} />
    </div>
  )

  return (
    <div className="card">
      <Toast ref={toast} />
      
      <Toolbar className="mb-4" left={leftToolbarTemplate} right={rightToolbarTemplate} />

      <DataTable
        value={users}
        selection={selectedUser}
        onSelectionChange={(e) => setSelectedUser(e.value)}
        dataKey="id"
        paginator
        rows={10}
        rowsPerPageOptions={[5, 10, 25]}
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} users"
        globalFilter={globalFilter}
        filters={filters}
        loading={loading}
        header="Users Management"
        responsiveLayout="scroll"
      >
        <Column field="username" header="Username" sortable filter filterPlaceholder="Search by username" />
        <Column field="email" header="Email" sortable filter filterPlaceholder="Search by email" />
        <Column field="firstName" header="First Name" sortable />
        <Column field="lastName" header="Last Name" sortable />
        <Column field="isActive" header="Status" body={statusBodyTemplate} sortable filter filterElement={<Dropdown options={[{label: 'Active', value: true}, {label: 'Inactive', value: false}]} placeholder="Select status" />} />
        <Column field="role" header="Role" body={roleBodyTemplate} />
        <Column field="dateJoined" header="Date Joined" body={dateBodyTemplate} sortable />
        <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '12rem' }} />
      </DataTable>

      <Dialog
        visible={userDialog}
        style={{ width: '450px' }}
        header="User Details"
        modal
        className="p-fluid"
        footer={userDialogFooter}
        onHide={hideDialog}
      >
        <div className="field">
          <label htmlFor="username">Username</label>
          <InputText
            id="username"
            value={user.username || ''}
            onChange={(e) => onInputChange(e, 'username')}
            required
            autoFocus
          />
        </div>
        <div className="field">
          <label htmlFor="email">Email</label>
          <InputText
            id="email"
            value={user.email || ''}
            onChange={(e) => onInputChange(e, 'email')}
            required
          />
        </div>
        <div className="field">
          <label htmlFor="firstName">First Name</label>
          <InputText
            id="firstName"
            value={user.firstName || ''}
            onChange={(e) => onInputChange(e, 'firstName')}
          />
        </div>
        <div className="field">
          <label htmlFor="lastName">Last Name</label>
          <InputText
            id="lastName"
            value={user.lastName || ''}
            onChange={(e) => onInputChange(e, 'lastName')}
          />
        </div>
        <div className="field">
          <label htmlFor="isActive">Active</label>
          <InputSwitch
            id="isActive"
            checked={user.isActive || false}
            onChange={(e) => onInputSwitchChange(e, 'isActive')}
          />
        </div>
        <div className="field">
          <label htmlFor="isStaff">Staff</label>
          <InputSwitch
            id="isStaff"
            checked={user.isStaff || false}
            onChange={(e) => onInputSwitchChange(e, 'isStaff')}
          />
        </div>
        <div className="field">
          <label htmlFor="isSuperuser">Superuser</label>
          <InputSwitch
            id="isSuperuser"
            checked={user.isSuperuser || false}
            onChange={(e) => onInputSwitchChange(e, 'isSuperuser')}
          />
        </div>
      </Dialog>

      <Dialog
        visible={deleteUserDialog}
        style={{ width: '450px' }}
        header="Confirm"
        modal
        footer={deleteUserDialogFooter}
        onHide={hideDeleteUserDialog}
      >
        <div className="confirmation-content">
          <i className="pi pi-exclamation-triangle mr-3" style={{ fontSize: '2rem' }} />
          {selectedUser && (
            <span>
              Are you sure you want to delete <b>{selectedUser.username}</b>?
            </span>
          )}
        </div>
      </Dialog>
    </div>
  )
}
