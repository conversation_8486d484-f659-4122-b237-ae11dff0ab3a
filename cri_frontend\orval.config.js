const path = require("path");
require('dotenv').config({ path:path.resolve(__dirname,'.env.local')})
const input =`http://${process.env.NEXT_PUBLIC_API_IP}:${process.env.NEXT_PUBLIC_API_PORT}/api/schema/?format.json`// path.join(__dirname, "server", "openapi", "openapi.json");
const output = path.resolve(__dirname, process.env.NEXT_PUBLIC_REST_CLIENT_PATH);
console.log(input)
module.exports = {
  sdk: {
    // output: {
    //   baseUrl :`http://${process.env.NEXT_PUBLIC_API_IP}:${process.env.NEXT_PUBLIC_API_PORT}`,
    //   clean: true,
    //   prettier: true,
    //   mode: "tags-split",
    //   target: path.join(output, "api"),
    //   schemas: path.join(output, "schemas"),
    //   client: "swr",
    //   // ... more opts
    // },
    output: {
      baseUrl :`http://${process.env.NEXT_PUBLIC_API_IP}:${process.env.NEXT_PUBLIC_API_PORT}`,
      clean: true,
      prettier: true,
      mode: "tags-split",
      target: path.join(output, "api"),
      schemas: path.join(output, "schemas"),
      client: "react-query",
      // ... more opts
    },
    input: {
      target: input,
    },
  },
  // sdkZod: {
  //   output: {
  //     prettier: true,
  //     mode: "tags-split",
  //     target: path.join(output, "zod"),
  //     client: "zod",
  //     fileExtension: '.zod.ts',
  //     // ... more opts
  //   },
  //   input: {
  //     target: input,
  //   },
  // },
};