/* eslint-disable @next/next/no-img-element */

import React, { useContext } from 'react';
import AppMenuitem from './AppMenuitem';
import { LayoutContext } from './context/layoutcontext';
import { MenuProvider } from './context/menucontext';
import { AppMenuItem } from '@/types';
import { Can } from '@/app/Can';
// import { Can } from '@/app/layout';

const AppMenu = () => {
    const { layoutConfig } = useContext(LayoutContext);

    const model: AppMenuItem[] = [
        {
            label: 'Suivi des recommendations',
            id: 'cri',
            items: [
                { label: 'Tableau de bord', id: '', icon: 'pi pi-fw pi-home', to: '/' },
                {
                    label: 'Plans', id: 'plan', icon: 'pi pi-fw pi-calendar-plus',
                    items: [
                        {
                            label: 'Plan',
                            id: 'plan',
                            icon: 'pi pi-fw pi-eye',
                            to: '/plans',
                        },
                        {
                            label: 'Arbitrage',
                            id: 'plan',
                            icon: 'pi pi-fw pi-comments',
                            to: "/plans/arbitrations"
                        },
                    ]
                },
                {
                    label: 'Thèmes', id: '', icon: 'pi pi-fw pi-book',
                    items: [
                        { label: 'Vivier', id: '', icon: 'pi pi-fw pi-star-fill', to: '/themes' },
                        { label: 'Fiches d\'opportunités', id: '', icon: 'pi pi-fw pi-file', to: '/themes/opportunity_sheet' },
                    ]
                },
                {
                    label: 'Missions',
                    icon: 'pi pi-fw pi-briefcase',
                    id: 'mission',
                    to: '/missions'
                    // items: [
                    //     {
                    //         label: 'Toutes',
                    //         id:'all',
                    //         icon: 'pi pi-fw pi-eye',
                    //         to: '/missions',
                    //     },
                    //     {
                    //         label: 'Commandées',id:'',
                    //         icon: 'pi pi-fw pi-microsoft',
                    //         to: "/missions/commanded"
                    //     },
                    //     {
                    //         label: 'Planifiées',id:'',
                    //         icon: 'pi pi-fw pi-clock',
                    //         to: "/missions/planified"
                    //     },
                    //     {
                    //         label: 'Avis & Conseils',id:'',
                    //         icon: 'pi pi-fw pi-compass',
                    //         to: "/missions/avc"
                    //     },
                    //     {
                    //         label: 'Plan de travail',id:'',
                    //         icon: 'pi pi-fw pi-bookmark',
                    //         to: "/missions/work_plan"
                    //     }
                    // ]
                },
                {
                    label: 'Recommendations', id: '', icon: 'pi pi-fw pi-thumbs-up', to: '/recommendations/followup',
                    // items: [
                    // { label: 'Suivi Recommendations',id:'', icon: 'pi pi-fw pi-star-fill', to: '/recommendations/followup' },
                    // { label: 'Plan d\'action Recommendations',id:'', icon: 'pi pi-fw pi-star-fill', to: '/recommendations/work_plan' }
                    // ]
                },
                {
                    label: 'Validations', id: '', icon: 'pi pi-fw pi-thumbs-up', to: '/validation',
                    // items: [
                    // { label: 'Suivi Recommendations',id:'', icon: 'pi pi-fw pi-star-fill', to: '/recommendations/followup' },
                    // { label: 'Plan d\'action Recommendations',id:'', icon: 'pi pi-fw pi-star-fill', to: '/recommendations/work_plan' }
                    // ]
                },
                {
                    label: 'Organization', id: '', icon: 'pi pi-fw pi-sitemap',
                    items: [
                        // {
                        //     label: 'Structures',id:'',
                        //     icon: 'pi pi-fw pi-building',
                        //     to: '/org/structures',
                        // },
                        {
                            label: 'Intérims des structures', id: '',
                            icon: 'pi pi-fw pi-users',
                            to: "/org/interims"
                        },
                        {
                            label: 'Domaines & Processus', id: '', icon: 'pi pi-fw pi-box', to: '/uikit/formlayout',
                            items: [
                                {
                                    label: 'Domaines', id: '',
                                    icon: 'pi pi-fw pi-globe',
                                    to: '/domains',
                                },
                                {
                                    label: 'Processus', id: '',
                                    icon: 'pi pi-fw pi-cog',
                                    to: "/processes"
                                },
                            ]
                        },
                    ]
                },
                { label: 'Faits',id:'', icon: 'pi pi-fw pi-sparkles', to: '/facts' },
                // { label: 'Causes',id:'' ,icon: 'pi pi-fw pi-exclamation-triangle', to: '/causes' },
                // { label: 'Conséquences',id:'' ,icon: 'pi pi-fw pi-bolt', to: '/consequences' },
                { label: 'Constats',id:'', icon: 'pi pi-fw pi-check-square', to: '/constats' },
                // { label: 'Registre des risques',id:'', icon: 'pi pi-fw pi-star-fill', to: '/risks' },

            ]
        },
        {
            label: 'Administration',
            id: 'admin',
            items: [
                {
                    label: 'Dashboard',
                    id: 'admin-dashboard',
                    icon: 'pi pi-fw pi-chart-line',
                    to: '/admin'
                },
                {
                    label: 'Users',
                    id: 'admin-users',
                    icon: 'pi pi-fw pi-users',
                    to: '/admin/users'
                },
                {
                    label: 'Roles',
                    id: 'admin-roles',
                    icon: 'pi pi-fw pi-shield',
                    to: '/admin/roles'
                },
                {
                    label: 'Permissions',
                    id: 'admin-permissions',
                    icon: 'pi pi-fw pi-key',
                    to: '/admin/permissions'
                }
            ]
        },
    ];

    return (
        <MenuProvider>
            <ul className="layout-menu">
                {model.map((item, i) => {
                    return !item?.seperator ? (
                        item.id === 'admin' ? (
                            <Can I="read" a="User" key={item.label + item.to}>
                                <AppMenuitem item={item} root={true} index={i} />
                            </Can>
                        ) : (
                            <AppMenuitem item={item} root={true} index={i} key={item.label + item.to} />
                        )
                    ) : <li className="menu-separator"></li>;
                })}
            </ul>
        </MenuProvider>
    );
};

export default AppMenu;
