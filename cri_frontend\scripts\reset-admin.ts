import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function resetAdmin() {
  console.log('🔄 Resetting admin user...')
  
  const adminEmail = '<EMAIL>'
  
  try {
    // Delete existing admin user and related records
    await prisma.userRole.deleteMany({
      where: {
        user: { email: adminEmail }
      }
    })
    
    await prisma.account.deleteMany({
      where: {
        user: { email: adminEmail }
      }
    })
    
    await prisma.session.deleteMany({
      where: {
        user: { email: adminEmail }
      }
    })
    
    await prisma.user.deleteMany({
      where: { email: adminEmail }
    })
    
    console.log('✅ Admin user reset successfully!')
    console.log('🔑 You can now create a new admin user at: http://localhost:3001/create-admin.html')
    
  } catch (error) {
    console.error('❌ Error resetting admin user:', error)
  }
}

resetAdmin()
  .catch((e) => {
    console.error('❌ Error:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
