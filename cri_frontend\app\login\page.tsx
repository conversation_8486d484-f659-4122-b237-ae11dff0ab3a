/* eslint-disable @next/next/no-img-element */
'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useContext, useState, useEffect } from 'react';
import { Checkbox } from 'primereact/checkbox';
import { But<PERSON> } from 'primereact/button';
import { Password } from 'primereact/password';
import { LayoutContext } from '../../layout/context/layoutcontext';
import { InputText } from 'primereact/inputtext';
import { classNames } from 'primereact/utils';
import { Message } from 'primereact/message';
import { signIn } from '@/lib/auth-client'

const LoginPage = () => {
    const router = useRouter();
    const searchParams = useSearchParams();

    const [email, setEmail] = React.useState<string>('');
    const [password, setPassword] = React.useState<string>('');
    const [error, setError] = React.useState('');
    const [loading, setLoading] = React.useState(false);

    // Check for error messages from URL params
    // useEffect(() => {
    //     const errorParam = searchParams.get('error');
    //     if (errorParam === 'account-disabled') {
    //         setError('Your account has been disabled. Please contact an administrator.');
    //     } else if (errorParam === 'session-error') {
    //         setError('Session error. Please try logging in again.');
    //     }
    // }, [searchParams]);

    const onSubmit = async () => {
        if (!email || !password) {
            setError('Please enter both email and password');
            return;
        }

        setLoading(true);
        setError('');
        try {
            await signIn.email({
                email: email,
                password: password,
                rememberMe : checked,
                callbackURL :'/'
            }, {
                onResponse: () => {
                    setLoading(false)
                },
                onRequest: () => {
                    setLoading(true)
                },
                onSuccess: (ctx) => {
                    const callbackUrl = searchParams.get('callbackUrl') || '/';
                    router.push(callbackUrl);
                },
                onError: (ctx) => {
                    console.log(ctx)
                    setError(ctx.error.message);
                },
            });
        } catch (error) {
            console.log(error)
            setError("Something went wrong")
        }
        finally {
            setLoading(false);
        }
    };
    // const [password, setPassword] = useState('');
    const [checked, setChecked] = useState(false);
    const { layoutConfig } = useContext(LayoutContext);

    const containerClassName = classNames(' py-4 surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden', { 'p-input-filled': layoutConfig.inputStyle === 'filled' });

    return (
        <div className={containerClassName} style={{
            backgroundImage: ' url(/images/img1.webp)',
            backgroundSize: 'cover'

        }}>
            <div className="flex flex-column align-items-center justify-content-center">

                <div
                    style={{
                        borderRadius: '56px',
                        padding: '0.3rem',
                        background: 'linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%)'
                    }}
                >
                    <div className="w-full surface-card py-4 px-5 sm:px-5" style={{ borderRadius: '53px' }}>
                        <div className="text-center mb-5">
                            <img src={`/layout/images/logo-${layoutConfig.colorScheme === 'light' ? 'dark' : 'white'}.svg`} alt="Sakai logo" className="mb-5 w-4rem flex-shrink-0" />
                            {/* <img src="/demo/images/login/avatar.png" alt="Image" height="50" className="mb-3" /> */}
                            <div className="text-900 text-3xl font-medium mb-3">CRI | LQS</div>
                            {/* <span className="text-600 font-medium">Sign in to continue</span> */}
                        </div>

                        <div>
                            {error && (
                                <Message severity="error" text={error} className="w-full mb-3" />
                            )}

                            <label htmlFor="email" className="block text-900 text-xl font-medium mb-2">
                                Email
                            </label>
                            <InputText
                                id="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                type="email"
                                placeholder="Enter your email"
                                className="w-full md:w-30rem mb-5"
                                style={{ padding: '1rem' }}
                                onKeyDown={(e) => e.key === 'Enter' && onSubmit()}
                            />

                            <label htmlFor="password" className="block text-900 font-medium text-xl mb-2">
                                Password
                            </label>
                            <Password
                                feedback={false}
                                inputId="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                placeholder="Enter your password"
                                toggleMask
                                className="w-full mb-5"
                                inputClassName="w-full p-3 md:w-30rem"
                                onKeyDown={(e) => e.key === 'Enter' && onSubmit()}
                            />

                            <div className="flex align-items-center justify-content-center mb-5">
                                <div className="flex align-items-center">
                                    <Checkbox inputId="rememberme1" checked={checked} onChange={(e) => setChecked(e.checked ?? false)} className="mr-2"></Checkbox>
                                    <label htmlFor="rememberme1">Remember me</label>
                                </div>
                            </div>

                            <Button
                                type='submit'
                                label={loading ? "Signing In..." : "Sign In"}
                                className="w-full p-3 text-xl"
                                onClick={onSubmit}
                                loading={loading}
                                disabled={loading}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LoginPage;
