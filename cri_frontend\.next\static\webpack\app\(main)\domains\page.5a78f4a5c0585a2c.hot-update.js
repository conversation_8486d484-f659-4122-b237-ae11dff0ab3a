"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/domains/page",{

/***/ "(app-client)/./app/(main)/domains/(components)/GenericTAble.tsx":
/*!**********************************************************!*\
  !*** ./app/(main)/domains/(components)/GenericTAble.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    _s();\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { mutate: createDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainCreate)();\n    const { mutate: updateDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainUpdate)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [parentDropDown, setParentDropDown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: parse(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"parent\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"parent.title\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        var _row_original_parent;\n                        setParentDropDown({\n                            \"code\": row.original.parentId,\n                            \"name\": (_row_original_parent = row.original.parent) === null || _row_original_parent === void 0 ? void 0 : _row_original_parent.title\n                        });\n                        const onChange = (event)=>{\n                            console.log(\"################AAAAAAAAAAAAAAAAAAAA####################\", event.value);\n                            setParentDropDown(event.value);\n                            row._valuesCache[\"parentId\"] = event.value.code;\n                            if (row.id === \"mrt-row-create\") {\n                                table.setCreatingRow(row);\n                            } else {\n                                table.setEditingRow(row);\n                            }\n                        };\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_4__.Dropdown, {\n                            optionLabel: \"name\",\n                            value: parentDropDown,\n                            onChange: onChange,\n                            filter: true,\n                            options: data_.data_.data.results.map(function(val) {\n                                return {\n                                    \"code\": val.id,\n                                    \"name\": val.title\n                                };\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 22\n                        }, this);\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title3;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0][\"$ref\"] ? data_.data_type.properties[key].allOf[0][\"$ref\"].enum : [],\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"VP\" ? \"danger\" : cell.getValue() === \"STRUCT\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"VP\" ? \"Vice Pr\\xe9sident\" : cell.getValue() === \"STRUCT\" ? \"Structures\" : \"Contr\\xf4le Interne\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"NS\" ? \"danger\" : cell.getValue() === \"EC\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"NS\" ? \"Non lanc\\xe9e\" : cell.getValue() === \"SP\" ? \"Suspendue\" : cell.getValue() === \"EC\" ? \"En cours\" : \"Cl\\xf4tur\\xe9e\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title5;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key][\"$ref\"] && data_.data_type.properties[key][\"$ref\"].enum) {\n                console.log(\"#######enum##########\", key, value);\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+\" ,\"; }, \"\"):\"\",\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.row.original[key];\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        children: data_.data_type.properties[key][\"$ref\"].enum,\n                        SelectProps: {\n                        }\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title7, _data__data_type_properties_key_title8;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    console.log(\"############## data from api \", data_);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values } = param;\n            var _toast_current;\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            table.setEditingRow(null); //exit editing mode\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Sauvegarde en cours\"\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            delete values.id;\n            createDomain(values);\n            table.setCreatingRow(null); //exit creating mode\n        },\n        onCreatingRowCancel: ()=>{\n        //clear any validation errors\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        rounded: true,\n                        disabled: table.getIsSomeRowsSelected(),\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: handleClick,\n                        icon: \"pi pi-trash\",\n                        // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-bell\",\n                        rounded: true,\n                        color: rowActionEnabled ? \"secondary\" : \"primary\",\n                        size: \"small\",\n                        \"aria-label\": \"edit\",\n                        onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            zIndex: \"1302 !important\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                            position: \"right\",\n                            header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"align-content-center \",\n                                        children: [\n                                            \"Cr\\xe9ation \",\n                                            data_.data_type.name,\n                                            \" \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                            variant: \"text\",\n                                            table: table,\n                                            row: row\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            visible: createVisible,\n                            onHide: ()=>{\n                                table.setCreatingRow(null);\n                                setCreateVisible(false);\n                            },\n                            className: \"w-full md:w-9 lg:w-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: [\n                                    internalEditComponents,\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 7\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        zIndex: \"1302 !important\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        position: \"right\",\n                        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"align-content-center \",\n                                    children: [\n                                        \"Editer \",\n                                        data_.data_type.name,\n                                        \" \",\n                                        row.original.code\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                        variant: \"text\",\n                                        table: table,\n                                        row: row\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        visible: editVisible,\n                        onHide: ()=>{\n                            table.setEditingRow(null);\n                            setEditVisible(false);\n                        },\n                        className: \"w-full md:w-9 lg:w-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                gap: \"1.5rem\"\n                            },\n                            children: [\n                                internalEditComponents,\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 79\n                }, this)\n            }, void 0, false);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return row.original.description ? parse(row.original.description) : row.original.content ? parse(row.original.content) : row.original.staff ? row.original.staff.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    display: \"grid\",\n                    margin: \"auto\",\n                    //gridTemplateColumns: '1fr 1fr',\n                    width: \"100vw\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabView, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"staff\"].title,\n                            leftIcon: \"pi pi-user mr-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.staff.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 134\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 64\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"assistants\"].title,\n                            rightIcon: \"pi pi-user ml-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.assistants.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 139\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 69\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: \"Lettre\",\n                            leftIcon: \"pi pi-file-word mr-2\",\n                            rightIcon: \"pi pi-file-pdf ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    icon: \"pi pi-check\",\n                                    rounded: true,\n                                    onClick: ()=>setVisible(true),\n                                    disabled: row.original.document === null\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: [\n                                            \"Lettre de mission : \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    visible: visible,\n                                    onHide: ()=>setVisible(false),\n                                    className: \"w-full md:w-9 lg:w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                        children: [\n                                            row.original.document !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Document, {\n                                                file: row.original.document,\n                                                onLoadSuccess: onDocumentLoadSuccess,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Page, {\n                                                    pageNumber: pageNumber\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 27\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No Document\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Page \",\n                                                            pageNumber || (numPages ? 1 : \"--\"),\n                                                            \" of \",\n                                                            numPages || \"--\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row align-items-center justify-content-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber <= 1,\n                                                                onClick: previousPage,\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber >= numPages,\n                                                                onClick: nextPage,\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 23\n                                    }, this)\n                                }, row.original.id, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 19\n                        }, this),\n                        \"          \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 650,\n                columnNumber: 15\n            }, this) : null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n        },\n        renderRowActions: (param)=>// <Box sx={{ display: 'flex', gap: '1rem' }}>\n        /*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 709,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    // console.log(data_.isLoading)\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 737,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_16__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 737,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"2u2a2fJ1r5Dl3tiKJxMUn3gwaXA=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/domains/(components)/GenericTAble.tsx\n"));

/***/ })

});