"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx":
/*!*******************************************************************!*\
  !*** ./app/(main)/plans/(components)/GenericTAbleArbitration.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/editor */ \"(app-client)/./node_modules/primereact/editor/editor.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var _lib_enums__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/enums */ \"(app-client)/./lib/enums.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// import { Editor } from '@tinymce/tinymce-react';\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie, _plans, _arbitrations;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: plans, isLoading: plans_isLoading, error: plans_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList)();\n    const { data: arbitrations, isLoading: arbitrations_isLoading, error: arbitrations_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList)();\n    const { data: users, isLoading: users_isLoading, error: users_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList)();\n    const users_data = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _users;\n        return ((_users = users) === null || _users === void 0 ? void 0 : _users.data.results) || [];\n    }, [\n        users_isLoading\n    ]);\n    const [arbitrationID, setArbitrationID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [picklistTargetValueTeam, setPicklistTargetValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [picklistSourceValueTeam, setPicklistSourceValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(users_data);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { mutate: arbitration_create_trigger, isPending: isCreateMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate)();\n    const { mutate: arbitration_patch_trigger, isPending: isPatchMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate)();\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const accept_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    console.log(\"USERS {{{{{{{{{{{{{{{{{{\", users_data);\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"created\",\n                \"modified\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    // Cell: ({ cell }) => <div>{parse(cell.getValue<string>())}</div>,\n                    // Cell: ({ cell }) => { if ([\"description\", \"content\",\"report\"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Rapport\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_editor__WEBPACK_IMPORTED_MODULE_8__.Editor, {\n                                    // initialValue={row.original[key]}\n                                    // tinymceScriptSrc=\"http://localhost:3000/tinymce/tinymce.min.js\"\n                                    // apiKey='none'\n                                    value: row.original.report,\n                                    // onChange={(e) => { row._valuesCache.report = e.target.getContent() }}\n                                    onTextChange: (e)=>{\n                                        row._valuesCache.report = e.htmlValue;\n                                    },\n                                    style: {\n                                        height: \"320px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (key === \"plan\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"plan\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_9__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            children: cell.getValue().code\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 38\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { row } = param;\n                        var _row__valuesCache_plan, _row__valuesCache_plan1, _row__valuesCache_plan2, _plans_data, _plans;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n                                    optionLabel: \"name\",\n                                    placeholder: \"Choisir un plan\",\n                                    onChange: (e)=>{\n                                        var _plans, _plans1;\n                                        console.log(e);\n                                        setRowTobe({\n                                            ...rowTobe,\n                                            plan: (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data.results.find((plan)=>plan.id === e.value.code)\n                                        });\n                                        row._valuesCache = {\n                                            ...row._valuesCache,\n                                            plan: (_plans1 = plans) === null || _plans1 === void 0 ? void 0 : _plans1.data.results.find((plan)=>plan.id === e.value.code)\n                                        };\n                                    },\n                                    value: {\n                                        code: ((_row__valuesCache_plan = row._valuesCache.plan) === null || _row__valuesCache_plan === void 0 ? void 0 : _row__valuesCache_plan.id) || null,\n                                        name: \"\".concat((0,_lib_enums__WEBPACK_IMPORTED_MODULE_7__.transformerPlanLabel)((_row__valuesCache_plan1 = row._valuesCache.plan) === null || _row__valuesCache_plan1 === void 0 ? void 0 : _row__valuesCache_plan1.type), \"|\").concat((_row__valuesCache_plan2 = row._valuesCache.plan) === null || _row__valuesCache_plan2 === void 0 ? void 0 : _row__valuesCache_plan2.exercise) || null\n                                    },\n                                    options: (_plans = plans) === null || _plans === void 0 ? void 0 : (_plans_data = _plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.results.map((plan)=>{\n                                        return {\n                                            code: plan.id,\n                                            name: \"\".concat((0,_lib_enums__WEBPACK_IMPORTED_MODULE_7__.transformerPlanLabel)(plan.type), \"|\").concat(plan.exercise)\n                                        };\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            var _data__data_type_properties_key_title3;\n            if (key === \"id\") return {\n                header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                accessorKey: key,\n                id: key,\n                Edit: ()=>null\n            };\n            var _data__data_type_properties_key_title4;\n            if (key === \"team\") return {\n                header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                accessorKey: key,\n                id: key,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: cell.getValue().map((usr)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_4__.getUserFullname)(usr)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    console.log(\"[ARBITRATION]\", row._valuesCache.team);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"font-bold\",\n                                children: \"Membres\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_11__.PickList, {\n                                source: picklistTargetValueTeam.length === 0 ? picklistSourceValueTeam : picklistSourceValueTeam.filter((user)=>picklistTargetValueTeam.map((user)=>user.username).includes(user.username)),\n                                id: \"picklist_team\",\n                                target: picklistTargetValueTeam.length > 0 ? picklistTargetValueTeam : row._valuesCache.team,\n                                sourceHeader: \"De\",\n                                targetHeader: \"A\",\n                                itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            item.firstName,\n                                            \" \",\n                                            item.lastName\n                                        ]\n                                    }, item.username, true, void 0, void 0),\n                                onChange: (e)=>{\n                                    console.log(\"source Team\", e.source);\n                                    setPicklistSourceValueTeam([\n                                        ...e.source\n                                    ]);\n                                    setPicklistTargetValueTeam([\n                                        ...e.target\n                                    ]);\n                                    row._valuesCache.team = e.target;\n                                },\n                                sourceStyle: {\n                                    height: \"200px\"\n                                },\n                                targetStyle: {\n                                    height: \"200px\"\n                                },\n                                filter: true,\n                                filterBy: \"username,email,firstName,lastName\",\n                                filterMatchMode: \"contains\",\n                                sourceFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\",\n                                targetFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true);\n                }\n            };\n            else {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), [\n        users_isLoading,\n        (_plans = plans) === null || _plans === void 0 ? void 0 : _plans.data,\n        (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_12__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data ? data_.data_.data : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data ? data_.data_.data.length : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                report: false,\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_13__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onEditingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_patch_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setEditingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" mis \\xe0 ajour\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onCreatingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = values.team ? (_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id) : [];\n            arbitration_create_trigger(rest, {\n                onSuccess: ()=>{\n                    table.setCreatingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" cr\\xe9\\xe9\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"add\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"align-content-center \",\n                                children: \"Cr\\xe9ation nouveau arbitrage\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: createVisible,\n                    onHide: ()=>{\n                        table.setCreatingRow(null);\n                        setCreateVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: internalEditComponents\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"align-content-center \",\n                                children: [\n                                    \"Editer l'arbitrage n\\xb0 \",\n                                    row.original.id\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: editVisible,\n                    onHide: ()=>{\n                        table.setEditingRow(null);\n                        setEditVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: [\n                            internalEditComponents,\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, this);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.report);\n        },\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"update\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                setArbitrationID(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true);\n                                console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept: accept_row_deletion,\n                                    reject: reject_row_deletion\n                                })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 565,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_20__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 565,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"uua31+vusobRieuQlddYnb78zOA=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiArbitrationPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_12__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1jbGllbnQpLy4vYXBwLyhtYWluKS9wbGFucy8oY29tcG9uZW50cykvR2VuZXJpY1RBYmxlQXJiaXRyYXRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3lFO0FBTzNDO0FBQ3dDO0FBQzNCO0FBQzBCO0FBQ3hCO0FBRVI7QUFDSTtBQUNTO0FBQ1o7QUFDdEMsbURBQW1EO0FBQ0o7QUFDQTtBQUNOO0FBQ3FCO0FBQ25CO0FBQ1g7QUFDdUk7QUFFcEg7QUFFcEMsU0FBUzZCLGFBQW9DQyxLQUE4RjtRQUNoSVgsWUFnTUpZLFFBQWFDOztJQWhNakMsTUFBTUMsT0FBT0MsS0FBS2xCLEtBQUssQ0FBQ0csRUFBQUEsYUFBQUEsdURBQVNBLENBQUMscUJBQVZBLGlDQUFBQSxXQUFtQmdCLFFBQVEsT0FBTTtJQUV6RCxNQUFNQyxRQUFRdEIsNkNBQU1BLENBQWU7SUFDbkMsTUFBTSxFQUFDdUIsTUFBT04sS0FBSyxFQUFHTyxXQUFXQyxlQUFlLEVBQUVDLE9BQU9DLFdBQVcsRUFBRSxHQUFHZixpRUFBY0E7SUFDdkYsTUFBTSxFQUFDVyxNQUFNTCxZQUFZLEVBQUVNLFdBQVdJLHNCQUFzQixFQUFFRixPQUFPRyxrQkFBa0IsRUFBRSxHQUFHbkIsd0VBQXFCQTtJQUNqSCxNQUFNLEVBQUNhLE1BQU9PLEtBQUssRUFBRU4sV0FBV08sZUFBZSxFQUFFTCxPQUFPTSxXQUFXLEVBQUUsR0FBR25CLGlFQUFjQTtJQUV0RixNQUFNb0IsYUFBYWxDLDhDQUFPQSxDQUFDO1lBQU0rQjtlQUFBQSxFQUFBQSxTQUFBQSxtQkFBQUEsNkJBQUFBLE9BQU9QLElBQUksQ0FBQ1csT0FBTyxLQUFLLEVBQUU7T0FBRTtRQUFDSDtLQUFnQjtJQUM5RSxNQUFNLENBQUNJLGVBQWVDLGlCQUFpQixHQUFHbkMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDb0MsYUFBYUMsZUFBZSxHQUFHckMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDc0MsZUFBZUMsaUJBQWlCLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN3Qyx5QkFBeUJDLDJCQUEyQixHQUFHekMsK0NBQVFBLENBQUMsRUFBRTtJQUN6RSxNQUFNLENBQUMwQyx5QkFBeUJDLDJCQUEyQixHQUFHM0MsK0NBQVFBLENBQUNnQztJQUN2RSxNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBRzdDLCtDQUFRQSxDQUFDLENBQUM7SUFDeEMsTUFBTSxFQUFFOEMsUUFBUUMsMEJBQTBCLEVBQUVDLFdBQVdDLGdCQUFnQixFQUFFLEdBQUd6QywwRUFBdUJBO0lBQ25HLE1BQU0sRUFBRXNDLFFBQVFJLHlCQUF5QixFQUFFRixXQUFXRyxlQUFlLEVBQUUsR0FBR3pDLGlGQUE4QkE7SUFDeEcsTUFBTSxDQUFDMEMsVUFBVUMsWUFBWSxHQUFHckQsK0NBQVFBLENBQWdCO0lBQ3hELE1BQU0sQ0FBQ3NELFlBQVlDLGNBQWMsR0FBR3ZELCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3dELFVBQVVDLFlBQVksR0FBR3pELCtDQUFRQSxDQUFxQjtJQUM3RCxNQUFNLENBQUMwRCxrQkFBa0JDLG9CQUFvQixHQUFHM0QsK0NBQVFBLENBQUM7SUFDekQsTUFBTTRELE9BQU9DLFFBQVFMO0lBRXJCLFNBQVNNLG1CQUFtQkMsS0FBVTtRQUNwQ0MsUUFBUUMsR0FBRyxDQUFDbEQsTUFBTW1ELFVBQVU7UUFDNUJuRCxNQUFNbUQsVUFBVSxDQUFDQyxHQUFHLENBQUNKO0lBQ3ZCO0lBQ0EsU0FBU0ssc0JBQXNCLEtBQWtDO1lBQWxDLEVBQUVoQixRQUFRLEVBQXdCLEdBQWxDO1FBQzdCQyxZQUFZRDtRQUNaRyxjQUFjO0lBQ2hCO0lBQ0EsU0FBU2MsV0FBV0MsTUFBYztRQUNoQ2YsY0FBY2dCLENBQUFBLGlCQUFrQkEsaUJBQWlCRDtJQUNuRDtJQUNBLFNBQVNFO1FBQ1BILFdBQVcsQ0FBQztJQUNkO0lBQ0EsU0FBU0k7UUFDUEosV0FBVztJQUNiO0lBQ0EsTUFBTUssc0JBQXNCO1lBQzFCckQ7U0FBQUEsaUJBQUFBLE1BQU1zRCxPQUFPLGNBQWJ0RCxxQ0FBQUEsZUFBZXVELElBQUksQ0FBQztZQUFFQyxVQUFVO1lBQVFDLFNBQVM7WUFBYUMsUUFBUTtZQUFxQkMsTUFBTTtRQUFLO0lBQ3hHO0lBQ0EsTUFBTUMsc0JBQXNCO1lBQzFCNUQ7U0FBQUEsaUJBQUFBLE1BQU1zRCxPQUFPLGNBQWJ0RCxxQ0FBQUEsZUFBZXVELElBQUksQ0FBQztZQUFFQyxVQUFVO1lBQVFDLFNBQVM7WUFBWUMsUUFBUTtZQUFxQkMsTUFBTTtRQUFLO0lBQ3ZHO0lBRUEsTUFBTUUsY0FBYyxDQUFDQztRQUNuQjFCLFlBQVkwQixNQUFNQyxhQUFhO0lBQ2pDO0lBQ0FwQixRQUFRQyxHQUFHLENBQUMsNEJBQTJCakM7SUFDdkMsTUFBTXFELFVBQVV2Riw4Q0FBT0EsQ0FDckIsSUFDRXdGLE9BQU9DLE9BQU8sQ0FBQ3hFLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsRUFBRUMsTUFBTSxDQUFDLFFBQWVDO2dCQUFkLENBQUNDLEtBQUtDLE1BQU07bUJBQVksQ0FBQztnQkFBQztnQkFBZTtnQkFBYztnQkFBVzthQUFZLENBQUNDLFFBQVEsQ0FBQ0Y7UUFBRyxHQUFHRyxHQUFHLENBQUMsUUFBZUo7Z0JBQWQsQ0FBQ0MsS0FBS0MsTUFBTTtZQUNoSyxJQUFJO2dCQUFDO2dCQUFVO2dCQUFXO2dCQUFRO2dCQUFTO2dCQUFXO2FBQWMsQ0FBQ0MsUUFBUSxDQUFDRixNQUFNO29CQUV4RTdFO2dCQURWLE9BQU87b0JBQ0xpRixRQUFRakYsQ0FBQUEsd0NBQUFBLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDbEYsbURBQUFBLHdDQUF5QzZFO29CQUNqRE0sYUFBYU47b0JBQ2JPLElBQUlQO29CQUNKLG1FQUFtRTtvQkFDbkUsdUpBQXVKO29CQUN2SlEsTUFBTTs0QkFBQyxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsR0FBRyxFQUFFQyxLQUFLLEVBQUU7d0JBQ2pDLHFCQUNFOzs4Q0FDRSw4REFBQ0M7b0NBQU1DLFdBQVU7OENBQVk7Ozs7Ozs4Q0FDN0IsOERBQUNwRyxxREFBTUE7b0NBQ0wsbUNBQW1DO29DQUNuQyxrRUFBa0U7b0NBQ2xFLGdCQUFnQjtvQ0FDaEJ1RixPQUFPVSxJQUFJSSxRQUFRLENBQUNDLE1BQU07b0NBQzFCLHdFQUF3RTtvQ0FDeEVDLGNBQWMsQ0FBQ0M7d0NBQVFQLElBQUlRLFlBQVksQ0FBQ0gsTUFBTSxHQUFHRSxFQUFFRSxTQUFTO29DQUFDO29DQUM3REMsT0FBTzt3Q0FBRUMsUUFBUTtvQ0FBUTs7Ozs7Ozs7b0JBa0JqQztnQkFDRjtZQUNGO1lBQ0EsSUFBSXRCLFFBQVEsUUFBUTtvQkFFUjdFO2dCQURWLE9BQU87b0JBQ0xpRixRQUFRakYsQ0FBQUEseUNBQUFBLE1BQU15RSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDbEYsb0RBQUFBLHlDQUF5QzZFO29CQUNqRE0sYUFBYTtvQkFFYmlCLHVCQUF1Qjt3QkFDckJDLE9BQU87b0JBQ1Q7b0JBQ0FDLHVCQUF1Qjt3QkFDckJELE9BQU87b0JBQ1Q7b0JBRUFFLHlCQUF5Qjt3QkFDdkJGLE9BQU87b0JBQ1Q7b0JBQ0FHLE1BQU07NEJBQUMsRUFBRWxCLElBQUksRUFBRUUsR0FBRyxFQUFFOytCQUFLLDhEQUFDM0csK0NBQUdBOzRCQUFDOEcsV0FBVTtzQ0FBbUJMLEtBQUttQixRQUFRLEdBQVNDLElBQUk7Ozs7OztvQkFBTTtvQkFDM0ZyQixNQUFNOzRCQUFDLEVBQUVHLEdBQUcsRUFBRTs0QkFXT0Esd0JBQWtFQSx5QkFBZ0NBLHlCQUN4R3ZGLGFBQUFBOzZDQVhiOzs4Q0FDRSw4REFBQ3lGO29DQUFNQyxXQUFVOzhDQUFZOzs7Ozs7OENBQzdCLDhEQUFDdkcsMERBQVFBO29DQUNQdUgsYUFBWTtvQ0FDWkMsYUFBWTtvQ0FDWkMsVUFBVSxDQUFDZDs0Q0FFc0I5RixRQUNpQkE7d0NBRmhEZ0QsUUFBUUMsR0FBRyxDQUFDNkM7d0NBQ1pqRSxXQUFXOzRDQUFFLEdBQUdELE9BQU87NENBQUVpRixJQUFJLEdBQUU3RyxTQUFBQSxtQkFBQUEsNkJBQUFBLE9BQU9NLElBQUksQ0FBQ1csT0FBTyxDQUFDNkYsSUFBSSxDQUFDLENBQUNELE9BQWVBLEtBQUsxQixFQUFFLEtBQUtXLEVBQUVqQixLQUFLLENBQUM0QixJQUFJO3dDQUFFO3dDQUNsR2xCLElBQUlRLFlBQVksR0FBRzs0Q0FBRSxHQUFHUixJQUFJUSxZQUFZOzRDQUFFYyxJQUFJLEdBQUU3RyxVQUFBQSxtQkFBQUEsOEJBQUFBLFFBQU9NLElBQUksQ0FBQ1csT0FBTyxDQUFDNkYsSUFBSSxDQUFDLENBQUNELE9BQWVBLEtBQUsxQixFQUFFLEtBQUtXLEVBQUVqQixLQUFLLENBQUM0QixJQUFJO3dDQUFFO29DQUNySDtvQ0FDQTVCLE9BQU87d0NBQUU0QixNQUFNbEIsRUFBQUEseUJBQUFBLElBQUlRLFlBQVksQ0FBQ2MsSUFBSSxjQUFyQnRCLDZDQUFBQSx1QkFBdUJKLEVBQUUsS0FBSTt3Q0FBTTRCLE1BQU8sVUFBR2xILGdFQUFvQkEsRUFBQzBGLDBCQUFBQSxJQUFJUSxZQUFZLENBQUNjLElBQUksY0FBckJ0Qiw4Q0FBQUEsd0JBQXVCeUIsSUFBSSxHQUFFLEtBQW1DLFFBQWhDekIsMEJBQUFBLElBQUlRLFlBQVksQ0FBQ2MsSUFBSSxjQUFyQnRCLDhDQUFBQSx3QkFBdUIwQixRQUFRLEtBQU07b0NBQUs7b0NBQzNKQyxPQUFPLEdBQUVsSCxTQUFBQSxtQkFBQUEsOEJBQUFBLGNBQUFBLE9BQU9NLElBQUksY0FBWE4sa0NBQUFBLFlBQWFpQixPQUFPLENBQUM4RCxHQUFHLENBQUMsQ0FBQzhCO3dDQUFpQixPQUFPOzRDQUFFSixNQUFNSSxLQUFLMUIsRUFBRTs0Q0FBRTRCLE1BQU0sR0FBc0NGLE9BQW5DaEgsZ0VBQW9CQSxDQUFDZ0gsS0FBS0csSUFBSSxHQUFFLEtBQWlCLE9BQWRILEtBQUtJLFFBQVE7d0NBQUc7b0NBQUU7Ozs7Ozs7OztnQkFLbEo7WUFDRjtZQUNBLElBQUlsSCxNQUFNeUUsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ3VDLE1BQU0sS0FBSyxlQUFlcEgsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUN1QyxNQUFNLEtBQUssUUFBUTtvQkFJckdwSDtnQkFGVixPQUFPO29CQUNMcUgsWUFBWSxDQUFDN0IsTUFBUSxJQUFJOEIsS0FBS3pDLE9BQU8sWUFBWVcsSUFBSStCLE9BQU8sR0FBRy9CLElBQUlnQyxRQUFRO29CQUMzRXZDLFFBQVFqRixDQUFBQSx5Q0FBQUEsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckNsRixvREFBQUEseUNBQXlDNkU7b0JBQ2pENEMsZUFBZTtvQkFDZkMsVUFBVTtvQkFDVkMsV0FBVztvQkFDWHhDLGFBQWFOO29CQUNiMkIsTUFBTTs0QkFBQyxFQUFFbEIsSUFBSSxFQUFFOzRCQUFLQTtnQ0FBQUEsaUJBQUFBLEtBQUttQixRQUFRLGdCQUFibkIscUNBQUFBLGVBQXVCc0Msa0JBQWtCLENBQUM7O29CQUM5RHhDLElBQUlQO29CQUNKUSxNQUFNLElBQU07Z0JBQ2Q7WUFDRjtnQkFHVXJGO1lBRFYsSUFBSTZFLFFBQVEsTUFBTSxPQUFPO2dCQUN2QkksUUFBUWpGLENBQUFBLHlDQUFBQSxNQUFNeUUsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ0ssS0FBSyxjQUFyQ2xGLG9EQUFBQSx5Q0FBeUM2RTtnQkFDakRNLGFBQWFOO2dCQUNiTyxJQUFJUDtnQkFDSlEsTUFBTSxJQUFNO1lBQ2Q7Z0JBRVVyRjtZQURWLElBQUk2RSxRQUFRLFFBQVEsT0FBTztnQkFDekJJLFFBQVFqRixDQUFBQSx5Q0FBQUEsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckNsRixvREFBQUEseUNBQXlDNkU7Z0JBQ2pETSxhQUFhTjtnQkFDYk8sSUFBSVA7Z0JBQ0oyQixNQUFNO3dCQUFDLEVBQUVsQixJQUFJLEVBQUVFLEdBQUcsRUFBRTsyQkFBSyw4REFBQ3FDO2tDQUFJdkMsS0FBS21CLFFBQVEsR0FBV3pCLEdBQUcsQ0FBQyxDQUFDOEMsb0JBQVEsOERBQUNDOzBDQUFJekksMkVBQWVBLENBQUN3STs7Ozs7Ozs7Ozs7Z0JBQWdCO2dCQUN4R3pDLE1BQU07d0JBQUMsRUFBRUcsR0FBRyxFQUFFO29CQUNadkMsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQnNDLElBQUlRLFlBQVksQ0FBQ2dDLElBQUk7b0JBQ2xELHFCQUFROzswQ0FDTiw4REFBQ3RDO2dDQUFNQyxXQUFVOzBDQUFZOzs7Ozs7MENBQzdCLDhEQUFDeEcsMERBQVFBO2dDQUNQOEksUUFBUXhHLHdCQUF3QnlHLE1BQU0sS0FBSyxJQUFJdkcsMEJBQTBCQSx3QkFBd0JnRCxNQUFNLENBQUN4RSxDQUFBQSxPQUFRc0Isd0JBQXdCdUQsR0FBRyxDQUFDN0UsQ0FBQUEsT0FBUUEsS0FBS2dJLFFBQVEsRUFBRXBELFFBQVEsQ0FBQzVFLEtBQUtnSSxRQUFRO2dDQUN6TC9DLElBQUc7Z0NBQ0hnRCxRQUFRM0csd0JBQXdCeUcsTUFBTSxHQUFHLElBQUl6RywwQkFBMEIrRCxJQUFJUSxZQUFZLENBQUNnQyxJQUFJO2dDQUM1RkssY0FBYTtnQ0FDYkMsY0FBYTtnQ0FDYkMsY0FBYyxDQUFDQyxxQkFBUyw4REFBQ0M7OzRDQUF5QkQsS0FBS0UsU0FBUzs0Q0FBQzs0Q0FBRUYsS0FBS0csUUFBUTs7dUNBQTlDSCxLQUFLTCxRQUFRO2dDQUMvQ3RCLFVBQVUsQ0FBQ2Q7b0NBQ1Q5QyxRQUFRQyxHQUFHLENBQUMsZUFBZTZDLEVBQUVrQyxNQUFNO29DQUNuQ3JHLDJCQUEyQjsyQ0FBSW1FLEVBQUVrQyxNQUFNO3FDQUFDO29DQUN4Q3ZHLDJCQUEyQjsyQ0FBSXFFLEVBQUVxQyxNQUFNO3FDQUFDO29DQUN4QzVDLElBQUlRLFlBQVksQ0FBQ2dDLElBQUksR0FBR2pDLEVBQUVxQyxNQUFNO2dDQUNsQztnQ0FDQVEsYUFBYTtvQ0FBRXpDLFFBQVE7Z0NBQVE7Z0NBQy9CMEMsYUFBYTtvQ0FBRTFDLFFBQVE7Z0NBQVE7Z0NBQy9CeEIsTUFBTTtnQ0FBQ21FLFVBQVM7Z0NBQ2hCQyxpQkFBZ0I7Z0NBQ2hCQyx5QkFBd0I7Z0NBQThCQyx5QkFBd0I7Ozs7Ozs7O2dCQUtwRjtZQUNGO2lCQUNLO29CQUVPako7Z0JBRFYsT0FBTztvQkFDTGlGLFFBQVFqRixDQUFBQSx5Q0FBQUEsTUFBTXlFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckNsRixvREFBQUEseUNBQXlDNkU7b0JBQ2pETSxhQUFhTjtvQkFDYk8sSUFBSVA7Z0JBRU47WUFFRjtRQUNGLElBRUY7UUFBQzlEO1NBQWlCZCxTQUFBQSxtQkFBQUEsNkJBQUFBLE9BQU9NLElBQUk7U0FBRUwsZ0JBQUFBLDBCQUFBQSxvQ0FBQUEsY0FBY0ssSUFBSTtLQUFDO0lBRXBELE1BQU1rRixRQUFRbEgsNEVBQXFCQSxDQUFDO1FBQ2xDK0Y7UUFDQS9ELE1BQU1QLE1BQU1VLEtBQUssR0FBRyxFQUFFLEdBQUdWLE1BQU1BLEtBQUssQ0FBQ08sSUFBSSxHQUFHUCxNQUFNQSxLQUFLLENBQUNPLElBQUksR0FBRztZQUFDUCxNQUFNQSxLQUFLLENBQUNPLElBQUk7U0FBQztRQUNqRjJJLFVBQVVsSixNQUFNVSxLQUFLLEdBQUcsSUFBSVYsTUFBTUEsS0FBSyxDQUFDTyxJQUFJLEdBQUdQLE1BQU1BLEtBQUssQ0FBQ08sSUFBSSxDQUFDMkgsTUFBTSxHQUFHO1FBQ3pFaUIsb0JBQW9CO1FBQ3BCQyxzQkFBc0I7UUFDdEJDLG9CQUFvQjtRQUNwQkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLGtCQUFrQjtRQUNsQkMsb0JBQW9CO1FBQ3BCQyxvQkFBb0I7UUFDcEJDLHFCQUFxQjtRQUNyQkMsc0JBQXNCO1FBQ3RCQyxrQkFBa0I7UUFDbEJDLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmQyxpQkFBaUI7UUFDakJDLGtCQUFrQjtRQUNsQkMsY0FBYztZQUNaL0csWUFBWTtnQkFBRWdILFVBQVU7Z0JBQUdDLFdBQVc7WUFBRTtZQUN4Q0Msa0JBQWtCO2dCQUFFeEUsUUFBUTtnQkFBT3lFLFlBQVk7Z0JBQU8vQyxTQUFTO2dCQUFPZ0QsWUFBWTtnQkFBTy9DLFVBQVU7Z0JBQU9nRCxhQUFhO2dCQUFPQyxPQUFPO2dCQUFPQyxZQUFZO2dCQUFPdEYsSUFBSTtnQkFBT3VGLFVBQVU7WUFBTTtZQUMxTEMsU0FBUztZQUNUQyxrQkFBa0I7WUFDbEJDLFNBQVM7Z0JBQUM7b0JBQUUxRixJQUFJO29CQUFNMkYsTUFBTTtnQkFBTTthQUFFO1FBQ3RDO1FBQ0EvSCxPQUFPO1lBQ0xHLFlBQVluRCxNQUFNbUQsVUFBVSxDQUFDNkgsSUFBSTtZQUNqQ3hLLFdBQVdSLE1BQU1RLFNBQVM7UUFHNUI7UUFDQXlLLGNBQWN6TSxpRkFBbUJBO1FBQ2pDdUUsb0JBQW9CQTtRQUNwQm1JLHlCQUF5QjtZQUN2QixlQUFlO2dCQUNiQyxjQUFjO1lBQ2hCO1lBQ0Esa0JBQWtCO2dCQUNoQkEsY0FBYztZQUNoQjtZQUNBLG1CQUFtQjtnQkFDakIsK0NBQStDO2dCQUMvQ0MsTUFBTTtnQkFDTkQsY0FBYztZQUNoQjtZQUNBLG1CQUFtQjtnQkFDakJBLGNBQWM7WUFDaEI7UUFDRjtRQUNBRSxlQUFlO1lBQ2JDLE1BQU07WUFDTkMsaUJBQWlCO1FBQ25CO1FBQ0FDLG9CQUFvQjtnQkFBQyxFQUFFL0YsS0FBSyxFQUFFO21CQUFNO2dCQUNsQywyQ0FBMkM7Z0JBQzNDLHdCQUF3QjtnQkFDeEJFLFdBQVc7Z0JBQ1g4RixTQUFTO29CQUFFQyxNQUFNO2dCQUFxRDtnQkFFdEVDLElBQUk7b0JBQ0Z4RixRQUFTO29CQUNULGlDQUFpQztvQkFDakMscUJBQXFCO29CQUNyQixnQ0FBZ0M7b0JBQ2hDeUYsaUJBQWlCO29CQUNqQkMsWUFBWTtvQkFDWkMscUJBQXFCO29CQUNyQiwrQkFBK0I7d0JBQzdCRixpQkFBaUI7d0JBQ2pCQyxZQUFZO3dCQUNaQyxxQkFBcUI7d0JBQ3JCQyxPQUFPO29CQUNUO29CQUNBLG1CQUFtQjt3QkFDakJILGlCQUFpQjt3QkFDakJDLFlBQVk7d0JBQ1pDLHFCQUFxQjt3QkFDckJDLE9BQU87b0JBQ1Q7Z0JBQ0Y7WUFDRjtRQUFBO1FBQ0FDLHVCQUF1QjtnQkFBQyxFQUFFeEcsR0FBRyxFQUFFQyxLQUFLLEVBQUU7bUJBQU07Z0JBQzFDNUMsTUFBTXhCLGVBQWVFO2dCQUNyQm9LLElBQUk7b0JBQ0YscUJBQXFCO3dCQUNuQk0sU0FBUztvQkFDWDtvQkFDQSwwQkFBMEI7d0JBQ3hCQSxTQUFTO29CQUNYO29CQUNBQyxRQUFRO2dCQUVWO1lBQ0Y7UUFBQTtRQUNBQyxpQkFBaUI7UUFDakJDLG1CQUFtQjtRQUNuQkMsa0JBQWtCO2dCQUFDLEVBQUU1RyxLQUFLLEVBQUU2RyxNQUFNLEVBQUU5RyxHQUFHLEVBQUU7Z0JBSTNCOEc7WUFIWnJKLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JvSjtZQUNoQyxNQUFNLEVBQUVsSCxFQUFFLEVBQUUsR0FBR21ILE1BQU0sR0FBR0Q7WUFDeEJDLEtBQUt6RixJQUFJLEdBQUd3RixPQUFPeEYsSUFBSSxDQUFDMUIsRUFBRTtZQUMxQm1ILEtBQUt2RSxJQUFJLEdBQUdzRSxFQUFBQSxlQUFBQSxPQUFPdEUsSUFBSSxjQUFYc0UsbUNBQUFBLGFBQWF0SCxHQUFHLENBQUM3RSxDQUFBQSxPQUFRQSxLQUFLaUYsRUFBRSxNQUFLLEVBQUU7WUFDbkRqRCwwQkFBMEJvSyxNQUFNO2dCQUM5QkMsWUFBWTtnQkFDWkMsV0FBVztvQkFDVGhILE1BQU1pSCxhQUFhLENBQUMsT0FBTyxvQkFBb0I7b0JBQy9DcE0sTUFBTXNELE9BQU8sQ0FBRUMsSUFBSSxDQUFDO3dCQUFFQyxVQUFVO3dCQUFXQyxTQUFTO3dCQUFRQyxRQUFRLGFBQThCLE9BQWpCc0ksT0FBT3hGLElBQUksQ0FBQ0osSUFBSSxFQUFDO29CQUFjO2dCQUNsSDtnQkFDQWlHLFNBQVMsQ0FBQ2pNO3dCQUM2REE7b0JBQXJFSixNQUFNc0QsT0FBTyxDQUFFQyxJQUFJLENBQUM7d0JBQUVDLFVBQVU7d0JBQVNDLFNBQVM7d0JBQVFDLFFBQVEsR0FBOEIsUUFBM0J0RCxrQkFBQUEsTUFBTWtNLFFBQVEsY0FBZGxNLHNDQUFBQSxnQkFBZ0JtTSxVQUFVO29CQUFHO29CQUNsRyxrREFBa0Q7b0JBQ2xEckgsSUFBSVEsWUFBWSxHQUFHO3dCQUFFdEYsT0FBT0EsTUFBTWtNLFFBQVE7d0JBQUUsR0FBR3BILElBQUlRLFlBQVk7b0JBQUM7b0JBQ2hFO2dCQUNGO1lBQ0Y7UUFDRjtRQUNBOEcsb0JBQW9CO2dCQUNsQiw2QkFBNkI7WUFDN0J4TTthQUFBQSxpQkFBQUEsTUFBTXNELE9BQU8sY0FBYnRELHFDQUFBQSxlQUFldUQsSUFBSSxDQUFDO2dCQUFFQyxVQUFVO2dCQUFRQyxTQUFTO2dCQUFRQyxRQUFRO1lBQWE7UUFDaEY7UUFDQStJLG1CQUFtQjtnQkFBQyxFQUFFdEgsS0FBSyxFQUFFNkcsTUFBTSxFQUFFOUcsR0FBRyxFQUFFO2dCQUlkOEc7WUFIMUJySixRQUFRQyxHQUFHLENBQUMscUJBQXFCb0o7WUFDakMsTUFBTSxFQUFFbEgsRUFBRSxFQUFFLEdBQUdtSCxNQUFNLEdBQUdEO1lBQ3hCQyxLQUFLekYsSUFBSSxHQUFHd0YsT0FBT3hGLElBQUksQ0FBQzFCLEVBQUU7WUFDMUJtSCxLQUFLdkUsSUFBSSxHQUFHc0UsT0FBT3RFLElBQUksSUFBR3NFLGVBQUFBLE9BQU90RSxJQUFJLGNBQVhzRSxtQ0FBQUEsYUFBYXRILEdBQUcsQ0FBQzdFLENBQUFBLE9BQVFBLEtBQUtpRixFQUFFLElBQUksRUFBRTtZQUNoRXBELDJCQUEyQnVLLE1BQU07Z0JBQy9CRSxXQUFXO29CQUNUaEgsTUFBTXVILGNBQWMsQ0FBQyxPQUFPLG9CQUFvQjtvQkFDaEQxTSxNQUFNc0QsT0FBTyxDQUFFQyxJQUFJLENBQUM7d0JBQUVDLFVBQVU7d0JBQVdDLFNBQVM7d0JBQVFDLFFBQVEsYUFBOEIsT0FBakJzSSxPQUFPeEYsSUFBSSxDQUFDSixJQUFJLEVBQUM7b0JBQU87Z0JBQzNHO2dCQUNBaUcsU0FBUyxDQUFDak07d0JBQzZEQTtvQkFBckVKLE1BQU1zRCxPQUFPLENBQUVDLElBQUksQ0FBQzt3QkFBRUMsVUFBVTt3QkFBU0MsU0FBUzt3QkFBUUMsUUFBUSxHQUE4QixRQUEzQnRELGtCQUFBQSxNQUFNa00sUUFBUSxjQUFkbE0sc0NBQUFBLGdCQUFnQm1NLFVBQVU7b0JBQUc7b0JBQ2xHLGtEQUFrRDtvQkFDbERySCxJQUFJUSxZQUFZLEdBQUc7d0JBQUV0RixPQUFPQSxNQUFNa00sUUFBUTt3QkFBRSxHQUFHcEgsSUFBSVEsWUFBWTtvQkFBQztvQkFDaEU7Z0JBQ0Y7WUFDRjtRQUNGO1FBQ0FpSCxxQkFBcUI7Z0JBQ25CM007YUFBQUEsaUJBQUFBLE1BQU1zRCxPQUFPLGNBQWJ0RCxxQ0FBQUEsZUFBZXVELElBQUksQ0FBQztnQkFBRUMsVUFBVTtnQkFBUUMsU0FBUztnQkFBUUMsUUFBUTtZQUFhO1FBQ2hGO1FBQ0FrSixxQkFBcUI7WUFDbkJ2SCxXQUFXO1lBQ1hnRyxJQUFJO2dCQUNGLDBCQUEwQjtvQkFDeEJDLGlCQUFpQjtnQkFDbkI7Z0JBQ0FBLGlCQUFpQjtnQkFDakJHLE9BQU87Z0JBQ1BGLFlBQVk7Z0JBQ1pDLHFCQUFxQjtZQUN2QjtRQUNGO1FBQ0FxQix3QkFBd0I7Z0JBQUMsRUFBRTFILEtBQUssRUFBRTtnQkFRaURBLG1DQUFzREE7bUJBUmpHO2dCQUN0Q0UsV0FBVztnQkFDWGdHLElBQUk7b0JBQ0ZFLFlBQVk7b0JBQ1pDLHFCQUFxQjtvQkFDckIscUJBQXFCO29CQUNyQixnQ0FBZ0M7b0JBQ2hDRixpQkFBaUI7b0JBQ2pCekYsUUFBUVYsTUFBTTJILFFBQVEsR0FBR0MsWUFBWSxHQUFJLGdCQUFlLCtCQUF1QjVILG9DQUFBQSxNQUFNNkgsSUFBSSxDQUFDQyxhQUFhLENBQUMzSixPQUFPLGNBQWhDNkIsd0RBQUFBLGtDQUFrQytILFlBQVksRUFBQyxTQUF5RCxRQUFsRC9ILHVDQUFBQSxNQUFNNkgsSUFBSSxDQUFDRyxnQkFBZ0IsQ0FBQzdKLE9BQU8sY0FBbkM2QiwyREFBQUEscUNBQXFDK0gsWUFBWSxFQUFDO2dCQUV6TDtZQUNGOztRQUNBRSxvQkFBb0I7WUFDbEIvSCxXQUFXO1lBQ1hnRyxJQUFJO2dCQUVGLHFCQUFxQjtnQkFDckIsZ0NBQWdDO2dCQUNoQ0MsaUJBQWlCO2dCQUNqQkcsT0FBTztnQkFDUEYsWUFBWTtnQkFDWkMscUJBQXFCO1lBQ3ZCO1FBQ0Y7UUFDQTFGLHVCQUF1QjtZQUNyQnVGLElBQUk7Z0JBQ0YscUJBQXFCO2dCQUNyQixnQ0FBZ0M7Z0JBQ2hDQyxpQkFBaUI7Z0JBQ2pCRyxPQUFPO2dCQUNQRixZQUFZO2dCQUNaQyxxQkFBcUI7WUFDdkI7UUFDRjtRQUNBNkIsb0JBQW9CO1lBQ2xCaEksV0FBVztZQUNYZ0csSUFBSTtnQkFDRkUsWUFBWTtnQkFDWkMscUJBQXFCO2dCQUNyQixxQkFBcUI7Z0JBQ3JCLGdDQUFnQztnQkFDaENGLGlCQUFpQjtnQkFDakJHLE9BQU87WUFDVDtRQUVGO1FBQ0E2QixtQkFBbUI7WUFDakJqSSxXQUFXO1lBQ1hnRyxJQUFJO2dCQUNGRSxZQUFZO2dCQUNaQyxxQkFBcUI7Z0JBQ3JCLCtDQUErQztnQkFDL0MsOEJBQThCO29CQUM1QkYsaUJBQWlCO29CQUNqQkcsT0FBTztvQkFDUEYsWUFBWTtvQkFDWkMscUJBQXFCO2dCQUN2QjtnQkFDQSwrQkFBK0I7b0JBQzdCRixpQkFBaUI7b0JBQ2pCRyxPQUFPO29CQUNQRixZQUFZO29CQUNaQyxxQkFBcUI7Z0JBQ3ZCO1lBQ0Y7UUFDRjtRQUNBK0IsK0JBQStCO2dCQUFDLEVBQUVwSSxLQUFLLEVBQUU7bUJBQ3ZDLDhEQUFDckgsc0RBQUtBO2dCQUFDMFAsV0FBVztnQkFBT0MsU0FBUzs7a0NBQ2hDLDhEQUFDdk8seUNBQUdBO3dCQUFDd08sR0FBRTt3QkFBTUMsR0FBRTtrQ0FDYiw0RUFBQ3hQLHNEQUFNQTs0QkFDTHlQLE1BQUs7NEJBQ0xDLE9BQU87NEJBQ1Asb0JBQW9COzRCQUNwQkMsaUJBQWV2TCxPQUFPLGVBQWV3TDs0QkFDckNDLGlCQUFjOzRCQUNkQyxpQkFBZTFMLE9BQU8sU0FBU3dMOzRCQUMvQkcsU0FBUyxDQUFDcEs7Z0NBQ1JxQixNQUFNdUgsY0FBYyxDQUFDO2dDQUFPeEwsaUJBQWlCLE9BQU95QixRQUFRQyxHQUFHLENBQUM7NEJBQ2xFOzRCQUNBa0ksTUFBSzs7Ozs7Ozs7Ozs7a0NBSVQsOERBQUM1TCx5Q0FBR0E7d0JBQUN3TyxHQUFFO3dCQUFTQyxHQUFFO2tDQUNoQiw0RUFBQ3hQLHNEQUFNQTs0QkFDTDBQLE9BQU87NEJBQ1BNLFVBQVVoSixNQUFNaUoscUJBQXFCOzRCQUNyQyxvQkFBb0I7NEJBQ3BCTixpQkFBZXZMLE9BQU8sZUFBZXdMOzRCQUNyQ0MsaUJBQWM7NEJBQ2RDLGlCQUFlMUwsT0FBTyxTQUFTd0w7NEJBQy9CRyxTQUFTcks7NEJBQ1QrSixNQUFLOzRCQUNMLG9GQUFvRjs0QkFDcEY5QyxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztRQUdKO1FBRVR1RCxxQkFBcUIsSUFBTztnQkFDMUJoRCxJQUFJLENBQUNpRCxRQUFXO3dCQUNkaEQsaUJBQ0VnRCxNQUFNQyxPQUFPLENBQUNDLElBQUksS0FBSyxTQUNuQiwwQkFDQTtvQkFDUjtZQUNGO1FBQ0FDLDhCQUE4QjtnQkFBQyxFQUFFQyxzQkFBc0IsRUFBRXhKLEdBQUcsRUFBRUMsS0FBSyxFQUFFO21CQUNuRSw4REFBQ2dEO2dCQUFJdkMsT0FBTztvQkFBRWdHLFFBQVE7Z0JBQWtCOzBCQUN0Qyw0RUFBQ3ROLHdEQUFPQTtvQkFBQ3FRLFVBQVM7b0JBQ2hCaEssc0JBQ0UsOERBQUN3RDt3QkFBSTlDLFdBQVU7OzBDQUNiLDhEQUFDdUo7Z0NBQUt2SixXQUFVOzBDQUF3Qjs7MENBQ3hDLDhEQUFDekgsc0RBQWFBOzBDQUNaLDRFQUFDRyx3RUFBcUJBO29DQUFDOFEsU0FBUTtvQ0FBTzFKLE9BQU9BO29DQUFPRCxLQUFLQTs7Ozs7b0JBSS9ENEosU0FBUzdOO29CQUNUOE4sUUFBUTt3QkFBUTVKLE1BQU11SCxjQUFjLENBQUM7d0JBQU94TCxpQkFBaUI7b0JBQU87b0JBQ3BFbUUsV0FBVTs4QkFDViw0RUFBQ3hILHNEQUFhQTt3QkFDWndOLElBQUk7NEJBQ0ZNLFNBQVM7NEJBQ1RxRCxlQUFlOzRCQUNmQyxLQUFLO3dCQUNQO2tDQUVDUDs7Ozs7Ozs7Ozs7Ozs7OztRQUdGO1FBQ1BRLDRCQUE0QjtnQkFBQyxFQUFFUixzQkFBc0IsRUFBRXhKLEdBQUcsRUFBRUMsS0FBSyxFQUFFO21CQUNqRSw4REFBQ2dEO2dCQUFJdkMsT0FBTztvQkFBRWdHLFFBQVE7Z0JBQWtCOzBCQUN0Qyw0RUFBQ3ROLHdEQUFPQTtvQkFBQ3FRLFVBQVM7b0JBQ2hCaEssc0JBQ0UsOERBQUN3RDt3QkFBSTlDLFdBQVU7OzBDQUNiLDhEQUFDOEo7Z0NBQUc5SixXQUFVOztvQ0FBd0I7b0NBQXVCSCxJQUFJSSxRQUFRLENBQUNSLEVBQUU7OzswQ0FDNUUsOERBQUNsSCxzREFBYUE7MENBQ1osNEVBQUNHLHdFQUFxQkE7b0NBQUM4USxTQUFRO29DQUFPMUosT0FBT0E7b0NBQU9ELEtBQUtBOzs7OztvQkFJL0Q0SixTQUFTL047b0JBQ1RnTyxRQUFRO3dCQUNONUosTUFBTWlILGFBQWEsQ0FBQzt3QkFDcEJwTCxlQUFlO29CQUNqQjtvQkFDQXFFLFdBQVU7OEJBQ1YsNEVBQUN4SCxzREFBYUE7d0JBQ1p3TixJQUFJOzRCQUFFTSxTQUFTOzRCQUFRcUQsZUFBZTs0QkFBVUMsS0FBSzt3QkFBUzs7NEJBRTdEUDs0QkFBdUI7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBR3pCO1FBQ1BVLG1CQUFtQjtnQkFBQyxFQUFFbEssR0FBRyxFQUFFO21CQUN6QnRHLDZEQUFLQSxDQUFDc0csSUFBSUksUUFBUSxDQUFDQyxNQUFNO1FBQUE7UUFDM0I4SixrQkFBa0I7Z0JBQUMsRUFBRXJLLElBQUksRUFBRUUsR0FBRyxFQUFFQyxLQUFLLEVBQUU7bUJBQ3JDLDhEQUFDeUo7Z0JBQUt2SixXQUFVOztrQ0FDZCw4REFBQ25HLHlDQUFHQTt3QkFBQ3dPLEdBQUU7d0JBQVNDLEdBQUU7a0NBQ2hCLDRFQUFDeFAsc0RBQU1BOzRCQUFDMk0sTUFBSzs0QkFBUThDLE1BQUs7NEJBQWVNLFNBQVM7Z0NBQ2hEcE4saUJBQWlCb0UsSUFBSUksUUFBUSxDQUFDUixFQUFFO2dDQUNoQ0ssTUFBTWlILGFBQWEsQ0FBQ2xIO2dDQUNwQmxFLGVBQWU7Z0NBQ2YyQixRQUFRQyxHQUFHLENBQUM7NEJBQ2Q7NEJBQUdpTCxPQUFPOzRCQUFDeUIsUUFBUTs7Ozs7Ozs7Ozs7a0NBRXJCLDhEQUFDcFEseUNBQUdBO3dCQUFDd08sR0FBRTt3QkFBU0MsR0FBRTtrQ0FDaEIsNEVBQUN4UCxzREFBTUE7NEJBQUMyTSxNQUFLOzRCQUFROEMsTUFBSzs0QkFBY0MsT0FBTzs0QkFBQ3lCLFFBQVE7NEJBQ3REcEIsU0FBUyxDQUFDcEssUUFBVXpGLHNFQUFZQSxDQUFDO29DQUMvQnlKLFFBQVFoRSxNQUFNQyxhQUFhO29DQUMzQndMLFNBQVM7b0NBQ1QzQixNQUFNO29DQUNOLDBCQUEwQjtvQ0FDMUI0QixpQkFBaUI7b0NBQ2pCQyxhQUFhO29DQUNiQyxhQUFhO29DQUNiQyxRQUFRdE07b0NBQ1J1TSxRQUFRaE07Z0NBQ1Y7Ozs7Ozs7Ozs7O2tDQUdKLDhEQUFDeEYsa0VBQVlBOzs7Ozs7Ozs7OztRQUNUO0lBRVY7SUFDQSxxQkFBTzs7MEJBQUUsOERBQUNKLHFFQUFrQkE7Z0JBQUNtSCxPQUFPQTs7Ozs7OzBCQUFTLDhEQUFDM0csb0RBQUtBO2dCQUFDcVIsS0FBSzdQOzs7Ozs7OztBQUMzRDtHQXhoQndCUDs7UUFJbURILDZEQUFjQTtRQUNLRixvRUFBcUJBO1FBQ3pDRyw2REFBY0E7UUFTVkosc0VBQXVCQTtRQUN6QkUsNkVBQThCQTtRQW1MMUZwQix3RUFBcUJBOzs7S0FuTWJ3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvKG1haW4pL3BsYW5zLyhjb21wb25lbnRzKS9HZW5lcmljVEFibGVBcmJpdHJhdGlvbi50c3g/Y2Q5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IHsgQm94LCBEaWFsb2dBY3Rpb25zLCBEaWFsb2dDb250ZW50LCBTdGFjayB9IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xyXG5pbXBvcnQge1xyXG4gIE1SVF9FZGl0QWN0aW9uQnV0dG9ucyxcclxuICBNUlRfUm93RGF0YSxcclxuICBNYXRlcmlhbFJlYWN0VGFibGUsXHJcbiAgdXNlTWF0ZXJpYWxSZWFjdFRhYmxlLFxyXG4gIHR5cGUgTVJUX0NvbHVtbkRlZlxyXG59IGZyb20gJ21hdGVyaWFsLXJlYWN0LXRhYmxlJztcclxuaW1wb3J0IHsgTVJUX0xvY2FsaXphdGlvbl9GUiB9IGZyb20gJ21hdGVyaWFsLXJlYWN0LXRhYmxlL2xvY2FsZXMvZnInO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdwcmltZXJlYWN0L2J1dHRvbic7XHJcbmltcG9ydCB7IENvbmZpcm1Qb3B1cCwgY29uZmlybVBvcHVwIH0gZnJvbSAncHJpbWVyZWFjdC9jb25maXJtcG9wdXAnO1xyXG5pbXBvcnQgeyBTaWRlYmFyIH0gZnJvbSAncHJpbWVyZWFjdC9zaWRlYmFyJztcclxuaW1wb3J0IHsgVGFiUGFuZWwsIFRhYlZpZXcgfSBmcm9tICdwcmltZXJlYWN0L3RhYnZpZXcnO1xyXG5pbXBvcnQgeyBUYWcgfSBmcm9tICdwcmltZXJlYWN0L3RhZyc7XHJcbmltcG9ydCB7IFRvYXN0IH0gZnJvbSAncHJpbWVyZWFjdC90b2FzdCc7XHJcbmltcG9ydCB7IHVzZU1lbW8sIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBwYXJzZSBmcm9tICdodG1sLXJlYWN0LXBhcnNlcic7XHJcbi8vIGltcG9ydCB7IEVkaXRvciB9IGZyb20gJ0B0aW55bWNlL3RpbnltY2UtcmVhY3QnO1xyXG5pbXBvcnQgeyBQaWNrTGlzdCB9IGZyb20gJ3ByaW1lcmVhY3QvcGlja2xpc3QnO1xyXG5pbXBvcnQgeyBEcm9wZG93biB9IGZyb20gJ3ByaW1lcmVhY3QvZHJvcGRvd24nO1xyXG5pbXBvcnQgeyBnZXRDb29raWUgfSBmcm9tICdjb29raWVzLW5leHQnO1xyXG5pbXBvcnQgeyBnZXRVc2VyRnVsbG5hbWUgfSBmcm9tICdAL3V0aWxpdGllcy9mdW5jdGlvbnMvdXRpbHMnO1xyXG5pbXBvcnQgeyBFZGl0b3IgfSBmcm9tICdwcmltZXJlYWN0L2VkaXRvcic7XHJcbmltcG9ydCB7IENhbiB9IGZyb20gJ0AvYXBwL0Nhbic7XHJcbmltcG9ydCB7IHVzZUFwaUFyYml0cmF0aW9uQ3JlYXRlLCB1c2VBcGlBcmJpdHJhdGlvbkxpc3QsIHVzZUFwaUFyYml0cmF0aW9uUGFydGlhbFVwZGF0ZSwgdXNlQXBpTWlzc2lvbkxpc3QsIHVzZUFwaVBsYW5MaXN0LCB1c2VBcGlVc2VyTGlzdCB9IGZyb20gJ0AvaG9va3MvdXNlTmV4dEFwaSc7XHJcbmltcG9ydCB7IFBsYW4sIFVzZXIsQXJiaXRyYXRpb24gfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XHJcbmltcG9ydCB7IHRyYW5zZm9ybWVyUGxhbkxhYmVsIH0gZnJvbSAnQC9saWIvZW51bXMnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gR2VuZXJpY1RhYmxlPFQgZXh0ZW5kcyBNUlRfUm93RGF0YT4oZGF0YV86IHsgaXNMb2FkaW5nOiBhbnk7IGRhdGFfOiBhbnksIGVycm9yOiBhbnksIGRhdGFfdHlwZTogYW55IHwgdW5kZWZpbmVkLCBwYWdpbmF0aW9uOiBhbnkgfSkge1xyXG4gIGNvbnN0IHVzZXIgPSBKU09OLnBhcnNlKGdldENvb2tpZSgndXNlcicpPy50b1N0cmluZygpIHx8ICd7fScpXHJcblxyXG4gIGNvbnN0IHRvYXN0ID0gdXNlUmVmPFRvYXN0IHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3Qge2RhdGEgOiBwbGFucyAsIGlzTG9hZGluZzogcGxhbnNfaXNMb2FkaW5nLCBlcnJvcjogcGxhbnNfZXJyb3IgfSA9IHVzZUFwaVBsYW5MaXN0KClcclxuICBjb25zdCB7ZGF0YSA6YXJiaXRyYXRpb25zLCBpc0xvYWRpbmc6IGFyYml0cmF0aW9uc19pc0xvYWRpbmcsIGVycm9yOiBhcmJpdHJhdGlvbnNfZXJyb3IgfSA9IHVzZUFwaUFyYml0cmF0aW9uTGlzdCgpXHJcbiAgY29uc3Qge2RhdGEgOiB1c2VycywgaXNMb2FkaW5nOiB1c2Vyc19pc0xvYWRpbmcsIGVycm9yOiB1c2Vyc19lcnJvciB9ID0gdXNlQXBpVXNlckxpc3QoKVxyXG5cclxuICBjb25zdCB1c2Vyc19kYXRhID0gdXNlTWVtbygoKSA9PiB1c2Vycz8uZGF0YS5yZXN1bHRzICB8fCBbXSwgW3VzZXJzX2lzTG9hZGluZ10pXHJcbiAgY29uc3QgW2FyYml0cmF0aW9uSUQsIHNldEFyYml0cmF0aW9uSURdID0gdXNlU3RhdGUoMClcclxuICBjb25zdCBbZWRpdFZpc2libGUsIHNldEVkaXRWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY3JlYXRlVmlzaWJsZSwgc2V0Q3JlYXRlVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3BpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtLCBzZXRQaWNrbGlzdFRhcmdldFZhbHVlVGVhbV0gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW3BpY2tsaXN0U291cmNlVmFsdWVUZWFtLCBzZXRQaWNrbGlzdFNvdXJjZVZhbHVlVGVhbV0gPSB1c2VTdGF0ZSh1c2Vyc19kYXRhKTtcclxuICBjb25zdCBbcm93VG9iZSwgc2V0Um93VG9iZV0gPSB1c2VTdGF0ZSh7fSk7XHJcbiAgY29uc3QgeyBtdXRhdGU6IGFyYml0cmF0aW9uX2NyZWF0ZV90cmlnZ2VyLCBpc1BlbmRpbmc6IGlzQ3JlYXRlTXV0YXRpbmcgfSA9IHVzZUFwaUFyYml0cmF0aW9uQ3JlYXRlKClcclxuICBjb25zdCB7IG11dGF0ZTogYXJiaXRyYXRpb25fcGF0Y2hfdHJpZ2dlciwgaXNQZW5kaW5nOiBpc1BhdGNoTXV0YXRpbmcgfSA9IHVzZUFwaUFyYml0cmF0aW9uUGFydGlhbFVwZGF0ZSgpXHJcbiAgY29uc3QgW251bVBhZ2VzLCBzZXROdW1QYWdlc10gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbcGFnZU51bWJlciwgc2V0UGFnZU51bWJlcl0gPSB1c2VTdGF0ZSgxKTtcclxuICBjb25zdCBbYW5jaG9yRWwsIHNldEFuY2hvckVsXSA9IHVzZVN0YXRlPG51bGwgfCBIVE1MRWxlbWVudD4obnVsbCk7XHJcbiAgY29uc3QgW3Jvd0FjdGlvbkVuYWJsZWQsIHNldFJvd0FjdGlvbkVuYWJsZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IG9wZW4gPSBCb29sZWFuKGFuY2hvckVsKTtcclxuXHJcbiAgZnVuY3Rpb24gb25QYWdpbmF0aW9uQ2hhbmdlKHN0YXRlOiBhbnkpIHtcclxuICAgIGNvbnNvbGUubG9nKGRhdGFfLnBhZ2luYXRpb24pO1xyXG4gICAgZGF0YV8ucGFnaW5hdGlvbi5zZXQoc3RhdGUpXHJcbiAgfTtcclxuICBmdW5jdGlvbiBvbkRvY3VtZW50TG9hZFN1Y2Nlc3MoeyBudW1QYWdlcyB9OiB7IG51bVBhZ2VzOiBudW1iZXIgfSkge1xyXG4gICAgc2V0TnVtUGFnZXMobnVtUGFnZXMpO1xyXG4gICAgc2V0UGFnZU51bWJlcigxKTtcclxuICB9XHJcbiAgZnVuY3Rpb24gY2hhbmdlUGFnZShvZmZzZXQ6IG51bWJlcikge1xyXG4gICAgc2V0UGFnZU51bWJlcihwcmV2UGFnZU51bWJlciA9PiBwcmV2UGFnZU51bWJlciArIG9mZnNldCk7XHJcbiAgfVxyXG4gIGZ1bmN0aW9uIHByZXZpb3VzUGFnZSgpIHtcclxuICAgIGNoYW5nZVBhZ2UoLTEpO1xyXG4gIH1cclxuICBmdW5jdGlvbiBuZXh0UGFnZSgpIHtcclxuICAgIGNoYW5nZVBhZ2UoMSk7XHJcbiAgfVxyXG4gIGNvbnN0IGFjY2VwdF9yb3dfZGVsZXRpb24gPSAoKSA9PiB7XHJcbiAgICB0b2FzdC5jdXJyZW50Py5zaG93KHsgc2V2ZXJpdHk6ICdpbmZvJywgc3VtbWFyeTogJ0NvbmZpcm1lZCcsIGRldGFpbDogJ1lvdSBoYXZlIGFjY2VwdGVkJywgbGlmZTogMzAwMCB9KTtcclxuICB9O1xyXG4gIGNvbnN0IHJlamVjdF9yb3dfZGVsZXRpb24gPSAoKSA9PiB7XHJcbiAgICB0b2FzdC5jdXJyZW50Py5zaG93KHsgc2V2ZXJpdHk6ICd3YXJuJywgc3VtbWFyeTogJ1JlamVjdGVkJywgZGV0YWlsOiAnWW91IGhhdmUgcmVqZWN0ZWQnLCBsaWZlOiAzMDAwIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNsaWNrID0gKGV2ZW50OiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxCdXR0b25FbGVtZW50PikgPT4ge1xyXG4gICAgc2V0QW5jaG9yRWwoZXZlbnQuY3VycmVudFRhcmdldCk7XHJcbiAgfTtcclxuICBjb25zb2xlLmxvZyhcIlVTRVJTIHt7e3t7e3t7e3t7e3t7e3t7e1wiLHVzZXJzX2RhdGEpXHJcbiAgY29uc3QgY29sdW1ucyA9IHVzZU1lbW88TVJUX0NvbHVtbkRlZjxUPltdPihcclxuICAgICgpID0+XHJcbiAgICAgIE9iamVjdC5lbnRyaWVzKGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzKS5maWx0ZXIoKFtrZXksIHZhbHVlXSwgaW5kZXgpID0+ICFbJ21vZGlmaWVkX2J5JywgJ2NyZWF0ZWRfYnknLCAnY3JlYXRlZCcsICdtb2RpZmllZCcsXS5pbmNsdWRlcyhrZXkpKS5tYXAoKFtrZXksIHZhbHVlXSwgaW5kZXgpID0+IHtcclxuICAgICAgICBpZiAoWydyZXBvcnQnLCAnY29udGVudCcsICdub3RlJywgJ29yZGVyJywgJ2NvbW1lbnQnLCAnZGVzY3JpcHRpb24nXS5pbmNsdWRlcyhrZXkpKSB7XHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICAvLyBDZWxsOiAoeyBjZWxsIH0pID0+IDxkaXY+e3BhcnNlKGNlbGwuZ2V0VmFsdWU8c3RyaW5nPigpKX08L2Rpdj4sXHJcbiAgICAgICAgICAgIC8vIENlbGw6ICh7IGNlbGwgfSkgPT4geyBpZiAoW1wiZGVzY3JpcHRpb25cIiwgXCJjb250ZW50XCIsXCJyZXBvcnRcIl0uaW5jbHVkZXMoa2V5KSkgcmV0dXJuIG51bGw7IGVsc2UgcmV0dXJuIDxkaXY+e3BhcnNlKGNlbGwuZ2V0VmFsdWU8c3RyaW5nPigpKX08L2Rpdj4gfSxcclxuICAgICAgICAgICAgRWRpdDogKHsgY2VsbCwgY29sdW1uLCByb3csIHRhYmxlIH0pID0+IHtcclxuICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT0nZm9udC1ib2xkJz5SYXBwb3J0PC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPEVkaXRvclxyXG4gICAgICAgICAgICAgICAgICAgIC8vIGluaXRpYWxWYWx1ZT17cm93Lm9yaWdpbmFsW2tleV19XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gdGlueW1jZVNjcmlwdFNyYz1cImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC90aW55bWNlL3RpbnltY2UubWluLmpzXCJcclxuICAgICAgICAgICAgICAgICAgICAvLyBhcGlLZXk9J25vbmUnXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Jvdy5vcmlnaW5hbC5yZXBvcnR9XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gb25DaGFuZ2U9eyhlKSA9PiB7IHJvdy5fdmFsdWVzQ2FjaGUucmVwb3J0ID0gZS50YXJnZXQuZ2V0Q29udGVudCgpIH19XHJcbiAgICAgICAgICAgICAgICAgICAgb25UZXh0Q2hhbmdlPXsoZSkgPT4geyByb3cuX3ZhbHVlc0NhY2hlLnJlcG9ydCA9IGUuaHRtbFZhbHVlIH19XHJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgaGVpZ2h0OiAnMzIwcHgnIH19XHJcbiAgICAgICAgICAgICAgICAgIC8vIGluaXQ9e3tcclxuICAgICAgICAgICAgICAgICAgLy8gICBsaWNlbnNlS2V5OidncGwnLFxyXG4gICAgICAgICAgICAgICAgICAvLyAgIGhlaWdodDogNTAwLFxyXG4gICAgICAgICAgICAgICAgICAvLyAgIG1lbnViYXI6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgcGx1Z2luczogW1xyXG4gICAgICAgICAgICAgICAgICAvLyAgICAgJ2Fkdmxpc3QnLCAnYXV0b2xpbmsnLCAnbGlzdHMnLCAnbGluaycsICdpbWFnZScsICdjaGFybWFwJywgJ3ByaW50JywgJ3ByZXZpZXcnLCAnYW5jaG9yJyxcclxuICAgICAgICAgICAgICAgICAgLy8gICAgICdzZWFyY2hyZXBsYWNlJywgJ3Zpc3VhbGJsb2NrcycsICdjb2RlJywgJ2Z1bGxzY3JlZW4nLFxyXG4gICAgICAgICAgICAgICAgICAvLyAgICAgJ2luc2VydGRhdGV0aW1lJywgJ21lZGlhJywgJ3RhYmxlJywgJ3Bhc3RlJywgJ2NvZGUnLCAnaGVscCcsICd3b3JkY291bnQnXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgXSxcclxuICAgICAgICAgICAgICAgICAgLy8gICB0b29sYmFyOlxyXG4gICAgICAgICAgICAgICAgICAvLyAgICAgJ3VuZG8gcmVkbyB8IGZvcm1hdHNlbGVjdCB8IGJvbGQgaXRhbGljIGJhY2tjb2xvciB8IFxcXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgICBhbGlnbmxlZnQgYWxpZ25jZW50ZXIgYWxpZ25yaWdodCBhbGlnbmp1c3RpZnkgfCBcXFxyXG4gICAgICAgICAgICAgICAgICAvLyAgICAgYnVsbGlzdCBudW1saXN0IG91dGRlbnQgaW5kZW50IHwgcmVtb3ZlZm9ybWF0IHwgaGVscCB8dmlzdWFsYmxvY2tzIGNvZGUgZnVsbHNjcmVlbidcclxuICAgICAgICAgICAgICAgICAgLy8gfX1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChrZXkgPT09IFwicGxhblwiKSB7XHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3BsYW4nLFxyXG5cclxuICAgICAgICAgICAgbXVpVGFibGVIZWFkQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdsZWZ0JyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgbXVpVGFibGVCb2R5Q2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdsZWZ0JyxcclxuICAgICAgICAgICAgfSxcclxuXHJcbiAgICAgICAgICAgIG11aVRhYmxlRm9vdGVyQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsLCByb3cgfSkgPT4gPFRhZyBjbGFzc05hbWU9J3ctMTFyZW0gdGV4dC1zbSc+e2NlbGwuZ2V0VmFsdWU8UGxhbj4oKS5jb2RlfTwvVGFnPixcclxuICAgICAgICAgICAgRWRpdDogKHsgcm93IH0pID0+IChcclxuICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT0nZm9udC1ib2xkJz5QbGFuPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgIDxEcm9wZG93blxyXG4gICAgICAgICAgICAgICAgICBvcHRpb25MYWJlbD1cIm5hbWVcIlxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkNob2lzaXIgdW4gcGxhblwiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpO1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFJvd1RvYmUoeyAuLi5yb3dUb2JlLCBwbGFuOiBwbGFucz8uZGF0YS5yZXN1bHRzLmZpbmQoKHBsYW46IFBsYW4pID0+IHBsYW4uaWQgPT09IGUudmFsdWUuY29kZSkgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgcm93Ll92YWx1ZXNDYWNoZSA9IHsgLi4ucm93Ll92YWx1ZXNDYWNoZSwgcGxhbjogcGxhbnM/LmRhdGEucmVzdWx0cy5maW5kKChwbGFuOiBQbGFuKSA9PiBwbGFuLmlkID09PSBlLnZhbHVlLmNvZGUpIH1cclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3sgY29kZTogcm93Ll92YWx1ZXNDYWNoZS5wbGFuPy5pZCB8fCBudWxsLCBuYW1lOiAgYCR7dHJhbnNmb3JtZXJQbGFuTGFiZWwocm93Ll92YWx1ZXNDYWNoZS5wbGFuPy50eXBlKX18JHtyb3cuX3ZhbHVlc0NhY2hlLnBsYW4/LmV4ZXJjaXNlfWAgfHwgbnVsbCB9fVxyXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtwbGFucz8uZGF0YT8ucmVzdWx0cy5tYXAoKHBsYW46IFBsYW4pID0+IHsgcmV0dXJuIHsgY29kZTogcGxhbi5pZCwgbmFtZTogYCR7dHJhbnNmb3JtZXJQbGFuTGFiZWwocGxhbi50eXBlKX18JHtwbGFuLmV4ZXJjaXNlfWAgfSB9KX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDwvRHJvcGRvd24+XHJcbiAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0uZm9ybWF0ID09PSAnZGF0ZS10aW1lJyB8fCBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLmZvcm1hdCA9PT0gJ2RhdGUnKSB7XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JGbjogKHJvdykgPT4gbmV3IERhdGUoa2V5ID09IFwiY3JlYXRlZFwiID8gcm93LmNyZWF0ZWQgOiByb3cubW9kaWZpZWQpLFxyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBmaWx0ZXJWYXJpYW50OiAnZGF0ZScsXHJcbiAgICAgICAgICAgIGZpbHRlckZuOiAnbGVzc1RoYW4nLFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46ICdkYXRldGltZScsXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwgfSkgPT4gY2VsbC5nZXRWYWx1ZTxEYXRlPigpPy50b0xvY2FsZURhdGVTdHJpbmcoJ2ZyJyksXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIEVkaXQ6ICgpID0+IG51bGwsXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAoa2V5ID09PSBcImlkXCIpIHJldHVybiB7XHJcbiAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICBFZGl0OiAoKSA9PiBudWxsLFxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoa2V5ID09PSBcInRlYW1cIikgcmV0dXJuIHtcclxuICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgIENlbGw6ICh7IGNlbGwsIHJvdyB9KSA9PiA8dWw+e2NlbGwuZ2V0VmFsdWU8VXNlcltdPigpLm1hcCgodXNyKSA9PiA8bGk+e2dldFVzZXJGdWxsbmFtZSh1c3IpfTwvbGk+KX08L3VsPixcclxuICAgICAgICAgIEVkaXQ6ICh7IHJvdyB9KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbQVJCSVRSQVRJT05dJywgcm93Ll92YWx1ZXNDYWNoZS50ZWFtKVxyXG4gICAgICAgICAgICByZXR1cm4gKDw+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT0nZm9udC1ib2xkJz5NZW1icmVzPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8UGlja0xpc3RcclxuICAgICAgICAgICAgICAgIHNvdXJjZT17cGlja2xpc3RUYXJnZXRWYWx1ZVRlYW0ubGVuZ3RoID09PSAwID8gcGlja2xpc3RTb3VyY2VWYWx1ZVRlYW0gOiBwaWNrbGlzdFNvdXJjZVZhbHVlVGVhbS5maWx0ZXIodXNlciA9PiBwaWNrbGlzdFRhcmdldFZhbHVlVGVhbS5tYXAodXNlciA9PiB1c2VyLnVzZXJuYW1lKS5pbmNsdWRlcyh1c2VyLnVzZXJuYW1lKSl9XHJcbiAgICAgICAgICAgICAgICBpZD0ncGlja2xpc3RfdGVhbSdcclxuICAgICAgICAgICAgICAgIHRhcmdldD17cGlja2xpc3RUYXJnZXRWYWx1ZVRlYW0ubGVuZ3RoID4gMCA/IHBpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtIDogcm93Ll92YWx1ZXNDYWNoZS50ZWFtfVxyXG4gICAgICAgICAgICAgICAgc291cmNlSGVhZGVyPVwiRGVcIlxyXG4gICAgICAgICAgICAgICAgdGFyZ2V0SGVhZGVyPVwiQVwiXHJcbiAgICAgICAgICAgICAgICBpdGVtVGVtcGxhdGU9eyhpdGVtKSA9PiA8ZGl2IGtleT17aXRlbS51c2VybmFtZX0+e2l0ZW0uZmlyc3ROYW1lfSB7aXRlbS5sYXN0TmFtZX08L2Rpdj59XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ3NvdXJjZSBUZWFtJywgZS5zb3VyY2UpXHJcbiAgICAgICAgICAgICAgICAgIHNldFBpY2tsaXN0U291cmNlVmFsdWVUZWFtKFsuLi5lLnNvdXJjZV0pXHJcbiAgICAgICAgICAgICAgICAgIHNldFBpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtKFsuLi5lLnRhcmdldF0pXHJcbiAgICAgICAgICAgICAgICAgIHJvdy5fdmFsdWVzQ2FjaGUudGVhbSA9IGUudGFyZ2V0XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgc291cmNlU3R5bGU9e3sgaGVpZ2h0OiAnMjAwcHgnIH19XHJcbiAgICAgICAgICAgICAgICB0YXJnZXRTdHlsZT17eyBoZWlnaHQ6ICcyMDBweCcgfX1cclxuICAgICAgICAgICAgICAgIGZpbHRlciBmaWx0ZXJCeT0ndXNlcm5hbWUsZW1haWwsZmlyc3ROYW1lLGxhc3ROYW1lJ1xyXG4gICAgICAgICAgICAgICAgZmlsdGVyTWF0Y2hNb2RlPSdjb250YWlucydcclxuICAgICAgICAgICAgICAgIHNvdXJjZUZpbHRlclBsYWNlaG9sZGVyPVwiUmVjaGVyY2hlciBwYXIgbm9tICYgcHLDqW5vbVwiIHRhcmdldEZpbHRlclBsYWNlaG9sZGVyPVwiUmVjaGVyY2hlciBwYXIgbm9tICYgcHLDqW5vbVwiXHJcbiAgICAgICAgICAgICAgPlxyXG5cclxuICAgICAgICAgICAgICA8L1BpY2tMaXN0PlxyXG4gICAgICAgICAgICA8Lz4pXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgICAgLy8gRWRpdDogKCkgPT4gbnVsbCxcclxuICAgICAgICAgIH07XHJcblxyXG4gICAgICAgIH1cclxuICAgICAgfSlcclxuICAgICxcclxuICAgIFt1c2Vyc19pc0xvYWRpbmcsIHBsYW5zPy5kYXRhLCBhcmJpdHJhdGlvbnM/LmRhdGFdLFxyXG4gICk7XHJcbiAgY29uc3QgdGFibGUgPSB1c2VNYXRlcmlhbFJlYWN0VGFibGUoe1xyXG4gICAgY29sdW1ucyxcclxuICAgIGRhdGE6IGRhdGFfLmVycm9yID8gW10gOiBkYXRhXy5kYXRhXy5kYXRhID8gZGF0YV8uZGF0YV8uZGF0YSA6IFtkYXRhXy5kYXRhXy5kYXRhXSwgLy9tdXN0IGJlIG1lbW9pemVkIG9yIHN0YWJsZSAodXNlU3RhdGUsIHVzZU1lbW8sIGRlZmluZWQgb3V0c2lkZSBvZiB0aGlzIGNvbXBvbmVudCwgZXRjLilcclxuICAgIHJvd0NvdW50OiBkYXRhXy5lcnJvciA/IDAgOiBkYXRhXy5kYXRhXy5kYXRhID8gZGF0YV8uZGF0YV8uZGF0YS5sZW5ndGggOiAxLFxyXG4gICAgZW5hYmxlUm93U2VsZWN0aW9uOiB0cnVlLCAvL2VuYWJsZSBzb21lIGZlYXR1cmVzXHJcbiAgICBlbmFibGVDb2x1bW5PcmRlcmluZzogdHJ1ZSwgLy9lbmFibGUgYSBmZWF0dXJlIGZvciBhbGwgY29sdW1uc1xyXG4gICAgZW5hYmxlR2xvYmFsRmlsdGVyOiB0cnVlLCAvL3R1cm4gb2ZmIGEgZmVhdHVyZVxyXG4gICAgZW5hYmxlR3JvdXBpbmc6IHRydWUsXHJcbiAgICBlbmFibGVSb3dBY3Rpb25zOiB0cnVlLFxyXG4gICAgZW5hYmxlUm93UGlubmluZzogdHJ1ZSxcclxuICAgIGVuYWJsZVN0aWNreUhlYWRlcjogdHJ1ZSxcclxuICAgIGVuYWJsZVN0aWNreUZvb3RlcjogdHJ1ZSxcclxuICAgIGVuYWJsZUNvbHVtblBpbm5pbmc6IHRydWUsXHJcbiAgICBlbmFibGVDb2x1bW5SZXNpemluZzogdHJ1ZSxcclxuICAgIGVuYWJsZVJvd051bWJlcnM6IHRydWUsXHJcbiAgICBlbmFibGVFeHBhbmRBbGw6IHRydWUsXHJcbiAgICBlbmFibGVFZGl0aW5nOiB0cnVlLFxyXG4gICAgZW5hYmxlRXhwYW5kaW5nOiB0cnVlLFxyXG4gICAgbWFudWFsUGFnaW5hdGlvbjogdHJ1ZSxcclxuICAgIGluaXRpYWxTdGF0ZToge1xyXG4gICAgICBwYWdpbmF0aW9uOiB7IHBhZ2VTaXplOiA1LCBwYWdlSW5kZXg6IDEgfSxcclxuICAgICAgY29sdW1uVmlzaWJpbGl0eTogeyByZXBvcnQ6IGZhbHNlLCBjcmVhdGVkX2J5OiBmYWxzZSwgY3JlYXRlZDogZmFsc2UsIG1vZGZpZWRfYnk6IGZhbHNlLCBtb2RpZmllZDogZmFsc2UsIG1vZGlmaWVkX2J5OiBmYWxzZSwgc3RhZmY6IGZhbHNlLCBhc3Npc3RhbnRzOiBmYWxzZSwgaWQ6IGZhbHNlLCBkb2N1bWVudDogZmFsc2UgfSxcclxuICAgICAgZGVuc2l0eTogJ2NvbXBhY3QnLFxyXG4gICAgICBzaG93R2xvYmFsRmlsdGVyOiB0cnVlLFxyXG4gICAgICBzb3J0aW5nOiBbeyBpZDogJ2lkJywgZGVzYzogZmFsc2UgfV0sXHJcbiAgICB9LFxyXG4gICAgc3RhdGU6IHtcclxuICAgICAgcGFnaW5hdGlvbjogZGF0YV8ucGFnaW5hdGlvbi5wYWdpLFxyXG4gICAgICBpc0xvYWRpbmc6IGRhdGFfLmlzTG9hZGluZywgLy9jZWxsIHNrZWxldG9ucyBhbmQgbG9hZGluZyBvdmVybGF5XHJcbiAgICAgIC8vc2hvd1Byb2dyZXNzQmFyczogaXNMb2FkaW5nLCAvL3Byb2dyZXNzIGJhcnMgd2hpbGUgcmVmZXRjaGluZ1xyXG4gICAgICAvLyBpc1NhdmluZzogaXNTYXZpbmdUb2RvcywgLy9wcm9ncmVzcyBiYXJzIGFuZCBzYXZlIGJ1dHRvbiBzcGlubmVyc1xyXG4gICAgfSxcclxuICAgIGxvY2FsaXphdGlvbjogTVJUX0xvY2FsaXphdGlvbl9GUixcclxuICAgIG9uUGFnaW5hdGlvbkNoYW5nZTogb25QYWdpbmF0aW9uQ2hhbmdlLFxyXG4gICAgZGlzcGxheUNvbHVtbkRlZk9wdGlvbnM6IHtcclxuICAgICAgJ21ydC1yb3ctcGluJzoge1xyXG4gICAgICAgIGVuYWJsZUhpZGluZzogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAgJ21ydC1yb3ctZXhwYW5kJzoge1xyXG4gICAgICAgIGVuYWJsZUhpZGluZzogdHJ1ZSxcclxuICAgICAgfSxcclxuICAgICAgJ21ydC1yb3ctYWN0aW9ucyc6IHtcclxuICAgICAgICAvLyBoZWFkZXI6ICdFZGl0JywgLy9jaGFuZ2UgXCJBY3Rpb25zXCIgdG8gXCJFZGl0XCJcclxuICAgICAgICBzaXplOiAxMDAsXHJcbiAgICAgICAgZW5hYmxlSGlkaW5nOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICAnbXJ0LXJvdy1udW1iZXJzJzoge1xyXG4gICAgICAgIGVuYWJsZUhpZGluZzogdHJ1ZSwgLy9ub3cgcm93IG51bWJlcnMgYXJlIGhpZGFibGUgdG9vXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgZGVmYXVsdENvbHVtbjoge1xyXG4gICAgICBncm93OiB0cnVlLFxyXG4gICAgICBlbmFibGVNdWx0aVNvcnQ6IHRydWUsXHJcbiAgICB9LFxyXG4gICAgbXVpVGFibGVQYXBlclByb3BzOiAoeyB0YWJsZSB9KSA9PiAoe1xyXG4gICAgICAvL2VsZXZhdGlvbjogMCwgLy9jaGFuZ2UgdGhlIG11aSBib3ggc2hhZG93XHJcbiAgICAgIC8vY3VzdG9taXplIHBhcGVyIHN0eWxlc1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgY2xhc3NlczogeyByb290OiAncC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGwnIH0sXHJcblxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGhlaWdodDogYGNhbGMoMTAwdmggLSA5cmVtKWAsXHJcbiAgICAgICAgLy8gaGVpZ2h0OiBgY2FsYygxMDB2aCAtIDIwMHB4KWAsXHJcbiAgICAgICAgLy8gYm9yZGVyUmFkaXVzOiAnMCcsXHJcbiAgICAgICAgLy8gYm9yZGVyOiAnMXB4IGRhc2hlZCAjZTBlMGUwJyxcclxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwidmFyKC0tc3VyZmFjZS1jYXJkKVwiLFxyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICBcIiYgLk11aVRhYmxlUGFnaW5hdGlvbi1yb290IFwiOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwidmFyKC0tc3VyZmFjZS1jYXJkKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBcIiYgLk11aUJveC1yb290IFwiOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwidmFyKC0tc3VyZmFjZS1jYXJkKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgIH0pLFxyXG4gICAgbXVpRWRpdFJvd0RpYWxvZ1Byb3BzOiAoeyByb3csIHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIG9wZW46IGVkaXRWaXNpYmxlIHx8IGNyZWF0ZVZpc2libGUsXHJcbiAgICAgIHN4OiB7XHJcbiAgICAgICAgJyYgLk11aURpYWxvZy1yb290Jzoge1xyXG4gICAgICAgICAgZGlzcGxheTogJ25vbmUnXHJcbiAgICAgICAgfSxcclxuICAgICAgICAnJiAuTXVpRGlhbG9nLWNvbnRhaW5lcic6IHtcclxuICAgICAgICAgIGRpc3BsYXk6ICdub25lJ1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgekluZGV4OiAxMTAwXHJcblxyXG4gICAgICB9XHJcbiAgICB9KSxcclxuICAgIGVkaXREaXNwbGF5TW9kZTogJ21vZGFsJyxcclxuICAgIGNyZWF0ZURpc3BsYXlNb2RlOiAnbW9kYWwnLFxyXG4gICAgb25FZGl0aW5nUm93U2F2ZTogKHsgdGFibGUsIHZhbHVlcywgcm93IH0pID0+IHtcclxuICAgICAgY29uc29sZS5sb2coXCJvbkVkaXRpbmdSb3dTYXZlXCIsIHZhbHVlcylcclxuICAgICAgY29uc3QgeyBpZCwgLi4ucmVzdCB9ID0gdmFsdWVzXHJcbiAgICAgIHJlc3QucGxhbiA9IHZhbHVlcy5wbGFuLmlkXHJcbiAgICAgIHJlc3QudGVhbSA9IHZhbHVlcy50ZWFtPy5tYXAodXNlciA9PiB1c2VyLmlkKSB8fCBbXVxyXG4gICAgICBhcmJpdHJhdGlvbl9wYXRjaF90cmlnZ2VyKHJlc3QsIHtcclxuICAgICAgICByZXZhbGlkYXRlOiB0cnVlLFxyXG4gICAgICAgIG9uU3VjY2VzczogKCkgPT4ge1xyXG4gICAgICAgICAgdGFibGUuc2V0RWRpdGluZ1JvdyhudWxsKTsgLy9leGl0IGNyZWF0aW5nIG1vZGVcclxuICAgICAgICAgIHRvYXN0LmN1cnJlbnQhLnNob3coeyBzZXZlcml0eTogJ3N1Y2Nlc3MnLCBzdW1tYXJ5OiAnSW5mbycsIGRldGFpbDogYEFyYml0cmFnZSAke3ZhbHVlcy5wbGFuLmNvZGV9IG1pcyDDoCBham91cmAgfSk7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcclxuICAgICAgICAgIHRvYXN0LmN1cnJlbnQhLnNob3coeyBzZXZlcml0eTogJ2Vycm9yJywgc3VtbWFyeTogJ0luZm8nLCBkZXRhaWw6IGAke2Vycm9yLnJlc3BvbnNlPy5zdGF0dXNUZXh0fWAgfSk7XHJcbiAgICAgICAgICAvL2NvbnNvbGUubG9nKFwib25FZGl0aW5nUm93U2F2ZVwiLCBlcnJvci5yZXNwb25zZSk7XHJcbiAgICAgICAgICByb3cuX3ZhbHVlc0NhY2hlID0geyBlcnJvcjogZXJyb3IucmVzcG9uc2UsIC4uLnJvdy5fdmFsdWVzQ2FjaGUgfTtcclxuICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgIH0pXHJcbiAgICB9LFxyXG4gICAgb25FZGl0aW5nUm93Q2FuY2VsOiAoKSA9PiB7XHJcbiAgICAgIC8vY2xlYXIgYW55IHZhbGlkYXRpb24gZXJyb3JzXHJcbiAgICAgIHRvYXN0LmN1cnJlbnQ/LnNob3coeyBzZXZlcml0eTogJ2luZm8nLCBzdW1tYXJ5OiAnSW5mbycsIGRldGFpbDogJ0FubnVsYXRpb24nIH0pO1xyXG4gICAgfSxcclxuICAgIG9uQ3JlYXRpbmdSb3dTYXZlOiAoeyB0YWJsZSwgdmFsdWVzLCByb3cgfSkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIm9uQ3JlYXRpbmdSb3dTYXZlXCIsIHZhbHVlcylcclxuICAgICAgY29uc3QgeyBpZCwgLi4ucmVzdCB9ID0gdmFsdWVzXHJcbiAgICAgIHJlc3QucGxhbiA9IHZhbHVlcy5wbGFuLmlkXHJcbiAgICAgIHJlc3QudGVhbSA9IHZhbHVlcy50ZWFtID8gdmFsdWVzLnRlYW0/Lm1hcCh1c2VyID0+IHVzZXIuaWQpIDogW11cclxuICAgICAgYXJiaXRyYXRpb25fY3JlYXRlX3RyaWdnZXIocmVzdCwge1xyXG4gICAgICAgIG9uU3VjY2VzczogKCkgPT4ge1xyXG4gICAgICAgICAgdGFibGUuc2V0Q3JlYXRpbmdSb3cobnVsbCk7IC8vZXhpdCBjcmVhdGluZyBtb2RlXHJcbiAgICAgICAgICB0b2FzdC5jdXJyZW50IS5zaG93KHsgc2V2ZXJpdHk6ICdzdWNjZXNzJywgc3VtbWFyeTogJ0luZm8nLCBkZXRhaWw6IGBBcmJpdHJhZ2UgJHt2YWx1ZXMucGxhbi5jb2RlfSBjcsOpw6lgIH0pO1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICB0b2FzdC5jdXJyZW50IS5zaG93KHsgc2V2ZXJpdHk6ICdlcnJvcicsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiBgJHtlcnJvci5yZXNwb25zZT8uc3RhdHVzVGV4dH1gIH0pO1xyXG4gICAgICAgICAgLy9jb25zb2xlLmxvZyhcIm9uRWRpdGluZ1Jvd1NhdmVcIiwgZXJyb3IucmVzcG9uc2UpO1xyXG4gICAgICAgICAgcm93Ll92YWx1ZXNDYWNoZSA9IHsgZXJyb3I6IGVycm9yLnJlc3BvbnNlLCAuLi5yb3cuX3ZhbHVlc0NhY2hlIH07XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICB9KVxyXG4gICAgfSxcclxuICAgIG9uQ3JlYXRpbmdSb3dDYW5jZWw6ICgpID0+IHtcclxuICAgICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnaW5mbycsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiAnQW5udWxhdGlvbicgfSk7XHJcbiAgICB9LFxyXG4gICAgbXVpVGFibGVGb290ZXJQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBcIiYgLk11aVRhYmxlRm9vdGVyLXJvb3RcIjoge1xyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIG11aVRhYmxlQ29udGFpbmVyUHJvcHM6ICh7IHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgaGVpZ2h0OiB0YWJsZS5nZXRTdGF0ZSgpLmlzRnVsbFNjcmVlbiA/IGBjYWxjKDEwMHZoKWAgOiBgY2FsYygxMDB2aCAtIDlyZW0gLSAke3RhYmxlLnJlZnMudG9wVG9vbGJhclJlZi5jdXJyZW50Py5vZmZzZXRIZWlnaHR9cHggLSAke3RhYmxlLnJlZnMuYm90dG9tVG9vbGJhclJlZi5jdXJyZW50Py5vZmZzZXRIZWlnaHR9cHgpYFxyXG5cclxuICAgICAgfSxcclxuICAgIH0pLFxyXG4gICAgbXVpUGFnaW5hdGlvblByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG5cclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICBjb2xvcjogXCJ2YXIoLS1zdXJmYWNlLTkwMCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZUhlYWRDZWxsUHJvcHM6IHtcclxuICAgICAgc3g6IHtcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgbXVpVG9wVG9vbGJhclByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIlxyXG4gICAgICB9LFxyXG5cclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZUJvZHlQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgICAgLy9zdHJpcGUgdGhlIHJvd3MsIG1ha2Ugb2RkIHJvd3MgYSBkYXJrZXIgY29sb3JcclxuICAgICAgICAnJiB0cjpudGgtb2YtdHlwZShvZGQpID4gdGQnOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1zdXJmYWNlLWNhcmQpJyxcclxuICAgICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgICcmIHRyOm50aC1vZi10eXBlKGV2ZW4pID4gdGQnOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1zdXJmYWNlLWJvcmRlciknLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgcmVuZGVyVG9wVG9vbGJhckN1c3RvbUFjdGlvbnM6ICh7IHRhYmxlIH0pID0+IChcclxuICAgICAgPFN0YWNrIGRpcmVjdGlvbj17XCJyb3dcIn0gc3BhY2luZz17MX0+XHJcbiAgICAgICAgPENhbiBJPVwiYWRkXCIgYT0nYXJiaXRyYXRpb24nPlxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICBpY29uPVwicGkgcGktcGx1c1wiXHJcbiAgICAgICAgICAgIHJvdW5kZWRcclxuICAgICAgICAgICAgLy8gaWQ9XCJiYXNpYy1idXR0b25cIlxyXG4gICAgICAgICAgICBhcmlhLWNvbnRyb2xzPXtvcGVuID8gJ2Jhc2ljLW1lbnUnIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICBhcmlhLWhhc3BvcHVwPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgIGFyaWEtZXhwYW5kZWQ9e29wZW4gPyAndHJ1ZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eyhldmVudCkgPT4ge1xyXG4gICAgICAgICAgICAgIHRhYmxlLnNldENyZWF0aW5nUm93KHRydWUpOyBzZXRDcmVhdGVWaXNpYmxlKHRydWUpLCBjb25zb2xlLmxvZyhcImNyZWF0aW5nIHJvdyAuLi5cIik7XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICA8L0Nhbj5cclxuICAgICAgICA8Q2FuIEk9XCJkZWxldGVcIiBhPSdhcmJpdHJhdGlvbic+XHJcbiAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgIHJvdW5kZWRcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3RhYmxlLmdldElzU29tZVJvd3NTZWxlY3RlZCgpfVxyXG4gICAgICAgICAgICAvLyBpZD1cImJhc2ljLWJ1dHRvblwiXHJcbiAgICAgICAgICAgIGFyaWEtY29udHJvbHM9e29wZW4gPyAnYmFzaWMtbWVudScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgIGFyaWEtaGFzcG9wdXA9XCJ0cnVlXCJcclxuICAgICAgICAgICAgYXJpYS1leHBhbmRlZD17b3BlbiA/ICd0cnVlJyA6IHVuZGVmaW5lZH1cclxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2xpY2t9XHJcbiAgICAgICAgICAgIGljb249XCJwaSBwaS10cmFzaFwiXHJcbiAgICAgICAgICAgIC8vIHN0eWxlPXt7ICBib3JkZXJSYWRpdXM6ICcwJywgYm94U2hhZG93OiAnbm9uZScsIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JyB9fVxyXG4gICAgICAgICAgICBzaXplPVwic21hbGxcIlxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0Nhbj5cclxuICAgICAgPC9TdGFjaz5cclxuICAgICksXHJcbiAgICBtdWlEZXRhaWxQYW5lbFByb3BzOiAoKSA9PiAoe1xyXG4gICAgICBzeDogKHRoZW1lKSA9PiAoe1xyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjpcclxuICAgICAgICAgIHRoZW1lLnBhbGV0dGUubW9kZSA9PT0gJ2RhcmsnXHJcbiAgICAgICAgICAgID8gJ3JnYmEoMjU1LDIxMCwyNDQsMC4xKSdcclxuICAgICAgICAgICAgOiAncmdiYSgwLDAsMCwwLjEpJyxcclxuICAgICAgfSksXHJcbiAgICB9KSxcclxuICAgIHJlbmRlckNyZWF0ZVJvd0RpYWxvZ0NvbnRlbnQ6ICh7IGludGVybmFsRWRpdENvbXBvbmVudHMsIHJvdywgdGFibGUgfSkgPT5cclxuICAgICAgPGRpdiBzdHlsZT17eyB6SW5kZXg6ICcxMzAyICFpbXBvcnRhbnQnIH19PlxyXG4gICAgICAgIDxTaWRlYmFyIHBvc2l0aW9uPSdyaWdodCdcclxuICAgICAgICAgIGhlYWRlcj17XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtcm93IHctZnVsbCBmbGV4LXdyYXAganVzdGlmeS1jb250ZW50LWJldHdlZW4nPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nYWxpZ24tY29udGVudC1jZW50ZXIgJz5DcsOpYXRpb24gbm91dmVhdSBhcmJpdHJhZ2U8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPERpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgICAgICA8TVJUX0VkaXRBY3Rpb25CdXR0b25zIHZhcmlhbnQ9XCJ0ZXh0XCIgdGFibGU9e3RhYmxlfSByb3c9e3Jvd30gLz5cclxuICAgICAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdmlzaWJsZT17Y3JlYXRlVmlzaWJsZX1cclxuICAgICAgICAgIG9uSGlkZT17KCkgPT4geyB0YWJsZS5zZXRDcmVhdGluZ1JvdyhudWxsKTsgc2V0Q3JlYXRlVmlzaWJsZShmYWxzZSkgfX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTkgbGc6dy04XCI+XHJcbiAgICAgICAgICA8RGlhbG9nQ29udGVudFxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcclxuICAgICAgICAgICAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcclxuICAgICAgICAgICAgICBnYXA6ICcxLjVyZW0nXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHtpbnRlcm5hbEVkaXRDb21wb25lbnRzfVxyXG4gICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICAgIDwvU2lkZWJhcj5cclxuICAgICAgPC9kaXY+LFxyXG4gICAgcmVuZGVyRWRpdFJvd0RpYWxvZ0NvbnRlbnQ6ICh7IGludGVybmFsRWRpdENvbXBvbmVudHMsIHJvdywgdGFibGUgfSkgPT5cclxuICAgICAgPGRpdiBzdHlsZT17eyB6SW5kZXg6ICcxMzAyICFpbXBvcnRhbnQnIH19PlxyXG4gICAgICAgIDxTaWRlYmFyIHBvc2l0aW9uPSdyaWdodCdcclxuICAgICAgICAgIGhlYWRlcj17XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtcm93IHctZnVsbCBmbGV4LXdyYXAganVzdGlmeS1jb250ZW50LWJldHdlZW4nPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J2FsaWduLWNvbnRlbnQtY2VudGVyICc+RWRpdGVyIGwnYXJiaXRyYWdlIG7CsCB7cm93Lm9yaWdpbmFsLmlkfTwvaDM+XHJcbiAgICAgICAgICAgICAgPERpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgICAgICA8TVJUX0VkaXRBY3Rpb25CdXR0b25zIHZhcmlhbnQ9XCJ0ZXh0XCIgdGFibGU9e3RhYmxlfSByb3c9e3Jvd30gLz5cclxuICAgICAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdmlzaWJsZT17ZWRpdFZpc2libGV9XHJcbiAgICAgICAgICBvbkhpZGU9eygpID0+IHtcclxuICAgICAgICAgICAgdGFibGUuc2V0RWRpdGluZ1JvdyhudWxsKTtcclxuICAgICAgICAgICAgc2V0RWRpdFZpc2libGUoZmFsc2UpXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctOSBsZzp3LThcIj5cclxuICAgICAgICAgIDxEaWFsb2dDb250ZW50XHJcbiAgICAgICAgICAgIHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsIGdhcDogJzEuNXJlbScgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAge2ludGVybmFsRWRpdENvbXBvbmVudHN9IHsvKiBvciByZW5kZXIgY3VzdG9tIGVkaXQgY29tcG9uZW50cyBoZXJlICovfVxyXG4gICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICAgIDwvU2lkZWJhcj5cclxuICAgICAgPC9kaXY+LFxyXG4gICAgcmVuZGVyRGV0YWlsUGFuZWw6ICh7IHJvdyB9KSA9PlxyXG4gICAgICBwYXJzZShyb3cub3JpZ2luYWwucmVwb3J0KSxcclxuICAgIHJlbmRlclJvd0FjdGlvbnM6ICh7IGNlbGwsIHJvdywgdGFibGUgfSkgPT4gKFxyXG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJwLWJ1dHRvbnNldCBmbGV4IHAtMVwiPlxyXG4gICAgICAgIDxDYW4gST1cInVwZGF0ZVwiIGE9J2FyYml0cmF0aW9uJz5cclxuICAgICAgICAgIDxCdXR0b24gc2l6ZT0nc21hbGwnIGljb249XCJwaSBwaS1wZW5jaWxcIiBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIHNldEFyYml0cmF0aW9uSUQocm93Lm9yaWdpbmFsLmlkKTtcclxuICAgICAgICAgICAgdGFibGUuc2V0RWRpdGluZ1Jvdyhyb3cpO1xyXG4gICAgICAgICAgICBzZXRFZGl0VmlzaWJsZSh0cnVlKTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJlZGl0aW5nIHJvdyAuLi5cIik7XHJcbiAgICAgICAgICB9fSByb3VuZGVkIG91dGxpbmVkIC8+XHJcbiAgICAgICAgPC9DYW4+XHJcbiAgICAgICAgPENhbiBJPVwiZGVsZXRlXCIgYT0nYXJiaXRyYXRpb24nPlxyXG4gICAgICAgICAgPEJ1dHRvbiBzaXplPSdzbWFsbCcgaWNvbj1cInBpIHBpLXRyYXNoXCIgcm91bmRlZCBvdXRsaW5lZFxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoZXZlbnQpID0+IGNvbmZpcm1Qb3B1cCh7XHJcbiAgICAgICAgICAgICAgdGFyZ2V0OiBldmVudC5jdXJyZW50VGFyZ2V0LFxyXG4gICAgICAgICAgICAgIG1lc3NhZ2U6ICdWb3VsZXotdm91cyBzdXBwcmltZXIgY2V0dGUgbGlnbmU/JyxcclxuICAgICAgICAgICAgICBpY29uOiAncGkgcGktaW5mby1jaXJjbGUnLFxyXG4gICAgICAgICAgICAgIC8vIGRlZmF1bHRGb2N1czogJ3JlamVjdCcsXHJcbiAgICAgICAgICAgICAgYWNjZXB0Q2xhc3NOYW1lOiAncC1idXR0b24tZGFuZ2VyJyxcclxuICAgICAgICAgICAgICBhY2NlcHRMYWJlbDogJ091aScsXHJcbiAgICAgICAgICAgICAgcmVqZWN0TGFiZWw6ICdOb24nLFxyXG4gICAgICAgICAgICAgIGFjY2VwdDogYWNjZXB0X3Jvd19kZWxldGlvbixcclxuICAgICAgICAgICAgICByZWplY3Q6IHJlamVjdF9yb3dfZGVsZXRpb25cclxuICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvQ2FuPlxyXG4gICAgICAgIDxDb25maXJtUG9wdXAgLz5cclxuICAgICAgPC9zcGFuPlxyXG4gICAgKSxcclxuICB9KTtcclxuICByZXR1cm4gPD48TWF0ZXJpYWxSZWFjdFRhYmxlIHRhYmxlPXt0YWJsZX0gLz48VG9hc3QgcmVmPXt0b2FzdH0gLz48Lz47XHJcbn1cclxuIl0sIm5hbWVzIjpbIkRpYWxvZ0FjdGlvbnMiLCJEaWFsb2dDb250ZW50IiwiU3RhY2siLCJNUlRfRWRpdEFjdGlvbkJ1dHRvbnMiLCJNYXRlcmlhbFJlYWN0VGFibGUiLCJ1c2VNYXRlcmlhbFJlYWN0VGFibGUiLCJNUlRfTG9jYWxpemF0aW9uX0ZSIiwiQnV0dG9uIiwiQ29uZmlybVBvcHVwIiwiY29uZmlybVBvcHVwIiwiU2lkZWJhciIsIlRhZyIsIlRvYXN0IiwidXNlTWVtbyIsInVzZVJlZiIsInVzZVN0YXRlIiwicGFyc2UiLCJQaWNrTGlzdCIsIkRyb3Bkb3duIiwiZ2V0Q29va2llIiwiZ2V0VXNlckZ1bGxuYW1lIiwiRWRpdG9yIiwiQ2FuIiwidXNlQXBpQXJiaXRyYXRpb25DcmVhdGUiLCJ1c2VBcGlBcmJpdHJhdGlvbkxpc3QiLCJ1c2VBcGlBcmJpdHJhdGlvblBhcnRpYWxVcGRhdGUiLCJ1c2VBcGlQbGFuTGlzdCIsInVzZUFwaVVzZXJMaXN0IiwidHJhbnNmb3JtZXJQbGFuTGFiZWwiLCJHZW5lcmljVGFibGUiLCJkYXRhXyIsInBsYW5zIiwiYXJiaXRyYXRpb25zIiwidXNlciIsIkpTT04iLCJ0b1N0cmluZyIsInRvYXN0IiwiZGF0YSIsImlzTG9hZGluZyIsInBsYW5zX2lzTG9hZGluZyIsImVycm9yIiwicGxhbnNfZXJyb3IiLCJhcmJpdHJhdGlvbnNfaXNMb2FkaW5nIiwiYXJiaXRyYXRpb25zX2Vycm9yIiwidXNlcnMiLCJ1c2Vyc19pc0xvYWRpbmciLCJ1c2Vyc19lcnJvciIsInVzZXJzX2RhdGEiLCJyZXN1bHRzIiwiYXJiaXRyYXRpb25JRCIsInNldEFyYml0cmF0aW9uSUQiLCJlZGl0VmlzaWJsZSIsInNldEVkaXRWaXNpYmxlIiwiY3JlYXRlVmlzaWJsZSIsInNldENyZWF0ZVZpc2libGUiLCJwaWNrbGlzdFRhcmdldFZhbHVlVGVhbSIsInNldFBpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtIiwicGlja2xpc3RTb3VyY2VWYWx1ZVRlYW0iLCJzZXRQaWNrbGlzdFNvdXJjZVZhbHVlVGVhbSIsInJvd1RvYmUiLCJzZXRSb3dUb2JlIiwibXV0YXRlIiwiYXJiaXRyYXRpb25fY3JlYXRlX3RyaWdnZXIiLCJpc1BlbmRpbmciLCJpc0NyZWF0ZU11dGF0aW5nIiwiYXJiaXRyYXRpb25fcGF0Y2hfdHJpZ2dlciIsImlzUGF0Y2hNdXRhdGluZyIsIm51bVBhZ2VzIiwic2V0TnVtUGFnZXMiLCJwYWdlTnVtYmVyIiwic2V0UGFnZU51bWJlciIsImFuY2hvckVsIiwic2V0QW5jaG9yRWwiLCJyb3dBY3Rpb25FbmFibGVkIiwic2V0Um93QWN0aW9uRW5hYmxlZCIsIm9wZW4iLCJCb29sZWFuIiwib25QYWdpbmF0aW9uQ2hhbmdlIiwic3RhdGUiLCJjb25zb2xlIiwibG9nIiwicGFnaW5hdGlvbiIsInNldCIsIm9uRG9jdW1lbnRMb2FkU3VjY2VzcyIsImNoYW5nZVBhZ2UiLCJvZmZzZXQiLCJwcmV2UGFnZU51bWJlciIsInByZXZpb3VzUGFnZSIsIm5leHRQYWdlIiwiYWNjZXB0X3Jvd19kZWxldGlvbiIsImN1cnJlbnQiLCJzaG93Iiwic2V2ZXJpdHkiLCJzdW1tYXJ5IiwiZGV0YWlsIiwibGlmZSIsInJlamVjdF9yb3dfZGVsZXRpb24iLCJoYW5kbGVDbGljayIsImV2ZW50IiwiY3VycmVudFRhcmdldCIsImNvbHVtbnMiLCJPYmplY3QiLCJlbnRyaWVzIiwiZGF0YV90eXBlIiwicHJvcGVydGllcyIsImZpbHRlciIsImluZGV4Iiwia2V5IiwidmFsdWUiLCJpbmNsdWRlcyIsIm1hcCIsImhlYWRlciIsInRpdGxlIiwiYWNjZXNzb3JLZXkiLCJpZCIsIkVkaXQiLCJjZWxsIiwiY29sdW1uIiwicm93IiwidGFibGUiLCJsYWJlbCIsImNsYXNzTmFtZSIsIm9yaWdpbmFsIiwicmVwb3J0Iiwib25UZXh0Q2hhbmdlIiwiZSIsIl92YWx1ZXNDYWNoZSIsImh0bWxWYWx1ZSIsInN0eWxlIiwiaGVpZ2h0IiwibXVpVGFibGVIZWFkQ2VsbFByb3BzIiwiYWxpZ24iLCJtdWlUYWJsZUJvZHlDZWxsUHJvcHMiLCJtdWlUYWJsZUZvb3RlckNlbGxQcm9wcyIsIkNlbGwiLCJnZXRWYWx1ZSIsImNvZGUiLCJvcHRpb25MYWJlbCIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJwbGFuIiwiZmluZCIsIm5hbWUiLCJ0eXBlIiwiZXhlcmNpc2UiLCJvcHRpb25zIiwiZm9ybWF0IiwiYWNjZXNzb3JGbiIsIkRhdGUiLCJjcmVhdGVkIiwibW9kaWZpZWQiLCJmaWx0ZXJWYXJpYW50IiwiZmlsdGVyRm4iLCJzb3J0aW5nRm4iLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ1bCIsInVzciIsImxpIiwidGVhbSIsInNvdXJjZSIsImxlbmd0aCIsInVzZXJuYW1lIiwidGFyZ2V0Iiwic291cmNlSGVhZGVyIiwidGFyZ2V0SGVhZGVyIiwiaXRlbVRlbXBsYXRlIiwiaXRlbSIsImRpdiIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwic291cmNlU3R5bGUiLCJ0YXJnZXRTdHlsZSIsImZpbHRlckJ5IiwiZmlsdGVyTWF0Y2hNb2RlIiwic291cmNlRmlsdGVyUGxhY2Vob2xkZXIiLCJ0YXJnZXRGaWx0ZXJQbGFjZWhvbGRlciIsInJvd0NvdW50IiwiZW5hYmxlUm93U2VsZWN0aW9uIiwiZW5hYmxlQ29sdW1uT3JkZXJpbmciLCJlbmFibGVHbG9iYWxGaWx0ZXIiLCJlbmFibGVHcm91cGluZyIsImVuYWJsZVJvd0FjdGlvbnMiLCJlbmFibGVSb3dQaW5uaW5nIiwiZW5hYmxlU3RpY2t5SGVhZGVyIiwiZW5hYmxlU3RpY2t5Rm9vdGVyIiwiZW5hYmxlQ29sdW1uUGlubmluZyIsImVuYWJsZUNvbHVtblJlc2l6aW5nIiwiZW5hYmxlUm93TnVtYmVycyIsImVuYWJsZUV4cGFuZEFsbCIsImVuYWJsZUVkaXRpbmciLCJlbmFibGVFeHBhbmRpbmciLCJtYW51YWxQYWdpbmF0aW9uIiwiaW5pdGlhbFN0YXRlIiwicGFnZVNpemUiLCJwYWdlSW5kZXgiLCJjb2x1bW5WaXNpYmlsaXR5IiwiY3JlYXRlZF9ieSIsIm1vZGZpZWRfYnkiLCJtb2RpZmllZF9ieSIsInN0YWZmIiwiYXNzaXN0YW50cyIsImRvY3VtZW50IiwiZGVuc2l0eSIsInNob3dHbG9iYWxGaWx0ZXIiLCJzb3J0aW5nIiwiZGVzYyIsInBhZ2kiLCJsb2NhbGl6YXRpb24iLCJkaXNwbGF5Q29sdW1uRGVmT3B0aW9ucyIsImVuYWJsZUhpZGluZyIsInNpemUiLCJkZWZhdWx0Q29sdW1uIiwiZ3JvdyIsImVuYWJsZU11bHRpU29ydCIsIm11aVRhYmxlUGFwZXJQcm9wcyIsImNsYXNzZXMiLCJyb290Iiwic3giLCJiYWNrZ3JvdW5kQ29sb3IiLCJmb250RmFtaWx5IiwiZm9udEZlYXR1cmVTZXR0aW5ncyIsImNvbG9yIiwibXVpRWRpdFJvd0RpYWxvZ1Byb3BzIiwiZGlzcGxheSIsInpJbmRleCIsImVkaXREaXNwbGF5TW9kZSIsImNyZWF0ZURpc3BsYXlNb2RlIiwib25FZGl0aW5nUm93U2F2ZSIsInZhbHVlcyIsInJlc3QiLCJyZXZhbGlkYXRlIiwib25TdWNjZXNzIiwic2V0RWRpdGluZ1JvdyIsIm9uRXJyb3IiLCJyZXNwb25zZSIsInN0YXR1c1RleHQiLCJvbkVkaXRpbmdSb3dDYW5jZWwiLCJvbkNyZWF0aW5nUm93U2F2ZSIsInNldENyZWF0aW5nUm93Iiwib25DcmVhdGluZ1Jvd0NhbmNlbCIsIm11aVRhYmxlRm9vdGVyUHJvcHMiLCJtdWlUYWJsZUNvbnRhaW5lclByb3BzIiwiZ2V0U3RhdGUiLCJpc0Z1bGxTY3JlZW4iLCJyZWZzIiwidG9wVG9vbGJhclJlZiIsIm9mZnNldEhlaWdodCIsImJvdHRvbVRvb2xiYXJSZWYiLCJtdWlQYWdpbmF0aW9uUHJvcHMiLCJtdWlUb3BUb29sYmFyUHJvcHMiLCJtdWlUYWJsZUJvZHlQcm9wcyIsInJlbmRlclRvcFRvb2xiYXJDdXN0b21BY3Rpb25zIiwiZGlyZWN0aW9uIiwic3BhY2luZyIsIkkiLCJhIiwiaWNvbiIsInJvdW5kZWQiLCJhcmlhLWNvbnRyb2xzIiwidW5kZWZpbmVkIiwiYXJpYS1oYXNwb3B1cCIsImFyaWEtZXhwYW5kZWQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJnZXRJc1NvbWVSb3dzU2VsZWN0ZWQiLCJtdWlEZXRhaWxQYW5lbFByb3BzIiwidGhlbWUiLCJwYWxldHRlIiwibW9kZSIsInJlbmRlckNyZWF0ZVJvd0RpYWxvZ0NvbnRlbnQiLCJpbnRlcm5hbEVkaXRDb21wb25lbnRzIiwicG9zaXRpb24iLCJzcGFuIiwidmFyaWFudCIsInZpc2libGUiLCJvbkhpZGUiLCJmbGV4RGlyZWN0aW9uIiwiZ2FwIiwicmVuZGVyRWRpdFJvd0RpYWxvZ0NvbnRlbnQiLCJoMyIsInJlbmRlckRldGFpbFBhbmVsIiwicmVuZGVyUm93QWN0aW9ucyIsIm91dGxpbmVkIiwibWVzc2FnZSIsImFjY2VwdENsYXNzTmFtZSIsImFjY2VwdExhYmVsIiwicmVqZWN0TGFiZWwiLCJhY2NlcHQiLCJyZWplY3QiLCJyZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx\n"));

/***/ })

});