'use client';

import { ApiMissionListEtat } from '@/services/schemas';
import { useApiRecommendationList } from '@/services/api/api/api';
import { $Recommendation, Recommendation } from '@/services/openapi_client';
import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import GenericTable from '../(components)/GenericTAble';
import GenericTableRecommendationFolluwUp from '../(components)/GenericTAbleFollowUp';
import { getCookie } from 'cookies-next';
import { usebaseData } from '@/utilities/hooks/useBaseData';


const TableDemo = () => {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')

    const {  recommendations} = usebaseData();
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex: 0,
        pageSize: 5, //customize the default page size
    });
    if (recommendations.isLoading) return (<div></div>)
    return (
        <div className="grid">
            <div className="col-12">
                <GenericTableRecommendationFolluwUp<Recommendation> data_={recommendations.data} isLoading={recommendations.isLoading} error={recommendations.error} data_type={$Recommendation} pagination={{ "set": setPagination, "pagi": pagination }}></GenericTableRecommendationFolluwUp>
            </div>
        </div>
    );
};

export default TableDemo;
