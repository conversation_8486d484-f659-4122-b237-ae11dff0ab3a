{"/api/comments/route": "app/api/comments/route.js", "/api/themes/route": "app/api/themes/route.js", "/api/users/route": "app/api/users/route.js", "/api/plans/route": "app/api/plans/route.js", "/api/risks/route": "app/api/risks/route.js", "/api/goals/route": "app/api/goals/route.js", "/api/domains/route": "app/api/domains/route.js", "/api/structures/route": "app/api/structures/route.js", "/api/processes/route": "app/api/processes/route.js", "/(main)/themes/page": "app/(main)/themes/page.js", "/(main)/domains/page": "app/(main)/domains/page.js", "/(main)/processes/page": "app/(main)/processes/page.js", "/api/auth/session/route": "app/api/auth/session/route.js", "/(main)/plans/arbitrations/page": "app/(main)/plans/arbitrations/page.js", "/(main)/themes/opportunity_sheet/page": "app/(main)/themes/opportunity_sheet/page.js"}