from rest_framework.serializers import (
    ListSerializer,
    ValidationError,
    IntegerField,
    CharField,
    Field,
    SlugRelatedField,
    FileField,
    SerializerMethodField,
    ModelSerializer,
    PrimaryKeyRelatedField,
    RelatedField,
)
from backend.models import (
    Notification,
    FACT,
    Comment,
    StructureLQSInterim,
    CriStructview,
    UserProfile,
    StructureLQSCorrespondents,
    Theme,
    Domain,
    Process,
    Risk,
    RiskImpact,
    Goal,
    Arbitration,
    ArbitratedTheme,
    Plan,
    Mission,
    Action,
    Recommendation,
    Constat,
    Cause,
    Consequence,
)
from drf_writable_nested.serializers import WritableNestedModelSerializer
from django.contrib.auth.models import User
from rest_framework_recursive.fields import RecursiveField

# from casl_django.models import CASLPermission,UserPermission
from django.contrib.contenttypes.models import ContentType
from drf_spectacular.utils import extend_schema_field
from datetime import datetime

class UserSerializer(ModelSerializer):
    class Meta:
        model = User
        fields = ["username", "first_name", "last_name", "email", "id"]
        # depth = 1


class CreatorUserSerializer(ModelSerializer):
    class Meta:
        model = User
        fields = ["username", "first_name", "last_name", "email"]
        depth = 2


class ConcernedUserSerializer(ModelSerializer):
    class Meta:
        model = User
        fields = ["username", "first_name", "last_name", "email"]
        depth = 2


class StructureLQSInterimSerializer(ModelSerializer):
    # structure = CriStructviewSerializer()
    interim = UserSerializer(many=False, label="Intérim")
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = StructureLQSInterim
        fields = "__all__"
        depth = 1


class StructureLQSCorrespondentsSerializer(ModelSerializer):
    correspondents = UserSerializer(many=True)
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    # interims = StructureLQSInterimSerializer(many=True,source="struct_interim")
    class Meta:
        model = StructureLQSCorrespondents
        fields = "__all__"
        depth = 1

class CriStructviewSerializer(ModelSerializer):
    correspondents = SerializerMethodField(label="Correspondents")

    def get_correspondents(self, obj):
        if obj is not None:
            return StructureLQSCorrespondentsSerializer(
                StructureLQSCorrespondents.objects.filter(structure=obj), many=True
            ).data
        else:
            return StructureLQSCorrespondentsSerializer(
                StructureLQSCorrespondents.objects.none(), many=True
            ).data

    class Meta:
        model = CriStructview
        fields = "__all__"


class SubDomainSerializer(ModelSerializer):
    parent = SerializerMethodField()
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    def get_parent(self, obj):
        return None

    class Meta:
        model = Domain
        fields = "__all__"
        depth = 1


class DomainSerializer(ModelSerializer):
    parent = SubDomainSerializer(allow_null=True, required=False, many=False)
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    # def to_representation(self, instance):
    #     self.fields['parent'] = DomainSerializer(allow_null=True,required=False)
    #     return super(DomainSerializer, self).to_representation(instance)
    class Meta:
        model = Domain
        fields = [
            "id",
            "parent",
            "short_title",
            "title",
            "created_by",
            "created",
            "modified_by",
            "modified",
        ]
        depth = 1
        # extra_kwargs = {
        #     'parent': {'required': False},
        # }


class SubProcessSerializer(ModelSerializer):
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = Process
        fields = "__all__"
        depth = 2


class ProcessSerializer(ModelSerializer):
    parent = RecursiveField(allow_null=True)
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = Process
        fields = "__all__"
        # depth = 2

class ConsequenceSerializer(ModelSerializer):

    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    constat=PrimaryKeyRelatedField(queryset=Constat.objects.all(), label="Constat")
   
    # def to_representation(self, instance):
    #     self.fields["facts"] = FACTSerializer(many=True, read_only=True)
    #     return super(ConsequenceSerializer, self).to_representation(instance)

    class Meta:
        model = Consequence
        fields = "__all__"

class RiskImpactSerializer(ModelSerializer):

    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = RiskImpact
        fields = "__all__"
        depth = 2

class RiskSerializer(ModelSerializer):
    consequence = ConsequenceSerializer(many=False, label="conséquence")
    impacts = RiskImpactSerializer(many=True)
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = Risk
        fields = "__all__"
        depth = 2

class GoalSerializer(ModelSerializer):

    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = Goal
        fields = "__all__"
        depth = 2

class ThemeSerializer(ModelSerializer):
    domain = DomainSerializer(label="domaine")
    process = ProcessSerializer(label="processus")
    goals = GoalSerializer(many=True, label="objectifs")
    risks = RiskSerializer(many=True, label="risques")
    proposing_structures = CriStructviewSerializer(
        many=True, label="structures proposantes"
    )
    concerned_structures = CriStructviewSerializer(
        many=True, label="structures concernées"
    )
    created_by = UserSerializer(many=False, read_only=True, label="Créé par")
    modified_by = UserSerializer(many=False, read_only=True, label="Mis à jour par")

    class Meta:
        model = Theme
        fields = "__all__"
        depth = 1

    # def validate(self, data):
    #     """
    #     Check that start is before finish.
    #     """
    #     print(data)
    #     if data['month_start'] >= data['month_end']:
    #         raise ValidationError("Date_Fin ne peut pas être inférieur à Date_Début")
    #     return data


class ThemeSerializerWriter(ModelSerializer):
    domain = PrimaryKeyRelatedField(queryset=Domain.objects.all(), label="domaine")
    process = PrimaryKeyRelatedField(queryset=Process.objects.all(), label="processus")
    goals = PrimaryKeyRelatedField(
        queryset=Goal.objects.all(), many=True, label="objectifs"
    )
    risks = PrimaryKeyRelatedField(
        queryset=Risk.objects.all(), many=True, label="risques"
    )
    proposing_structures = PrimaryKeyRelatedField(
        queryset=CriStructview.objects.all(), many=True, label="structures proposantes"
    )
    concerned_structures = PrimaryKeyRelatedField(
        queryset=CriStructview.objects.all(), many=True, label="structures concernées"
    )
    created_by = UserSerializer(many=False, read_only=True, label="Créé par")
    modified_by = UserSerializer(many=False, read_only=True, label="Mis à jour par")

    class Meta:
        model = Theme
        fields = "__all__"
        depth = 1

    def validate_month_start(self, value):
        """
        Check that start is before finish.
        """
        if (
            value
            > datetime.strptime(
                self.context["request"].data["month_end"], "%Y-%m-%d"
            ).date()
        ):
            raise ValidationError("Date Debut ne peut pas être supérieur à Date Fin")
        return value

    def validate_proposing_structures(self, value):
        """
        Check that proposing_structures is not empty when proposed_by is "structures".
        """
        # print(value)
        if (
            self.context["request"].data["proposed_by"] == "Structures"
            and len(value) == 0
        ):
            raise ValidationError(
                "La liste des structures proposantes ne doit pas être vide."
            )
        return value

    def validate_month_end(self, value):
        """
        Check that start is before finish.
        """
        # print(self.context['request'].data)
        if (
            value
            < datetime.strptime(
                self.context["request"].data["month_start"], "%Y-%m-%d"
            ).date()
        ):
            raise ValidationError("Date Fin ne peut pas être inférieur à Date Début")
        return value


class PlanSerializer(ModelSerializer):
    # exercise = ExerciseSerializer()
    # missions= MissionSerializer(many=True, source="plan_missions")
    code = SerializerMethodField()

    def get_code(self, obj):
        return f"{obj.exercise}|{obj.type}"

    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = Plan
        fields = "__all__"
        depth = 1


class CauseSerializer(ModelSerializer):
    
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    constat=PrimaryKeyRelatedField(queryset=Constat.objects.all(), label="Constat")
 
    # def to_representation(self, instance):
    #     self.fields["facts"] = FACTSerializer(many=True, read_only=True)
    #     return super(CauseSerializer, self).to_representation(instance)

    class Meta:
        model = Cause
        fields = "__all__"
        # depth = 1


class FACTSerializer(ModelSerializer):
   
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    constat=PrimaryKeyRelatedField(queryset=Constat.objects.all(), label="Constat")
    class Meta:
        model = FACT
        fields = "__all__"
        # depth = 2


class ConstatSerializer(ModelSerializer):
    
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    mission=PrimaryKeyRelatedField(queryset=Mission.objects.all(), label="Mission En Cours")
    # missiontest=SerializerMethodField(label="Mission")
   
    def get_missiontest(self, obj):
        if obj is not None:
            return MissionSerializer(Mission.objects.filter(id=obj.mission.id)).data
        else:
            return None
     
    class Meta:
        model = Constat
        fields = "__all__"
        depth = 1


class ActionSerializer(ModelSerializer):
    job_leader = UserSerializer(many=False, read_only=True)
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    comments = SerializerMethodField(label="Commentaires")

    def get_comments(self, obj):
        # print(Comment.objects.filter(action=obj))
        if obj is not None and Comment.objects.filter(action=obj).exists():
            return CommentReadSerializer1(
                Comment.objects.filter(action=obj).order_by('-created'), many=True
            ).data
        else:
            return []

    class Meta:
        model = Action
        fields = "__all__"
        # depth = 1


class ActionWriteSerializer(ModelSerializer):
    id = IntegerField(read_only=True)
    job_leader = PrimaryKeyRelatedField(
        many=False,
        queryset=User.objects.all(),
        read_only=False,
        required=True,
        label="Job leader",
    )
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    proof = FileField(required=False)
    recommendation = PrimaryKeyRelatedField(
        many=False,
        queryset=Recommendation.objects.all(),
        read_only=False,
        required=True,
    )

    class Meta:
        model = Action
        fields = [
            "id",
            "description",
            "recommendation",
            "job_leader",
            "start_date",
            "end_date",
            # "dependencies",
            "validated",
            "status",
            "progress",
            "accepted",
            "proof",
            "created_by",
            "modified_by",
            "modified",
            "created",
        ]
        depth = 1


class RecommendationWriteSerializer(WritableNestedModelSerializer, ModelSerializer):
    mission = PrimaryKeyRelatedField(queryset=Mission.objects.all())
    concerned_structure = PrimaryKeyRelatedField(queryset=CriStructview.objects.all())
    causes = PrimaryKeyRelatedField(queryset=Cause.objects.all(), many=True)
    constat = PrimaryKeyRelatedField(queryset=Constat.objects.all())
    actions = ActionWriteSerializer(many=True)
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    def create(self, validated_data):
        actions_data = validated_data.pop("actions")
        causes_data = validated_data.pop("causes")
        recommendation = Recommendation.objects.create(**validated_data)
        recommendation.causes_set = causes_data
        for action_data in actions_data:
            # deps_data = action_data.pop("dependencies")
            Action.objects.create(recommendation=recommendation, **action_data)
        # for cause_data in causes_data:
        #     deps_data= action_data.pop("dependencies")
        #     Cause.objects.create(recommendation=recommendation, **cause_data)
        return recommendation

    def update(self, instance, validated_data):
        actions_data = validated_data.pop("actions", None)
        causes_data = validated_data.pop("causes", None)
        recommendation = Recommendation.objects.filter(id=instance.id).update(
            **validated_data
        )
        if actions_data is not None:
            for action_data in actions_data:
                # deps_data = action_data.pop("dependencies", None)
                if action_data.get("id", None) is not None:
                    print("update_update_action", action_data.get("id", None))
                    action_id = action_data.pop("id")
                    Action.objects.filter(id=action_id).update(
                        recommendation=instance, **action_data
                    )
                else:
                    print("update_create_action", action_data.get("id", None))
                    Action.objects.create(recommendation=instance, **action_data)
        return recommendation

    class Meta:
        model = Recommendation
        fields = "__all__"
        depth = 1


class RecommendationSerializer(ModelSerializer):
    actions = SerializerMethodField(label="Actions")
    # actions = ActionSerializer(label="Actions",source='action_set')
    mission = (SerializerMethodField())
             # PrimaryKeyRelatedField(queryset=Mission.objects.all())
    concerned_structure = CriStructviewSerializer(label="Structure concernée")
    causes = CauseSerializer(many=True)
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    comments = SerializerMethodField(label="Commentaires")
    constat = ConstatSerializer()

    def get_comments(self, obj):
        # print(Comment.objects.filter(recommendation=obj))
        if obj is not None:
            return CommentReadSerializer1(
                Comment.objects.filter(recommendation=obj).order_by('-created'), many=True
            ).data
        else:
            return []

    def get_actions(self, obj):
        # print(Action.objects.filter(recommendation=obj))
        if obj is not None:
            return ActionSerializer(
                Action.objects.filter(recommendation=obj), many=True
            ).data
        else:
            return ActionSerializer(Action.objects.none(), many=True).data

    # def get_action_plan(self,obj):
    #     # print(ActionPlanRecommendation.objects.filter(recommendation__id=obj.id)[0].actions.all())
    #     if ActionPlanRecommendation.objects.filter(recommendation__id=obj.id).exists() :
    #         return ActionPlanRecommendationSerializer(ActionPlanRecommendation.objects.get(recommendation__id=obj.id)).data
    #     else :
    #         return None

    def get_mission(self, obj):
        # print(obj)
        return obj.mission.code

    # @extend_schema_field(ConstatSerializer)
    # def get_constats(self, obj):
    #     facts = []
    #     constats = []
    #     for cause in obj.causes.all():
    #         facts.extend(list(cause.facts.all()))
    #     # print("################CONSTATS########################")
    #     # print(facts)
    #     # print("######################################################")
    #     for fact in facts:
    #         constats.extend(list(fact.constat_facts.all()))
    #     # print("######################################################")
    #     # print([cst.id for cst in constats])
    #     # print("######################################################")
    #     return ConstatSerializer(
    #         Constat.objects.filter(id__in=[cst.id for cst in constats]), many=True
    #     ).data

    class Meta:
        model = Recommendation
        fields = "__all__"
        depth = 1


class UserProfileSerializer(ModelSerializer):

    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = UserProfile
        fields = "__all__"
        # depth = 2


class ArbitrationSerializer(ModelSerializer):
    plan = PlanSerializer()
    team = UserSerializer(many=True, label="Membres présents")
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = Arbitration
        fields = "__all__"
        depth = 2


class ArbitratedThemeSerializer(ModelSerializer):
    arbitration = ArbitrationSerializer()
    theme = ThemeSerializer()
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    mission = PrimaryKeyRelatedField(
        queryset=Mission.objects.all(), many=True, allow_empty=True
    )
    # def to_representation(self, instance):
    #     self.fields['mission'] = MissionSerializerRead(many=True)
    #     return super(ArbitratedThemeSerializer, self).to_representation(instance)
    missions = SerializerMethodField(label="Mission")

    def get_missions(self, obj):
        if obj is not None and Mission.objects.filter(theme=obj).exists():
            return Mission.objects.filter(theme=obj).values_list("code", flat=True)
        else:
            return []

    class Meta:
        model = ArbitratedTheme
        fields = "__all__"
        # depth = 2


class ArbitratedThemeWriteSerializer(ModelSerializer):
    arbitration = PrimaryKeyRelatedField(
        queryset=Arbitration.objects.all(), required=True
    )
    theme = PrimaryKeyRelatedField(queryset=Theme.objects.all(), required=True)

    class Meta:
        model = ArbitratedTheme
        fields = "__all__"
        # depth = 2


class MissionSerializer(ModelSerializer):
    head = PrimaryKeyRelatedField(queryset=User.objects.all(), label="Chef de mission")
    supervisor = PrimaryKeyRelatedField(
        queryset=User.objects.all(), label="Supervisuer"
    )
    staff = PrimaryKeyRelatedField(
        queryset=User.objects.all(), many=True, label="Auditeurs/Controleurs"
    )
    assistants = PrimaryKeyRelatedField(
        queryset=User.objects.all(), many=True, label="Assistants"
    )
    plan = PrimaryKeyRelatedField(
        queryset=Plan.objects.all(), required=False, allow_null=True, default=None
    )
    document = FileField(required=False)
    created_by = UserSerializer(many=False, read_only=True)
    theme = PrimaryKeyRelatedField(
        many=False,
        queryset=ArbitratedTheme.objects.all(),
        read_only=False,
        required=True,
        label="Thème",
    )
    modified_by = UserSerializer(many=False, read_only=True)
    # def get_validation_exclusions(self):
    #     exclusions = super(MissionSerializer, self).get_validation_exclusions()
    #     return exclusions + ['plan','exercise']
    # def create(self, validated_data):
    #     print(validated_data)
    #     #plan_data = validated_data.pop('plan')

    #     mission = Mission.objects.create(**validated_data)
    #     # obj = Plan.objects.get(**plan_data)
    #     # mission.plan.add(obj.id)
    #     return mission
    class Meta:
        model = Mission
        fields = "__all__"
        depth = 2
        extra_kwargs = {"plan": {"required": False}, "exercise": {"required": False}}


class MissionSerializerRead(ModelSerializer):
    # staff =MissionStaffSerializerRead(read_only=True,label="Equipe CI/AI & Assistants")
    # assistants =UserSerializer(many=True,read_only=True,label="Assistants CI/AI")
    head = UserSerializer(label="Chef de mission")
    supervisor = UserSerializer(label="Supervisuer")
    staff = UserSerializer(many=True, label="Auditeurs/Controleurs")
    assistants = UserSerializer(many=True, label="Assistants")
    created_by = UserSerializer(many=False, read_only=True, label="Créé par")
    plan = SlugRelatedField(
        queryset=Plan.objects.all(), slug_field="code"
    )  # PlanSerializer(label="Plan")
    modified_by = UserSerializer(many=False, read_only=True, label="Mis à jour par")
    theme = ArbitratedThemeSerializer(label="Thème")
    concerned_structures = SerializerMethodField()
    recommendations = SerializerMethodField()

    def get_concerned_structures(self, obj):
        if obj.theme:
            return CriStructviewSerializer(
                obj.theme.theme.concerned_structures, many=True
            ).data
        else:
            return CriStructviewSerializer(CriStructview.objects.none()).data

    @extend_schema_field(RecommendationSerializer)
    def get_recommendations(self, obj):
        return RecommendationSerializer(
            Recommendation.objects.filter(mission=obj), many=True
        ).data

    # def create(self, validated_data):
    #     print(validated_data)
    #     #plan_data = validated_data.pop('plan')

    #     mission = Mission.objects.create(**validated_data)
    #     # obj = Plan.objects.get(**plan_data)
    #     # mission.plan.add(obj.id)
    #     return mission
    class Meta:
        model = Mission
        fields = "__all__"
        depth = 2

class ContentTypeSerializer(ModelSerializer):
    class Meta:
        model = ContentType
        fields = "__all__"


class NotificationSerializer(ModelSerializer):
    user = UserSerializer()
    content_type = ContentTypeSerializer()
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    class Meta:
        model = Notification
        fields = "__all__"
        depth = 2

class CommentReadSerializer1(ModelSerializer):
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    # proof = FileField(label="Justificatif")
    class Meta:
        model = Comment
        fields = [
            "comment",
            "created_by",
            "modified_by",
            "created",
            "modified",
            "id",
            "action",
            "recommendation",
        ]

class CommentWriteSerializer(ModelSerializer):
    recommendation = PrimaryKeyRelatedField(
        queryset=Recommendation.objects.all(),
        label="Recommandation",
        allow_null=True,
        required=False,
    )
    action = PrimaryKeyRelatedField(
        queryset=Action.objects.all(), label="Action", allow_null=True, required=False
    )
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)
    # proof = FileField(label="Justificatif")

    class Meta:
        model = Comment
        fields = "__all__"
        # list_serializer_class = CommentWriteListSerializer


class CommentReadSerializer(ModelSerializer):
    recommendation = RecommendationSerializer(
        allow_null=True, required=False, many=False
    )
    action = ActionSerializer(allow_null=True, required=False, many=False)
    comment = CharField()
    created_by = UserSerializer(many=False, read_only=True)
    modified_by = UserSerializer(many=False, read_only=True)

    # proof = FileField(label="Justificatif")
    # def __init__(self, *args, **kwargs):
    #     kwargs['many'] = kwargs.get('many', True)
    #     super(CommentReadSerializer,self).__init__(*args, **kwargs)
    class Meta:
        model = Comment
        fields = "__all__"


class CommentReadListSerializer(ListSerializer):
    child = CommentReadSerializer1()
    def __init__(self, *args, **kwargs):
        kwargs["child"] = kwargs.get('child',CommentReadSerializer1())
        # print(type(self.child))
        super(CommentReadListSerializer,self).__init__(*args, **kwargs)
    class Meta:
        model = Comment
        fields = "__all__"


class CommentWriteListSerializer(ListSerializer):
    child = CommentWriteSerializer()

    # def create(self, validated_data):
    #     user = None
    #     request = self.context.get("request")
    #     if request and hasattr(request, "user"):
    #         user = request.user
    #     comments = [Comment(**item, created_by_id=user.id) for item in validated_data]
    #     # print(validated_data,user)
    #     return Comment.objects.bulk_create(comments)

    # def update(self, validated_data):
    #     user = None
    #     request = self.context.get("request")
    #     if request and hasattr(request, "user"):
    #         user = request.user
    #     comments = [Comment(**item, modified_by_id=user.id) for item in validated_data]
    #     return Comment.objects.bulk_update(comments)
    # def update(self, instance, validated_data):
    #     # Maps for id->instance and id->data item.
    #     book_mapping = {book.id: book for book in instance}
    #     data_mapping = {item['id']: item for item in validated_data}

    #     # Perform creations and updates.
    #     ret = []
    #     # for book_id, data in data_mapping.items():
    #     #     book = book_mapping.get(book_id, None)
    #     #     if book is None:
    #     #         ret.append(self.child.create(data))
    #     #     else:
    #     #         ret.append(self.child.update(book, data))

    #     # # Perform deletions.
    #     # for book_id, book in book_mapping.items():
    #     #     if book_id not in data_mapping:
    #     #         book.delete()

    #     return ret
    def __init__(self, *args, **kwargs):
        # kwargs["many"] = kwargs.get("many", True)
        kwargs["child"] = kwargs.get("child", CommentWriteSerializer())
        super(CommentWriteListSerializer, self).__init__(*args, **kwargs)

    class Meta:
        model = Comment
        fields = "__all__"

    # Just for read


