"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/page",{

/***/ "(app-client)/./app/(main)/page.tsx":
/*!*****************************!*\
  !*** ./app/(main)/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_chart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/chart */ \"(app-client)/./node_modules/primereact/chart/chart.esm.js\");\n/* harmony import */ var primereact_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/menu */ \"(app-client)/./node_modules/primereact/menu/menu.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../layout/context/layoutcontext */ \"(app-client)/./layout/context/layoutcontext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-client)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_service_ProductService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/service/ProductService */ \"(app-client)/./utilities/service/ProductService.tsx\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* eslint-disable @next/next/no-img-element */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst lineData = {\n    labels: [\n        \"January\",\n        \"February\",\n        \"March\",\n        \"April\",\n        \"May\",\n        \"June\",\n        \"July\"\n    ],\n    datasets: [\n        {\n            label: \"First Dataset\",\n            data: [\n                65,\n                59,\n                80,\n                81,\n                56,\n                55,\n                40\n            ],\n            fill: false,\n            backgroundColor: \"#2f4860\",\n            borderColor: \"#2f4860\",\n            tension: 0.4\n        },\n        {\n            label: \"Second Dataset\",\n            data: [\n                28,\n                48,\n                40,\n                19,\n                86,\n                27,\n                90\n            ],\n            fill: false,\n            backgroundColor: \"#00bb7e\",\n            borderColor: \"#00bb7e\",\n            tension: 0.4\n        }\n    ]\n};\nconst Dashboard = ()=>{\n    var _recommendations_data, _recommendations;\n    _s();\n    // Fetch data using specific hooks\n    const { data: recommendations, isLoading: recommendationsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiRecommendationList)({\n        limit: 10\n    });\n    const { data: plans, isLoading: plansLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiPlanList)({\n        limit: 5\n    });\n    const { data: missions, isLoading: missionsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiMissionList)({\n        limit: 5\n    });\n    console.log(\"[Dashboard]\", recommendations);\n    const menu1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const menu2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [lineOptions, setLineOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { layoutConfig } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__.LayoutContext);\n    const applyLightTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#495057\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    const applyDarkTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#ebedef\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        _utilities_service_ProductService__WEBPACK_IMPORTED_MODULE_4__.ProductService.getProductsSmall().then((data)=>setProducts(data));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (layoutConfig.colorScheme === \"light\") {\n            applyLightTheme();\n        } else {\n            applyDarkTheme();\n        }\n    }, [\n        layoutConfig.colorScheme\n    ]);\n    const formatCurrency = (value)=>{\n        var _value;\n        return (_value = value) === null || _value === void 0 ? void 0 : _value.toLocaleString(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Missions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(missions) ? missions.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-blue-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-briefcase text-blue-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(recommendations) ? recommendations.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-orange-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-thumbs-up-fill text-orange-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-cyan-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-cog text-cyan-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Comments\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: \"152 Unread\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-purple-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-comment text-purple-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Commentaires\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 17\n                            }, undefined),\n                            recommendationsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_6__.ProgressSpinner, {\n                                style: {\n                                    width: \"50px\",\n                                    height: \"50px\"\n                                },\n                                strokeWidth: \"8\",\n                                fill: \"var(--surface-ground)\",\n                                animationDuration: \".5s\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 44\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-600\",\n                                children: \"Commentaires r\\xe9cents seront affich\\xe9s ici.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-content-between align-items-center mb-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Plans d'actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu1_current;\n                                                    return (_menu1_current = menu1.current) === null || _menu1_current === void 0 ? void 0 : _menu1_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_8__.Menu, {\n                                                ref: menu1,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-none p-0 m-0\",\n                                children: (_recommendations = recommendations) === null || _recommendations === void 0 ? void 0 : (_recommendations_data = _recommendations.data) === null || _recommendations_data === void 0 ? void 0 : _recommendations_data.map((rec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-900 font-medium mr-2 mb-1 md:mb-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/recommendations/\" + rec.id,\n                                                                children: [\n                                                                    \"Recommandation n\\xb0 \",\n                                                                    rec.numrecommandation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 90\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 text-600\",\n                                                            children: rec.mission\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 md:mt-0 flex align-items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\",\n                                                            style: {\n                                                                height: \"8px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-orange-500 h-full\",\n                                                                style: {\n                                                                    width: \"\".concat(rec.actions.reduce((accumulator, act)=>accumulator += act.progress || 0, 0), \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 37\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-500 ml-3 font-medium\",\n                                                            children: [\n                                                                \"%\",\n                                                                (rec.actions.reduce((accumulator, act)=>accumulator += act.progress || 0, 0) / rec.actions.length || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Sales Overview\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chart__WEBPACK_IMPORTED_MODULE_9__.Chart, {\n                                type: \"line\",\n                                data: lineData,\n                                options: lineOptions\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu2_current;\n                                                    return (_menu2_current = menu2.current) === null || _menu2_current === void 0 ? void 0 : _menu2_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_8__.Menu, {\n                                                ref: menu2,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"TODAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 mx-0 mt-0 mb-4 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Richard Jones\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a blue t-shirt for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"79$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-orange-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-download text-xl text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-700 line-height-3\",\n                                                children: [\n                                                    \"Your request for withdrawal of \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-500 font-medium\",\n                                                        children: \"2500$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 60\n                                                    }, undefined),\n                                                    \" has been initiated.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"YESTERDAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 m-0 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Keyser Wick\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a black jacket for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"59$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-pink-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-question text-xl text-pink-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Jane Davis\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: \" has posted a new questions about your product.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-5 shadow-2 flex flex-column md:flex-row md:align-items-center justify-content-between mb-3\",\n                        style: {\n                            borderRadius: \"1rem\",\n                            background: \"linear-gradient(0deg, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.5)), linear-gradient(92.54deg, #1C80CF 47.88%, #FFFFFF 100.01%)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100 font-medium text-xl mt-2 mb-3\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white font-medium text-5xl\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 mr-auto md:mt-0 md:mr-0\",\n                                children: \"TEST\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n        lineNumber: 137,\n        columnNumber: 13\n    }, undefined);\n};\n_s(Dashboard, \"dIK6/XOP/w75Cc1eGiU9nkJpz0A=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiRecommendationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiMissionList\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/page.tsx\n"));

/***/ })

});