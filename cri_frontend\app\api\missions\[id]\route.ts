import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/missions/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    const mission = await prisma.mission.findUnique({
      where: { id },
      include: {
        plan: true,
        theme: {
          include: {
            theme: {
              include: {
                domain: true,
                process: true,
              },
            },
          },
        },
        head: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        supervisor: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        staff: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assistants: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        documents: true,
        recommendations: {
          include: {
            concernedStructure: true,
            comments: true,
            actions: true,
          },
        },
        constats: {
          include: {
            facts: true,
            causes: true,
            consequences: true,
          },
        },
      },
    })
    
    if (!mission) {
      return NextResponse.json(
        { error: 'Mission not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(mission)
  } catch (error) {
    console.error('Error fetching mission:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/missions/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const body = await request.json()
    
    const {
      planId,
      exercise,
      type,
      code,
      etat,
      startDate,
      endDate,
      themeId,
      headId,
      supervisorId,
      staff,
      assistants,
    } = body
    
    // Prepare update data
    const updateData: any = {}
    
    if (planId !== undefined) updateData.planId = planId
    if (exercise !== undefined) updateData.exercise = exercise
    if (type !== undefined) updateData.type = type
    if (code !== undefined) updateData.code = code
    if (etat !== undefined) updateData.etat = etat
    if (startDate !== undefined) updateData.startDate = new Date(startDate)
    if (endDate !== undefined) updateData.endDate = new Date(endDate)
    if (themeId !== undefined) updateData.themeId = themeId
    if (headId !== undefined) updateData.headId = headId
    if (supervisorId !== undefined) updateData.supervisorId = supervisorId
    
    // Handle many-to-many relationships
    if (staff !== undefined) {
      updateData.staff = { set: staff.map((id: number) => ({ id })) }
    }
    if (assistants !== undefined) {
      updateData.assistants = { set: assistants.map((id: number) => ({ id })) }
    }
    
    const mission = await prisma.mission.update({
      where: { id },
      data: updateData,
      include: {
        plan: true,
        theme: {
          include: {
            theme: {
              include: {
                domain: true,
                process: true,
              },
            },
          },
        },
        head: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        supervisor: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        staff: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assistants: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    })
    
    return NextResponse.json(mission)
  } catch (error) {
    console.error('Error updating mission:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/missions/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    await prisma.mission.delete({
      where: { id },
    })
    
    return NextResponse.json({ message: 'Mission deleted successfully' })
  } catch (error) {
    console.error('Error deleting mission:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
