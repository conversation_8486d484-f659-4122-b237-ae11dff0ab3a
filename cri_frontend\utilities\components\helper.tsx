import { Action, Recommendation } from "@/services/schemas";
import { Task } from "gantt-task-react/dist/types/public-types";
import parse from 'html-react-parser';


export function initTasks() {
  const currentDate = new Date();
  const tasks: Task[] = [
    {
      start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1),
      end: new Date(currentDate.getFullYear(), currentDate.getMonth(), 15),
      name: "Some Project",
      id: "ProjectSample",
      progress: 25,
      type: "project",
      hideChildren: false,
      displayOrder: 1,
    },
    {
      start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1),
      end: new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        2,
        12,
        28
      ),
      name: "Idea",
      id: "Task 0",
      progress: 45,
      type: "task",
      project: "ProjectSample",
      displayOrder: 2,
    },
    {
      start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 2),
      end: new Date(currentDate.getFullYear(), currentDate.getMonth(), 4, 0, 0),
      name: "Research",
      id: "Task 1",
      progress: 25,
      dependencies: ["Task 0"],
      type: "task",
      project: "ProjectSample",
      displayOrder: 3,
    },
    {
      start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 4),
      end: new Date(currentDate.getFullYear(), currentDate.getMonth(), 8, 0, 0),
      name: "Discussion with team",
      id: "Task 2",
      progress: 10,
      dependencies: ["Task 1"],
      type: "task",
      project: "ProjectSample",
      displayOrder: 4,
    },
    {
      start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 8),
      end: new Date(currentDate.getFullYear(), currentDate.getMonth(), 9, 0, 0),
      name: "Developing",
      id: "Task 3",
      progress: 2,
      dependencies: ["Task 2"],
      type: "task",
      project: "ProjectSample",
      displayOrder: 5,
    },
    {
      start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 8),
      end: new Date(currentDate.getFullYear(), currentDate.getMonth(), 10),
      name: "Review",
      id: "Task 4",
      type: "task",
      progress: 70,
      dependencies: ["Task 2"],
      project: "ProjectSample",
      displayOrder: 6,
    },
    {
      start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 15),
      end: new Date(currentDate.getFullYear(), currentDate.getMonth(), 15),
      name: "Release",
      id: "Task 6",
      progress: currentDate.getMonth(),
      type: "milestone",
      dependencies: ["Task 4"],
      project: "ProjectSample",
      displayOrder: 7,
    },
    {
      start: new Date(currentDate.getFullYear(), currentDate.getMonth(), 18),
      end: new Date(currentDate.getFullYear(), currentDate.getMonth(), 19),
      name: "Party Time",
      id: "Task 9",
      progress: 0,
      isDisabled: true,
      type: "task",
    },
  ];
  return tasks;
}

export function getStartEndDateForProject(tasks: Task[]) {
  let progress = 0;
  let start, end;
  const projects = tasks.filter(t => t.type === 'project');
  console.log(projects)
  projects.forEach((project, idx) => {
    progress = 0;
    const projectTasks = tasks.filter(t => t.project === project.id);
    projectTasks.forEach((task, idx) => {
      progress += task.progress
      start = projectTasks[idx]?.start;
      end = projectTasks[idx]?.end;

      for (let i = 0; i < projectTasks.length; i++) {
        const task = projectTasks[i];
        if (start!.getTime() > task!.start.getTime()) {
          start = task!.start;
        }
        if (end!.getTime() < task!.end.getTime()) {
          end = task!.end;
        }
      }
      project.start = start!;
      project.end = end!;
      project.progress = projectTasks.length > 0 ? progress / projectTasks.length : 0;
      console.log(end, start)
    })


  })

  return [start, end];
}

export function recommendationActionsPlanToTasks(recommendations: Recommendation[]) {

  let tasks: Task[] = [];
  let recom_idx = 1;
  if (recommendations) {
    recommendations.forEach((recommendation, idx) => {
      console.log("hhhhhhhhhhhhhhhhh", idx)

      tasks.push(
        {
          id: `recomm_${recommendation.id}_${recommendation.recommendation}_${recommendation.mission}`,
          type: 'project',
          name: `${recommendation.recommendation}\n${recommendation.mission}\n${recommendation.concerned_structure.abbrev}`,
          start: new Date(),
          end: new Date(),
          hideChildren: false,
          progress: 1,
          // styles?: {
          //     backgroundColor?: string;
          //     backgroundSelectedColor?: string;
          //     progressColor?: string;
          //     progressSelectedColor?: string;
          // };
          // isDisabled?: boolean;
          // project: 'string',

          displayOrder: recom_idx
        });
      recommendation.actions.sort((a: Action, b: Action) => new Date(a.start_date) - new Date(b.start_date)).forEach((action: Action, indx: number) => {
        recom_idx += 1;
        tasks.push(
          {
            id: `recomm_${recommendation.id}_${recommendation.recommendation}_${recommendation.mission}_${action.id}`,
            type: 'task',
            name: `Action #${action.id}`,
            start: new Date(action.start_date),
            end: new Date(action.end_date),
            dependencies: action.dependencies?.map((val) => `recomm_${recommendation.id}_${recommendation.recommendation}_${recommendation.mission}_${recommendation.actions.filter((t: Action) => t.id === val)[0].id}`),
            progress: action.progress || 0,
            // styles?: {
            //     backgroundColor?: string;
            //     backgroundSelectedColor?: string;
            //     progressColor?: string;
            //     progressSelectedColor?: string;
            // };
            // isDisabled?: boolean;
            project: `recomm_${recommendation.id}_${recommendation.recommendation}_${recommendation.mission}`,

            displayOrder: recom_idx
          }

        );
      }
      );


    });
  }
  else { return tasks }
  getStartEndDateForProject(tasks);
  console.log(tasks)
  return tasks;

}