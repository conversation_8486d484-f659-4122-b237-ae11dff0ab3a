import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/plans
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const exercise = searchParams.get('exercise')
    const type = searchParams.get('type')

    const skip = (page - 1) * limit

    const where: any = {}

    if (exercise) {
      where.exercise = parseInt(exercise)
    }

    if (type) {
      where.type = type
    }

    const [plans, total] = await Promise.all([
      prisma.plan.findMany({
        where,
        skip,
        take: limit,
        include: {
          missions: {
            select: {
              id: true,
              code: true,
              type: true,
              etat: true,
            },
          },
          arbitrations: {
            include: {
              arbitratedThemes: {
                include: {
                  theme: {
                    select: {
                      id: true,
                      title: true,
                      validated: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: { exercise: 'desc' },
      }),
      prisma.plan.count({ where }),
    ])

    return NextResponse.json({
      data: {
        results: plans,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching plans:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/plans
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to create plans (staff only)
    if (!session.user.isStaff) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()

    const { title, exercise, type } = body

    const plan = await prisma.plan.create({
      data: {
        title: title || null,
        exercise: exercise || null,
        type: type || 'PLN',
        createdBy: session.user.id,
      },
      include: {
        missions: true,
        arbitrations: true,
      },
    })

    return NextResponse.json(plan, { status: 201 })
  } catch (error) {
    console.error('Error creating plan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
