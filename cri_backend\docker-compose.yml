
services:
  backend:
    build:
      context: ./cri_backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    volumes:
      - ./cri_backend:/app
    command: gunicorn --config gunicorn_config.py app.wsgi:application
  development:
    build:
      context: ./cri_frontend
      dockerfile: ./Dockerfile.prod
      target: development  # Use the 'development' stage from Dockerfile.prod for development
    ports:
      - "3000:3000"
    volumes:
      - .:/app
    command: npm run dev

  production:
    build:
      context: ./cri_frontend
      dockerfile: ./Dockerfile.prod
      target: production  # Use the 'production' stage from Dockerfile.prod for production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped