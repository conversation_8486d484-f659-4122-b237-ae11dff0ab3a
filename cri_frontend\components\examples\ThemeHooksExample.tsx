/**
 * Example component demonstrating how to use Theme hooks
 * This shows different ways to use the Theme API hooks
 */

import React, { useState } from 'react';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Checkbox } from 'primereact/checkbox';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
import { 
  useApiThemeList, 
  useApiThemeCreate, 
  useApiThemeUpdate, 
  useApiThemePartialUpdate,
  useApiThemeDestroy,
  useApiThemeRetrieve
} from '@/hooks/useNextApi';
import { $Theme } from '@/lib/schemas';

export default function ThemeHooksExample() {
  const [selectedThemeId, setSelectedThemeId] = useState<number | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState<any>(null);
  const [formData, setFormData] = useState({
    title: '',
    code: '',
    validated: false,
    proposedBy: 'INTERNAL',
    monthStart: '',
    monthEnd: '',
    domainId: null,
    processId: null,
  });

  const toast = useRef<Toast>(null);

  // Fetch data
  const { data: themesResponse, isLoading: themesLoading, refetch: refetchThemes } = useApiThemeList({
    limit: 50
  });

  const { data: themeDetails, isLoading: themeDetailsLoading } = useApiThemeRetrieve(
    selectedThemeId || 0
  );

  // Mutations
  const createTheme = useApiThemeCreate();
  const updateTheme = useApiThemeUpdate();
  const partialUpdateTheme = useApiThemePartialUpdate();
  const deleteTheme = useApiThemeDestroy();

  // Proposed by options
  const proposedByOptions = [
    { label: 'Interne', value: 'INTERNAL' },
    { label: 'Externe', value: 'EXTERNAL' },
    { label: 'Mixte', value: 'MIXED' }
  ];

  const themes = Array.isArray(themesResponse?.data?.results) ? themesResponse.data.results : [];

  const handleCreate = async () => {
    try {
      await createTheme.mutateAsync(formData);
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Thème créé avec succès' 
      });
      
      setShowCreateDialog(false);
      resetForm();
      refetchThemes();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la création' 
      });
    }
  };

  const handleUpdate = async () => {
    try {
      await updateTheme.mutateAsync({
        id: selectedTheme.id,
        data: formData,
      });
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Thème mis à jour avec succès' 
      });
      
      setShowEditDialog(false);
      resetForm();
      refetchThemes();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la mise à jour' 
      });
    }
  };

  const handlePartialUpdate = async (themeId: number, updates: any) => {
    try {
      await partialUpdateTheme.mutateAsync({
        id: themeId,
        data: updates,
      });
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Thème mis à jour' 
      });
      
      refetchThemes();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la mise à jour' 
      });
    }
  };

  const handleDelete = async (themeId: number) => {
    try {
      await deleteTheme.mutateAsync(themeId);
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Thème supprimé avec succès' 
      });
      
      refetchThemes();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la suppression' 
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      code: '',
      validated: false,
      proposedBy: 'INTERNAL',
      monthStart: '',
      monthEnd: '',
      domainId: null,
      processId: null,
    });
  };

  const openCreateDialog = () => {
    resetForm();
    setShowCreateDialog(true);
  };

  const openEditDialog = (theme: any) => {
    setSelectedTheme(theme);
    setFormData({
      title: theme.title || '',
      code: theme.code || '',
      validated: theme.validated || false,
      proposedBy: theme.proposedBy || 'INTERNAL',
      monthStart: theme.monthStart || '',
      monthEnd: theme.monthEnd || '',
      domainId: theme.domainId || null,
      processId: theme.processId || null,
    });
    setShowEditDialog(true);
  };

  const openDetailsDialog = (theme: any) => {
    setSelectedThemeId(theme.id);
    setShowDetailsDialog(true);
  };

  // Column templates
  const validatedBodyTemplate = (rowData: any) => {
    return rowData.validated ? 
      <Tag value="Validé" severity="success" /> : 
      <Tag value="Non validé" severity="warning" />;
  };

  const proposedByBodyTemplate = (rowData: any) => {
    const option = proposedByOptions.find(opt => opt.value === rowData.proposedBy);
    return option ? option.label : rowData.proposedBy;
  };

  const actionsBodyTemplate = (rowData: any) => {
    return (
      <div className="flex gap-2">
        <Button 
          icon="pi pi-eye" 
          size="small" 
          onClick={() => openDetailsDialog(rowData)}
          tooltip="Voir détails"
        />
        <Button 
          icon="pi pi-pencil" 
          size="small" 
          onClick={() => openEditDialog(rowData)}
          tooltip="Modifier"
        />
        <Button 
          icon="pi pi-check" 
          size="small" 
          severity="success"
          onClick={() => handlePartialUpdate(rowData.id, { validated: true })}
          tooltip="Valider"
          disabled={rowData.validated}
        />
        <Button 
          icon="pi pi-trash" 
          size="small" 
          severity="danger"
          onClick={() => handleDelete(rowData.id)}
          tooltip="Supprimer"
        />
      </div>
    );
  };

  return (
    <div className="theme-hooks-example">
      <Toast ref={toast} />
      
      <div className="card">
        <h4>Exemple d'utilisation des hooks Theme</h4>
        
        {/* Themes table */}
        <div className="flex justify-content-between align-items-center mb-3">
          <h5>Thèmes</h5>
          <Button 
            label="Nouveau Thème" 
            icon="pi pi-plus" 
            onClick={openCreateDialog}
          />
        </div>

        <DataTable 
          value={themes} 
          loading={themesLoading}
          paginator 
          rows={10}
          emptyMessage="Aucun thème trouvé"
        >
          <Column field="id" header={$Theme.properties.id.title} />
          <Column field="title" header={$Theme.properties.title.title} style={{ maxWidth: '300px' }} />
          <Column field="code" header={$Theme.properties.code.title} />
          <Column field="validated" header={$Theme.properties.validated.title} body={validatedBodyTemplate} />
          <Column field="proposedBy" header={$Theme.properties.proposedBy.title} body={proposedByBodyTemplate} />
          <Column field="monthStart" header={$Theme.properties.monthStart.title} />
          <Column field="monthEnd" header={$Theme.properties.monthEnd.title} />
          <Column header="Actions" body={actionsBodyTemplate} />
        </DataTable>
      </div>

      {/* Create Dialog */}
      <Dialog 
        header="Créer un Thème" 
        visible={showCreateDialog} 
        onHide={() => setShowCreateDialog(false)}
        style={{ width: '500px' }}
      >
        <div className="field">
          <label htmlFor="title">Titre *</label>
          <InputText
            id="title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="code">Code</label>
          <InputText
            id="code"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value })}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="proposedBy">Proposé par</label>
          <Dropdown
            id="proposedBy"
            value={formData.proposedBy}
            options={proposedByOptions}
            onChange={(e) => setFormData({ ...formData, proposedBy: e.value })}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="monthStart">Mois début</label>
          <InputText
            id="monthStart"
            value={formData.monthStart}
            onChange={(e) => setFormData({ ...formData, monthStart: e.target.value })}
            placeholder="YYYY-MM"
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="monthEnd">Mois fin</label>
          <InputText
            id="monthEnd"
            value={formData.monthEnd}
            onChange={(e) => setFormData({ ...formData, monthEnd: e.target.value })}
            placeholder="YYYY-MM"
            className="w-full"
          />
        </div>

        <div className="field-checkbox">
          <Checkbox
            id="validated"
            checked={formData.validated}
            onChange={(e) => setFormData({ ...formData, validated: e.checked || false })}
          />
          <label htmlFor="validated">Validé</label>
        </div>

        <div className="flex justify-content-end gap-2 mt-4">
          <Button 
            label="Annuler" 
            severity="secondary" 
            onClick={() => setShowCreateDialog(false)} 
          />
          <Button 
            label="Créer" 
            onClick={handleCreate}
            loading={createTheme.isPending}
            disabled={!formData.title}
          />
        </div>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog 
        header="Modifier le Thème" 
        visible={showEditDialog} 
        onHide={() => setShowEditDialog(false)}
        style={{ width: '500px' }}
      >
        <div className="field">
          <label htmlFor="edit-title">Titre *</label>
          <InputText
            id="edit-title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="edit-code">Code</label>
          <InputText
            id="edit-code"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value })}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="edit-proposedBy">Proposé par</label>
          <Dropdown
            id="edit-proposedBy"
            value={formData.proposedBy}
            options={proposedByOptions}
            onChange={(e) => setFormData({ ...formData, proposedBy: e.value })}
            className="w-full"
          />
        </div>

        <div className="field-checkbox">
          <Checkbox
            id="edit-validated"
            checked={formData.validated}
            onChange={(e) => setFormData({ ...formData, validated: e.checked || false })}
          />
          <label htmlFor="edit-validated">Validé</label>
        </div>

        <div className="flex justify-content-end gap-2 mt-4">
          <Button 
            label="Annuler" 
            severity="secondary" 
            onClick={() => setShowEditDialog(false)} 
          />
          <Button 
            label="Mettre à jour" 
            onClick={handleUpdate}
            loading={updateTheme.isPending}
            disabled={!formData.title}
          />
        </div>
      </Dialog>

      {/* Details Dialog */}
      <Dialog 
        header="Détails du Thème" 
        visible={showDetailsDialog} 
        onHide={() => setShowDetailsDialog(false)}
        style={{ width: '600px' }}
      >
        {themeDetailsLoading ? (
          <div className="text-center">Chargement...</div>
        ) : themeDetails?.data ? (
          <div className="theme-details">
            <div className="grid">
              <div className="col-6">
                <strong>ID:</strong> {themeDetails.data.id}
              </div>
              <div className="col-6">
                <strong>Code:</strong> {themeDetails.data.code || 'N/A'}
              </div>
              <div className="col-12">
                <strong>Titre:</strong> {themeDetails.data.title}
              </div>
              <div className="col-6">
                <strong>Validé:</strong> {themeDetails.data.validated ? 'Oui' : 'Non'}
              </div>
              <div className="col-6">
                <strong>Proposé par:</strong> {proposedByBodyTemplate(themeDetails.data)}
              </div>
              <div className="col-6">
                <strong>Mois début:</strong> {themeDetails.data.monthStart || 'N/A'}
              </div>
              <div className="col-6">
                <strong>Mois fin:</strong> {themeDetails.data.monthEnd || 'N/A'}
              </div>
              <div className="col-6">
                <strong>Domaine:</strong> {themeDetails.data.domain?.title || 'N/A'}
              </div>
              <div className="col-6">
                <strong>Processus:</strong> {themeDetails.data.process?.title || 'N/A'}
              </div>
              <div className="col-6">
                <strong>Créé:</strong> {new Date(themeDetails.data.created).toLocaleDateString('fr')}
              </div>
              <div className="col-6">
                <strong>Modifié:</strong> {new Date(themeDetails.data.modified).toLocaleDateString('fr')}
              </div>
            </div>
          </div>
        ) : (
          <div>Aucun détail disponible</div>
        )}
      </Dialog>
    </div>
  );
}
