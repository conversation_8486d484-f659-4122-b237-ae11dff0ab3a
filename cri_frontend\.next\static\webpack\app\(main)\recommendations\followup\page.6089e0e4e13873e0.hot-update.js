"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/recommendations/followup/page",{

/***/ "(app-client)/./hooks/useNextApi.ts":
/*!*****************************!*\
  !*** ./hooks/useNextApi.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApiActionCreate: function() { return /* binding */ useApiActionCreate; },\n/* harmony export */   useApiActionDestroy: function() { return /* binding */ useApiActionDestroy; },\n/* harmony export */   useApiActionList: function() { return /* binding */ useApiActionList; },\n/* harmony export */   useApiActionPartialUpdate: function() { return /* binding */ useApiActionPartialUpdate; },\n/* harmony export */   useApiActionRetrieve: function() { return /* binding */ useApiActionRetrieve; },\n/* harmony export */   useApiActionUpdate: function() { return /* binding */ useApiActionUpdate; },\n/* harmony export */   useApiArbitratedThemeCreate: function() { return /* binding */ useApiArbitratedThemeCreate; },\n/* harmony export */   useApiArbitratedThemeDestroy: function() { return /* binding */ useApiArbitratedThemeDestroy; },\n/* harmony export */   useApiArbitratedThemeList: function() { return /* binding */ useApiArbitratedThemeList; },\n/* harmony export */   useApiArbitratedThemePartialUpdate: function() { return /* binding */ useApiArbitratedThemePartialUpdate; },\n/* harmony export */   useApiArbitratedThemeRetrieve: function() { return /* binding */ useApiArbitratedThemeRetrieve; },\n/* harmony export */   useApiArbitratedThemeUpdate: function() { return /* binding */ useApiArbitratedThemeUpdate; },\n/* harmony export */   useApiArbitrationCreate: function() { return /* binding */ useApiArbitrationCreate; },\n/* harmony export */   useApiArbitrationDestroy: function() { return /* binding */ useApiArbitrationDestroy; },\n/* harmony export */   useApiArbitrationList: function() { return /* binding */ useApiArbitrationList; },\n/* harmony export */   useApiArbitrationPartialUpdate: function() { return /* binding */ useApiArbitrationPartialUpdate; },\n/* harmony export */   useApiArbitrationRetrieve: function() { return /* binding */ useApiArbitrationRetrieve; },\n/* harmony export */   useApiCommentCreate: function() { return /* binding */ useApiCommentCreate; },\n/* harmony export */   useApiCommentDestroy: function() { return /* binding */ useApiCommentDestroy; },\n/* harmony export */   useApiCommentList: function() { return /* binding */ useApiCommentList; },\n/* harmony export */   useApiCommentPartialUpdate: function() { return /* binding */ useApiCommentPartialUpdate; },\n/* harmony export */   useApiCommentRetrieve: function() { return /* binding */ useApiCommentRetrieve; },\n/* harmony export */   useApiDocsCreate: function() { return /* binding */ useApiDocsCreate; },\n/* harmony export */   useApiDocsDestroy: function() { return /* binding */ useApiDocsDestroy; },\n/* harmony export */   useApiDocsUpdate: function() { return /* binding */ useApiDocsUpdate; },\n/* harmony export */   useApiDocumentsList: function() { return /* binding */ useApiDocumentsList; },\n/* harmony export */   useApiMissionCreate: function() { return /* binding */ useApiMissionCreate; },\n/* harmony export */   useApiMissionDestroy: function() { return /* binding */ useApiMissionDestroy; },\n/* harmony export */   useApiMissionDocsCreate: function() { return /* binding */ useApiMissionDocsCreate; },\n/* harmony export */   useApiMissionDocumentsList: function() { return /* binding */ useApiMissionDocumentsList; },\n/* harmony export */   useApiMissionList: function() { return /* binding */ useApiMissionList; },\n/* harmony export */   useApiMissionPartialUpdate: function() { return /* binding */ useApiMissionPartialUpdate; },\n/* harmony export */   useApiMissionRetrieve: function() { return /* binding */ useApiMissionRetrieve; },\n/* harmony export */   useApiPlanCreate: function() { return /* binding */ useApiPlanCreate; },\n/* harmony export */   useApiPlanDestroy: function() { return /* binding */ useApiPlanDestroy; },\n/* harmony export */   useApiPlanList: function() { return /* binding */ useApiPlanList; },\n/* harmony export */   useApiPlanRetrieve: function() { return /* binding */ useApiPlanRetrieve; },\n/* harmony export */   useApiPlanUpdate: function() { return /* binding */ useApiPlanUpdate; },\n/* harmony export */   useApiRecommendationCreate: function() { return /* binding */ useApiRecommendationCreate; },\n/* harmony export */   useApiRecommendationDestroy: function() { return /* binding */ useApiRecommendationDestroy; },\n/* harmony export */   useApiRecommendationList: function() { return /* binding */ useApiRecommendationList; },\n/* harmony export */   useApiRecommendationPartialUpdate: function() { return /* binding */ useApiRecommendationPartialUpdate; },\n/* harmony export */   useApiRecommendationRetrieve: function() { return /* binding */ useApiRecommendationRetrieve; },\n/* harmony export */   useApiThemeCreate: function() { return /* binding */ useApiThemeCreate; },\n/* harmony export */   useApiThemeDestroy: function() { return /* binding */ useApiThemeDestroy; },\n/* harmony export */   useApiThemeList: function() { return /* binding */ useApiThemeList; },\n/* harmony export */   useApiThemePartialUpdate: function() { return /* binding */ useApiThemePartialUpdate; },\n/* harmony export */   useApiThemeRetrieve: function() { return /* binding */ useApiThemeRetrieve; },\n/* harmony export */   useApiThemeUpdate: function() { return /* binding */ useApiThemeUpdate; },\n/* harmony export */   useApiUserCreate: function() { return /* binding */ useApiUserCreate; },\n/* harmony export */   useApiUserList: function() { return /* binding */ useApiUserList; },\n/* harmony export */   useApiUserRetrieve: function() { return /* binding */ useApiUserRetrieve; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api/nextApi */ \"(app-client)/./services/api/nextApi.ts\");\n// React hooks for Next.js API to replace Django API hooks\n\n\n// Mission hooks\nfunction useApiMissionList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"missions\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMissions(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiMissionRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"missions\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMission(id),\n        enabled: !!id\n    });\n}\nfunction useApiMissionCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createMission(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateMission(id, data);\n        },\n        onSuccess: (data, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteMission(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// Recommendation hooks\nfunction useApiRecommendationList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"recommendations\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRecommendations(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiRecommendationRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"recommendations\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRecommendation(id),\n        enabled: !!id\n    });\n}\nfunction useApiRecommendationCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createRecommendation(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRecommendationPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateRecommendation(id, data);\n        },\n        onSuccess: (data, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRecommendationDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteRecommendation(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// User hooks\nfunction useApiUserList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"users\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUsers(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiUserRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"users\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUser(id),\n        enabled: !!id\n    });\n}\nfunction useApiUserCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createUser(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"users\"\n                ]\n            });\n        }\n    });\n}\n// Plan hooks\nfunction useApiPlanList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"plans\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlans(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiPlanRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"plans\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlan(id),\n        enabled: !!id\n    });\n}\nfunction useApiPlanCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createPlan(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiPlanUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updatePlan(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiPlanDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deletePlan(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n        }\n    });\n}\n// Theme hooks\nfunction useApiThemeList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"themes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getThemes(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiThemeRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"themes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getTheme(id),\n        enabled: !!id\n    });\n}\nfunction useApiThemeCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createTheme(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemeUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemePartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemeDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteTheme(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Arbitration hooks\nfunction useApiArbitrationList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrations\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitrations(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiArbitrationRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrations\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitration(id),\n        enabled: !!id\n    });\n}\nfunction useApiArbitrationCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createArbitration(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitrationPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitration(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitrationDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteArbitration(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\n// Comment hooks\nfunction useApiCommentList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"comments\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComments(params),\n        staleTime: 2 * 60 * 1000\n    });\n}\nfunction useApiCommentRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"comments\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComment(id),\n        enabled: !!id\n    });\n}\nfunction useApiCommentCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createComment(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiCommentPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateComment(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiCommentDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteComment(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// Document hooks\nfunction useApiDocsCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].uploadDocument(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDocsCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { missionId, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].uploadMissionDocuments(missionId, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\",\n                    variables.missionId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\",\n                    variables.missionId\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDocumentsList(missionId) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"mission-documents\",\n            missionId\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMissionDocuments(missionId),\n        enabled: !!missionId\n    });\n}\nfunction useApiDocumentsList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"documents\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getDocuments(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiDocsDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteDocument(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiDocsUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateDocument(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\"\n                ]\n            });\n        }\n    });\n}\n// Action hooks\nfunction useApiActionList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"actions\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getActions(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiActionRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"actions\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAction(id),\n        enabled: !!id\n    });\n}\nfunction useApiActionCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createAction(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateAction(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateAction(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteAction(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\n// ArbitratedTheme hooks\nfunction useApiArbitratedThemeList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrated-themes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitratedThemes(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiArbitratedThemeRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrated-themes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitratedTheme(id),\n        enabled: !!id\n    });\n}\nfunction useApiArbitratedThemeCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createArbitratedTheme(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemeUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitratedTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemePartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitratedTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemeDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteArbitratedTheme(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./hooks/useNextApi.ts\n"));

/***/ })

});