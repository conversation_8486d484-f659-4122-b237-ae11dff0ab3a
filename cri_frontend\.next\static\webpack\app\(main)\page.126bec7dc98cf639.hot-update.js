"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/page",{

/***/ "(app-client)/./app/(main)/page.tsx":
/*!*****************************!*\
  !*** ./app/(main)/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_chart__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/chart */ \"(app-client)/./node_modules/primereact/chart/chart.esm.js\");\n/* harmony import */ var primereact_column__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/column */ \"(app-client)/./node_modules/primereact/column/column.esm.js\");\n/* harmony import */ var primereact_datatable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/datatable */ \"(app-client)/./node_modules/primereact/datatable/datatable.esm.js\");\n/* harmony import */ var primereact_menu__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/menu */ \"(app-client)/./node_modules/primereact/menu/menu.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../layout/context/layoutcontext */ \"(app-client)/./layout/context/layoutcontext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-client)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_service_ProductService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/service/ProductService */ \"(app-client)/./utilities/service/ProductService.tsx\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* eslint-disable @next/next/no-img-element */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst lineData = {\n    labels: [\n        \"January\",\n        \"February\",\n        \"March\",\n        \"April\",\n        \"May\",\n        \"June\",\n        \"July\"\n    ],\n    datasets: [\n        {\n            label: \"First Dataset\",\n            data: [\n                65,\n                59,\n                80,\n                81,\n                56,\n                55,\n                40\n            ],\n            fill: false,\n            backgroundColor: \"#2f4860\",\n            borderColor: \"#2f4860\",\n            tension: 0.4\n        },\n        {\n            label: \"Second Dataset\",\n            data: [\n                28,\n                48,\n                40,\n                19,\n                86,\n                27,\n                90\n            ],\n            fill: false,\n            backgroundColor: \"#00bb7e\",\n            borderColor: \"#00bb7e\",\n            tension: 0.4\n        }\n    ]\n};\nconst Dashboard = ()=>{\n    var _recommendations_data, _recommendations_data1, _recommendations_data2, _recommendations;\n    _s();\n    // Fetch data using specific hooks\n    const { data: recommendations, isLoading: recommendationsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiRecommendationList)({\n        limit: 10\n    });\n    const { data: plans, isLoading: plansLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiPlanList)({\n        limit: 5\n    });\n    const { data: missions, isLoading: missionsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiMissionList)({\n        limit: 5\n    });\n    console.log(\"[Dashboard]\", recommendations);\n    const menu1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const menu2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [lineOptions, setLineOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { layoutConfig } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__.LayoutContext);\n    const applyLightTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#495057\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    const applyDarkTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#ebedef\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        _utilities_service_ProductService__WEBPACK_IMPORTED_MODULE_4__.ProductService.getProductsSmall().then((data)=>setProducts(data));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (layoutConfig.colorScheme === \"light\") {\n            applyLightTheme();\n        } else {\n            applyDarkTheme();\n        }\n    }, [\n        layoutConfig.colorScheme\n    ]);\n    const formatCurrency = (value)=>{\n        var _value;\n        return (_value = value) === null || _value === void 0 ? void 0 : _value.toLocaleString(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Missions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(missions) ? missions.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-blue-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-briefcase text-blue-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(recommendations) ? recommendations.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-orange-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-thumbs-up-fill text-orange-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-cyan-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-cog text-cyan-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Comments\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: \"152 Unread\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-purple-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-comment text-purple-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Commentaires\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 17\n                            }, undefined),\n                            ((_recommendations_data = recommendations.data) === null || _recommendations_data === void 0 ? void 0 : _recommendations_data.isLoading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_6__.ProgressSpinner, {\n                                style: {\n                                    width: \"50px\",\n                                    height: \"50px\"\n                                },\n                                strokeWidth: \"8\",\n                                fill: \"var(--surface-ground)\",\n                                animationDuration: \".5s\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 53\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                                stripedRows: true,\n                                value: (_recommendations_data1 = recommendations.data) === null || _recommendations_data1 === void 0 ? void 0 : _recommendations_data1.flatMap((rec)=>rec.comments),\n                                rows: 10,\n                                scrollable: true,\n                                paginator: true,\n                                resizableColumns: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"left\",\n                                        field: \"recommendation\",\n                                        body: (data)=>\"\".concat(recommendations.data.data.results.find((rec)=>rec.id === data.recommendation).mission, \" * \").concat(data.recommendation),\n                                        header: \"Recommendation\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"20%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"left\",\n                                        field: \"comment\",\n                                        body: (data)=>data.comment,\n                                        header: \"Commentaire\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"70%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"center\",\n                                        field: \"created\",\n                                        body: (data)=>new Date(data.created).toLocaleString(\"fr\"),\n                                        header: \"Date\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"12%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"center\",\n                                        field: \"created_by\",\n                                        body: (data)=>{\n                                            var _data_created_by, _data_created_by1;\n                                            return data.created_by ? \"\".concat((_data_created_by = data.created_by) === null || _data_created_by === void 0 ? void 0 : _data_created_by.last_name, \" \").concat((_data_created_by1 = data.created_by) === null || _data_created_by1 === void 0 ? void 0 : _data_created_by1.first_name) : \"\";\n                                        },\n                                        header: \"Utilisateur\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"12%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        rowEditor: true,\n                                        header: \"Action\",\n                                        sortableDisabled: true,\n                                        field: \"action\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"6%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-content-between align-items-center mb-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Plans d'actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu1_current;\n                                                    return (_menu1_current = menu1.current) === null || _menu1_current === void 0 ? void 0 : _menu1_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_10__.Menu, {\n                                                ref: menu1,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-none p-0 m-0\",\n                                children: (_recommendations = recommendations) === null || _recommendations === void 0 ? void 0 : (_recommendations_data2 = _recommendations.data) === null || _recommendations_data2 === void 0 ? void 0 : _recommendations_data2.map((rec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-900 font-medium mr-2 mb-1 md:mb-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/recommendations/\" + rec.id,\n                                                                children: [\n                                                                    \"Recommandation n\\xb0 \",\n                                                                    rec.numrecommandation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 90\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 text-600\",\n                                                            children: rec.mission\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 md:mt-0 flex align-items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\",\n                                                            style: {\n                                                                height: \"8px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-orange-500 h-full\",\n                                                                style: {\n                                                                    width: \"\".concat(rec.actions.reduce((accumulator, act)=>accumulator += act.progress || 0, 0), \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 37\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-500 ml-3 font-medium\",\n                                                            children: [\n                                                                \"%\",\n                                                                (rec.actions.reduce((accumulator, act)=>accumulator += act.progress || 0, 0) / rec.actions.length || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Sales Overview\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chart__WEBPACK_IMPORTED_MODULE_11__.Chart, {\n                                type: \"line\",\n                                data: lineData,\n                                options: lineOptions\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu2_current;\n                                                    return (_menu2_current = menu2.current) === null || _menu2_current === void 0 ? void 0 : _menu2_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_10__.Menu, {\n                                                ref: menu2,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"TODAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 mx-0 mt-0 mb-4 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Richard Jones\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a blue t-shirt for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"79$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-orange-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-download text-xl text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-700 line-height-3\",\n                                                children: [\n                                                    \"Your request for withdrawal of \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-500 font-medium\",\n                                                        children: \"2500$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 60\n                                                    }, undefined),\n                                                    \" has been initiated.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"YESTERDAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 m-0 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Keyser Wick\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a black jacket for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"59$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-pink-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-question text-xl text-pink-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Jane Davis\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: \" has posted a new questions about your product.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-5 shadow-2 flex flex-column md:flex-row md:align-items-center justify-content-between mb-3\",\n                        style: {\n                            borderRadius: \"1rem\",\n                            background: \"linear-gradient(0deg, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.5)), linear-gradient(92.54deg, #1C80CF 47.88%, #FFFFFF 100.01%)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100 font-medium text-xl mt-2 mb-3\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white font-medium text-5xl\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 mr-auto md:mt-0 md:mr-0\",\n                                children: \"TEST\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n        lineNumber: 137,\n        columnNumber: 13\n    }, undefined);\n};\n_s(Dashboard, \"dIK6/XOP/w75Cc1eGiU9nkJpz0A=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiRecommendationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiMissionList\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/page.tsx\n"));

/***/ })

});