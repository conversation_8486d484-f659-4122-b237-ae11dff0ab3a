FROM python:3.12-alpine

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
# RUN apt-get update -y 
# RUN  apt-get install -y --no-install-recommends \
#   build-essential \
#   python3-dev \
#   python3-pip \
#   python3-setuptools \
#   python3-wheel \
#   python3-venv \
#   cython3
RUN apk add --no-cache \
  gcc \
  musl-dev \
  libffi-dev \
  make \
  python3-dev \
  py3-pip \
  py3-setuptools \
  py3-wheel
RUN pip config set global.index http://orndevrepos.corp.sonatrach.dz/repository/python/pypi
RUN pip config set global.index-url http://orndevrepos.corp.sonatrach.dz/repository/python/simple
RUN pip config set global.trusted-host orndevrepos.corp.sonatrach.dz

# Allows docker to cache installed dependencies between builds
COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Mounts the application code to the image
COPY . /app

EXPOSE 3001

# RUN python manage.py migrate  
#RUN python manage.py runserver 0.0.0.0:3001

CMD ["python", "manage.py", "runserver", "0.0.0.0:3001"]  