from django.db.models.signals import m2m_changed, post_save, pre_save
from django.db.models import F, Q
from backend.models import *
from django.dispatch import receiver

# @receiver(post_save,sender=Cause)
# def set_numcause(sender,instance,**kwargs):
#     print(instance.constat.all())
#     # print(Constat.objects.filter(Q(constat=instance.constat) ))
#     # numinstance=Cause.objects.filter(Q(constat__in=instance.constat) & Q(constat__mission=instance.constat.mission)).aggregate(numinstance=models.Max('numcause'))['numinstance'] or 0
#     # instance.numcause=numinstance+1

# @receiver(pre_save,sender=Action)
# def set_numaction(sender,instance,**kwargs):
#     numinstance=Action.objects.filter(Q(recommendation=instance.recommendation)&Q(recommendation__mission=instance.recommendation.mission) ).aggregate(numinstance=models.Max('numaction'))['numinstance'] or 0
#     instance.numaction=numinstance+1

@receiver(post_save, sender=Action, dispatch_uid="new_action_notif")
def new_cmt_notif(sender, instance, **kwargs):
    Notification.objects.create(
        user=instance.job_leader,
        object_id=instance.id,
        content_type=ContentType.objects.get_for_model(instance),
    )

# @receiver(post_save, sender=Comment, dispatch_uid="new_cmt_notif")
# def new_cmt_notif(sender, instance, **kwargs):
#     Notification.objects.create(
#         user=instance.user,
#         object_id=instance.id,
#         content_type=ContentType.objects.get_for_model(instance),
#     )

@receiver(pre_save,sender=Constat)
def set_numconstat(sender,instance,**kwargs):
    numinstance=Constat.objects.filter(mission=instance.mission).aggregate(numinstance=models.Max('numconstat'))['numinstance'] or 0
    instance.numconstat=numinstance+1

@receiver(pre_save,sender=Recommendation)
def set_numrecommandation(sender,instance,**kwargs):
    numinstance=Recommendation.objects.filter(mission=instance.mission).aggregate(numinstance=models.Max('numrecommandation'))['numinstance'] or 0
    instance.numrecommandation=numinstance+1

@receiver(pre_save,sender=Cause)
def set_numcause_(sender,instance,**kwargs):
    numinstance=Cause.objects.filter(constat=instance.constat).aggregate(numinstance=models.Max('numcause'))['numinstance'] or 0
    instance.numcause=numinstance+1

@receiver(pre_save,sender=Consequence)
def set_numconsequence_(sender,instance,**kwargs):
    numinstance=Consequence.objects.filter(constat=instance.constat).aggregate(numinstance=models.Max('numconsequence'))['numinstance'] or 0
    instance.numconsequence=numinstance+1