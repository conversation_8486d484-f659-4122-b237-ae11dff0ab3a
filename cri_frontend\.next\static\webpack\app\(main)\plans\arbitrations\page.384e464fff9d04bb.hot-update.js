"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./services/api/nextApi.ts":
/*!*********************************!*\
  !*** ./services/api/nextApi.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nextApiService: function() { return /* binding */ nextApiService; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-client)/./node_modules/next/dist/build/polyfills/process.js\");\n// Next.js API Service to replace Django API calls\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"/api\";\nclass NextApiService {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const { method = \"GET\", headers = {}, body } = options;\n        const config = {\n            method,\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...headers\n            }\n        };\n        if (body && method !== \"GET\") {\n            config.body = JSON.stringify(body);\n        }\n        const response = await fetch(\"\".concat(API_BASE_URL).concat(endpoint), config);\n        if (!response.ok) {\n            throw new Error(\"API Error: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        return response.json();\n    }\n    // Mission API methods\n    async getMissions(params) {\n        var _params, _params1, _params2;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        const query = searchParams.toString();\n        return this.request(\"/missions\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getMission(id) {\n        return this.request(\"/missions/\".concat(id));\n    }\n    async createMission(data) {\n        return this.request(\"/missions\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateMission(id, data) {\n        return this.request(\"/missions/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteMission(id) {\n        return this.request(\"/missions/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Recommendation API methods\n    async getRecommendations(params) {\n        var _params, _params1, _params2, _params3;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.missionId) searchParams.append(\"missionId\", params.missionId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/recommendations\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getRecommendation(id) {\n        return this.request(\"/recommendations/\".concat(id));\n    }\n    async createRecommendation(data) {\n        return this.request(\"/recommendations\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateRecommendation(id, data) {\n        return this.request(\"/recommendations/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteRecommendation(id) {\n        return this.request(\"/recommendations/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // User API methods\n    async getUsers(params) {\n        var _params, _params1, _params2;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        const query = searchParams.toString();\n        return this.request(\"/users\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getUser(id) {\n        return this.request(\"/users/\".concat(id));\n    }\n    async createUser(data) {\n        return this.request(\"/users\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    // Plan API methods\n    async getPlans(params) {\n        var _params, _params1, _params2, _params3;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.exercise) searchParams.append(\"exercise\", params.exercise.toString());\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.type) searchParams.append(\"type\", params.type);\n        const query = searchParams.toString();\n        return this.request(\"/plans\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getPlan(id) {\n        return this.request(\"/plans/\".concat(id));\n    }\n    async createPlan(data) {\n        return this.request(\"/plans\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updatePlan(id, data) {\n        return this.request(\"/plans/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deletePlan(id) {\n        return this.request(\"/plans/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Theme API methods\n    async getThemes(params) {\n        var _params, _params1, _params2, _params3, _params4;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if (((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.validated) !== undefined) searchParams.append(\"validated\", params.validated.toString());\n        if ((_params4 = params) === null || _params4 === void 0 ? void 0 : _params4.domainId) searchParams.append(\"domainId\", params.domainId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/themes\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getTheme(id) {\n        return this.request(\"/themes/\".concat(id));\n    }\n    async createTheme(data) {\n        return this.request(\"/themes\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateTheme(id, data) {\n        return this.request(\"/themes/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteTheme(id) {\n        return this.request(\"/themes/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Arbitration API methods\n    async getArbitrations(params) {\n        var _params, _params1, _params2, _params3;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.planId) searchParams.append(\"planId\", params.planId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/arbitrations\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getArbitration(id) {\n        return this.request(\"/arbitrations/\".concat(id));\n    }\n    async createArbitration(data) {\n        return this.request(\"/arbitrations\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateArbitration(id, data) {\n        return this.request(\"/arbitrations/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteArbitration(id) {\n        return this.request(\"/arbitrations/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Comment API methods\n    async getComments(params) {\n        var _params, _params1, _params2, _params3, _params4, _params5;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.recommendationId) searchParams.append(\"recommendationId\", params.recommendationId.toString());\n        if ((_params4 = params) === null || _params4 === void 0 ? void 0 : _params4.missionId) searchParams.append(\"missionId\", params.missionId.toString());\n        if ((_params5 = params) === null || _params5 === void 0 ? void 0 : _params5.userId) searchParams.append(\"userId\", params.userId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/comments\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getComment(id) {\n        return this.request(\"/comments/\".concat(id));\n    }\n    async createComment(data) {\n        return this.request(\"/comments\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateComment(id, data) {\n        return this.request(\"/comments/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteComment(id) {\n        return this.request(\"/comments/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Document API methods\n    async uploadDocument(data) {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/documents\"), {\n            method: \"POST\",\n            credentials: \"include\",\n            body: data\n        });\n        if (!response.ok) {\n            throw new Error(\"Upload Error: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        return response.json();\n    }\n    async uploadMissionDocuments(missionId, data) {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/missions/\").concat(missionId, \"/documents\"), {\n            method: \"POST\",\n            credentials: \"include\",\n            body: data\n        });\n        if (!response.ok) {\n            throw new Error(\"Upload Error: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        return response.json();\n    }\n    async getMissionDocuments(missionId) {\n        return this.request(\"/missions/\".concat(missionId, \"/documents\"));\n    }\n    async getDocuments(params) {\n        var _params, _params1;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.missionId) searchParams.append(\"missionId\", params.missionId.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.context) searchParams.append(\"context\", params.context);\n        const query = searchParams.toString();\n        return this.request(\"/documents\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async deleteDocument(id) {\n        return this.request(\"/documents/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    async updateDocument(id, data) {\n        return this.request(\"/documents/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    // Action API methods\n    async getActions(params) {\n        var _params, _params1, _params2, _params3, _params4;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.recommendationId) searchParams.append(\"recommendationId\", params.recommendationId.toString());\n        if ((_params4 = params) === null || _params4 === void 0 ? void 0 : _params4.status) searchParams.append(\"status\", params.status);\n        const query = searchParams.toString();\n        return this.request(\"/actions\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getAction(id) {\n        return this.request(\"/actions/\".concat(id));\n    }\n    async createAction(data) {\n        return this.request(\"/actions\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateAction(id, data) {\n        return this.request(\"/actions/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteAction(id) {\n        return this.request(\"/actions/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // ArbitratedTheme API methods\n    async getArbitratedThemes(params) {\n        var _params, _params1, _params2, _params3, _params4;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.arbitrationId) searchParams.append(\"arbitrationId\", params.arbitrationId.toString());\n        if ((_params4 = params) === null || _params4 === void 0 ? void 0 : _params4.themeId) searchParams.append(\"themeId\", params.themeId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/arbitrated-themes\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getArbitratedTheme(id) {\n        return this.request(\"/arbitrated-themes/\".concat(id));\n    }\n    async createArbitratedTheme(data) {\n        return this.request(\"/arbitrated-themes\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateArbitratedTheme(id, data) {\n        return this.request(\"/arbitrated-themes/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteArbitratedTheme(id) {\n        return this.request(\"/arbitrated-themes/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n}\nconst nextApiService = new NextApiService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (nextApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./services/api/nextApi.ts\n"));

/***/ })

});