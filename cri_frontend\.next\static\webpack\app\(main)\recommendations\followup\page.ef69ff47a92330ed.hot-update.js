"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/recommendations/followup/page",{

/***/ "(app-client)/./app/(main)/recommendations/(components)/GenericTAbleFollowUp.tsx":
/*!**************************************************************************!*\
  !*** ./app/(main)/recommendations/(components)/GenericTAbleFollowUp.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTableRecommendationFolluwUp; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-client)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _editForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./editForm */ \"(app-client)/./app/(main)/recommendations/(components)/editForm.tsx\");\n/* harmony import */ var _followup_recommendation_id_page__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../followup/[recommendation_id]/page */ \"(app-client)/./app/(main)/recommendations/followup/[recommendation_id]/page.tsx\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var primereact_datatable__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! primereact/datatable */ \"(app-client)/./node_modules/primereact/datatable/datatable.esm.js\");\n/* harmony import */ var primereact_column__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/column */ \"(app-client)/./node_modules/primereact/column/column.esm.js\");\n/* harmony import */ var primereact_progressbar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/progressbar */ \"(app-client)/./node_modules/primereact/progressbar/progressbar.esm.js\");\n/* harmony import */ var primereact_overlaypanel__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! primereact/overlaypanel */ \"(app-client)/./node_modules/primereact/overlaypanel/overlaypanel.esm.js\");\n/* harmony import */ var primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! primereact/inputtextarea */ \"(app-client)/./node_modules/primereact/inputtextarea/inputtextarea.esm.js\");\n/* harmony import */ var _CommentActionDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CommentActionDialog */ \"(app-client)/./app/(main)/recommendations/(components)/CommentActionDialog.tsx\");\n/* harmony import */ var primereact_badge__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/badge */ \"(app-client)/./node_modules/primereact/badge/badge.esm.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(app-client)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"(app-client)/./node_modules/@fortawesome/free-regular-svg-icons/index.mjs\");\n/* harmony import */ var primereact_togglebutton__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/togglebutton */ \"(app-client)/./node_modules/primereact/togglebutton/togglebutton.esm.js\");\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTableRecommendationFolluwUp(data_) {\n    var _getCookie;\n    _s();\n    // const ReactJson = useMemo(() => dynamic(() => import('react-json-view'), { ssr: false }), []);\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_7__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const { push } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const planActionDataTableRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const overlayProofPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const overlayValidationAcceptationPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const [overlayValidationAcceptationTitle, setOverlayValidationAcceptationTitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [recommendation_id, setRecommendationId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [recommendation$, setRecommendation$] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [action_comment, setActionComment] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [action_comments, setActionComments] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [actionId, setActionId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [detailsDialogVisible, setDetailsDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [actionCommentDialogVisible, setActionCommentDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const [clickEvent, setClickEvent] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const open = Boolean(anchorEl);\n    // Hooks\n    const { data: comment_create_data, isPending: comment_create_isMutating, error: comment_create_error, mutate: trigger_comment_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiCommentCreate)();\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationCreate)();\n    const { data: data_modify, error: error_modify, isPending: isMutating_modify, mutate: trigger_modify } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationPartialUpdate)();\n    const { data: data_delete, error: error_delete, isPending: isMutating_delete, mutate: trigger_delete } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationDestroy)();\n    const { data: data_action_update, error: error_action_update, isPending: isMutating_action_update, mutate: trigger_action_update } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiActionPartialUpdate)();\n    // Functions\n    function onPaginationChange(state) {\n        console.log(\"PAGINATION\", data_.pagination);\n        data_.pagination.set(state);\n    }\n    function updateActionAcceptation(e, actionId) {\n        setActionId(actionId);\n        // setClickEvent(e);\n        if (!e.value) {\n            setOverlayValidationAcceptationTitle(\"Acceptation : \".concat(actionId));\n            overlayValidationAcceptationPanelRef.current.toggle(e.originalEvent);\n        } else {\n            trigger_action_update({\n                id: actionId,\n                data: {\n                    accepted: true\n                }\n            });\n        }\n    }\n    function updateActionValidation(e, actionId) {\n        setActionId(actionId);\n        if (!e.value) {\n            setOverlayValidationAcceptationTitle(\"Validation : \".concat(actionId));\n            overlayValidationAcceptationPanelRef.current.toggle(e.originalEvent);\n        } else {\n            trigger_action_update({\n                id: actionId,\n                data: {\n                    validated: true\n                }\n            });\n        }\n    }\n    function attachementViewProofClick(event) {\n        overlayProofPanelRef.current.toggle(event);\n    }\n    function addCommentClick(event, action_id, recommendation_id_) {\n        var _data__data__data_results_find_actions_find, _data__data__data_results_find;\n        setActionId(action_id);\n        console.log(action_id, recommendation_id_);\n        console.log(data_.data_.data.results);\n        console.log(data_.data_.data.results.filter((recommendation)=>recommendation.id === recommendation_id_));\n        setActionComments((_data__data__data_results_find = data_.data_.data.results.find((recommendation)=>recommendation.id === recommendation_id_)) === null || _data__data__data_results_find === void 0 ? void 0 : (_data__data__data_results_find_actions_find = _data__data__data_results_find.actions.find((action)=>action.id === action_id)) === null || _data__data__data_results_find_actions_find === void 0 ? void 0 : _data__data__data_results_find_actions_find.comments.sort((a, b)=>new Date(b.created).getTime() - new Date(a.created).getTime()));\n        if (!actionCommentDialogVisible) setActionCommentDialogVisible(true);\n    // overlayProofPanelRef.current.toggle(event);\n    }\n    const accept = ()=>{\n        trigger_delete({\n            id: actionId\n        }, {\n            onSuccess: ()=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"Enregistrement supprim\\xe9\"\n                });\n            },\n            onError: (error)=>{\n                var _toast_current;\n                return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                    severity: \"info\",\n                    summary: \"Suppression\",\n                    detail: \"\".concat(error.code)\n                });\n            }\n        });\n    };\n    const reject = ()=>{\n        toast.current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        console.log(\"recommendation_id\", recommendation_id);\n    }, [\n        recommendation_id,\n        actionId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n    // updateActionAcceptation(clickEvent,actionId)\n    }, [\n        actionId\n    ]);\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"causes\",\n                \"comments\",\n                \"constats\",\n                \"actions\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_1__.Editor, {\n                            onChange: (e)=>{\n                                row._valuesCache.content = e.target.getContent();\n                            },\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    accessorFn: (row)=>new Date(row[key]),\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            if (key === \"concerned_structure\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    accessorFn: (row)=>row[key].code_mnemonique,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            style: {\n                                fontSize: 12,\n                                fontWeight: \"bold\",\n                                fontFamily: \"monospace\",\n                                color: \"var(--text-color)\",\n                                background: \"transparent\",\n                                border: \" 2px dotted green\",\n                                borderRadius: 50\n                            },\n                            label: cell.getValue() || row.original.concerned_structure.code_stru\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"validated\") {\n                var _data__data_type_properties_key_title3;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            severity: cell.getValue() ? \"success\" : \"danger\",\n                            icon: cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__.FontAwesomeIcon, {\n                                size: \"2x\",\n                                icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faSquareCheck\n                            }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__.FontAwesomeIcon, {\n                                size: \"2x\",\n                                icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faRectangleXmark\n                            }, void 0, false, void 0, void 0),\n                            value: \"\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"accepted\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            severity: cell.getValue() ? \"success\" : \"danger\",\n                            icon: cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__.FontAwesomeIcon, {\n                                size: \"2x\",\n                                icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faCircleCheck\n                            }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__.FontAwesomeIcon, {\n                                size: \"2x\",\n                                icon: _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faCircleXmark\n                            }, void 0, false, void 0, void 0),\n                            value: \"\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"priority\") {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            className: \"w-9rem text-md\",\n                            severity: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_10__.getPrioritySeverity)(cell.getValue()),\n                            value: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"recommendation\") {\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    accessorKey: key,\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"white-space-normal\",\n                            children: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title7;\n                return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0][\"$ref\"] ? data_.data_type.properties[key].allOf[0][\"$ref\"].enum : [],\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            severity: cell.getValue() === \"VP\" ? \"danger\" : cell.getValue() === \"STRUCT\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"VP\" ? \"Vice Pr\\xe9sident\" : cell.getValue() === \"STRUCT\" ? \"Structures\" : \"Contr\\xf4le Interne\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"status\") {\n                var _data__data_type_properties_key_title8;\n                return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.getValue() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            severity: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_10__.getStatusSeverity)(cell.getValue()),\n                            value: cell.getValue().toUpperCase(),\n                            className: \"w-9rem text-md\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 64\n                        }, this) : \"N/D\";\n                    }\n                };\n            }\n            if (key === \"type\") {\n                var _data__data_type_properties_key_title9;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title9 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title9 !== void 0 ? _data__data_type_properties_key_title9 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            severity: cell.getValue() === \"Command\\xe9e\" ? \"danger\" : cell.getValue() === \"Planifi\\xe9e\" ? \"warning\" : cell.getValue() === \"AI\" ? \"info\" : \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title10;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title10 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title10 !== void 0 ? _data__data_type_properties_key_title10 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"mission\") {\n                var _data__data_type_properties_key_title11;\n                return {\n                    header: (_data__data_type_properties_key_title11 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title11 !== void 0 ? _data__data_type_properties_key_title11 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            className: \"w-full\",\n                            style: {\n                                fontSize: 12,\n                                fontFamily: \"monospace\",\n                                color: \"var(--text-color)\",\n                                background: \"transparent\",\n                                border: \" 2px solid orange\"\n                            },\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"theme\") {\n                var _data__data_type_properties_key_title12;\n                return {\n                    header: (_data__data_type_properties_key_title12 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title12 !== void 0 ? _data__data_type_properties_key_title12 : key,\n                    accessorKey: \"theme.theme.title\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key\n                };\n            }\n            if (key === \"numrecommandation\") {\n                var _data__data_type_properties_key_title13;\n                return {\n                    header: (_data__data_type_properties_key_title13 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title13 !== void 0 ? _data__data_type_properties_key_title13 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_badge__WEBPACK_IMPORTED_MODULE_16__.Badge, {\n                            style: {\n                                background: \"transparent\",\n                                border: \"2px solid var(--primary-500)\",\n                                fontSize: 12,\n                                fontWeight: \"bold\",\n                                fontFamily: \"monospace\",\n                                color: \"var(--text-color)\"\n                            },\n                            size: \"large\",\n                            value: \"\".concat(cell.getValue()),\n                            severity: \"warning\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key][\"$ref\"] && data_.data_type.properties[key][\"$ref\"].enum) {\n                var _data__data_type_properties_key_title14;\n                return {\n                    header: (_data__data_type_properties_key_title14 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title14 !== void 0 ? _data__data_type_properties_key_title14 : key,\n                    // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+\" ,\"; }, \"\"):\"\",\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.row.original[key];\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        children: data_.data_type.properties[key][\"$ref\"].enum,\n                        SelectProps: {\n                        }\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title15, _data__data_type_properties_key_title16;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title15 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title15 !== void 0 ? _data__data_type_properties_key_title15 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title16 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title16 !== void 0 ? _data__data_type_properties_key_title16 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    // const actionBodyTemplate = (rowData: any, options: ColumnBodyOptions) => {\n    //     return (\n    //         <React.Fragment>\n    //             {!options.rowEditor?.editing && <Button icon=\"pi pi-pencil\" rounded outlined severity=\"info\" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.element?.props.onClick(e) }} />}\n    //             {options.rowEditor?.editing && <Button icon=\"pi pi-save\" rounded outlined severity=\"success\" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.onSaveClick(e) }} />}\n    //             {options.rowEditor?.editing && <Button icon=\"pi pi-times\" rounded outlined severity=\"info\" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.onCancelClick(e) }} />}\n    //             {!options.rowEditor?.editing && <Button icon=\"pi pi-trash\" rounded outlined severity=\"danger\" onClick={(e) => { setCommentID(rowData.id); setCommentID(rowData.id); confirm1(e, rowData.id) }} />}\n    //         </React.Fragment>\n    //     );\n    // }\n    const generateRecommandationActionsColumns = ()=>{\n        let columns = [];\n        for (const [key, value] of Object.entries($Action.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"created_by\",\n                \"dependencies\",\n                \"modified_by\",\n                \"created\",\n                \"modified\",\n                \"id\"\n            ].includes(key);\n        })){\n            if (key === \"description\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n                    field: key,\n                    body: (data)=>data.description,\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 564,\n                    columnNumber: 22\n                }, this));\n            } else if (key === \"job_leader\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n                    field: key,\n                    body: (data)=>{\n                        var _data_job_leader, _data_job_leader1;\n                        return \"\".concat((_data_job_leader = data.job_leader) === null || _data_job_leader === void 0 ? void 0 : _data_job_leader.last_name, \" \").concat((_data_job_leader1 = data.job_leader) === null || _data_job_leader1 === void 0 ? void 0 : _data_job_leader1.first_name);\n                    },\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 567,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"start_date\",\n                \"end_date\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n                    field: key,\n                    body: (data)=>new Date(data[key]).toLocaleDateString(),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"progress\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressbar__WEBPACK_IMPORTED_MODULE_18__.ProgressBar, {\n                            value: data[key]\n                        }, void 0, false, void 0, void 0),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"status\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_14__.Tag, {\n                            className: \"w-7rem\",\n                            value: data[key].toUpperCase()\n                        }, void 0, false, void 0, void 0),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 579,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"accepted\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_togglebutton__WEBPACK_IMPORTED_MODULE_19__.ToggleButton, {\n                            checked: data.accepted,\n                            onLabel: \"Oui\",\n                            offLabel: \"Non\",\n                            onIcon: \"pi pi-check\",\n                            offIcon: \"pi pi-times\",\n                            onChange: (e)=>{\n                                updateActionAcceptation(e, data.id);\n                            }\n                        }, void 0, false, void 0, void 0),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 582,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"validated\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_togglebutton__WEBPACK_IMPORTED_MODULE_19__.ToggleButton, {\n                            checked: data.validated,\n                            onLabel: \"Oui\",\n                            offLabel: \"Non\",\n                            onIcon: \"pi pi-check\",\n                            offIcon: \"pi pi-times\",\n                            onChange: (e)=>{\n                                updateActionValidation(e, data.id);\n                            }\n                        }, void 0, false, void 0, void 0),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 22\n                }, this));\n            } else if ([\n                \"proof\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                            severity: \"warning\",\n                            icon: \"pi pi-paperclip\",\n                            onClick: attachementViewProofClick\n                        }, void 0, false, void 0, void 0),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 22\n                }, this));\n            }\n        }\n        var _data_comments_length;\n        columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_17__.Column, {\n            align: \"center\",\n            field: \"comment\",\n            body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                    rounded: true,\n                    outlined: true,\n                    severity: \"info\",\n                    icon: \"pi pi-comments\",\n                    onClick: (e)=>{\n                        console.log(\"addCommentClick\", data);\n                        addCommentClick(e, data.id, data.recommendation);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_badge__WEBPACK_IMPORTED_MODULE_16__.Badge, {\n                        value: (_data_comments_length = data.comments.length) !== null && _data_comments_length !== void 0 ? _data_comments_length : 0,\n                        severity: \"danger\"\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0),\n            header: \"Commentaires\",\n            sortable: true,\n            style: {\n                width: \"25%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n            lineNumber: 593,\n            columnNumber: 18\n        }, this));\n        return columns;\n    };\n    console.log(\"#######################\", data_);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_21__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data.results ? data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading,\n            showProgressBars: data_.isLoading,\n            isSaving: isMutating_create\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_22__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 160,\n                enableHiding: true,\n                grow: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiSvgIcon-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiInputBase-input\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiTableSortLabel-root\": {\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            //validate data\n            //save data to api\n            const { created, modified, id, mission, recommendation, concerned_structure, causes, actions_add, ...rest } = values;\n            // values._changedValues.actions= values._changedValues.actions.filter(action => action.id === undefined)\n            values._changedValues.actions.forEach((action)=>action.job_leader = action.job_leader.id);\n            console.log(\"onEditingRowSave\", values, rest, values._changedValues);\n            trigger_modify({\n                id: id,\n                data: values._changedValues\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"Enregistrement modifi\\xe9\"\n                    });\n                },\n                onError: (error)=>{\n                    var _toast_current;\n                    return (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"\".concat(error.message)\n                    });\n                }\n            });\n            table.setEditingRow(null); //exit editing mode\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            const { created, modified, id, ...rest } = values;\n            values.actions = values.actions.filter((action)=>action.id === null || action.id === undefined).forEach((action)=>action.job_leader = action.job_leader.id);\n            console.log(\"onCreatingRowSave\", values);\n            trigger_create({\n                data: values\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.statusText)\n                    });\n                    console.log(\"onCreatingRowSave\", err.response);\n                    row._valuesCache = {\n                        error: err.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n        //clear any validation errors\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                    },\n                    \"& .MuiDialog-container\": {\n                        // display: 'none'\n                        \"& .MuiPaper-root\": {\n                            maxWidth: \"50vw\"\n                        }\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 846,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 862,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 861,\n                        columnNumber: 9\n                    }, this),\n                    isMutating_action_update && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_24__.ProgressSpinner, {\n                        style: {\n                            width: \"32px\",\n                            height: \"32px\"\n                        },\n                        strokeWidth: \"8\",\n                        fill: \"var(--surface-ground)\",\n                        animationDuration: \".5s\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 875,\n                        columnNumber: 38\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 845,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        renderEditRowDialogContent: _editForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        renderDetailPanel: (param)=>/*#__PURE__*/ {\n            let { row } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                sx: {\n                    display: \"grid\",\n                    margin: \"auto\",\n                    //gridTemplateColumns: '1fr 1fr',\n                    width: \"100vw\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_26__.TabView, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_26__.TabPanel, {\n                        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex align-items-center justify-content-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                                    tooltip: \"Ajouter/Editer les actions\",\n                                    size: \"small\",\n                                    icon: \"pi pi-plus\",\n                                    onClick: ()=>{\n                                        row._valuesCache.actions_add = true;\n                                        setRecommendationId(row.original.id);\n                                        table.setEditingRow(row);\n                                        setEditVisible(true), console.log(\"editing row ...\");\n                                    },\n                                    rounded: true,\n                                    outlined: true\n                                }, void 0, false, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Plan d'actions\"\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        rightIcon: \"pi pi-thumbs-up ml-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_27__.DataTable, {\n                                ref: planActionDataTableRef,\n                                onRowClick: (event)=>setActionId(event.data.id),\n                                tableStyle: {\n                                    maxWidth: \"70vw\"\n                                },\n                                value: row.original.actions,\n                                rows: 5,\n                                paginator: true,\n                                resizableColumns: true,\n                                responsiveLayout: \"scroll\",\n                                children: generateRecommandationActionsColumns()\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: actionId\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 905,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_overlaypanel__WEBPACK_IMPORTED_MODULE_28__.OverlayPanel, {\n                                showCloseIcon: true,\n                                closeOnEscape: true,\n                                dismissable: false,\n                                className: \"grid w-3\",\n                                pt: {\n                                    content: {\n                                        className: \"w-full h-15rem\t\"\n                                    },\n                                    closeButton: {\n                                        style: {\n                                            left: \"-1rem\",\n                                            right: \"0\"\n                                        }\n                                    }\n                                },\n                                ref: overlayProofPanelRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_29__.InputTextarea, {\n                                                rows: 5,\n                                                className: \"w-full\",\n                                                placeholder: \"Commentaire\",\n                                                onChange: (e)=>{\n                                                    setActionComment(e.target.value);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                                                className: \"w-full\",\n                                                icon: \"pi pi-save\",\n                                                onClick: (e)=>{\n                                                    trigger_comment_create({\n                                                        recommendation: null,\n                                                        action: actionId,\n                                                        comment: action_comment,\n                                                        type: \"\"\n                                                    }, {\n                                                        revalidate: true,\n                                                        populateCache: true\n                                                    });\n                                                    overlayProofPanelRef.current.toggle(e);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                                lineNumber: 927,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 907,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_overlaypanel__WEBPACK_IMPORTED_MODULE_28__.OverlayPanel, {\n                                showCloseIcon: true,\n                                closeOnEscape: true,\n                                dismissable: false,\n                                className: \"grid w-3\",\n                                pt: {\n                                    content: {\n                                        className: \"w-full h-18rem\t\"\n                                    },\n                                    closeButton: {\n                                        style: {\n                                            left: \"-1rem\",\n                                            right: \"0\"\n                                        }\n                                    }\n                                },\n                                ref: overlayValidationAcceptationPanelRef,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: overlayValidationAcceptationTitle\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 952,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_29__.InputTextarea, {\n                                                rows: 5,\n                                                className: \"w-full\",\n                                                placeholder: \"Commentaire\",\n                                                onChange: (e)=>{\n                                                    setActionComment(e.target.value);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                                lineNumber: 956,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-12 lg:col-12 xl:col-12 cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                                                className: \"w-full\",\n                                                icon: \"pi pi-save\",\n                                                onClick: (e)=>{\n                                                    trigger_comment_create({\n                                                        data: {\n                                                            recommendation: null,\n                                                            action: actionId,\n                                                            comment: action_comment,\n                                                            type: overlayValidationAcceptationTitle.includes(\"Validation\") ? \"Validation\" : \"Acceptation\",\n                                                            value: overlayValidationAcceptationTitle.includes(\"Validation\") ? \"Non Valid\\xe9\" : \"Non Accept\\xe9\"\n                                                        }\n                                                    });\n                                                    trigger_action_update(overlayValidationAcceptationTitle.includes(\"Validation\") ? {\n                                                        id: actionId,\n                                                        data: {\n                                                            validated: false\n                                                        }\n                                                    } : {\n                                                        id: actionId,\n                                                        data: {\n                                                            accepted: false\n                                                        }\n                                                    });\n                                                    overlayValidationAcceptationPanelRef.current.toggle(e);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommentActionDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                visible: actionCommentDialogVisible,\n                                setVisible: setActionCommentDialogVisible,\n                                action_comments: action_comments,\n                                action_id: actionId\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 899,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                    lineNumber: 898,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 890,\n                columnNumber: 7\n            }, this);\n        },\n        // Les buttons action de la ligne (row)\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                            tooltip: \"voir d\\xe9tails recommandation\",\n                            size: \"small\",\n                            icon: \"pi pi-eye\",\n                            onClick: ()=>{\n                                setRecommendationId(row.original.id);\n                                setRecommendation$(row.original);\n                                setDetailsDialogVisible(true);\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 42\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 986,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                            tooltip: \"Modifier recommandation\",\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                row._valuesCache.actions_add = false;\n                                setRecommendationId(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true), console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 987,\n                            columnNumber: 42\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 987,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_11__.Can, {\n                        I: \"view\",\n                        a: \"recommendation\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_20__.Button, {\n                            tooltip: \"Supprimer recommandation\",\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>{\n                                setRecommendationId(row.original.id);\n                                (0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_30__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept,\n                                    reject\n                                });\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 42\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_30__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                        lineNumber: 1003,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 985,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_21__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 1010,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_31__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 1011,\n                columnNumber: 7\n            }, this),\n            recommendation$ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_followup_recommendation_id_page__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                params: {\n                    recommendation: recommendation$,\n                    detailsDialogVisible,\n                    setDetailsDialogVisible\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\GenericTAbleFollowUp.tsx\",\n                lineNumber: 1012,\n                columnNumber: 27\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTableRecommendationFolluwUp, \"sjdK9xF+VM1Y6D3Wg1oeXHgcBLk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiCommentCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationPartialUpdate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiRecommendationDestroy,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_12__.useApiActionPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_21__.useMaterialReactTable\n    ];\n});\n_c = GenericTableRecommendationFolluwUp;\nvar _c;\n$RefreshReg$(_c, \"GenericTableRecommendationFolluwUp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/recommendations/(components)/GenericTAbleFollowUp.tsx\n"));

/***/ })

});