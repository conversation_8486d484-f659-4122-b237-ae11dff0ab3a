import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/missions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    
    const skip = (page - 1) * limit
    
    const where = search
      ? {
          OR: [
            { code: { contains: search, mode: 'insensitive' as const } },
            { exercise: { contains: search, mode: 'insensitive' as const } },
            { type: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : {}
    
    const [missions, total] = await Promise.all([
      prisma.mission.findMany({
        where,
        skip,
        take: limit,
        include: {
          plan: true,
          theme: {
            include: {
              theme: {
                include: {
                  domain: true,
                  process: true,
                },
              },
            },
          },
          head: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          supervisor: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          staff: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          assistants: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          documents: true,
          recommendations: {
            include: {
              concernedStructure: true,
              comments: true,
              actions: true,
            },
          },
          constats: {
            include: {
              facts: true,
              causes: true,
              consequences: true,
            },
          },
        },
        orderBy: { id: 'desc' },
      }),
      prisma.mission.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: missions,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching missions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/missions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      planId,
      exercise,
      type,
      code,
      etat,
      startDate,
      endDate,
      themeId,
      headId,
      supervisorId,
      staff,
      assistants,
    } = body
    
    const mission = await prisma.mission.create({
      data: {
        planId,
        exercise,
        type,
        code,
        etat,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        themeId,
        headId,
        supervisorId,
        staff: staff ? { connect: staff.map((id: number) => ({ id })) } : undefined,
        assistants: assistants ? { connect: assistants.map((id: number) => ({ id })) } : undefined,
      },
      include: {
        plan: true,
        theme: {
          include: {
            theme: {
              include: {
                domain: true,
                process: true,
              },
            },
          },
        },
        head: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        supervisor: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        staff: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        assistants: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    })
    
    return NextResponse.json(mission, { status: 201 })
  } catch (error) {
    console.error('Error creating mission:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
