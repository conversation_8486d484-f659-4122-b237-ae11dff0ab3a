"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/page",{

/***/ "(app-client)/./app/(main)/page.tsx":
/*!*****************************!*\
  !*** ./app/(main)/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_chart__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/chart */ \"(app-client)/./node_modules/primereact/chart/chart.esm.js\");\n/* harmony import */ var primereact_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/menu */ \"(app-client)/./node_modules/primereact/menu/menu.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../layout/context/layoutcontext */ \"(app-client)/./layout/context/layoutcontext.tsx\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* eslint-disable @next/next/no-img-element */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst lineData = {\n    labels: [\n        \"January\",\n        \"February\",\n        \"March\",\n        \"April\",\n        \"May\",\n        \"June\",\n        \"July\"\n    ],\n    datasets: [\n        {\n            label: \"First Dataset\",\n            data: [\n                65,\n                59,\n                80,\n                81,\n                56,\n                55,\n                40\n            ],\n            fill: false,\n            backgroundColor: \"#2f4860\",\n            borderColor: \"#2f4860\",\n            tension: 0.4\n        },\n        {\n            label: \"Second Dataset\",\n            data: [\n                28,\n                48,\n                40,\n                19,\n                86,\n                27,\n                90\n            ],\n            fill: false,\n            backgroundColor: \"#00bb7e\",\n            borderColor: \"#00bb7e\",\n            tension: 0.4\n        }\n    ]\n};\nconst Dashboard = ()=>{\n    _s();\n    // Fetch data using specific hooks\n    const { data: recommendations, isLoading: recommendationsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiRecommendationList)({\n        limit: 10\n    });\n    const { data: plans, isLoading: plansLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiPlanList)({\n        limit: 5\n    });\n    const { data: missions, isLoading: missionsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiMissionList)({\n        limit: 5\n    });\n    console.log(\"[Dashboard]\", recommendations);\n    const menu1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const menu2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [lineOptions, setLineOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { layoutConfig } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__.LayoutContext);\n    const applyLightTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#495057\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    const applyDarkTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#ebedef\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    // Removed ProductService call as it's not needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (layoutConfig.colorScheme === \"light\") {\n            applyLightTheme();\n        } else {\n            applyDarkTheme();\n        }\n    }, [\n        layoutConfig.colorScheme\n    ]);\n    const formatCurrency = (value)=>{\n        var _value;\n        return (_value = value) === null || _value === void 0 ? void 0 : _value.toLocaleString(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Missions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(missions) ? missions.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-blue-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-briefcase text-blue-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(recommendations) ? recommendations.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-orange-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-thumbs-up-fill text-orange-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-cyan-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-cog text-cyan-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Comments\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: \"152 Unread\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-purple-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-comment text-purple-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Commentaires\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 17\n                            }, undefined),\n                            recommendationsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_4__.ProgressSpinner, {\n                                style: {\n                                    width: \"50px\",\n                                    height: \"50px\"\n                                },\n                                strokeWidth: \"8\",\n                                fill: \"var(--surface-ground)\",\n                                animationDuration: \".5s\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 44\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-600\",\n                                children: \"Commentaires r\\xe9cents seront affich\\xe9s ici.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-content-between align-items-center mb-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Plans d'actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu1_current;\n                                                    return (_menu1_current = menu1.current) === null || _menu1_current === void 0 ? void 0 : _menu1_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                                ref: menu1,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-600\",\n                                children: \"Plans d'actions r\\xe9cents seront affich\\xe9s ici.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 196,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Sales Overview\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chart__WEBPACK_IMPORTED_MODULE_7__.Chart, {\n                                type: \"line\",\n                                data: lineData,\n                                options: lineOptions\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu2_current;\n                                                    return (_menu2_current = menu2.current) === null || _menu2_current === void 0 ? void 0 : _menu2_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                                ref: menu2,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"TODAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 mx-0 mt-0 mb-4 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Richard Jones\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a blue t-shirt for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"79$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-orange-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-download text-xl text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-700 line-height-3\",\n                                                children: [\n                                                    \"Your request for withdrawal of \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-500 font-medium\",\n                                                        children: \"2500$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 60\n                                                    }, undefined),\n                                                    \" has been initiated.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"YESTERDAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 m-0 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Keyser Wick\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a black jacket for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"59$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-pink-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-question text-xl text-pink-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Jane Davis\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: \" has posted a new questions about your product.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-5 shadow-2 flex flex-column md:flex-row md:align-items-center justify-content-between mb-3\",\n                        style: {\n                            borderRadius: \"1rem\",\n                            background: \"linear-gradient(0deg, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.5)), linear-gradient(92.54deg, #1C80CF 47.88%, #FFFFFF 100.01%)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100 font-medium text-xl mt-2 mb-3\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white font-medium text-5xl\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 mr-auto md:mt-0 md:mr-0\",\n                                children: \"TEST\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n        lineNumber: 135,\n        columnNumber: 13\n    }, undefined);\n};\n_s(Dashboard, \"jBhkOA/N3tnJG9qS4giFvD80gL4=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiRecommendationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiMissionList\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/page.tsx\n"));

/***/ })

});