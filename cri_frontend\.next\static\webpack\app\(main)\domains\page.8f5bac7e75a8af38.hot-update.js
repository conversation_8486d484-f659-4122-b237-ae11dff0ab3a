"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/domains/page",{

/***/ "(app-client)/./app/(main)/domains/(components)/GenericTAble.tsx":
/*!**********************************************************!*\
  !*** ./app/(main)/domains/(components)/GenericTAble.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    _s();\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { mutate: createDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainCreate)();\n    const { mutate: updateDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainUpdate)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [parentDropDown, setParentDropDown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: parse(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"parent\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"parent.title\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        var _row_original_parent;\n                        setParentDropDown({\n                            \"code\": row.original.parentId,\n                            \"name\": (_row_original_parent = row.original.parent) === null || _row_original_parent === void 0 ? void 0 : _row_original_parent.title\n                        });\n                        const onChange = (event)=>{\n                            console.log(\"################AAAAAAAAAAAAAAAAAAAA####################\", event.value);\n                            setParentDropDown(event.value);\n                            row._valuesCache[\"parent\"] = event.value.code;\n                            if (row.id === \"mrt-row-create\") {\n                                table.setCreatingRow(row);\n                            } else {\n                                table.setEditingRow(row);\n                            }\n                        };\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_4__.Dropdown, {\n                            optionLabel: \"name\",\n                            value: parentDropDown,\n                            onChange: onChange,\n                            filter: true,\n                            options: data_.data_.data.results.map(function(val) {\n                                return {\n                                    \"code\": val.id,\n                                    \"name\": val.title\n                                };\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 22\n                        }, this);\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title3;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0][\"$ref\"] ? data_.data_type.properties[key].allOf[0][\"$ref\"].enum : [],\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"VP\" ? \"danger\" : cell.getValue() === \"STRUCT\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"VP\" ? \"Vice Pr\\xe9sident\" : cell.getValue() === \"STRUCT\" ? \"Structures\" : \"Contr\\xf4le Interne\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"NS\" ? \"danger\" : cell.getValue() === \"EC\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"NS\" ? \"Non lanc\\xe9e\" : cell.getValue() === \"SP\" ? \"Suspendue\" : cell.getValue() === \"EC\" ? \"En cours\" : \"Cl\\xf4tur\\xe9e\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title5;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key][\"$ref\"] && data_.data_type.properties[key][\"$ref\"].enum) {\n                console.log(\"#######enum##########\", key, value);\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+\" ,\"; }, \"\"):\"\",\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.row.original[key];\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        children: data_.data_type.properties[key][\"$ref\"].enum,\n                        SelectProps: {\n                        }\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title7, _data__data_type_properties_key_title8;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    console.log(\"############## data from api \", data_);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values } = param;\n            var _toast_current;\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            table.setEditingRow(null); //exit editing mode\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Sauvegarde en cours\"\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            delete values.id;\n            createDomain(values);\n            table.setCreatingRow(null); //exit creating mode\n        },\n        onCreatingRowCancel: ()=>{\n        //clear any validation errors\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        rounded: true,\n                        disabled: table.getIsSomeRowsSelected(),\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: handleClick,\n                        icon: \"pi pi-trash\",\n                        // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-bell\",\n                        rounded: true,\n                        color: rowActionEnabled ? \"secondary\" : \"primary\",\n                        size: \"small\",\n                        \"aria-label\": \"edit\",\n                        onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 512,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            zIndex: \"1302 !important\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                            position: \"right\",\n                            header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"align-content-center \",\n                                        children: [\n                                            \"Cr\\xe9ation \",\n                                            data_.data_type.name,\n                                            \" \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                            variant: \"text\",\n                                            table: table,\n                                            row: row\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            visible: createVisible,\n                            onHide: ()=>{\n                                table.setCreatingRow(null);\n                                setCreateVisible(false);\n                            },\n                            className: \"w-full md:w-9 lg:w-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: [\n                                    internalEditComponents,\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 7\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        zIndex: \"1302 !important\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        position: \"right\",\n                        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"align-content-center \",\n                                    children: [\n                                        \"Editer \",\n                                        data_.data_type.name,\n                                        \" \",\n                                        row.original.code\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                        variant: \"text\",\n                                        table: table,\n                                        row: row\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        visible: editVisible,\n                        onHide: ()=>{\n                            table.setEditingRow(null);\n                            setEditVisible(false);\n                        },\n                        className: \"w-full md:w-9 lg:w-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                gap: \"1.5rem\"\n                            },\n                            children: [\n                                internalEditComponents,\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 79\n                }, this)\n            }, void 0, false);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return row.original.description ? parse(row.original.description) : row.original.content ? parse(row.original.content) : row.original.staff ? row.original.staff.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    display: \"grid\",\n                    margin: \"auto\",\n                    //gridTemplateColumns: '1fr 1fr',\n                    width: \"100vw\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabView, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"staff\"].title,\n                            leftIcon: \"pi pi-user mr-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.staff.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 134\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 64\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"assistants\"].title,\n                            rightIcon: \"pi pi-user ml-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.assistants.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 139\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 69\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: \"Lettre\",\n                            leftIcon: \"pi pi-file-word mr-2\",\n                            rightIcon: \"pi pi-file-pdf ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    icon: \"pi pi-check\",\n                                    rounded: true,\n                                    onClick: ()=>setVisible(true),\n                                    disabled: row.original.document === null\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: [\n                                            \"Lettre de mission : \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    visible: visible,\n                                    onHide: ()=>setVisible(false),\n                                    className: \"w-full md:w-9 lg:w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                        children: [\n                                            row.original.document !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Document, {\n                                                file: row.original.document,\n                                                onLoadSuccess: onDocumentLoadSuccess,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Page, {\n                                                    pageNumber: pageNumber\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 27\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No Document\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Page \",\n                                                            pageNumber || (numPages ? 1 : \"--\"),\n                                                            \" of \",\n                                                            numPages || \"--\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row align-items-center justify-content-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber <= 1,\n                                                                onClick: previousPage,\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber >= numPages,\n                                                                onClick: nextPage,\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 23\n                                    }, this)\n                                }, row.original.id, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 19\n                        }, this),\n                        \"          \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 646,\n                columnNumber: 15\n            }, this) : null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n        },\n        renderRowActions: (param)=>// <Box sx={{ display: 'flex', gap: '1rem' }}>\n        /*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 705,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    // console.log(data_.isLoading)\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 733,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_16__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 733,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"2u2a2fJ1r5Dl3tiKJxMUn3gwaXA=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/domains/(components)/GenericTAble.tsx\n"));

/***/ })

});