"""
Django settings for cri_backend project.

Generated by 'django-admin startproject' using Django 5.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
import os
import oracledb
oracledb.defaults.fetch_lobs = False

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-ikt9plb=5hwc_56l(d%l%nqpe@3*gng2%qqv(*$qfs)b%lly=d"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["************", "127.0.0.1", "*************", "*************","localhost"]
INTERNAL_IPS = [
    # ...
    "127.0.0.1",
    # ...
]

# Application definition
AJAX_SELECT_BOOTSTRAP = True
INSTALLED_APPS = [
    # "unfold",  # before django.contrib.admin
    # "unfold.contrib.filters",  # optional, if special filters are needed
    # "unfold.contrib.forms",  # optional, if special form elements are needed
    # "unfold.contrib.inlines",  # optional, if special inlines are needed
    # "unfold.contrib.import_export",  # optional, if django-import-export package is used
    # "unfold.contrib.guardian",  # optional, if django-guardian package is used
    # "unfold.contrib.simple_history",  # optional, if django-simple-history package is used
    #'codemirror2',
    #'river',
    #'ajax_select',
    'django_cleanup.apps.CleanupConfig',
    "admin_interface",
    "colorfield",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # 'ninja_extra',
    "corsheaders",
    # 'dal',
    # 'dal_select2',
    # 'django_select2',
    "field_history",
    "django_python3_ldap",
    "django_celery_results",
    "django_celery_beat",
    "backend.apps.BackendConfig",
    "debug_toolbar",
    "django_extensions",
    "drf_material",
    "rest_framework",
    "drf_generators",
    "django_filters",
    "drf_spectacular",
    "hijack",
    "hijack.contrib.admin",
    # 'casl_django',
    "rest_framework.authtoken",
    "tinymce",
]

CASL_DJANGO = {"subject-length": 256, "action-length": 256}
AUTHENTICATION_BACKENDS = [
    # "django_auth_ldap.backend.LDAPBackend",
    # "django_python3_ldap.auth.LDAPBackend",
    "django.contrib.auth.backends.ModelBackend",
]
CORS_ALLOW_ALL_ORIGINS = True
X_FRAME_OPTIONS = "SAMEORIGIN"
REST_FRAMEWORK = {
    "DEFAULT_PAGINATION_CLASS": "backend.paginators.StandardResultsSetPagination",
    "PAGE_SIZE": 5,
    "UNICODE_JSON": True,
    "EXCEPTION_HANDLER": "backend.utils.custom_exception_handler",
    "DEFAULT_PERMISSION_CLASSES": (
        "rest_framework.permissions.DjangoModelPermissionsOrAnonReadOnly",
    ),
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework.authentication.TokenAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.OrderingFilter",
    ],
    "DEFAULT_METADATA_CLASS": "rest_framework.metadata.SimpleMetadata",
    "DEFAULT_SCHEMA_CLASS": "backend.schemas.CustomAutoSchema",
    "JSON_EDITOR": True,
}
SPECTACULAR_SETTINGS = {
    "TITLE": "CRI|LQS/INF/SLM",
    "DESCRIPTION": "CRI|LQS/INF/SLM",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": True,
    "COMPONENT_SPLIT_REQUEST": True,
    "JSON_EDITOR": True,
    # OTHER SETTINGS
    'ENUM_NAME_OVERRIDES': {
        'MissionTypeEnum': 'backend.enums.MissionType',
        'MissionEtatEnum': 'backend.enums.MissionEtat',
        'PlanTypeEnum': 'backend.enums.PlanType',
        'ActionTypeEnum': 'backend.enums.ActionTypeEnum',
        'ActionStateEnum': 'backend.enums.ActionStateEnum',
        'ValidationEnum': 'backend.enums.ValidationEnum',
        'RecommendationActionTypeEnum': 'backend.enums.RecommendationActionTypeEnum'
    }
}
MIDDLEWARE = [
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django_currentuser.middleware.ThreadLocalUserMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "field_history.middleware.FieldHistoryMiddleware",
    "hijack.middleware.HijackUserMiddleware",
]

ROOT_URLCONF = "cri_backend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "cri_backend.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    },
    # "default2": {
    #     "ENGINE": "django.db.backends.postgresql_psycopg2",
    #     "NAME": "cri_db",
    #     "USER": "postgres",
    #     "PASSWORD": "postgres",
    #     "HOST": "localhost",
    #     "PORT": "5433",
    # },
    # "default": {
    #     "ENGINE": "django.db.backends.oracle",  #'grisp_dbengine'
    #     "NAME": "***********:1521/DBDEV21C",
    #     "USER": "CRI_DEV",
    #     "PASSWORD": "CRIDEV",
    #     # "HOST": "***********",
    #     # "PORT": "1521",
    #     "schemas": [
    #         "CRI_DEV",
    #     ],
    #     "OPTIONS": {
    #         "purity": oracledb.ATTR_PURITY_SELF,
    #         # "threaded": True,
    #         #'array_size':1000,
    #     },
    # },
}


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = "fr-FR"

TIME_ZONE = "Africa/Algiers"

USE_I18N = True

USE_TZ = True

DATA_UPLOAD_MAX_NUMBER_FIELDS = 910240
# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/
LOCALE_PATHS = [
    os.path.join(BASE_DIR, "backend", "locale"),
]
STATIC_URL = "static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")
# STATICFILES_DIRS = [
#     os.path.join(BASE_DIR, 'static'),
#     os.path.join(BASE_DIR,'media')
# ]
# print(STATIC_ROOT)
MEDIA_URL = "media/"
MEDIA_ROOT = BASE_DIR / "media"
# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

X_FRAME_OPTIONS = "SAMEORIGIN"
SILENCED_SYSTEM_CHECKS = ["security.W019"]
###############################################################################
############################## LDAP CONFIG ####################################
###############################################################################
# The URL of the LDAP server.
LDAP_AUTH_URL = os.environ.get("LDAP_AUTH_URL", "ldap://*************:389")

# Initiate TLS on connection.
LDAP_AUTH_USE_TLS = bool(os.environ.get("LDAP_AUTH_USE_TLS", False))

# The LDAP search base for looking up users.
LDAP_AUTH_SEARCH_BASE = os.environ.get(
    "LDAP_AUTH_SEARCH_BASE", "OU=ORN,OU=Pole,DC=corp,DC=sonatrach,DC=dz"
)

# The LDAP class that represents a user.
LDAP_AUTH_OBJECT_CLASS = "user"

# User model fields mapped to the LDAP
# attributes that represent them.
LDAP_AUTH_USER_FIELDS = {
    "username": "sAMAccountName",
    "first_name": "givenName",
    "last_name": "sn",
    "email": "mail",
}

# A tuple of django model fields used to uniquely identify a user.
LDAP_AUTH_USER_LOOKUP_FIELDS = ("username",)

# Path to a callable that takes a dict of {model_field_name: value},
# returning a dict of clean model data.
# Use this to customize how data loaded from LDAP is saved to the User model.
LDAP_AUTH_CLEAN_USER_DATA = "django_python3_ldap.utils.clean_user_data"
# LDAP_AUTH_CLEAN_USER_DATA = "backend.utils.clean_user_data"

# Path to a callable that takes a user model and a dict of {ldap_field_name: [value]},
# and saves any additional user relationships based on the LDAP data.
# Use this to customize how data loaded from LDAP is saved to User model relations.
# For customizing non-related User model fields, use LDAP_AUTH_CLEAN_USER_DATA.
LDAP_AUTH_SYNC_USER_RELATIONS = "backend.utils.sync_user_relations"
#LDAP_AUTH_SYNC_USER_RELATIONS = "django_python3_ldap.utils.sync_user_relations"
# Path to a callable that takes a dict of {ldap_field_name: value},
# returning a list of [ldap_search_filter]. The search filters will then be AND'd
# together when creating the final search filter.
LDAP_AUTH_FORMAT_SEARCH_FILTERS = "backend.utils.custom_format_search_filters"
# LDAP_AUTH_FORMAT_SEARCH_FILTERS = "django_python3_ldap.utils.format_search_filters"

# Path to a callable that takes a dict of {model_field_name: value}, and returns
# a string of the username to bind to the LDAP server.
# Use this to support different types of LDAP server.
LDAP_AUTH_FORMAT_USERNAME = "django_python3_ldap.utils.format_username_active_directory"

# Sets the login domain for Active Directory users.
LDAP_AUTH_ACTIVE_DIRECTORY_DOMAIN = os.environ.get(
    "LDAP_AUTH_ACTIVE_DIRECTORY_DOMAIN", "SONATRACH"
)

# The LDAP username and password of a user for querying the LDAP database for user
# details. If None, then the authenticated user will be used for querying, and
# the `ldap_sync_users` command will perform an anonymous query.
LDAP_AUTH_CONNECTION_USERNAME = os.environ.get(
    "LDAP_AUTH_CONNECTION_USERNAME", "s-ORN-Alertix"
)
LDAP_AUTH_CONNECTION_PASSWORD = os.environ.get(
    "LDAP_AUTH_CONNECTION_PASSWORD", "@lertix2023"
)

# LDAP_AUTH_CONNECTION_USERNAME = os.environ.get("LDAP_AUTH_CONNECTION_USERNAME","sonv11628")
# LDAP_AUTH_CONNECTION_PASSWORD = os.environ.get("LDAP_AUTH_CONNECTION_PASSWORD","Red@128888")

# Set connection/receive timeouts (in seconds) on the underlying `ldap3` library.
LDAP_AUTH_CONNECT_TIMEOUT = None
LDAP_AUTH_RECEIVE_TIMEOUT = None
###############################################################################
############################## SMTP CONFIG ####################################
###############################################################################
EMAIL_BACKEND = os.environ.get("EMAIL_BACKEND", "django_smtp_ntlm_backend.NTLMEmail")
EMAIL_HOST = os.environ.get("EMAIL_HOST", "*************")
EMAIL_HOST_USER = os.environ.get("EMAIL_HOST_USER", "<EMAIL>")
# EMAIL_HOST_PASSWORD = os.environ.get("#EMAIL_HOST_PASSWORD",'@lertix2022')
EMAIL_PORT = os.environ.get("EMAIL_PORT", 25)
EMAIL_USE_TLS = bool(os.environ.get("EMAIL_USE_TLS", False))
EMAIL_USE_SSL = bool(os.environ.get("EMAIL_USE_SSL", False))
SERVER_EMAIL = os.environ.get("SERVER_EMAIL", "<EMAIL>")
DEFAULT_FROM_EMAIL = os.environ.get(
    "DEFAULT_FROM_EMAIL", "Alertix <<EMAIL>>"
)

ADMINS = os.environ.get(
    "ADMINS",
    [
        ("TALEB REDA", "<EMAIL>"),
    ],
)
MANAGERS = os.environ.get("MANAGERS", ADMINS)


# Celery Configuration Options
CELERY_TIMEZONE = "Africa/Algiers"
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60
CELERY_RESULT_BACKEND = "django-db"

CELERY_BROKER_URL = "redis://localhost:6379"
CELERY_RESULT_BACKEND = "redis://localhost:6379"
###############################################################################
############################## TINYMCE ########################################
###############################################################################
TINYMCE_DEFAULT_CONFIG = {
    "height": "520px",
    "menubar": "file edit view insert format tools table help loremipsum",
    "plugins": "loremipsum advlist autolink lists link image charmap print preview anchor searchreplace visualblocks code "
    "fullscreen insertdatetime media table code help wordcount spellchecker",
    "toolbar": "loremipsum | undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | alignleft "
    "aligncenter alignright alignjustify | outdent indent |  numlist bullist checklist | forecolor "
    "backcolor casechange permanentpen formatpainter removeformat | pagebreak | charmap emoticons | "
    "fullscreen  preview save print | insertfile image media pageembed template link anchor codesample | "
    "a11ycheck ltr rtl | showcomments addcomment code",
    "custom_undo_redo_levels": 10,
    "language": "fr_FR",  # To force a specific language instead of the Django current language.
    "paste_data_images": True,
    "fontsize_formats": "8pt 9pt 10pt 11pt 12pt 14pt 18pt 24pt 30pt 36pt 48pt 60pt 72pt 96pt",
    "images_upload_handler": "function(blobInfo, success, failure) {"
    "var reader = new FileReader();"
    "reader.readAsDataURL(blobInfo.blob());"
    " reader.onload = function() {"
    "success(this.result);"
    " }"
    "}",
}
TINYMCE_SPELLCHECKER = True
TINYMCE_COMPRESSOR = False
# TINYMCE_EXTRA_MEDIA = {
#     'css': {
#         'all': [
#             ...
#         ],
#     },
#     'js': [
#         ...
#     ],
# }
