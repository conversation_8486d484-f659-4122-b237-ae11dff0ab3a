'use client'

import React from 'react'
import { Button } from 'primereact/button'
import { Card } from 'primereact/card'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function UnauthorizedPage() {
  const router = useRouter()
  const { user } = useAuth()

  const goBack = () => {
    router.back()
  }

  const goHome = () => {
    router.push('/')
  }

  return (
    <div className="flex align-items-center justify-content-center min-h-screen">
      <Card className="w-full max-w-md">
        <div className="text-center">
          <div className="mb-4">
            <i className="pi pi-exclamation-triangle text-6xl text-orange-500"></i>
          </div>
          
          <h1 className="text-3xl font-bold text-900 mb-3">
            Access Denied
          </h1>
          
          <p className="text-600 mb-4">
            You don't have permission to access this page.
          </p>
          
          {user && (
            <div className="bg-gray-50 p-3 border-round mb-4">
              <p className="text-sm text-600 mb-2">
                <strong>Current User:</strong> {user.username} ({user.email})
              </p>
              <p className="text-sm text-600">
                <strong>Role:</strong> {
                  user.isSuperuser ? 'Super Admin' :
                  user.isStaff ? 'Staff' : 'User'
                }
              </p>
            </div>
          )}
          
          <div className="flex gap-2 justify-content-center">
            <Button 
              label="Go Back" 
              icon="pi pi-arrow-left" 
              onClick={goBack}
              className="p-button-outlined"
            />
            <Button 
              label="Go Home" 
              icon="pi pi-home" 
              onClick={goHome}
            />
          </div>
          
          <div className="mt-4">
            <p className="text-sm text-500">
              If you believe this is an error, please contact your administrator.
            </p>
          </div>
        </div>
      </Card>
    </div>
  )
}
