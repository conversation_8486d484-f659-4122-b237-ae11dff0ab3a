from ajax_select import register, LookupChannel
from .models import *
from django.db.models import Q
from django.contrib.auth.models import User
@register('users')
class UsersLookup(LookupChannel):

    model = User

    def get_query(self, q, request):
        print(q)
        return self.model.objects.filter(Q(first_name__icontains=q) | Q(last_name__icontains=q)| Q(username__icontains=q))

    def format_item_display(self, item):
        return u"<span class='tag'>%s, %s</span>" % (item.first_name,item.last_name)