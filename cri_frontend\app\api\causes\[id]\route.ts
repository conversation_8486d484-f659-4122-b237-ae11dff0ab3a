import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/causes/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid cause ID' },
        { status: 400 }
      )
    }

    const cause = await prisma.cause.findUnique({
      where: { id },
      include: {
        constat: {
          include: {
            mission: {
              select: {
                id: true,
                code: true,
                type: true,
              }
            }
          }
        },
        recommendations: {
          select: {
            id: true,
            recommendation: true,
            priority: true,
            status: true,
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        },
        modifiedByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        }
      },
    })

    if (!cause) {
      return NextResponse.json(
        { error: 'Cause not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ data: cause })
  } catch (error) {
    console.error('Error fetching cause:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/causes/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can update causes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid cause ID' },
        { status: 400 }
      )
    }

    // Check if cause exists
    const existingCause = await prisma.cause.findUnique({
      where: { id }
    })

    if (!existingCause) {
      return NextResponse.json(
        { error: 'Cause not found' },
        { status: 404 }
      )
    }

    const body = await request.json()
    
    const {
      numcause,
      content,
      constatId,
    } = body

    // If constatId is being updated, check if the new constat exists
    if (constatId && constatId !== existingCause.constatId) {
      const constat = await prisma.constat.findUnique({
        where: { id: constatId }
      })
      
      if (!constat) {
        return NextResponse.json(
          { error: 'Constat not found' },
          { status: 404 }
        )
      }
    }

    const updatedCause = await prisma.cause.update({
      where: { id },
      data: {
        ...(numcause !== undefined && { numcause }),
        ...(content !== undefined && { content }),
        ...(constatId !== undefined && { constatId }),
        modifiedBy: session.user.id,
      },
      include: {
        constat: {
          include: {
            mission: {
              select: {
                id: true,
                code: true,
                type: true,
              }
            }
          }
        },
        recommendations: {
          select: {
            id: true,
            recommendation: true,
            priority: true,
            status: true,
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        },
        modifiedByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        }
      },
    })

    return NextResponse.json({ data: updatedCause })
  } catch (error) {
    console.error('Error updating cause:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/causes/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can delete causes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid cause ID' },
        { status: 400 }
      )
    }

    // Check if cause exists
    const existingCause = await prisma.cause.findUnique({
      where: { id }
    })

    if (!existingCause) {
      return NextResponse.json(
        { error: 'Cause not found' },
        { status: 404 }
      )
    }

    await prisma.cause.delete({
      where: { id }
    })

    return NextResponse.json({ 
      data: { message: 'Cause deleted successfully' }
    })
  } catch (error) {
    console.error('Error deleting cause:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
