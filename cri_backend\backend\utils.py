
from __future__ import unicode_literals
from django.db import IntegrityError
from django.contrib.auth.models import User
from rest_framework.views import Response, exception_handler
from rest_framework import status
from django_python3_ldap.utils import format_search_filters
from .models import UserProfile, CriAgents
#############################################################################################
# custom format serach from LDAP
#############################################################################################
def custom_format_search_filters(ldap_fields):
    
    search_filters = []    
    search_filters = format_search_filters(ldap_fields)
    search_filters.append("(postalCode=*)")
    search_filters.append("(sAMAccountName=SONV*)")
    search_filters.append("(mail=*)")

    
    print(search_filters)
    return search_filters
#############################################################################################
# Sync users from LDAP
#############################################################################################
def sync_user_relations(user, ldap_attributes, *, connection=None, dn=None):
    # do nothing by default
    # print("User :")
    # print(dn)
    # print("##########################")
    # print(ldap_attributes)
    # pass
    # The LDAP connection has the last result attached...
    #dn = connection.response[0].get('dn')
    #print("ffff %s ffdfdf" % "" if ldap_attributes.get('description')==None else ldap_attributes.get('description')[0] )
    #postalCode/mat, title,description,department
    created = False
    print(user,ldap_attributes.get('postalCode')[0], CriAgents.objects.filter(matric_agnt=ldap_attributes.get('postalCode')[0]).first())
    # print(CriAgents.objects.get(matric_agnt=ldap_attributes.get('postalCode')[0]))
    if UserProfile.objects.filter(user=user).count() == 0 and ldap_attributes.get('postalCode')[0] not in ['45685Y','64097H','95252T','96170G','10371T','11424E','11433G','13233Y','30492H','30754V','31227N','80660G','65584H','44161V','80496Q','42442F','58930E','52376D','60968F','76649M','69165L','76547Y','69165L','87401M','88480M','58930E','69165L','05838M','52376D','95234P','31227N','96347B','95501V','95272C','94442P','84333T','87304K','80661J'] :
        agnt  = CriAgents.objects.filter(matric_agnt=ldap_attributes.get('postalCode')[0]).first()
        UserProfile.objects.create(user=user,
                                   agent = agnt
                                                                    # serial = "" if ldap_attributes.get('postalCode')==None else ldap_attributes.get('postalCode')[0] ,
                                                                    # title= "" if ldap_attributes.get('title')==None else ldap_attributes.get('title')[0] ,
                                                                    # department=  "" if ldap_attributes.get('department')==None else ldap_attributes.get('department')[0],
                                                                    # unit = "" if ldap_attributes.get('distinguishedName')==None else ldap_attributes.get('distinguishedName')[0].split(',')[-6].split('=')[1],
                                                                    )

    # print("UserProfile %s %s" % (user,"created !" if created else "not created but updated !"))
    print(user)



def custom_exception_handler(exc, context):
    # Call REST framework's default exception handler first to get the standard error response.
    response = exception_handler(exc, context)

    # if there is an IntegrityError and the error response hasn't already been generated
    if isinstance(exc, IntegrityError) and not response:
        response = Response(
            {
                'message': '%s' %exc.__cause__
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    return response

def clean_user_data(model_fields):
    # print('---------------------------------------------------------')
    # print(model_fields)
    # print('---------------------------------------------------------')
    pass