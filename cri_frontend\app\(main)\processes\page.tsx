'use client';

import {  useApiDomain<PERSON>ist, useApiProcessList, useApiRiskList } from '@/services/api/api/api';
import { $Domain, $Process, $Risk, Domain, Process, Risk } from '@/services/openapi_client';
import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import GenericTable from './(components)/GenericTAble';

const ProcessesTable = () => {
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex:0,
        pageSize: 30, //customize the default page size
    });
    const { data: processes, isLoading:isLoading ,error:error} = useApiProcessList({ page: pagination.pageIndex + 1 ,page_size:pagination.pageSize});

    
    if( isLoading )  return (<div></div>)
    return   (        
        <div className="grid">
            <div className="col-12">
                <GenericTable<Process> data_={processes} isLoading={isLoading} error={error} data_type={$Process} pagination={{"set":setPagination,"pagi":pagination}}></GenericTable>
            </div>
        </div>
    );
};

export default ProcessesTable;
