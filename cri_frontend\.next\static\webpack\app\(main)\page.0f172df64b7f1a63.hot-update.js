"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/page",{

/***/ "(app-client)/./app/(main)/page.tsx":
/*!*****************************!*\
  !*** ./app/(main)/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_chart__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/chart */ \"(app-client)/./node_modules/primereact/chart/chart.esm.js\");\n/* harmony import */ var primereact_column__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/column */ \"(app-client)/./node_modules/primereact/column/column.esm.js\");\n/* harmony import */ var primereact_datatable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/datatable */ \"(app-client)/./node_modules/primereact/datatable/datatable.esm.js\");\n/* harmony import */ var primereact_menu__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/menu */ \"(app-client)/./node_modules/primereact/menu/menu.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../layout/context/layoutcontext */ \"(app-client)/./layout/context/layoutcontext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-client)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_service_ProductService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/service/ProductService */ \"(app-client)/./utilities/service/ProductService.tsx\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* eslint-disable @next/next/no-img-element */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst lineData = {\n    labels: [\n        \"January\",\n        \"February\",\n        \"March\",\n        \"April\",\n        \"May\",\n        \"June\",\n        \"July\"\n    ],\n    datasets: [\n        {\n            label: \"First Dataset\",\n            data: [\n                65,\n                59,\n                80,\n                81,\n                56,\n                55,\n                40\n            ],\n            fill: false,\n            backgroundColor: \"#2f4860\",\n            borderColor: \"#2f4860\",\n            tension: 0.4\n        },\n        {\n            label: \"Second Dataset\",\n            data: [\n                28,\n                48,\n                40,\n                19,\n                86,\n                27,\n                90\n            ],\n            fill: false,\n            backgroundColor: \"#00bb7e\",\n            borderColor: \"#00bb7e\",\n            tension: 0.4\n        }\n    ]\n};\nconst Dashboard = ()=>{\n    var _actions_data_data, _actions_data, _actions, _recommendations_data, _recommendations_data1, _recommendations_data2, _recommendations;\n    _s();\n    // Fetch data using specific hooks\n    const { data: recommendations, isLoading: recommendationsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiRecommendationList)({\n        limit: 10\n    });\n    const { data: plans, isLoading: plansLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiPlanList)({\n        limit: 5\n    });\n    const { data: missions, isLoading: missionsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiMissionList)({\n        limit: 5\n    });\n    console.log(\"[Dashboard]\", recommendations);\n    const menu1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const menu2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [lineOptions, setLineOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { layoutConfig } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__.LayoutContext);\n    const applyLightTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#495057\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    const applyDarkTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#ebedef\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        _utilities_service_ProductService__WEBPACK_IMPORTED_MODULE_4__.ProductService.getProductsSmall().then((data)=>setProducts(data));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (layoutConfig.colorScheme === \"light\") {\n            applyLightTheme();\n        } else {\n            applyDarkTheme();\n        }\n    }, [\n        layoutConfig.colorScheme\n    ]);\n    const formatCurrency = (value)=>{\n        var _value;\n        return (_value = value) === null || _value === void 0 ? void 0 : _value.toLocaleString(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Missions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(missions) ? missions.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-blue-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-briefcase text-blue-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(recommendations) ? recommendations.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-orange-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-thumbs-up-fill text-orange-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: (_actions = actions) === null || _actions === void 0 ? void 0 : (_actions_data = _actions.data) === null || _actions_data === void 0 ? void 0 : (_actions_data_data = _actions_data.data) === null || _actions_data_data === void 0 ? void 0 : _actions_data_data.count\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-cyan-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-cog text-cyan-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Comments\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: \"152 Unread\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-purple-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-comment text-purple-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Commentaires\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 17\n                            }, undefined),\n                            ((_recommendations_data = recommendations.data) === null || _recommendations_data === void 0 ? void 0 : _recommendations_data.isLoading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_6__.ProgressSpinner, {\n                                style: {\n                                    width: \"50px\",\n                                    height: \"50px\"\n                                },\n                                strokeWidth: \"8\",\n                                fill: \"var(--surface-ground)\",\n                                animationDuration: \".5s\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 53\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                                stripedRows: true,\n                                value: (_recommendations_data1 = recommendations.data) === null || _recommendations_data1 === void 0 ? void 0 : _recommendations_data1.flatMap((rec)=>rec.comments),\n                                rows: 10,\n                                scrollable: true,\n                                paginator: true,\n                                resizableColumns: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"left\",\n                                        field: \"recommendation\",\n                                        body: (data)=>\"\".concat(recommendations.data.data.results.find((rec)=>rec.id === data.recommendation).mission, \" * \").concat(data.recommendation),\n                                        header: \"Recommendation\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"20%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"left\",\n                                        field: \"comment\",\n                                        body: (data)=>data.comment,\n                                        header: \"Commentaire\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"70%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"center\",\n                                        field: \"created\",\n                                        body: (data)=>new Date(data.created).toLocaleString(\"fr\"),\n                                        header: \"Date\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"12%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"center\",\n                                        field: \"created_by\",\n                                        body: (data)=>{\n                                            var _data_created_by, _data_created_by1;\n                                            return data.created_by ? \"\".concat((_data_created_by = data.created_by) === null || _data_created_by === void 0 ? void 0 : _data_created_by.last_name, \" \").concat((_data_created_by1 = data.created_by) === null || _data_created_by1 === void 0 ? void 0 : _data_created_by1.first_name) : \"\";\n                                        },\n                                        header: \"Utilisateur\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"12%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        rowEditor: true,\n                                        header: \"Action\",\n                                        sortableDisabled: true,\n                                        field: \"action\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"6%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-content-between align-items-center mb-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Plans d'actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu1_current;\n                                                    return (_menu1_current = menu1.current) === null || _menu1_current === void 0 ? void 0 : _menu1_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_10__.Menu, {\n                                                ref: menu1,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-none p-0 m-0\",\n                                children: (_recommendations = recommendations) === null || _recommendations === void 0 ? void 0 : (_recommendations_data2 = _recommendations.data) === null || _recommendations_data2 === void 0 ? void 0 : _recommendations_data2.map((rec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-900 font-medium mr-2 mb-1 md:mb-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/recommendations/\" + rec.id,\n                                                                children: [\n                                                                    \"Recommandation n\\xb0 \",\n                                                                    rec.numrecommandation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 90\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 text-600\",\n                                                            children: rec.mission\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 md:mt-0 flex align-items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\",\n                                                            style: {\n                                                                height: \"8px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-orange-500 h-full\",\n                                                                style: {\n                                                                    width: \"\".concat(rec.actions.reduce((accumulator, act)=>accumulator += act.progress || 0, 0), \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 37\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-500 ml-3 font-medium\",\n                                                            children: [\n                                                                \"%\",\n                                                                (rec.actions.reduce((accumulator, act)=>accumulator += act.progress || 0, 0) / rec.actions.length || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Sales Overview\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chart__WEBPACK_IMPORTED_MODULE_11__.Chart, {\n                                type: \"line\",\n                                data: lineData,\n                                options: lineOptions\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu2_current;\n                                                    return (_menu2_current = menu2.current) === null || _menu2_current === void 0 ? void 0 : _menu2_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_10__.Menu, {\n                                                ref: menu2,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"TODAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 mx-0 mt-0 mb-4 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Richard Jones\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a blue t-shirt for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"79$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-orange-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-download text-xl text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-700 line-height-3\",\n                                                children: [\n                                                    \"Your request for withdrawal of \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-500 font-medium\",\n                                                        children: \"2500$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 60\n                                                    }, undefined),\n                                                    \" has been initiated.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"YESTERDAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 m-0 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Keyser Wick\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a black jacket for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"59$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-pink-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-question text-xl text-pink-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Jane Davis\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: \" has posted a new questions about your product.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-5 shadow-2 flex flex-column md:flex-row md:align-items-center justify-content-between mb-3\",\n                        style: {\n                            borderRadius: \"1rem\",\n                            background: \"linear-gradient(0deg, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.5)), linear-gradient(92.54deg, #1C80CF 47.88%, #FFFFFF 100.01%)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100 font-medium text-xl mt-2 mb-3\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white font-medium text-5xl\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 mr-auto md:mt-0 md:mr-0\",\n                                children: \"TEST\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n        lineNumber: 137,\n        columnNumber: 13\n    }, undefined);\n};\n_s(Dashboard, \"dIK6/XOP/w75Cc1eGiU9nkJpz0A=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiRecommendationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiMissionList\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/page.tsx\n"));

/***/ })

});