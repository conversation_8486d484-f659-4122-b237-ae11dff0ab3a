"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/processes/page",{

/***/ "(app-client)/./app/(main)/processes/(components)/GenericTAble.tsx":
/*!************************************************************!*\
  !*** ./app/(main)/processes/(components)/GenericTAble.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    _s();\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { mutate: createProcess } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessCreate)();\n    const { mutate: updateProcess } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessUpdate)();\n    const { mutate: deleteProcess } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessDestroy)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 115\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_3__.Editor, {\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"parent\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"parent.title\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title3;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0][\"$ref\"] ? data_.data_type.properties[key].allOf[0][\"$ref\"].enum : [],\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"VP\" ? \"danger\" : cell.getValue() === \"STRUCT\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"VP\" ? \"Vice Pr\\xe9sident\" : cell.getValue() === \"STRUCT\" ? \"Structures\" : \"Contr\\xf4le Interne\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"NS\" ? \"danger\" : cell.getValue() === \"EC\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"NS\" ? \"Non lanc\\xe9e\" : cell.getValue() === \"SP\" ? \"Suspendue\" : cell.getValue() === \"EC\" ? \"En cours\" : \"Cl\\xf4tur\\xe9e\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title5;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key][\"$ref\"] && data_.data_type.properties[key][\"$ref\"].enum) {\n                console.log(\"#######enum##########\", key, value);\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+\" ,\"; }, \"\"):\"\",\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.row.original[key];\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        children: data_.data_type.properties[key][\"$ref\"].enum,\n                        SelectProps: {\n                        }\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title7, _data__data_type_properties_key_title8;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    console.log(\"############## data from api \", data_);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values } = param;\n            var _toast_current;\n            //validate data\n            //save data to api\n            table.setEditingRow(null); //exit editing mode\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Sauvegarde en cours\"\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values } = param;\n            //validate data\n            //save data to api\n            createProcess(values);\n            table.setCreatingRow(null); //exit creating mode\n        },\n        onCreatingRowCancel: ()=>{\n        //clear any validation errors\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        rounded: true,\n                        disabled: table.getIsSomeRowsSelected(),\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: handleClick,\n                        icon: \"pi pi-trash\",\n                        // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-bell\",\n                        rounded: true,\n                        color: rowActionEnabled ? \"secondary\" : \"primary\",\n                        size: \"small\",\n                        \"aria-label\": \"edit\",\n                        onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 491,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            zIndex: \"1302 !important\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                            position: \"right\",\n                            header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"align-content-center \",\n                                        children: [\n                                            \"Cr\\xe9ation \",\n                                            data_.data_type.name,\n                                            \" \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                            variant: \"text\",\n                                            table: table,\n                                            row: row\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            visible: createVisible,\n                            onHide: ()=>{\n                                table.setCreatingRow(null);\n                                setCreateVisible(false);\n                            },\n                            className: \"w-full md:w-9 lg:w-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: [\n                                    internalEditComponents,\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 7\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        zIndex: \"1302 !important\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        position: \"right\",\n                        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"align-content-center \",\n                                    children: [\n                                        \"Editer \",\n                                        data_.data_type.name,\n                                        \" \",\n                                        row.original.code\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                        variant: \"text\",\n                                        table: table,\n                                        row: row\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        visible: editVisible,\n                        onHide: ()=>{\n                            table.setEditingRow(null);\n                            setEditVisible(false);\n                        },\n                        className: \"w-full md:w-9 lg:w-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                gap: \"1.5rem\"\n                            },\n                            children: [\n                                internalEditComponents,\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 79\n                }, this)\n            }, void 0, false);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return row.original.description ? (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.description) : row.original.content ? (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.content) : row.original.staff ? row.original.staff.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    display: \"grid\",\n                    margin: \"auto\",\n                    //gridTemplateColumns: '1fr 1fr',\n                    width: \"100vw\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabView, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"staff\"].title,\n                            leftIcon: \"pi pi-user mr-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.staff.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 134\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 64\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"assistants\"].title,\n                            rightIcon: \"pi pi-user ml-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.assistants.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 139\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 69\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: \"Lettre\",\n                            leftIcon: \"pi pi-file-word mr-2\",\n                            rightIcon: \"pi pi-file-pdf ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    icon: \"pi pi-check\",\n                                    rounded: true,\n                                    onClick: ()=>setVisible(true),\n                                    disabled: row.original.document === null\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: [\n                                            \"Lettre de mission : \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    visible: visible,\n                                    onHide: ()=>setVisible(false),\n                                    className: \"w-full md:w-9 lg:w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                        children: [\n                                            row.original.document !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Document, {\n                                                file: row.original.document,\n                                                onLoadSuccess: onDocumentLoadSuccess,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Page, {\n                                                    pageNumber: pageNumber\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 27\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No Document\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Page \",\n                                                            pageNumber || (numPages ? 1 : \"--\"),\n                                                            \" of \",\n                                                            numPages || \"--\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row align-items-center justify-content-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber <= 1,\n                                                                onClick: previousPage,\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 660,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber >= numPages,\n                                                                onClick: nextPage,\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 23\n                                    }, this)\n                                }, row.original.id, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 19\n                        }, this),\n                        \"          \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 624,\n                columnNumber: 15\n            }, this) : null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n        },\n        renderRowActions: (param)=>// <Box sx={{ display: 'flex', gap: '1rem' }}>\n        /*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 684,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 683,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    // console.log(data_.isLoading)\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 711,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_16__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\processes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 711,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"3RAFQliHDbfbMvEtoep07KyNeFs=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessUpdate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiProcessDestroy,\n        material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1jbGllbnQpLy4vYXBwLyhtYWluKS9wcm9jZXNzZXMvKGNvbXBvbmVudHMpL0dlbmVyaWNUQWJsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3lFO0FBTzNDO0FBQ3dDO0FBQzNCO0FBQzBCO0FBQ3hCO0FBQ1U7QUFDbEI7QUFDSTtBQUNTO0FBQ1o7QUFDVTtBQUNvRDtBQUlyRixTQUFTd0IsYUFBb0NDLEtBQThGOztJQUV4SixNQUFNQyxRQUFRVCw2Q0FBTUEsQ0FBYTtJQUNqQyxNQUFNLEVBQUVVLFFBQVNDLGFBQWEsRUFBQyxHQUFHUCxzRUFBbUJBO0lBQ3JELE1BQU0sRUFBRU0sUUFBU0UsYUFBYSxFQUFDLEdBQUdOLHNFQUFtQkE7SUFDckQsTUFBTSxFQUFFSSxRQUFTRyxhQUFhLEVBQUMsR0FBR1IsdUVBQW9CQTtJQUN0RCxNQUFNLENBQUNTLFNBQVNDLFdBQVcsR0FBR2QsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDZSxhQUFhQyxlQUFlLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNpQixlQUFlQyxpQkFBaUIsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQ25ELFNBQVNtQixtQkFBbUJDLEtBQVU7UUFDcENDLFFBQVFDLEdBQUcsQ0FBQ2YsTUFBTWdCLFVBQVU7UUFDNUJoQixNQUFNZ0IsVUFBVSxDQUFDQyxHQUFHLENBQUNKO0lBQ3ZCO0lBQ0EsTUFBTSxDQUFDSyxVQUFVQyxZQUFZLEdBQUcxQiwrQ0FBUUEsQ0FBYztJQUN0RCxNQUFNLENBQUMyQixZQUFZQyxjQUFjLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUc3QyxNQUFNNkIsU0FBUztZQUNickI7U0FBQUEsaUJBQUFBLE1BQU1zQixPQUFPLGNBQWJ0QixxQ0FBQUEsZUFBZXVCLElBQUksQ0FBQztZQUFFQyxVQUFVO1lBQVFDLFNBQVM7WUFBYUMsUUFBUTtZQUFxQkMsTUFBTTtRQUFLO0lBQ3hHO0lBRUEsTUFBTUMsU0FBUztZQUNiNUI7U0FBQUEsaUJBQUFBLE1BQU1zQixPQUFPLGNBQWJ0QixxQ0FBQUEsZUFBZXVCLElBQUksQ0FBQztZQUFFQyxVQUFVO1lBQVFDLFNBQVM7WUFBWUMsUUFBUTtZQUFxQkMsTUFBTTtRQUFLO0lBQ3ZHO0lBRUEsU0FBU0Usc0JBQXNCLEtBQW1DO1lBQW5DLEVBQUVaLFFBQVEsRUFBeUIsR0FBbkM7UUFDN0JDLFlBQVlEO1FBQ1pHLGNBQWM7SUFDaEI7SUFDQSxTQUFTVSxXQUFXQyxNQUFjO1FBQ2hDWCxjQUFjWSxDQUFBQSxpQkFBa0JBLGlCQUFpQkQ7SUFDbkQ7SUFFQSxTQUFTRTtRQUNQSCxXQUFXLENBQUM7SUFDZDtJQUVBLFNBQVNJO1FBQ1BKLFdBQVc7SUFDYjtJQUNBLE1BQU0sQ0FBQ0ssVUFBVUMsWUFBWSxHQUFHNUMsK0NBQVFBLENBQXFCO0lBQzdELE1BQU0sQ0FBQzZDLGtCQUFrQkMsb0JBQW9CLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNK0MsT0FBT0MsUUFBUUw7SUFDckIsTUFBTU0sY0FBYyxDQUFDQztRQUNuQk4sWUFBWU0sTUFBTUMsYUFBYTtJQUNqQztJQUVBLE1BQU1DLFVBQVV0RCw4Q0FBT0EsQ0FDckIsSUFDRXVELE9BQU9DLE9BQU8sQ0FBQy9DLE1BQU1nRCxTQUFTLENBQUNDLFVBQVUsRUFBRUMsTUFBTSxDQUFDLFFBQWVDO2dCQUFkLENBQUNDLEtBQUtDLE1BQU07bUJBQVksQ0FBQztnQkFBQztnQkFBZTthQUFjLENBQUNDLFFBQVEsQ0FBQ0Y7UUFBRyxHQUFHRyxHQUFHLENBQUMsUUFBZUo7Z0JBQWQsQ0FBQ0MsS0FBS0MsTUFBTTtZQUN6SSxJQUFJO2dCQUFDO2dCQUFVO2dCQUFXO2dCQUFRO2dCQUFTO2dCQUFXO2FBQWMsQ0FBQ0MsUUFBUSxDQUFDRixNQUFNO29CQUV4RXBEO2dCQURWLE9BQU87b0JBQ0x3RCxRQUFReEQsQ0FBQUEsd0NBQUFBLE1BQU1nRCxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDekQsbURBQUFBLHdDQUF5Q29EO29CQUNqRE0sYUFBYU47b0JBQ2JPLElBQUlQO29CQUNKUSxNQUFNOzRCQUFDLEVBQUVDLElBQUksRUFBRTt3QkFBTyxJQUFJOzRCQUFDOzRCQUFlOzRCQUFVO3lCQUFTLENBQUNQLFFBQVEsQ0FBQ0YsTUFBTSxPQUFPOzZCQUFXLHFCQUFPLDhEQUFDVTtzQ0FBS3BFLDZEQUFLQSxDQUFDbUUsS0FBS0UsUUFBUTs7Ozs7O29CQUFtQjtvQkFDbEpDLE1BQU07NEJBQUMsRUFBRUgsSUFBSSxFQUFFSSxNQUFNLEVBQUVDLEdBQUcsRUFBRUMsS0FBSyxFQUFFO3dCQUNqQyxxQkFBTyw4REFBQ3hFLDBEQUFNQTs0QkFDWnlFLGNBQWNGLElBQUlHLFFBQVEsQ0FBQ2pCLElBQUk7NEJBQy9Ca0Isa0JBQWlCOzRCQUNqQkMsUUFBTzs0QkFDUEMsTUFBTTtnQ0FDSkMsUUFBUTtnQ0FDUkMsU0FBUztnQ0FDVEMsU0FBUztvQ0FDUDtvQ0FBVztvQ0FBWTtvQ0FBUztvQ0FBUTtvQ0FBUztvQ0FBVztvQ0FBUztvQ0FBVztvQ0FDaEY7b0NBQWlCO29DQUFnQjtvQ0FBUTtvQ0FDekM7b0NBQWtCO29DQUFTO29DQUFTO29DQUFTO29DQUFRO29DQUFRO2lDQUM5RDtnQ0FDREMsU0FDRTs0QkFHSjs7Ozs7O29CQUdKO2dCQUNGO1lBQ0Y7WUFDQSxJQUFJeEIsUUFBTSxVQUFTO29CQUdQcEQ7Z0JBRFYsT0FBTztvQkFDTHdELFFBQVF4RCxDQUFBQSx5Q0FBQUEsTUFBTWdELFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckN6RCxvREFBQUEseUNBQXlDb0Q7b0JBQ2pETSxhQUFhO29CQUNibUIsdUJBQXVCO3dCQUNyQkMsT0FBTztvQkFDVDtvQkFDQUMsdUJBQXVCO3dCQUNyQkQsT0FBTztvQkFDVDtvQkFHQUUseUJBQXlCO3dCQUN2QkYsT0FBTztvQkFDVDtvQkFDQW5CLElBQUlQO2dCQUNOO1lBQ0Y7WUFDQSxJQUFJcEQsTUFBTWdELFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUM2QixNQUFNLEtBQUssZUFBZWpGLE1BQU1nRCxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDNkIsTUFBTSxLQUFLLFFBQVE7b0JBSXJHakY7Z0JBRlYsT0FBTztvQkFDTGtGLFlBQVksQ0FBQ2hCLE1BQVEsSUFBSWlCLEtBQUsvQixPQUFPLFlBQVljLElBQUlrQixPQUFPLEdBQUdsQixJQUFJbUIsUUFBUTtvQkFDM0U3QixRQUFReEQsQ0FBQUEseUNBQUFBLE1BQU1nRCxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDekQsb0RBQUFBLHlDQUF5Q29EO29CQUNqRGtDLGVBQWU7b0JBQ2ZDLFVBQVU7b0JBQ1ZDLFdBQVc7b0JBQ1g5QixhQUFhTjtvQkFDYlEsTUFBTTs0QkFBQyxFQUFFQyxJQUFJLEVBQUU7NEJBQUtBO2dDQUFBQSxpQkFBQUEsS0FBS0UsUUFBUSxnQkFBYkYscUNBQUFBLGVBQXVCNEIsa0JBQWtCLENBQUM7O29CQUM5RDlCLElBQUlQO29CQUNKWSxNQUFNLElBQU07Z0JBQ2Q7WUFDRjtZQUNBLElBQUlaLFFBQVEsZUFBZTtvQkFHZnBEO2dCQURWLE9BQU87b0JBQ0x3RCxRQUFReEQsQ0FBQUEseUNBQUFBLE1BQU1nRCxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDekQsb0RBQUFBLHlDQUF5Q29EO29CQUNqRE0sYUFBYU47b0JBQ2JzQyxtQkFBbUIxRixNQUFNZ0QsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ3VDLEtBQUssSUFBSTNGLE1BQU1nRCxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDdUMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxPQUFPLEdBQUczRixNQUFNZ0QsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ3VDLEtBQUssQ0FBQyxFQUFFLENBQUMsT0FBTyxDQUFDQyxJQUFJLEdBQUcsRUFBRTtvQkFDdktDLHVCQUF1Qjt3QkFDdkJDLFFBQVE7d0JBQ1JDLFNBQVM7d0JBRVRDLGFBQWE7NEJBQ1hDLFVBQVU7d0JBR1o7b0JBSUY7b0JBQ0FwQix1QkFBdUI7d0JBQ3JCQyxPQUFPO29CQUNUO29CQUNBQyx1QkFBdUI7d0JBQ3JCRCxPQUFPO29CQUNUO29CQUNBRSx5QkFBeUI7d0JBQ3ZCRixPQUFPO29CQUNUO29CQUNBbkIsSUFBSVA7b0JBQ0pRLE1BQU07NEJBQUMsRUFBRUMsSUFBSSxFQUFFSyxHQUFHLEVBQUU7K0JBQUssOERBQUM3RSwrQ0FBR0E7NEJBRTNCb0MsVUFBVW9DLEtBQUtFLFFBQVEsT0FBZSxPQUNwQyxXQUFXRixLQUFLRSxRQUFRLE9BQWUsV0FBVyxZQUFZOzRCQUVoRVYsT0FBT1EsS0FBS0UsUUFBUSxPQUFlLE9BQ2pDLHNCQUFtQkYsS0FBS0UsUUFBUSxPQUFlLFdBQVcsZUFBZTsyQkFMdEVHLElBQUlHLFFBQVEsQ0FBQzZCLElBQUksR0FBR2hDLElBQUlHLFFBQVEsQ0FBQ2UsT0FBTzs7Ozs7b0JBUTFDO2dCQUNQO1lBQ0Y7WUFDQSxJQUFJaEMsUUFBUSxRQUFRO29CQUVScEQ7Z0JBRFYsT0FBTztvQkFDTHdELFFBQVF4RCxDQUFBQSx5Q0FBQUEsTUFBTWdELFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckN6RCxvREFBQUEseUNBQXlDb0Q7b0JBQ2pETSxhQUFhTjtvQkFDYnlCLHVCQUF1Qjt3QkFDckJDLE9BQU87b0JBQ1Q7b0JBQ0FDLHVCQUF1Qjt3QkFDckJELE9BQU87b0JBQ1Q7b0JBQ0FFLHlCQUF5Qjt3QkFDdkJGLE9BQU87b0JBQ1Q7b0JBQ0Esb0VBQW9FO29CQUNwRWUsdUJBQXVCO3dCQUNyQkMsUUFBUTt3QkFDUkMsU0FBUzt3QkFDVCwwREFBMEQ7d0JBQzFEQyxhQUFhO3dCQUliO29CQUdGO29CQUNBckMsSUFBSVA7b0JBQ0pRLE1BQU07NEJBQUMsRUFBRUMsSUFBSSxFQUFFSyxHQUFHLEVBQUU7K0JBQUssOERBQUM3RSwrQ0FBR0E7NEJBRTNCb0MsVUFBVW9DLEtBQUtFLFFBQVEsT0FBZSxPQUNwQyxXQUFXRixLQUFLRSxRQUFRLE9BQWUsT0FBTyxZQUFZOzRCQUU1RFYsT0FBT1EsS0FBS0UsUUFBUSxPQUFlLE9BQ2pDLGtCQUFlRixLQUFLRSxRQUFRLE9BQWUsT0FBTyxjQUFjRixLQUFLRSxRQUFRLE9BQWUsT0FBTyxhQUFhOzJCQUw3R0csSUFBSUcsUUFBUSxDQUFDNkIsSUFBSSxHQUFHaEMsSUFBSUcsUUFBUSxDQUFDZSxPQUFPOzs7OztvQkFRMUM7Z0JBQ1A7WUFDRjtZQUVBLElBQUloQyxRQUFRLFlBQVk7b0JBR1pwRDtnQkFGVixxRUFBcUU7Z0JBQ3JFLE9BQU87b0JBQ0x3RCxRQUFReEQsQ0FBQUEseUNBQUFBLE1BQU1nRCxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDekQsb0RBQUFBLHlDQUF5Q29EO29CQUNqRE0sYUFBYU47b0JBQ2J5Qix1QkFBdUI7d0JBQ3JCQyxPQUFPO29CQUNUO29CQUNBQyx1QkFBdUI7d0JBQ3JCRCxPQUFPO29CQUNUO29CQUVBRSx5QkFBeUI7d0JBQ3ZCRixPQUFPO29CQUNUO29CQUNBbkIsSUFBSVA7b0JBQ0pRLE1BQU07NEJBQUMsRUFBRUMsSUFBSSxFQUFFSyxHQUFHLEVBQUU7K0JBQ2xCLDhEQUFDN0UsK0NBQUdBOzRCQUVGb0MsVUFDRTs0QkFFRjRCLE9BQU9RLEtBQUtFLFFBQVE7MkJBSmZHLElBQUlHLFFBQVEsQ0FBQzZCLElBQUksR0FBR2hDLElBQUlHLFFBQVEsQ0FBQ2UsT0FBTzs7Ozs7b0JBS3pDO2dCQUNWO1lBQ0Y7WUFDQSxJQUFJcEYsTUFBTWdELFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUMsT0FBTyxJQUFJcEQsTUFBTWdELFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUMsT0FBTyxDQUFDd0MsSUFBSSxFQUFFO2dCQUMzRjlFLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJxQyxLQUFLQztvQkFFaENyRDtnQkFEVixPQUFPO29CQUNMd0QsUUFBUXhELENBQUFBLHlDQUFBQSxNQUFNZ0QsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ0ssS0FBSyxjQUFyQ3pELG9EQUFBQSx5Q0FBeUNvRDtvQkFDakQsb0pBQW9KO29CQUNwSk0sYUFBYU47b0JBQ2JPLElBQUlQO29CQUNKUSxNQUFNOzRCQUFDLEVBQUVDLElBQUksRUFBRUssR0FBRyxFQUFFOytCQUFLTCxLQUFLSyxHQUFHLENBQUNHLFFBQVEsQ0FBQ2pCLElBQUk7b0JBQUQ7b0JBQzlDc0MsbUJBQW1CMUYsTUFBTWdELFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUMsT0FBTyxDQUFDd0MsSUFBSTtvQkFDL0RDLHVCQUF1Qjt3QkFDckJDLFFBQVE7d0JBQ1JDLFNBQVM7d0JBQ1RJLFVBQVVuRyxNQUFNZ0QsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQyxPQUFPLENBQUN3QyxJQUFJO3dCQUN0REksYUFBYTt3QkFJYjtvQkFHRjtnQkFDRjtZQUNGLE9BQ0s7b0JBRU9oRyx3Q0FNRUE7Z0JBUFosSUFBSW9ELFFBQVEsTUFBTSxPQUFPO29CQUN2QkksUUFBUXhELENBQUFBLHlDQUFBQSxNQUFNZ0QsU0FBUyxDQUFDQyxVQUFVLENBQUNHLElBQUksQ0FBQ0ssS0FBSyxjQUFyQ3pELG9EQUFBQSx5Q0FBeUNvRDtvQkFDakRNLGFBQWFOO29CQUNiTyxJQUFJUDtvQkFDSlksTUFBTSxJQUFNO2dCQUNkO3FCQUNFLE9BQU87b0JBQ0xSLFFBQVF4RCxDQUFBQSx5Q0FBQUEsTUFBTWdELFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckN6RCxvREFBQUEseUNBQXlDb0Q7b0JBQ2pETSxhQUFhTjtvQkFDYk8sSUFBSVA7Z0JBRU47WUFFSjtRQUNGLElBRUYsRUFBRTtJQUdKdEMsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ2Y7SUFFN0MsTUFBTW1FLFFBQVF0RiwyRUFBcUJBLENBQUM7UUFDbENnRTtRQUNBdUQsTUFBTXBHLE1BQU1xRyxLQUFLLEdBQUcsRUFBRSxHQUFHckcsTUFBTUEsS0FBSyxDQUFDb0csSUFBSSxDQUFDRSxPQUFPLEdBQUl0RyxNQUFNQSxLQUFLLENBQUNvRyxJQUFJLENBQUNFLE9BQU8sR0FBRztZQUFDdEcsTUFBTUEsS0FBSyxDQUFDb0csSUFBSTtTQUFDO1FBQ2xHRyxVQUFVdkcsTUFBTXFHLEtBQUssR0FBRyxJQUFJckcsTUFBTUEsS0FBSyxDQUFDb0csSUFBSSxDQUFDRSxPQUFPLEdBQUV0RyxNQUFNQSxLQUFLLENBQUNvRyxJQUFJLENBQUNJLEtBQUssR0FBRztRQUMvRUMsb0JBQW9CO1FBQ3BCQyxzQkFBc0I7UUFDdEJDLG9CQUFvQjtRQUNwQkMsZ0JBQWdCO1FBQ2hCQyxrQkFBa0I7UUFDbEJDLGtCQUFrQjtRQUNsQkMsb0JBQW9CO1FBQ3BCQyxvQkFBb0I7UUFDcEJDLHFCQUFxQjtRQUNyQkMsc0JBQXNCO1FBQ3RCQyxrQkFBa0I7UUFDbEJDLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmQyxpQkFBaUI7UUFDakJDLGtCQUFrQjtRQUVsQkMsY0FBYztZQUNaeEcsWUFBWTtnQkFBRXlHLFVBQVU7Z0JBQUdDLFdBQVc7WUFBRTtZQUN4Q0Msa0JBQWtCO2dCQUFFQyxZQUFZO2dCQUFPeEMsU0FBUztnQkFBT3lDLFlBQVk7Z0JBQU94QyxVQUFVO2dCQUFPeUMsYUFBYTtnQkFBT0MsT0FBTztnQkFBT0MsWUFBWTtnQkFBT3JFLElBQUk7Z0JBQU9zRSxVQUFVO1lBQU07WUFDM0tDLFNBQVM7WUFDVEMsa0JBQWtCO1lBQ2xCQyxTQUFTO2dCQUFDO29CQUFFekUsSUFBSTtvQkFBTTBFLE1BQU07Z0JBQU07YUFBRTtRQUN0QztRQUNBeEgsT0FBTztZQUNMRyxZQUFZaEIsTUFBTWdCLFVBQVUsQ0FBQ3NILElBQUk7WUFDakNDLFdBQVd2SSxNQUFNdUksU0FBUztRQUc1QjtRQUNBQyxjQUFjMUosZ0ZBQW1CQTtRQUNqQzhCLG9CQUFvQkE7UUFDcEI2SCx5QkFBeUI7WUFDdkIsZUFBZTtnQkFDYkMsY0FBYztZQUNoQjtZQUNBLGtCQUFrQjtnQkFDaEJBLGNBQWM7WUFDaEI7WUFDQSxtQkFBbUI7Z0JBQ2pCLCtDQUErQztnQkFDL0NDLE1BQU07Z0JBQ05ELGNBQWM7WUFNaEI7WUFDQSxtQkFBbUI7Z0JBQ2pCQSxjQUFjO1lBQ2hCO1FBQ0Y7UUFDQUUsZUFBZTtZQUNiQyxNQUFNO1lBQ05DLGlCQUFpQjtRQUNuQjtRQUNBQyxvQkFBb0I7Z0JBQUMsRUFBRTVFLEtBQUssRUFBRTttQkFBTTtnQkFDbEMsMkNBQTJDO2dCQUMzQyx3QkFBd0I7Z0JBQ3hCNkUsV0FBVztnQkFDWEMsU0FBUztvQkFBRUMsTUFBTTtnQkFBcUQ7Z0JBRXRFQyxJQUFJO29CQUNGMUUsUUFBUztvQkFDVCxpQ0FBaUM7b0JBQ2pDLHFCQUFxQjtvQkFDckIsZ0NBQWdDO29CQUNoQzJFLGlCQUFpQjtvQkFDakJDLFlBQVk7b0JBQ1pDLHFCQUFxQjtvQkFDckIsK0JBQStCO3dCQUM3QkYsaUJBQWlCO3dCQUNqQkMsWUFBWTt3QkFDZEMscUJBQXFCO3dCQUNyQkMsT0FBTztvQkFDUDtvQkFDQSxtQkFBbUI7d0JBQ2pCSCxpQkFBaUI7d0JBQ2pCQyxZQUFZO3dCQUNkQyxxQkFBcUI7d0JBQ3JCQyxPQUFPO29CQUNQO2dCQUNGO1lBQ0Y7UUFBQTtRQUNBQyxpQkFBaUI7UUFDakJDLG1CQUFtQjtRQUNuQkMsa0JBQWtCO2dCQUFDLEVBQUV2RixLQUFLLEVBQUV3RixNQUFNLEVBQUU7Z0JBSWxDMUo7WUFIQSxlQUFlO1lBQ2Ysa0JBQWtCO1lBQ2xCa0UsTUFBTXlGLGFBQWEsQ0FBQyxPQUFPLG1CQUFtQjthQUM5QzNKLGlCQUFBQSxNQUFNc0IsT0FBTyxjQUFidEIscUNBQUFBLGVBQWV1QixJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQVFDLFNBQVM7Z0JBQVFDLFFBQVE7WUFBc0I7UUFDekY7UUFDQWtJLG9CQUFvQjtnQkFDbEIsNkJBQTZCO1lBQzdCNUo7YUFBQUEsaUJBQUFBLE1BQU1zQixPQUFPLGNBQWJ0QixxQ0FBQUEsZUFBZXVCLElBQUksQ0FBQztnQkFBRUMsVUFBVTtnQkFBUUMsU0FBUztnQkFBUUMsUUFBUTtZQUFhO1FBQ2hGO1FBQ0FtSSxtQkFBbUI7Z0JBQUMsRUFBRTNGLEtBQUssRUFBRXdGLE1BQU0sRUFBRTtZQUNuQyxlQUFlO1lBQ2Ysa0JBQWtCO1lBQ2xCeEosY0FBY3dKO1lBQ2R4RixNQUFNNEYsY0FBYyxDQUFDLE9BQU8sb0JBQW9CO1FBQ2xEO1FBQ0FDLHFCQUFxQjtRQUNuQiw2QkFBNkI7UUFDL0I7UUFDQUMsdUJBQXVCO2dCQUFDLEVBQUUvRixHQUFHLEVBQUVDLEtBQUssRUFBRTttQkFBTTtnQkFDMUMsaUNBQWlDO2dCQUNqQyxzQkFBc0I7Z0JBQ3RCM0IsTUFBTWhDLGVBQWVFO2dCQUNyQnlJLElBQUk7b0JBQ0YscUJBQXFCO3dCQUNuQmUsU0FBUztvQkFDWDtvQkFDQSwwQkFBMEI7d0JBQ3hCQSxTQUFTO29CQUNYO29CQUNBQyxRQUFRO2dCQUVWO1lBQ0Y7UUFBQTtRQUNBQyxxQkFBcUI7WUFDbkJwQixXQUFXO1lBQ1hHLElBQUk7Z0JBQ0YsMEJBQTBCO29CQUN4QkMsaUJBQWlCO2dCQUNuQjtnQkFDQUEsaUJBQWlCO2dCQUNqQkcsT0FBTztnQkFDUEYsWUFBWTtnQkFDWkMscUJBQXFCO1lBQ3ZCO1FBQ0Y7UUFDQWUsd0JBQXdCO2dCQUFDLEVBQUVsRyxLQUFLLEVBQUU7Z0JBUWlEQSxtQ0FBc0RBO21CQVJqRztnQkFDdEM2RSxXQUFXO2dCQUNYRyxJQUFJO29CQUNGRSxZQUFZO29CQUNaQyxxQkFBcUI7b0JBQ3JCLHFCQUFxQjtvQkFDckIsZ0NBQWdDO29CQUNoQ0YsaUJBQWlCO29CQUNqQjNFLFFBQVFOLE1BQU1tRyxRQUFRLEdBQUdDLFlBQVksR0FBSSxnQkFBZSwrQkFBdUJwRyxvQ0FBQUEsTUFBTXFHLElBQUksQ0FBQ0MsYUFBYSxDQUFDbEosT0FBTyxjQUFoQzRDLHdEQUFBQSxrQ0FBa0N1RyxZQUFZLEVBQUMsU0FBeUQsUUFBbER2Ryx1Q0FBQUEsTUFBTXFHLElBQUksQ0FBQ0csZ0JBQWdCLENBQUNwSixPQUFPLGNBQW5DNEMsMkRBQUFBLHFDQUFxQ3VHLFlBQVksRUFBQztnQkFFekw7WUFDRjs7UUFDQUUsb0JBQW9CO1lBQ2xCNUIsV0FBVztZQUNYRyxJQUFJO2dCQUVGLHFCQUFxQjtnQkFDckIsZ0NBQWdDO2dCQUNoQ0MsaUJBQWlCO2dCQUNqQkcsT0FBTztnQkFDUEYsWUFBWTtnQkFDWkMscUJBQXFCO1lBQ3ZCO1FBQ0Y7UUFDQXpFLHVCQUF1QjtZQUNyQnNFLElBQUk7Z0JBQ0YscUJBQXFCO2dCQUNyQixnQ0FBZ0M7Z0JBQ2hDQyxpQkFBaUI7Z0JBQ2pCRyxPQUFPO2dCQUNQRixZQUFZO2dCQUNaQyxxQkFBcUI7WUFDdkI7UUFDRjtRQUNBdUIsb0JBQW9CO1lBQ2xCN0IsV0FBVztZQUNYRyxJQUFJO2dCQUNGRSxZQUFZO2dCQUNaQyxxQkFBcUI7Z0JBQ3JCLHFCQUFxQjtnQkFDckIsZ0NBQWdDO2dCQUNoQ0YsaUJBQWlCO2dCQUNqQkcsT0FBTztZQUNUO1FBRUY7UUFDQXVCLG1CQUFtQjtZQUNqQjlCLFdBQVc7WUFDWEcsSUFBSTtnQkFDRkUsWUFBWTtnQkFDWkMscUJBQXFCO2dCQUNyQiwrQ0FBK0M7Z0JBQy9DLDhCQUE4QjtvQkFDNUJGLGlCQUFpQjtvQkFDakJHLE9BQU87b0JBQ1BGLFlBQVk7b0JBQ1pDLHFCQUFxQjtnQkFDdkI7Z0JBQ0EsK0JBQStCO29CQUM3QkYsaUJBQWlCO29CQUNqQkcsT0FBTztvQkFDUEYsWUFBWTtvQkFDWkMscUJBQXFCO2dCQUN2QjtZQUNGO1FBQ0Y7UUFDQXlCLCtCQUErQjtnQkFBQyxFQUFFNUcsS0FBSyxFQUFFO21CQUN2Qyw4REFBQ3pGLHFEQUFLQTtnQkFBQ3NNLFdBQVc7Z0JBQU9DLFNBQVM7O2tDQWtCaEMsOERBQUNsTSxxREFBTUE7d0JBQ0xtTSxNQUFLO3dCQUNMQyxPQUFPO3dCQUNQLG9CQUFvQjt3QkFDcEJDLGlCQUFlNUksT0FBTyxlQUFlNkk7d0JBQ3JDQyxpQkFBYzt3QkFDZEMsaUJBQWUvSSxPQUFPLFNBQVM2STt3QkFDL0JHLFNBQVMsQ0FBQzdJOzRCQUNSd0IsTUFBTTRGLGNBQWMsQ0FBQzs0QkFBT3BKLGlCQUFpQixPQUFPRyxRQUFRQyxHQUFHLENBQUM7d0JBQ2xFO3dCQUVBNEgsTUFBSzs7Ozs7O2tDQUdQLDhEQUFDNUoscURBQU1BO3dCQUNMb00sT0FBTzt3QkFDUE0sVUFBVXRILE1BQU11SCxxQkFBcUI7d0JBQ3JDLG9CQUFvQjt3QkFDcEJOLGlCQUFlNUksT0FBTyxlQUFlNkk7d0JBQ3JDQyxpQkFBYzt3QkFDZEMsaUJBQWUvSSxPQUFPLFNBQVM2STt3QkFDL0JHLFNBQVM5STt3QkFDVHdJLE1BQUs7d0JBQ0wsb0ZBQW9GO3dCQUNwRnZDLE1BQUs7Ozs7OztrQ0FHUCw4REFBQzVKLHFEQUFNQTt3QkFBQ21NLE1BQUs7d0JBQWFDLE9BQU87d0JBQUM1QixPQUFPakgsbUJBQW1CLGNBQWM7d0JBQVdxRyxNQUFLO3dCQUN4RmdELGNBQVc7d0JBQU9ILFNBQVMsSUFBTWpKLG9CQUFvQixDQUFDRDs7Ozs7Ozs7Ozs7O1FBSW5EO1FBc0NUc0oscUJBQXFCLElBQU87Z0JBQzFCekMsSUFBSSxDQUFDMEMsUUFBVzt3QkFDZHpDLGlCQUNFeUMsTUFBTUMsT0FBTyxDQUFDQyxJQUFJLEtBQUssU0FDbkIsMEJBQ0E7b0JBQ1I7WUFDRjtRQUNBQyw4QkFBOEI7Z0JBQUMsRUFBRUMsc0JBQXNCLEVBQUUvSCxHQUFHLEVBQUVDLEtBQUssRUFBRTttQkFBSzs7b0JBQUU7a0NBQUMsOERBQUNMO3dCQUFJb0ksT0FBTzs0QkFBRS9CLFFBQVE7d0JBQWtCO2tDQUNuSCw0RUFBQ2pMLHdEQUFPQTs0QkFBQ2lOLFVBQVM7NEJBQVEzSSxzQkFDeEIsOERBQUNNO2dDQUFJa0YsV0FBVTs7a0RBQXlELDhEQUFDb0Q7d0NBQUtwRCxXQUFVOzs0Q0FBd0I7NENBQVVoSixNQUFNZ0QsU0FBUyxDQUFDcUosSUFBSTs0Q0FBQzs0Q0FBRW5JLElBQUlHLFFBQVEsQ0FBQzZCLElBQUk7OztrREFDaEssOERBQUMxSCxzREFBYUE7a0RBQ1osNEVBQUNHLHVFQUFxQkE7NENBQUNvSCxTQUFROzRDQUFPNUIsT0FBT0E7NENBQU9ELEtBQUtBOzs7Ozs0QkFFckQ1RCxTQUFTSTs0QkFBZTRMLFFBQVE7Z0NBQVFuSSxNQUFNNEYsY0FBYyxDQUFDO2dDQUFPcEosaUJBQWlCOzRCQUFPOzRCQUFHcUksV0FBVTtzQ0FDakgsNEVBQUN2SyxzREFBYUE7Z0NBQ1owSyxJQUFJO29DQUFFZSxTQUFTO29DQUFRcUMsZUFBZTtvQ0FBVUMsS0FBSztnQ0FBUzs7b0NBRzdEUDtvQ0FBdUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFJdEI7UUFDUlEsNEJBQTRCO2dCQUFDLEVBQUVSLHNCQUFzQixFQUFFL0gsR0FBRyxFQUFFQyxLQUFLLEVBQUU7bUJBQUs7MEJBQUUsNEVBQUNMO29CQUFJb0ksT0FBTzt3QkFBRS9CLFFBQVE7b0JBQWtCOzhCQUNoSCw0RUFBQ2pMLHdEQUFPQTt3QkFBQ2lOLFVBQVM7d0JBQVEzSSxzQkFDeEIsOERBQUNNOzRCQUFJa0YsV0FBVTs7OENBQXlELDhEQUFDb0Q7b0NBQUtwRCxXQUFVOzt3Q0FBd0I7d0NBQVFoSixNQUFNZ0QsU0FBUyxDQUFDcUosSUFBSTt3Q0FBQzt3Q0FBRW5JLElBQUlHLFFBQVEsQ0FBQzZCLElBQUk7Ozs4Q0FDOUosOERBQUMxSCxzREFBYUE7OENBQ1osNEVBQUNHLHVFQUFxQkE7d0NBQUNvSCxTQUFRO3dDQUFPNUIsT0FBT0E7d0NBQU9ELEtBQUtBOzs7Ozt3QkFFckQ1RCxTQUFTRTt3QkFBYThMLFFBQVE7NEJBQVFuSSxNQUFNeUYsYUFBYSxDQUFDOzRCQUFPbkosZUFBZTt3QkFBTzt3QkFBR3VJLFdBQVU7a0NBQzVHLDRFQUFDdkssc0RBQWFBOzRCQUNaMEssSUFBSTtnQ0FBRWUsU0FBUztnQ0FBUXFDLGVBQWU7Z0NBQVVDLEtBQUs7NEJBQVM7O2dDQUc3RFA7Z0NBQXVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFJdEI7UUFDUlMsbUJBQW1CO2dCQUFDLEVBQUV4SSxHQUFHLEVBQUU7bUJBQ3pCQSxJQUFJRyxRQUFRLENBQUNzSSxXQUFXLEdBQUdqTiw2REFBS0EsQ0FBQ3dFLElBQUlHLFFBQVEsQ0FBQ3NJLFdBQVcsSUFDdkR6SSxJQUFJRyxRQUFRLENBQUN1SSxPQUFPLEdBQUdsTiw2REFBS0EsQ0FBQ3dFLElBQUlHLFFBQVEsQ0FBQ3VJLE9BQU8sSUFDL0MxSSxJQUFJRyxRQUFRLENBQUMwRCxLQUFLLEdBQ2hCN0QsSUFBSUcsUUFBUSxDQUFDMEQsS0FBSyxDQUFDOEUsTUFBTSxHQUFHLGtCQUMxQiw4REFBQ3RPLHNEQUFHQTtnQkFDRjRLLElBQUk7b0JBQ0ZlLFNBQVM7b0JBQ1Q0QyxRQUFRO29CQUNSLGlDQUFpQztvQkFDakNDLE9BQU87Z0JBQ1Q7MEJBRUEsNEVBQUMzTix3REFBT0E7O3NDQUVOLDhEQUFDRCx5REFBUUE7NEJBQUNxRSxRQUFReEQsTUFBTWdELFNBQVMsQ0FBQ0MsVUFBVSxDQUFDLFFBQVEsQ0FBQ1EsS0FBSzs0QkFBRXVKLFVBQVM7c0NBRXBFLDRFQUFDQzswQ0FBSS9JLElBQUlHLFFBQVEsQ0FBQzBELEtBQUssQ0FBQ3hFLEdBQUcsQ0FBQyxDQUFDMkosTUFBTUMsb0JBQVEsOERBQUNDO3dDQUF1Q0MsTUFBTSxZQUFZSCxLQUFLSSxLQUFLO2tEQUFFLDRFQUFDQzs7Z0RBQUlMLEtBQUtNLFNBQVM7Z0RBQUM7Z0RBQUVOLEtBQUtPLFVBQVU7Ozs7Ozs7dUNBQW5HUCxLQUFLSSxLQUFLLEdBQUdwSixJQUFJRyxRQUFRLENBQUM2QixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7c0NBRW5GLDhEQUFDL0cseURBQVFBOzRCQUFDcUUsUUFBUXhELE1BQU1nRCxTQUFTLENBQUNDLFVBQVUsQ0FBQyxhQUFhLENBQUNRLEtBQUs7NEJBQUVpSyxXQUFVO3NDQUUxRSw0RUFBQ1Q7MENBQUkvSSxJQUFJRyxRQUFRLENBQUMyRCxVQUFVLENBQUN6RSxHQUFHLENBQUMsQ0FBQzJKLE1BQU1DLG9CQUFRLDhEQUFDQzt3Q0FBdUNDLE1BQU0sWUFBWUgsS0FBS0ksS0FBSztrREFBRSw0RUFBQ0M7O2dEQUFJTCxLQUFLTSxTQUFTO2dEQUFDO2dEQUFFTixLQUFLTyxVQUFVOzs7Ozs7O3VDQUFuR1AsS0FBS0ksS0FBSyxHQUFHcEosSUFBSUcsUUFBUSxDQUFDNkIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7O3NDQUd4Riw4REFBQy9HLHlEQUFRQTs0QkFBQ3FFLFFBQU87NEJBQVN3SixVQUFTOzRCQUF1QlUsV0FBVTs7OENBQ2xFLDhEQUFDM08scURBQU1BO29DQUFDbU0sTUFBSztvQ0FBY0MsT0FBTztvQ0FBQ0ssU0FBUyxJQUFNakwsV0FBVztvQ0FBT2tMLFVBQVV2SCxJQUFJRyxRQUFRLENBQUM0RCxRQUFRLEtBQUs7Ozs7Ozs4Q0FDeEcsOERBQUMvSSx3REFBT0E7b0NBQXVCc0Usc0JBQVEsOERBQUNtSzs7NENBQUc7NENBQXFCekosSUFBSUcsUUFBUSxDQUFDNkIsSUFBSTs7O29DQUFRNUYsU0FBU0E7b0NBQVNnTSxRQUFRLElBQU0vTCxXQUFXO29DQUFReUksV0FBVTs4Q0FFcEosNEVBQUNsRjt3Q0FBSWtGLFdBQVU7OzRDQUNaOUUsSUFBSUcsUUFBUSxDQUFDNEQsUUFBUSxLQUFLLHFCQUN6Qiw4REFBQzJGO2dEQUNDQyxNQUFNM0osSUFBSUcsUUFBUSxDQUFDNEQsUUFBUTtnREFDM0I2RixlQUFlaE07MERBRWYsNEVBQUNpTTtvREFBSzNNLFlBQVlBOzs7Ozs7Ozs7O3FFQUNOLDhEQUFDNE07MERBQUU7Ozs7OzswREFDbkIsOERBQUNsSztnREFBSWtGLFdBQVU7O2tFQUNiLDhEQUFDZ0Y7OzREQUFFOzREQUNLNU0sY0FBZUYsQ0FBQUEsV0FBVyxJQUFJLElBQUc7NERBQUc7NERBQUtBLFlBQVk7Ozs7Ozs7a0VBRTdELDhEQUFDNEM7d0RBQUlrRixXQUFVOzswRUFDYiw4REFBQ2pLLHFEQUFNQTtnRUFDTGtQLE1BQUs7Z0VBQ0x4QyxVQUFVckssY0FBYztnRUFDeEJvSyxTQUFTdEo7MEVBQ1Y7Ozs7OzswRUFHRCw4REFBQ25ELHFEQUFNQTtnRUFDTGtQLE1BQUs7Z0VBQ0x4QyxVQUFVckssY0FBY0Y7Z0VBQ3hCc0ssU0FBU3JKOzBFQUNWOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBMUJLK0IsSUFBSUcsUUFBUSxDQUFDVixFQUFFOzs7Ozs7Ozs7Ozt3QkFpQ3BCOzs7Ozs7Ozs7Ozt1QkFFYixxQkFBTztRQUFJO1FBQ3ZCdUssa0JBQWtCLFNBQ2hCLDhDQUE4Qzs7Z0JBRDdCLEVBQUVySyxJQUFJLEVBQUVLLEdBQUcsRUFBRUMsS0FBSyxFQUFFO21CQUVyQyw4REFBQ2lJO2dCQUFLcEQsV0FBVTs7a0NBQ2QsOERBQUNqSyxxREFBTUE7d0JBQUM0SixNQUFLO3dCQUFRdUMsTUFBSzt3QkFBZU0sU0FBUzs0QkFBUXJILE1BQU15RixhQUFhLENBQUMxRjs0QkFBTXpELGVBQWUsT0FBT0ssUUFBUUMsR0FBRyxDQUFDO3dCQUFvQjt3QkFBR29LLE9BQU87d0JBQUNnRCxRQUFROzs7Ozs7a0NBQzdKLDhEQUFDcFAscURBQU1BO3dCQUFDNEosTUFBSzt3QkFBUXVDLE1BQUs7d0JBQWNDLE9BQU87d0JBQUNnRCxRQUFRO3dCQUN0RDNDLFNBQVMsQ0FBQzdJLFFBQVUxRCxzRUFBWUEsQ0FBQztnQ0FDL0JtUCxRQUFRekwsTUFBTUMsYUFBYTtnQ0FDM0J5TCxTQUFTO2dDQUNUbkQsTUFBTTtnQ0FDTiwwQkFBMEI7Z0NBQzFCb0QsaUJBQWlCO2dDQUNqQkMsYUFBYTtnQ0FDYkMsYUFBYTtnQ0FDYmxOO2dDQUNBTzs0QkFDRjs7Ozs7O2tDQUVGLDhEQUFDN0Msa0VBQVlBOzs7Ozs7Ozs7OztRQUNUO0lBUVY7SUFDQSwrQkFBK0I7SUFDL0IsMEhBQTBIO0lBQzFILDZGQUE2RjtJQUM3RixxQkFBTzs7MEJBQUUsOERBQUNKLG9FQUFrQkE7Z0JBQUN1RixPQUFPQTs7Ozs7OzBCQUFTLDhEQUFDN0Usb0RBQUtBO2dCQUFDbVAsS0FBS3hPOzs7Ozs7OztBQUMzRDtHQWhyQndCRjs7UUFHWUgsa0VBQW1CQTtRQUNuQkUsa0VBQW1CQTtRQUNuQkQsbUVBQW9CQTtRQW1ReENoQix1RUFBcUJBOzs7S0F4UWJrQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvKG1haW4pL3Byb2Nlc3Nlcy8oY29tcG9uZW50cykvR2VuZXJpY1RBYmxlLnRzeD80MTcwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5pbXBvcnQgeyBCb3gsIERpYWxvZ0FjdGlvbnMsIERpYWxvZ0NvbnRlbnQsIFN0YWNrIH0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XHJcbmltcG9ydCB7XHJcbiAgTVJUX0VkaXRBY3Rpb25CdXR0b25zLFxyXG4gIE1SVF9Sb3dEYXRhLFxyXG4gIE1hdGVyaWFsUmVhY3RUYWJsZSxcclxuICB1c2VNYXRlcmlhbFJlYWN0VGFibGUsXHJcbiAgdHlwZSBNUlRfQ29sdW1uRGVmXHJcbn0gZnJvbSAnbWF0ZXJpYWwtcmVhY3QtdGFibGUnO1xyXG5pbXBvcnQgeyBNUlRfTG9jYWxpemF0aW9uX0ZSIH0gZnJvbSAnbWF0ZXJpYWwtcmVhY3QtdGFibGUvbG9jYWxlcy9mcic7XHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ3ByaW1lcmVhY3QvYnV0dG9uJztcclxuaW1wb3J0IHsgQ29uZmlybVBvcHVwLCBjb25maXJtUG9wdXAgfSBmcm9tICdwcmltZXJlYWN0L2NvbmZpcm1wb3B1cCc7XHJcbmltcG9ydCB7IFNpZGViYXIgfSBmcm9tICdwcmltZXJlYWN0L3NpZGViYXInO1xyXG5pbXBvcnQgeyBUYWJQYW5lbCwgVGFiVmlldyB9IGZyb20gJ3ByaW1lcmVhY3QvdGFidmlldyc7XHJcbmltcG9ydCB7IFRhZyB9IGZyb20gJ3ByaW1lcmVhY3QvdGFnJztcclxuaW1wb3J0IHsgVG9hc3QgfSBmcm9tICdwcmltZXJlYWN0L3RvYXN0JztcclxuaW1wb3J0IHsgdXNlTWVtbywgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHBhcnNlIGZyb20gJ2h0bWwtcmVhY3QtcGFyc2VyJztcclxuaW1wb3J0IHsgRWRpdG9yIH0gZnJvbSAnQHRpbnltY2UvdGlueW1jZS1yZWFjdCc7XHJcbmltcG9ydCB7IHVzZUFwaVByb2Nlc3NDcmVhdGUsIHVzZUFwaVByb2Nlc3NEZXN0cm95LCB1c2VBcGlQcm9jZXNzVXBkYXRlIH0gZnJvbSAnQC9ob29rcy91c2VOZXh0QXBpJztcclxuaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSAnZG9tYWluJztcclxuXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBHZW5lcmljVGFibGU8VCBleHRlbmRzIE1SVF9Sb3dEYXRhPihkYXRhXzogeyBpc0xvYWRpbmc6IGFueTsgZGF0YV86IGFueSwgZXJyb3I6IGFueSwgZGF0YV90eXBlOiBhbnkgfCB1bmRlZmluZWQsIHBhZ2luYXRpb246IGFueSB9KSB7XHJcblxyXG4gIGNvbnN0IHRvYXN0ID0gdXNlUmVmPFRvYXN0fG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHsgbXV0YXRlIDogY3JlYXRlUHJvY2Vzc30gPSB1c2VBcGlQcm9jZXNzQ3JlYXRlKClcclxuICBjb25zdCB7IG11dGF0ZSA6IHVwZGF0ZVByb2Nlc3N9ID0gdXNlQXBpUHJvY2Vzc1VwZGF0ZSgpXHJcbiAgY29uc3QgeyBtdXRhdGUgOiBkZWxldGVQcm9jZXNzfSA9IHVzZUFwaVByb2Nlc3NEZXN0cm95KClcclxuICBjb25zdCBbdmlzaWJsZSwgc2V0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2VkaXRWaXNpYmxlLCBzZXRFZGl0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2NyZWF0ZVZpc2libGUsIHNldENyZWF0ZVZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGZ1bmN0aW9uIG9uUGFnaW5hdGlvbkNoYW5nZShzdGF0ZTogYW55KSB7XHJcbiAgICBjb25zb2xlLmxvZyhkYXRhXy5wYWdpbmF0aW9uKTtcclxuICAgIGRhdGFfLnBhZ2luYXRpb24uc2V0KHN0YXRlKVxyXG4gIH07XHJcbiAgY29uc3QgW251bVBhZ2VzLCBzZXROdW1QYWdlc10gPSB1c2VTdGF0ZTxudW1iZXJ8bnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3BhZ2VOdW1iZXIsIHNldFBhZ2VOdW1iZXJdID0gdXNlU3RhdGUoMSk7XHJcblxyXG5cclxuICBjb25zdCBhY2NlcHQgPSAoKSA9PiB7XHJcbiAgICB0b2FzdC5jdXJyZW50Py5zaG93KHsgc2V2ZXJpdHk6ICdpbmZvJywgc3VtbWFyeTogJ0NvbmZpcm1lZCcsIGRldGFpbDogJ1lvdSBoYXZlIGFjY2VwdGVkJywgbGlmZTogMzAwMCB9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCByZWplY3QgPSAoKSA9PiB7XHJcbiAgICB0b2FzdC5jdXJyZW50Py5zaG93KHsgc2V2ZXJpdHk6ICd3YXJuJywgc3VtbWFyeTogJ1JlamVjdGVkJywgZGV0YWlsOiAnWW91IGhhdmUgcmVqZWN0ZWQnLCBsaWZlOiAzMDAwIH0pO1xyXG4gIH07XHJcblxyXG4gIGZ1bmN0aW9uIG9uRG9jdW1lbnRMb2FkU3VjY2Vzcyh7IG51bVBhZ2VzIH0gOiB7IG51bVBhZ2VzIDogbnVtYmVyfSkge1xyXG4gICAgc2V0TnVtUGFnZXMobnVtUGFnZXMpO1xyXG4gICAgc2V0UGFnZU51bWJlcigxKTtcclxuICB9XHJcbiAgZnVuY3Rpb24gY2hhbmdlUGFnZShvZmZzZXQ6IG51bWJlcikge1xyXG4gICAgc2V0UGFnZU51bWJlcihwcmV2UGFnZU51bWJlciA9PiBwcmV2UGFnZU51bWJlciArIG9mZnNldCk7XHJcbiAgfVxyXG5cclxuICBmdW5jdGlvbiBwcmV2aW91c1BhZ2UoKSB7XHJcbiAgICBjaGFuZ2VQYWdlKC0xKTtcclxuICB9XHJcblxyXG4gIGZ1bmN0aW9uIG5leHRQYWdlKCkge1xyXG4gICAgY2hhbmdlUGFnZSgxKTtcclxuICB9XHJcbiAgY29uc3QgW2FuY2hvckVsLCBzZXRBbmNob3JFbF0gPSB1c2VTdGF0ZTxudWxsIHwgSFRNTEVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IFtyb3dBY3Rpb25FbmFibGVkLCBzZXRSb3dBY3Rpb25FbmFibGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBvcGVuID0gQm9vbGVhbihhbmNob3JFbCk7XHJcbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQ8SFRNTEJ1dHRvbkVsZW1lbnQ+KSA9PiB7XHJcbiAgICBzZXRBbmNob3JFbChldmVudC5jdXJyZW50VGFyZ2V0KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBjb2x1bW5zID0gdXNlTWVtbzxNUlRfQ29sdW1uRGVmPFQ+W10+KFxyXG4gICAgKCkgPT5cclxuICAgICAgT2JqZWN0LmVudHJpZXMoZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXMpLmZpbHRlcigoW2tleSwgdmFsdWVdLCBpbmRleCkgPT4gIVsnbW9kaWZpZWRfYnknLCAnY3JlYXRlZF9ieScsXS5pbmNsdWRlcyhrZXkpKS5tYXAoKFtrZXksIHZhbHVlXSwgaW5kZXgpID0+IHtcclxuICAgICAgICBpZiAoWydyZXBvcnQnLCAnY29udGVudCcsICdub3RlJywgJ29yZGVyJywgJ2NvbW1lbnQnLCAnZGVzY3JpcHRpb24nXS5pbmNsdWRlcyhrZXkpKSB7XHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsIH0pID0+IHsgaWYgKFtcImRlc2NyaXB0aW9uXCIsIFwiY29udGVudFwiLFwicmVwb3J0XCJdLmluY2x1ZGVzKGtleSkpIHJldHVybiBudWxsOyBlbHNlIHJldHVybiA8ZGl2PntwYXJzZShjZWxsLmdldFZhbHVlPHN0cmluZz4oKSl9PC9kaXY+IH0sXHJcbiAgICAgICAgICAgIEVkaXQ6ICh7IGNlbGwsIGNvbHVtbiwgcm93LCB0YWJsZSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIDxFZGl0b3JcclxuICAgICAgICAgICAgICAgIGluaXRpYWxWYWx1ZT17cm93Lm9yaWdpbmFsW2tleV19XHJcbiAgICAgICAgICAgICAgICB0aW55bWNlU2NyaXB0U3JjPVwiaHR0cDovL2xvY2FsaG9zdDozMDAwL3RpbnltY2UvdGlueW1jZS5taW4uanNcIlxyXG4gICAgICAgICAgICAgICAgYXBpS2V5PSdub25lJ1xyXG4gICAgICAgICAgICAgICAgaW5pdD17e1xyXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6IDUwMCxcclxuICAgICAgICAgICAgICAgICAgbWVudWJhcjogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgcGx1Z2luczogW1xyXG4gICAgICAgICAgICAgICAgICAgICdhZHZsaXN0JywgJ2F1dG9saW5rJywgJ2xpc3RzJyAsJ2xpbmsnLCAnaW1hZ2UnLCAnY2hhcm1hcCcgLCdwcmludCcsICdwcmV2aWV3JywgJ2FuY2hvcicsXHJcbiAgICAgICAgICAgICAgICAgICAgJ3NlYXJjaHJlcGxhY2UnLCAndmlzdWFsYmxvY2tzJywgJ2NvZGUnLCAnZnVsbHNjcmVlbicsXHJcbiAgICAgICAgICAgICAgICAgICAgJ2luc2VydGRhdGV0aW1lJyAsJ21lZGlhJyAsJ3RhYmxlJywgJ3Bhc3RlJywgJ2NvZGUnICwnaGVscCcsICd3b3JkY291bnQnXHJcbiAgICAgICAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgICAgICAgIHRvb2xiYXI6XHJcbiAgICAgICAgICAgICAgICAgICAgJ3VuZG8gcmVkbyB8IGZvcm1hdHNlbGVjdCB8IGJvbGQgaXRhbGljIGJhY2tjb2xvciB8IFxcXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWdubGVmdCBhbGlnbmNlbnRlciBhbGlnbnJpZ2h0IGFsaWduanVzdGlmeSB8IFxcXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJ1bGxpc3QgbnVtbGlzdCBvdXRkZW50IGluZGVudCB8IHJlbW92ZWZvcm1hdCB8IGhlbHAgfHZpc3VhbGJsb2NrcyBjb2RlIGZ1bGxzY3JlZW4nXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA7XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChrZXk9PT1cInBhcmVudFwiKXtcclxuICAgICAgICAgIFxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdwYXJlbnQudGl0bGUnLFxyXG4gICAgICAgICAgICBtdWlUYWJsZUhlYWRDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2xlZnQnLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUJvZHlDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2xlZnQnLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBcclxuXHJcbiAgICAgICAgICAgIG11aVRhYmxlRm9vdGVyQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBpZDoga2V5LCAgICAgICAgICBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0uZm9ybWF0ID09PSAnZGF0ZS10aW1lJyB8fCBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLmZvcm1hdCA9PT0gJ2RhdGUnKSB7XHJcblxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JGbjogKHJvdykgPT4gbmV3IERhdGUoa2V5ID09IFwiY3JlYXRlZFwiID8gcm93LmNyZWF0ZWQgOiByb3cubW9kaWZpZWQpLFxyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBmaWx0ZXJWYXJpYW50OiAnZGF0ZScsXHJcbiAgICAgICAgICAgIGZpbHRlckZuOiAnbGVzc1RoYW4nLFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46ICdkYXRldGltZScsXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIENlbGw6ICh7IGNlbGwgfSkgPT4gY2VsbC5nZXRWYWx1ZTxEYXRlPigpPy50b0xvY2FsZURhdGVTdHJpbmcoJ2ZyJyksXHJcbiAgICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICAgIEVkaXQ6ICgpID0+IG51bGwsXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChrZXkgPT09IFwicHJvcG9zZWRfYnlcIikge1xyXG5cclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIGVkaXRTZWxlY3RPcHRpb25zOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLmFsbE9mICYmIGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0uYWxsT2ZbMF1bJyRyZWYnXSA/IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0uYWxsT2ZbMF1bJyRyZWYnXS5lbnVtIDogW11cclxuICAgICAgICAgICAgLCBtdWlFZGl0VGV4dEZpZWxkUHJvcHM6IHtcclxuICAgICAgICAgICAgICBzZWxlY3Q6IHRydWUsXHJcbiAgICAgICAgICAgICAgdmFyaWFudDogJ291dGxpbmVkJyxcclxuXHJcbiAgICAgICAgICAgICAgU2VsZWN0UHJvcHM6IHtcclxuICAgICAgICAgICAgICAgIG11bHRpcGxlOiBmYWxzZSxcclxuICAgICAgICAgICAgICAgIC8vIHZhbHVlOiBmb3JtU3RhdGUudXNlclJvbGVzLFxyXG4gICAgICAgICAgICAgICAgLy8gb25DaGFuZ2U6IGhhbmRsZUZpZWxkQ2hhbmdlXHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAvLyBlcnJvcjogISF2YWxpZGF0aW9uRXJyb3JzPy5zdGF0ZSxcclxuICAgICAgICAgICAgICAvLyBoZWxwZXJUZXh0OiB2YWxpZGF0aW9uRXJyb3JzPy5zdGF0ZSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgbXVpVGFibGVIZWFkQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUJvZHlDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG11aVRhYmxlRm9vdGVyQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsLCByb3cgfSkgPT4gPFRhZ1xyXG4gICAgICAgICAgICAgIGtleT17cm93Lm9yaWdpbmFsLmNvZGUgKyByb3cub3JpZ2luYWwuY3JlYXRlZH1cclxuICAgICAgICAgICAgICBzZXZlcml0eT17Y2VsbC5nZXRWYWx1ZTxTdHJpbmc+KCkgPT09IFwiVlBcIiA/XHJcbiAgICAgICAgICAgICAgICBcImRhbmdlclwiIDogY2VsbC5nZXRWYWx1ZTxTdHJpbmc+KCkgPT09IFwiU1RSVUNUXCIgPyBcInN1Y2Nlc3NcIiA6IFwiaW5mb1wiXHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIHZhbHVlPXtjZWxsLmdldFZhbHVlPFN0cmluZz4oKSA9PT0gXCJWUFwiID9cclxuICAgICAgICAgICAgICAgIFwiVmljZSBQcsOpc2lkZW50XCIgOiBjZWxsLmdldFZhbHVlPFN0cmluZz4oKSA9PT0gXCJTVFJVQ1RcIiA/IFwiU3RydWN0dXJlc1wiIDogXCJDb250csO0bGUgSW50ZXJuZVwifVxyXG4gICAgICAgICAgICA+XHJcblxyXG4gICAgICAgICAgICA8L1RhZz5cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGtleSA9PT0gXCJldGF0XCIpIHtcclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICAgIG11aVRhYmxlSGVhZENlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgbXVpVGFibGVCb2R5Q2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUZvb3RlckNlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgLy8gZWRpdFNlbGVjdE9wdGlvbnM6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV1bJyRyZWYnXS5lbnVtICxcclxuICAgICAgICAgICAgbXVpRWRpdFRleHRGaWVsZFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgc2VsZWN0OiB0cnVlLFxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ6ICdvdXRsaW5lZCcsXHJcbiAgICAgICAgICAgICAgLy8gY2hpbGRyZW46IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV1bJyRyZWYnXS5lbnVtLFxyXG4gICAgICAgICAgICAgIFNlbGVjdFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgICAvLyBtdWx0aXBsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgIC8vIHZhbHVlOiBmb3JtU3RhdGUudXNlclJvbGVzLFxyXG4gICAgICAgICAgICAgICAgLy8gb25DaGFuZ2U6IGhhbmRsZUZpZWxkQ2hhbmdlXHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIC8vIGVycm9yOiAhIXZhbGlkYXRpb25FcnJvcnM/LnN0YXRlLFxyXG4gICAgICAgICAgICAgIC8vIGhlbHBlclRleHQ6IHZhbGlkYXRpb25FcnJvcnM/LnN0YXRlLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsLCByb3cgfSkgPT4gPFRhZ1xyXG4gICAgICAgICAgICAgIGtleT17cm93Lm9yaWdpbmFsLmNvZGUgKyByb3cub3JpZ2luYWwuY3JlYXRlZH1cclxuICAgICAgICAgICAgICBzZXZlcml0eT17Y2VsbC5nZXRWYWx1ZTxTdHJpbmc+KCkgPT09IFwiTlNcIiA/XHJcbiAgICAgICAgICAgICAgICBcImRhbmdlclwiIDogY2VsbC5nZXRWYWx1ZTxTdHJpbmc+KCkgPT09IFwiRUNcIiA/IFwic3VjY2Vzc1wiIDogXCJpbmZvXCJcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgdmFsdWU9e2NlbGwuZ2V0VmFsdWU8U3RyaW5nPigpID09PSBcIk5TXCIgP1xyXG4gICAgICAgICAgICAgICAgXCJOb24gbGFuY8OpZVwiIDogY2VsbC5nZXRWYWx1ZTxTdHJpbmc+KCkgPT09IFwiU1BcIiA/IFwiU3VzcGVuZHVlXCIgOiBjZWxsLmdldFZhbHVlPFN0cmluZz4oKSA9PT0gXCJFQ1wiID8gXCJFbiBjb3Vyc1wiIDogXCJDbMO0dHVyw6llXCJ9XHJcbiAgICAgICAgICAgID5cclxuXHJcbiAgICAgICAgICAgIDwvVGFnPlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICBcclxuICAgICAgICBpZiAoa2V5ID09PSBcImV4ZXJjaXNlXCIpIHtcclxuICAgICAgICAgIC8vIGNvbnNvbGUubG9nKGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0uYWxsT2ZbMF1bJyRyZWYnXS5lbnVtKVxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgbXVpVGFibGVIZWFkQ2VsbFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBtdWlUYWJsZUJvZHlDZWxsUHJvcHM6IHtcclxuICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgIH0sXHJcblxyXG4gICAgICAgICAgICBtdWlUYWJsZUZvb3RlckNlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgICAgQ2VsbDogKHsgY2VsbCwgcm93IH0pID0+XHJcbiAgICAgICAgICAgICAgPFRhZ1xyXG4gICAgICAgICAgICAgICAga2V5PXtyb3cub3JpZ2luYWwuY29kZSArIHJvdy5vcmlnaW5hbC5jcmVhdGVkfVxyXG4gICAgICAgICAgICAgICAgc2V2ZXJpdHk9e1xyXG4gICAgICAgICAgICAgICAgICAnc3VjY2VzcydcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXtjZWxsLmdldFZhbHVlPFN0cmluZz4oKSB9XHJcbiAgICAgICAgICAgICAgPjwvVGFnPlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XVsnJHJlZiddICYmIGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV1bJyRyZWYnXS5lbnVtKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIiMjIyMjIyNlbnVtIyMjIyMjIyMjI1wiLCBrZXksIHZhbHVlKVxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgLy8gYWNjZXNzb3JGbjogKG9yaWdpbmFsUm93KSA9Pm9yaWdpbmFsUm93W2tleV0ubGVuZ3RoID4wID8gb3JpZ2luYWxSb3dba2V5XS5yZWR1Y2UoZnVuY3Rpb24gKGFjYywgb2JqKSB7IHJldHVybiBhY2MgKyBvYmoudXNlcm5hbWUrXCIgLFwiOyB9LCBcIlwiKTpcIlwiLFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsLCByb3cgfSkgPT4gY2VsbC5yb3cub3JpZ2luYWxba2V5XSxcclxuICAgICAgICAgICAgZWRpdFNlbGVjdE9wdGlvbnM6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV1bJyRyZWYnXS5lbnVtLFxyXG4gICAgICAgICAgICBtdWlFZGl0VGV4dEZpZWxkUHJvcHM6IHtcclxuICAgICAgICAgICAgICBzZWxlY3Q6IHRydWUsXHJcbiAgICAgICAgICAgICAgdmFyaWFudDogJ291dGxpbmVkJyxcclxuICAgICAgICAgICAgICBjaGlsZHJlbjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XVsnJHJlZiddLmVudW0sXHJcbiAgICAgICAgICAgICAgU2VsZWN0UHJvcHM6IHtcclxuICAgICAgICAgICAgICAgIC8vIG11bHRpcGxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgLy8gdmFsdWU6IGZvcm1TdGF0ZS51c2VyUm9sZXMsXHJcbiAgICAgICAgICAgICAgICAvLyBvbkNoYW5nZTogaGFuZGxlRmllbGRDaGFuZ2VcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgLy8gZXJyb3I6ICEhdmFsaWRhdGlvbkVycm9ycz8uc3RhdGUsXHJcbiAgICAgICAgICAgICAgLy8gaGVscGVyVGV4dDogdmFsaWRhdGlvbkVycm9ycz8uc3RhdGUsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgaWYgKGtleSA9PT0gXCJpZFwiKSByZXR1cm4ge1xyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBFZGl0OiAoKSA9PiBudWxsLFxyXG4gICAgICAgICAgfTsgZWxzZVxyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgIGhlYWRlcjogZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS50aXRsZSA/PyBrZXksXHJcbiAgICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICAgIC8vIEVkaXQ6ICgpID0+IG51bGwsXHJcbiAgICAgICAgICAgIH07XHJcblxyXG4gICAgICAgIH1cclxuICAgICAgfSlcclxuICAgICxcclxuICAgIFtdLFxyXG4gICk7XHJcblxyXG4gIGNvbnNvbGUubG9nKFwiIyMjIyMjIyMjIyMjIyMgZGF0YSBmcm9tIGFwaSBcIiwgZGF0YV8pXHJcblxyXG4gIGNvbnN0IHRhYmxlID0gdXNlTWF0ZXJpYWxSZWFjdFRhYmxlKHtcclxuICAgIGNvbHVtbnMsXHJcbiAgICBkYXRhOiBkYXRhXy5lcnJvciA/IFtdIDogZGF0YV8uZGF0YV8uZGF0YS5yZXN1bHRzID8gIGRhdGFfLmRhdGFfLmRhdGEucmVzdWx0cyA6IFtkYXRhXy5kYXRhXy5kYXRhXSwgLy9tdXN0IGJlIG1lbW9pemVkIG9yIHN0YWJsZSAodXNlU3RhdGUsIHVzZU1lbW8sIGRlZmluZWQgb3V0c2lkZSBvZiB0aGlzIGNvbXBvbmVudCwgZXRjLilcclxuICAgIHJvd0NvdW50OiBkYXRhXy5lcnJvciA/IDAgOiBkYXRhXy5kYXRhXy5kYXRhLnJlc3VsdHM/IGRhdGFfLmRhdGFfLmRhdGEuY291bnQgOiAxLFxyXG4gICAgZW5hYmxlUm93U2VsZWN0aW9uOiB0cnVlLCAvL2VuYWJsZSBzb21lIGZlYXR1cmVzXHJcbiAgICBlbmFibGVDb2x1bW5PcmRlcmluZzogdHJ1ZSwgLy9lbmFibGUgYSBmZWF0dXJlIGZvciBhbGwgY29sdW1uc1xyXG4gICAgZW5hYmxlR2xvYmFsRmlsdGVyOiB0cnVlLCAvL3R1cm4gb2ZmIGEgZmVhdHVyZVxyXG4gICAgZW5hYmxlR3JvdXBpbmc6IHRydWUsXHJcbiAgICBlbmFibGVSb3dBY3Rpb25zOiB0cnVlLFxyXG4gICAgZW5hYmxlUm93UGlubmluZzogdHJ1ZSxcclxuICAgIGVuYWJsZVN0aWNreUhlYWRlcjogdHJ1ZSxcclxuICAgIGVuYWJsZVN0aWNreUZvb3RlcjogdHJ1ZSxcclxuICAgIGVuYWJsZUNvbHVtblBpbm5pbmc6IHRydWUsXHJcbiAgICBlbmFibGVDb2x1bW5SZXNpemluZzogdHJ1ZSxcclxuICAgIGVuYWJsZVJvd051bWJlcnM6IHRydWUsXHJcbiAgICBlbmFibGVFeHBhbmRBbGw6IHRydWUsXHJcbiAgICBlbmFibGVFZGl0aW5nOiB0cnVlLFxyXG4gICAgZW5hYmxlRXhwYW5kaW5nOiB0cnVlLFxyXG4gICAgbWFudWFsUGFnaW5hdGlvbjogdHJ1ZSxcclxuXHJcbiAgICBpbml0aWFsU3RhdGU6IHtcclxuICAgICAgcGFnaW5hdGlvbjogeyBwYWdlU2l6ZTogNSwgcGFnZUluZGV4OiAxIH0sXHJcbiAgICAgIGNvbHVtblZpc2liaWxpdHk6IHsgY3JlYXRlZF9ieTogZmFsc2UsIGNyZWF0ZWQ6IGZhbHNlLCBtb2RmaWVkX2J5OiBmYWxzZSwgbW9kaWZpZWQ6IGZhbHNlLCBtb2RpZmllZF9ieTogZmFsc2UsIHN0YWZmOiBmYWxzZSwgYXNzaXN0YW50czogZmFsc2UsIGlkOiBmYWxzZSwgZG9jdW1lbnQ6IGZhbHNlIH0sXHJcbiAgICAgIGRlbnNpdHk6ICdjb21wYWN0JyxcclxuICAgICAgc2hvd0dsb2JhbEZpbHRlcjogdHJ1ZSxcclxuICAgICAgc29ydGluZzogW3sgaWQ6ICdpZCcsIGRlc2M6IGZhbHNlIH1dLFxyXG4gICAgfSxcclxuICAgIHN0YXRlOiB7XHJcbiAgICAgIHBhZ2luYXRpb246IGRhdGFfLnBhZ2luYXRpb24ucGFnaSxcclxuICAgICAgaXNMb2FkaW5nOiBkYXRhXy5pc0xvYWRpbmcsIC8vY2VsbCBza2VsZXRvbnMgYW5kIGxvYWRpbmcgb3ZlcmxheVxyXG4gICAgICAvL3Nob3dQcm9ncmVzc0JhcnM6IGlzTG9hZGluZywgLy9wcm9ncmVzcyBiYXJzIHdoaWxlIHJlZmV0Y2hpbmdcclxuICAgICAgLy8gaXNTYXZpbmc6IGlzU2F2aW5nVG9kb3MsIC8vcHJvZ3Jlc3MgYmFycyBhbmQgc2F2ZSBidXR0b24gc3Bpbm5lcnNcclxuICAgIH0sXHJcbiAgICBsb2NhbGl6YXRpb246IE1SVF9Mb2NhbGl6YXRpb25fRlIsXHJcbiAgICBvblBhZ2luYXRpb25DaGFuZ2U6IG9uUGFnaW5hdGlvbkNoYW5nZSxcclxuICAgIGRpc3BsYXlDb2x1bW5EZWZPcHRpb25zOiB7XHJcbiAgICAgICdtcnQtcm93LXBpbic6IHtcclxuICAgICAgICBlbmFibGVIaWRpbmc6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgICdtcnQtcm93LWV4cGFuZCc6IHtcclxuICAgICAgICBlbmFibGVIaWRpbmc6IHRydWUsXHJcbiAgICAgIH0sXHJcbiAgICAgICdtcnQtcm93LWFjdGlvbnMnOiB7XHJcbiAgICAgICAgLy8gaGVhZGVyOiAnRWRpdCcsIC8vY2hhbmdlIFwiQWN0aW9uc1wiIHRvIFwiRWRpdFwiXHJcbiAgICAgICAgc2l6ZTogMTAwLFxyXG4gICAgICAgIGVuYWJsZUhpZGluZzogdHJ1ZSxcclxuXHJcbiAgICAgICAgLy91c2UgYSB0ZXh0IGJ1dHRvbiBpbnN0ZWFkIG9mIGEgaWNvbiBidXR0b25cclxuICAgICAgICAvLyBDZWxsOiAoeyByb3csIHRhYmxlIH0pID0+IChcclxuICAgICAgICAvLyAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gdGFibGUuc2V0RWRpdGluZ1Jvdyhyb3cpfT5FZGl0IEN1c3RvbWVyPC9CdXR0b24+XHJcbiAgICAgICAgLy8gKSxcclxuICAgICAgfSxcclxuICAgICAgJ21ydC1yb3ctbnVtYmVycyc6IHtcclxuICAgICAgICBlbmFibGVIaWRpbmc6IHRydWUsIC8vbm93IHJvdyBudW1iZXJzIGFyZSBoaWRhYmxlIHRvb1xyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIGRlZmF1bHRDb2x1bW46IHtcclxuICAgICAgZ3JvdzogdHJ1ZSxcclxuICAgICAgZW5hYmxlTXVsdGlTb3J0OiB0cnVlLFxyXG4gICAgfSwgICAgXHJcbiAgICBtdWlUYWJsZVBhcGVyUHJvcHM6ICh7IHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIC8vZWxldmF0aW9uOiAwLCAvL2NoYW5nZSB0aGUgbXVpIGJveCBzaGFkb3dcclxuICAgICAgLy9jdXN0b21pemUgcGFwZXIgc3R5bGVzXHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBjbGFzc2VzOiB7IHJvb3Q6ICdwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bCcgfSxcclxuXHJcbiAgICAgIHN4OiB7XHJcbiAgICAgICAgaGVpZ2h0OiBgY2FsYygxMDB2aCAtIDlyZW0pYCxcclxuICAgICAgICAvLyBoZWlnaHQ6IGBjYWxjKDEwMHZoIC0gMjAwcHgpYCxcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIFwiJiAuTXVpVGFibGVQYWdpbmF0aW9uLXJvb3QgXCIgOntcclxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICBjb2xvcjogXCJ2YXIoLS1zdXJmYWNlLTkwMCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgXCImIC5NdWlCb3gtcm9vdCBcIiA6e1xyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgIH0pLFxyXG4gICAgZWRpdERpc3BsYXlNb2RlOiAnbW9kYWwnLFxyXG4gICAgY3JlYXRlRGlzcGxheU1vZGU6ICdtb2RhbCcsXHJcbiAgICBvbkVkaXRpbmdSb3dTYXZlOiAoeyB0YWJsZSwgdmFsdWVzIH0pID0+IHtcclxuICAgICAgLy92YWxpZGF0ZSBkYXRhXHJcbiAgICAgIC8vc2F2ZSBkYXRhIHRvIGFwaVxyXG4gICAgICB0YWJsZS5zZXRFZGl0aW5nUm93KG51bGwpOyAvL2V4aXQgZWRpdGluZyBtb2RlXHJcbiAgICAgIHRvYXN0LmN1cnJlbnQ/LnNob3coeyBzZXZlcml0eTogJ2luZm8nLCBzdW1tYXJ5OiAnSW5mbycsIGRldGFpbDogJ1NhdXZlZ2FyZGUgZW4gY291cnMnIH0pO1xyXG4gICAgfSxcclxuICAgIG9uRWRpdGluZ1Jvd0NhbmNlbDogKCkgPT4ge1xyXG4gICAgICAvL2NsZWFyIGFueSB2YWxpZGF0aW9uIGVycm9yc1xyXG4gICAgICB0b2FzdC5jdXJyZW50Py5zaG93KHsgc2V2ZXJpdHk6ICdpbmZvJywgc3VtbWFyeTogJ0luZm8nLCBkZXRhaWw6ICdBbm51bGF0aW9uJyB9KTtcclxuICAgIH0sXHJcbiAgICBvbkNyZWF0aW5nUm93U2F2ZTogKHsgdGFibGUsIHZhbHVlcyB9KSA9PiB7XHJcbiAgICAgIC8vdmFsaWRhdGUgZGF0YVxyXG4gICAgICAvL3NhdmUgZGF0YSB0byBhcGlcclxuICAgICAgY3JlYXRlUHJvY2Vzcyh2YWx1ZXMpXHJcbiAgICAgIHRhYmxlLnNldENyZWF0aW5nUm93KG51bGwpOyAvL2V4aXQgY3JlYXRpbmcgbW9kZVxyXG4gICAgfSxcclxuICAgIG9uQ3JlYXRpbmdSb3dDYW5jZWw6ICgpID0+IHtcclxuICAgICAgLy9jbGVhciBhbnkgdmFsaWRhdGlvbiBlcnJvcnNcclxuICAgIH0sXHJcbiAgICBtdWlFZGl0Um93RGlhbG9nUHJvcHM6ICh7IHJvdywgdGFibGUgfSkgPT4gKHtcclxuICAgICAgLy9vcHRpb25hbGx5IGN1c3RvbWl6ZSB0aGUgZGlhbG9nXHJcbiAgICAgIC8vIGFib3V0OlwiZWRpdCBtb2RhbFwiLFxyXG4gICAgICBvcGVuOiBlZGl0VmlzaWJsZSB8fCBjcmVhdGVWaXNpYmxlLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgICcmIC5NdWlEaWFsb2ctcm9vdCc6IHtcclxuICAgICAgICAgIGRpc3BsYXk6ICdub25lJ1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgJyYgLk11aURpYWxvZy1jb250YWluZXInOiB7XHJcbiAgICAgICAgICBkaXNwbGF5OiAnbm9uZSdcclxuICAgICAgICB9LFxyXG4gICAgICAgIHpJbmRleDogMTEwMFxyXG5cclxuICAgICAgfVxyXG4gICAgfSksXHJcbiAgICBtdWlUYWJsZUZvb3RlclByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIFwiJiAuTXVpVGFibGVGb290ZXItcm9vdFwiIDp7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwidmFyKC0tc3VyZmFjZS1jYXJkKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwidmFyKC0tc3VyZmFjZS1jYXJkKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgbXVpVGFibGVDb250YWluZXJQcm9wczogKHsgdGFibGUgfSkgPT4gKHtcclxuICAgICAgY2xhc3NOYW1lOiBcInAtZGF0YXRhYmxlLWdyaWRsaW5lcyB0ZXh0LTkwMCBmb250LW1lZGl1bSB0ZXh0LXhsXCIsXHJcbiAgICAgIHN4OiB7XHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIC8vIGJvcmRlclJhZGl1czogJzAnLFxyXG4gICAgICAgIC8vIGJvcmRlcjogJzFweCBkYXNoZWQgI2UwZTBlMCcsXHJcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZClcIixcclxuICAgICAgICBoZWlnaHQ6IHRhYmxlLmdldFN0YXRlKCkuaXNGdWxsU2NyZWVuID8gYGNhbGMoMTAwdmgpYCA6IGBjYWxjKDEwMHZoIC0gOXJlbSAtICR7dGFibGUucmVmcy50b3BUb29sYmFyUmVmLmN1cnJlbnQ/Lm9mZnNldEhlaWdodH1weCAtICR7dGFibGUucmVmcy5ib3R0b21Ub29sYmFyUmVmLmN1cnJlbnQ/Lm9mZnNldEhlaWdodH1weClgXHJcblxyXG4gICAgICB9LFxyXG4gICAgfSksICAgIFxyXG4gICAgbXVpUGFnaW5hdGlvblByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgXHJcbiAgICAgICAgLy8gYm9yZGVyUmFkaXVzOiAnMCcsXHJcbiAgICAgICAgLy8gYm9yZGVyOiAnMXB4IGRhc2hlZCAjZTBlMGUwJyxcclxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwidmFyKC0tc3VyZmFjZS1jYXJkKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgbXVpVGFibGVIZWFkQ2VsbFByb3BzOiB7XHJcbiAgICAgIHN4OiB7XHJcbiAgICAgICAgLy8gYm9yZGVyUmFkaXVzOiAnMCcsXHJcbiAgICAgICAgLy8gYm9yZGVyOiAnMXB4IGRhc2hlZCAjZTBlMGUwJyxcclxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwidmFyKC0tc3VyZmFjZS1jYXJkKVwiLFxyXG4gICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIG11aVRvcFRvb2xiYXJQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgICAgLy8gYm9yZGVyUmFkaXVzOiAnMCcsXHJcbiAgICAgICAgLy8gYm9yZGVyOiAnMXB4IGRhc2hlZCAjZTBlMGUwJyxcclxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwidmFyKC0tc3VyZmFjZS1jYXJkKVwiLFxyXG4gICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCJcclxuICAgICAgfSxcclxuXHJcbiAgICB9LFxyXG4gICAgbXVpVGFibGVCb2R5UHJvcHM6IHtcclxuICAgICAgY2xhc3NOYW1lOiBcInAtZGF0YXRhYmxlLWdyaWRsaW5lcyB0ZXh0LTkwMCBmb250LW1lZGl1bSB0ZXh0LXhsXCIsXHJcbiAgICAgIHN4OiB7XHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIC8vc3RyaXBlIHRoZSByb3dzLCBtYWtlIG9kZCByb3dzIGEgZGFya2VyIGNvbG9yXHJcbiAgICAgICAgJyYgdHI6bnRoLW9mLXR5cGUob2RkKSA+IHRkJzoge1xyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndmFyKC0tc3VyZmFjZS1jYXJkKScsXHJcbiAgICAgICAgICBjb2xvcjogXCJ2YXIoLS1zdXJmYWNlLTkwMCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICAnJiB0cjpudGgtb2YtdHlwZShldmVuKSA+IHRkJzoge1xyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndmFyKC0tc3VyZmFjZS1ib3JkZXIpJyxcclxuICAgICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIHJlbmRlclRvcFRvb2xiYXJDdXN0b21BY3Rpb25zOiAoeyB0YWJsZSB9KSA9PiAoXHJcbiAgICAgIDxTdGFjayBkaXJlY3Rpb249e1wicm93XCJ9IHNwYWNpbmc9ezF9PlxyXG4gICAgICAgIHsvKiA8U3BlZWREaWFsXHJcbiAgICAgICAgICBkaXJlY3Rpb249J2Rvd24nXHJcbiAgICAgICAgICBhcmlhTGFiZWw9XCJTcGVlZERpYWwgYmFzaWMgZXhhbXBsZVwiXHJcbiAgICAgICAgICBzeD17eyB6SW5kZXg6IDMwIH19XHJcbiAgICAgICAgICBpY29uPXs8SW1hZ2Ugc3JjPXsnL2ltYWdlcy9wYXJraW5nL3BkZi5wbmcnfSBhbHQ9eydQREYgSUNPTid9IHdpZHRoPXszMn0gaGVpZ2h0PXszMn0gLz59XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPFNwZWVkRGlhbEFjdGlvblxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsYWNrIGRhcms6IXRleHQtYm9keWRhcmsxIGJvcmRlci1zdHJva2UgYmctYm9keWRhcmsgZGFyazpib3JkZXItc3Ryb2tlZGFyayBkYXJrOmJnLWJvZHlkYXJrIFwiXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHsgaGFuZGxlQ2xvc2UoKTsgaGFuZGxlRXhwb3J0Um93cyh0YWJsZS5nZXRSb3dNb2RlbCgpLnJvd3MpOyB9fSB0b29sdGlwVGl0bGU9e1wiRXhwb3J0ZXIgdG91dGVzIGxlcyBsaWduZXNcIn0+PC9TcGVlZERpYWxBY3Rpb24+XHJcbiAgICAgICAgICA8U3BlZWREaWFsQWN0aW9uXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmxhY2sgZGFyazohdGV4dC1ib2R5ZGFyazEgYm9yZGVyLXN0cm9rZSBiZy1ib2R5ZGFyayBkYXJrOmJvcmRlci1zdHJva2VkYXJrIGRhcms6YmctYm9keWRhcmsgXCJcclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4geyBoYW5kbGVDbG9zZSgpOyBoYW5kbGVFeHBvcnRSb3dzKHRhYmxlLmdldFJvd01vZGVsKCkucm93cyk7IH19IHRvb2x0aXBUaXRsZT17XCJFeHBvcnRlciBsZXMgbGlnbmVzIGRlIGxhIFBhZ2VcIn0+PC9TcGVlZERpYWxBY3Rpb24+XHJcbiAgICAgICAgICA8U3BlZWREaWFsQWN0aW9uXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmxhY2sgZGFyazohdGV4dC1ib2R5ZGFyazEgYm9yZGVyLXN0cm9rZSBiZy1ib2R5ZGFyayBkYXJrOmJvcmRlci1zdHJva2VkYXJrIGRhcms6YmctYm9keWRhcmsgXCJcclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4geyBoYW5kbGVDbG9zZSgpOyBoYW5kbGVFeHBvcnRSb3dzKHRhYmxlLmdldFJvd01vZGVsKCkucm93cyk7IH19IHRvb2x0aXBUaXRsZT17XCJFeHBvcnRlciBsZXMgbGlnbmVzIHNlbGVjdGlvbm7DqWVzXCJ9PjwvU3BlZWREaWFsQWN0aW9uPlxyXG4gICAgICAgIDwvU3BlZWREaWFsPiAqL31cclxuICAgICAgICB7LyogPFRvb2x0aXAgdGl0bGU9XCJFeHBvcnRlciBQREZcIj4gKi99XHJcbiAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgaWNvbj1cInBpIHBpLXBsdXNcIlxyXG4gICAgICAgICAgcm91bmRlZFxyXG4gICAgICAgICAgLy8gaWQ9XCJiYXNpYy1idXR0b25cIlxyXG4gICAgICAgICAgYXJpYS1jb250cm9scz17b3BlbiA/ICdiYXNpYy1tZW51JyA6IHVuZGVmaW5lZH1cclxuICAgICAgICAgIGFyaWEtaGFzcG9wdXA9XCJ0cnVlXCJcclxuICAgICAgICAgIGFyaWEtZXhwYW5kZWQ9e29wZW4gPyAndHJ1ZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICBvbkNsaWNrPXsoZXZlbnQpID0+IHtcclxuICAgICAgICAgICAgdGFibGUuc2V0Q3JlYXRpbmdSb3codHJ1ZSk7IHNldENyZWF0ZVZpc2libGUodHJ1ZSksIGNvbnNvbGUubG9nKFwiY3JlYXRpbmcgcm93IC4uLlwiKTtcclxuICAgICAgICAgIH19XHJcblxyXG4gICAgICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgcm91bmRlZFxyXG4gICAgICAgICAgZGlzYWJsZWQ9e3RhYmxlLmdldElzU29tZVJvd3NTZWxlY3RlZCgpfVxyXG4gICAgICAgICAgLy8gaWQ9XCJiYXNpYy1idXR0b25cIlxyXG4gICAgICAgICAgYXJpYS1jb250cm9scz17b3BlbiA/ICdiYXNpYy1tZW51JyA6IHVuZGVmaW5lZH1cclxuICAgICAgICAgIGFyaWEtaGFzcG9wdXA9XCJ0cnVlXCJcclxuICAgICAgICAgIGFyaWEtZXhwYW5kZWQ9e29wZW4gPyAndHJ1ZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGlja31cclxuICAgICAgICAgIGljb249XCJwaSBwaS10cmFzaFwiXHJcbiAgICAgICAgICAvLyBzdHlsZT17eyAgYm9yZGVyUmFkaXVzOiAnMCcsIGJveFNoYWRvdzogJ25vbmUnLCBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcgfX1cclxuICAgICAgICAgIHNpemU9XCJzbWFsbFwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIDxCdXR0b24gaWNvbj1cInBpIHBpLWJlbGxcIiByb3VuZGVkIGNvbG9yPXtyb3dBY3Rpb25FbmFibGVkID8gXCJzZWNvbmRhcnlcIiA6IFwicHJpbWFyeVwifSBzaXplPVwic21hbGxcIlxyXG4gICAgICAgICAgYXJpYS1sYWJlbD1cImVkaXRcIiBvbkNsaWNrPXsoKSA9PiBzZXRSb3dBY3Rpb25FbmFibGVkKCFyb3dBY3Rpb25FbmFibGVkKX0+XHJcbiAgICAgICAgPC9CdXR0b24+XHJcblxyXG5cclxuICAgICAgPC9TdGFjaz5cclxuICAgICAgLy8gPE1lbnVcclxuICAgICAgLy8gICBzeD17e1xyXG4gICAgICAvLyAgICAgZGlzcGxheTogJ2ZsZXgnLFxyXG4gICAgICAvLyAgICAgZ2FwOiAnMTZweCcsXHJcbiAgICAgIC8vICAgICBwYWRkaW5nOiAnOHB4JyxcclxuICAgICAgLy8gICAgIGZsZXhXcmFwOiAnd3JhcCcsXHJcbiAgICAgIC8vICAgfX0gb3Blbj17dHJ1ZX0gICAgICA+XHJcbiAgICAgIC8vICAgPEJ1dHRvblxyXG4gICAgICAvLyAgICAgZGlzYWJsZWQ9e3RhYmxlLmdldFByZVBhZ2luYXRpb25Sb3dNb2RlbCgpLnJvd3MubGVuZ3RoID09PSAwfVxyXG4gICAgICAvLyAgICAgLy9leHBvcnQgYWxsIHJvd3MsIGluY2x1ZGluZyBmcm9tIHRoZSBuZXh0IHBhZ2UsIChzdGlsbCByZXNwZWN0cyBmaWx0ZXJpbmcgYW5kIHNvcnRpbmcpXHJcbiAgICAgIC8vICAgICBvbkNsaWNrPXsoKSA9PlxyXG4gICAgICAvLyAgICAgICBoYW5kbGVFeHBvcnRSb3dzKHRhYmxlLmdldFJvd01vZGVsKCkucm93cylcclxuICAgICAgLy8gICAgIH1cclxuICAgICAgLy8gICAgIHN0YXJ0SWNvbj17PEZpbGVEb3dubG9hZEljb24gLz59XHJcbiAgICAgIC8vICAgPlxyXG4gICAgICAvLyAgICAgRXhwb3J0ZXIgdG91dGVzIGxlcyBsaWduZXNcclxuICAgICAgLy8gICA8L0J1dHRvbj5cclxuICAgICAgLy8gICA8QnV0dG9uXHJcbiAgICAgIC8vICAgICBkaXNhYmxlZD17dGFibGUuZ2V0Um93TW9kZWwoKS5yb3dzLmxlbmd0aCA9PT0gMH1cclxuICAgICAgLy8gICAgIC8vZXhwb3J0IGFsbCByb3dzIGFzIHNlZW4gb24gdGhlIHNjcmVlbiAocmVzcGVjdHMgcGFnaW5hdGlvbiwgc29ydGluZywgZmlsdGVyaW5nLCBldGMuKVxyXG4gICAgICAvLyAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRXhwb3J0Um93cyh0YWJsZS5nZXRSb3dNb2RlbCgpLnJvd3MpfVxyXG4gICAgICAvLyAgICAgc3RhcnRJY29uPXs8UGljdHVyZUFzUGRmIC8+fVxyXG4gICAgICAvLyAgID5cclxuICAgICAgLy8gICAgIEV4cG9ydGVyIGxlcyBsaWduZXMgZGUgbGEgUGFnZVxyXG4gICAgICAvLyAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIDxCdXR0b25cclxuICAgICAgLy8gICAgIGRpc2FibGVkPXtcclxuICAgICAgLy8gICAgICAgIXRhYmxlLmdldElzU29tZVJvd3NTZWxlY3RlZCgpICYmICF0YWJsZS5nZXRJc0FsbFJvd3NTZWxlY3RlZCgpXHJcbiAgICAgIC8vICAgICB9XHJcbiAgICAgIC8vICAgICAvL29ubHkgZXhwb3J0IHNlbGVjdGVkIHJvd3NcclxuICAgICAgLy8gICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUV4cG9ydFJvd3ModGFibGUuZ2V0U2VsZWN0ZWRSb3dNb2RlbCgpLnJvd3MpfVxyXG4gICAgICAvLyAgICAgc3RhcnRJY29uPXs8RmlsZURvd25sb2FkSWNvbiAvPn1cclxuICAgICAgLy8gICA+XHJcbiAgICAgIC8vICAgICBFeHBvcnRlciBsZXMgbGlnbmVzIHNlbGVjdGlvbm7DqWVzXHJcbiAgICAgIC8vICAgPC9CdXR0b24+XHJcbiAgICAgIC8vIDwvTWVudT5cclxuICAgICksXHJcbiAgICBtdWlEZXRhaWxQYW5lbFByb3BzOiAoKSA9PiAoe1xyXG4gICAgICBzeDogKHRoZW1lKSA9PiAoe1xyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjpcclxuICAgICAgICAgIHRoZW1lLnBhbGV0dGUubW9kZSA9PT0gJ2RhcmsnXHJcbiAgICAgICAgICAgID8gJ3JnYmEoMjU1LDIxMCwyNDQsMC4xKSdcclxuICAgICAgICAgICAgOiAncmdiYSgwLDAsMCwwLjEpJyxcclxuICAgICAgfSksXHJcbiAgICB9KSxcclxuICAgIHJlbmRlckNyZWF0ZVJvd0RpYWxvZ0NvbnRlbnQ6ICh7IGludGVybmFsRWRpdENvbXBvbmVudHMsIHJvdywgdGFibGUgfSkgPT4gPD4gPGRpdiBzdHlsZT17eyB6SW5kZXg6ICcxMzAyICFpbXBvcnRhbnQnIH19PlxyXG4gICAgICA8U2lkZWJhciBwb3NpdGlvbj0ncmlnaHQnIGhlYWRlcj17XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggZmxleC1yb3cgdy1mdWxsIGZsZXgtd3JhcCBqdXN0aWZ5LWNvbnRlbnQtYmV0d2Vlbic+PHNwYW4gY2xhc3NOYW1lPSdhbGlnbi1jb250ZW50LWNlbnRlciAnPkNyw6lhdGlvbiB7ZGF0YV8uZGF0YV90eXBlLm5hbWV9IHtyb3cub3JpZ2luYWwuY29kZX08L3NwYW4+XHJcbiAgICAgICAgICA8RGlhbG9nQWN0aW9ucz5cclxuICAgICAgICAgICAgPE1SVF9FZGl0QWN0aW9uQnV0dG9ucyB2YXJpYW50PVwidGV4dFwiIHRhYmxlPXt0YWJsZX0gcm93PXtyb3d9IC8+XHJcbiAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgPC9kaXY+fSB2aXNpYmxlPXtjcmVhdGVWaXNpYmxlfSBvbkhpZGU9eygpID0+IHsgdGFibGUuc2V0Q3JlYXRpbmdSb3cobnVsbCk7IHNldENyZWF0ZVZpc2libGUoZmFsc2UpIH19IGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTkgbGc6dy04XCI+XHJcbiAgICAgICAgPERpYWxvZ0NvbnRlbnRcclxuICAgICAgICAgIHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsIGdhcDogJzEuNXJlbScgfX1cclxuICAgICAgICA+XHJcblxyXG4gICAgICAgICAge2ludGVybmFsRWRpdENvbXBvbmVudHN9IHsvKiBvciByZW5kZXIgY3VzdG9tIGVkaXQgY29tcG9uZW50cyBoZXJlICovfVxyXG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuXHJcbiAgICAgIDwvU2lkZWJhcj5cclxuICAgIDwvZGl2PjwvPixcclxuICAgIHJlbmRlckVkaXRSb3dEaWFsb2dDb250ZW50OiAoeyBpbnRlcm5hbEVkaXRDb21wb25lbnRzLCByb3csIHRhYmxlIH0pID0+IDw+PGRpdiBzdHlsZT17eyB6SW5kZXg6ICcxMzAyICFpbXBvcnRhbnQnIH19PlxyXG4gICAgICA8U2lkZWJhciBwb3NpdGlvbj0ncmlnaHQnIGhlYWRlcj17XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggZmxleC1yb3cgdy1mdWxsIGZsZXgtd3JhcCBqdXN0aWZ5LWNvbnRlbnQtYmV0d2Vlbic+PHNwYW4gY2xhc3NOYW1lPSdhbGlnbi1jb250ZW50LWNlbnRlciAnPkVkaXRlciB7ZGF0YV8uZGF0YV90eXBlLm5hbWV9IHtyb3cub3JpZ2luYWwuY29kZX08L3NwYW4+XHJcbiAgICAgICAgICA8RGlhbG9nQWN0aW9ucz5cclxuICAgICAgICAgICAgPE1SVF9FZGl0QWN0aW9uQnV0dG9ucyB2YXJpYW50PVwidGV4dFwiIHRhYmxlPXt0YWJsZX0gcm93PXtyb3d9IC8+XHJcbiAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgPC9kaXY+fSB2aXNpYmxlPXtlZGl0VmlzaWJsZX0gb25IaWRlPXsoKSA9PiB7IHRhYmxlLnNldEVkaXRpbmdSb3cobnVsbCk7IHNldEVkaXRWaXNpYmxlKGZhbHNlKSB9fSBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy05IGxnOnctOFwiPlxyXG4gICAgICAgIDxEaWFsb2dDb250ZW50XHJcbiAgICAgICAgICBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLCBnYXA6ICcxLjVyZW0nIH19XHJcbiAgICAgICAgPlxyXG5cclxuICAgICAgICAgIHtpbnRlcm5hbEVkaXRDb21wb25lbnRzfSB7Lyogb3IgcmVuZGVyIGN1c3RvbSBlZGl0IGNvbXBvbmVudHMgaGVyZSAqL31cclxuICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XHJcblxyXG4gICAgICA8L1NpZGViYXI+XHJcbiAgICA8L2Rpdj48Lz4sXHJcbiAgICByZW5kZXJEZXRhaWxQYW5lbDogKHsgcm93IH0pID0+XHJcbiAgICAgIHJvdy5vcmlnaW5hbC5kZXNjcmlwdGlvbiA/IHBhcnNlKHJvdy5vcmlnaW5hbC5kZXNjcmlwdGlvbikgOlxyXG4gICAgICAgIHJvdy5vcmlnaW5hbC5jb250ZW50ID8gcGFyc2Uocm93Lm9yaWdpbmFsLmNvbnRlbnQpIDpcclxuICAgICAgICAgIHJvdy5vcmlnaW5hbC5zdGFmZiA/XHJcbiAgICAgICAgICAgIHJvdy5vcmlnaW5hbC5zdGFmZi5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdncmlkJyxcclxuICAgICAgICAgICAgICAgICAgbWFyZ2luOiAnYXV0bycsXHJcbiAgICAgICAgICAgICAgICAgIC8vZ3JpZFRlbXBsYXRlQ29sdW1uczogJzFmciAxZnInLFxyXG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMHZ3JyxcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPFRhYlZpZXc+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8VGFiUGFuZWwgaGVhZGVyPXtkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1tcInN0YWZmXCJdLnRpdGxlfSBsZWZ0SWNvbj1cInBpIHBpLXVzZXIgbXItMlwiPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8dWw+e3Jvdy5vcmlnaW5hbC5zdGFmZi5tYXAoKHVzZXIsIGlkeCkgPT4gPGEga2V5PXt1c2VyLmVtYWlsICsgcm93Lm9yaWdpbmFsLmNvZGV9IGhyZWY9e1wibWFpbHRvOlwiICsgdXNlci5lbWFpbH0+PGxpPnt1c2VyLmxhc3RfbmFtZX0ge3VzZXIuZmlyc3RfbmFtZX08L2xpPjwvYT4pfTwvdWw+XHJcbiAgICAgICAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJQYW5lbCBoZWFkZXI9e2RhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW1wiYXNzaXN0YW50c1wiXS50aXRsZX0gcmlnaHRJY29uPVwicGkgcGktdXNlciBtbC0yXCI+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDx1bD57cm93Lm9yaWdpbmFsLmFzc2lzdGFudHMubWFwKCh1c2VyLCBpZHgpID0+IDxhIGtleT17dXNlci5lbWFpbCArIHJvdy5vcmlnaW5hbC5jb2RlfSBocmVmPXtcIm1haWx0bzpcIiArIHVzZXIuZW1haWx9PjxsaT57dXNlci5sYXN0X25hbWV9IHt1c2VyLmZpcnN0X25hbWV9PC9saT48L2E+KX08L3VsPlxyXG5cclxuICAgICAgICAgICAgICAgICAgPC9UYWJQYW5lbD5cclxuICAgICAgICAgICAgICAgICAgPFRhYlBhbmVsIGhlYWRlcj1cIkxldHRyZVwiIGxlZnRJY29uPVwicGkgcGktZmlsZS13b3JkIG1yLTJcIiByaWdodEljb249XCJwaSBwaS1maWxlLXBkZiBtbC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBpY29uPVwicGkgcGktY2hlY2tcIiByb3VuZGVkIG9uQ2xpY2s9eygpID0+IHNldFZpc2libGUodHJ1ZSl9IGRpc2FibGVkPXtyb3cub3JpZ2luYWwuZG9jdW1lbnQgPT09IG51bGx9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNpZGViYXIga2V5PXtyb3cub3JpZ2luYWwuaWR9IGhlYWRlcj17PGgyPkxldHRyZSBkZSBtaXNzaW9uIDoge3Jvdy5vcmlnaW5hbC5jb2RlfTwvaDI+fSB2aXNpYmxlPXt2aXNpYmxlfSBvbkhpZGU9eygpID0+IHNldFZpc2libGUoZmFsc2UpfSBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy05IGxnOnctOFwiPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbHVtblx0YWxpZ24taXRlbXMtY2VudGVyIGp1c3RpZnktY29udGVudC1jZW50ZXIgZ2FwLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3Jvdy5vcmlnaW5hbC5kb2N1bWVudCAhPT0gbnVsbCA/XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPERvY3VtZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxlPXtyb3cub3JpZ2luYWwuZG9jdW1lbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkxvYWRTdWNjZXNzPXtvbkRvY3VtZW50TG9hZFN1Y2Nlc3N9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBhZ2UgcGFnZU51bWJlcj17cGFnZU51bWJlcn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0RvY3VtZW50PiA6IDxwPk5vIERvY3VtZW50PC9wPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggZmxleC1jb2x1bW5cdGFsaWduLWl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNvbnRlbnQtY2VudGVyIGdhcC0xJyA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBQYWdlIHtwYWdlTnVtYmVyIHx8IChudW1QYWdlcyA/IDEgOiAnLS0nKX0gb2Yge251bVBhZ2VzIHx8ICctLSd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtcm93XHRhbGlnbi1pdGVtcy1jZW50ZXIganVzdGlmeS1jb250ZW50LWNlbnRlciBnYXAtMScgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3BhZ2VOdW1iZXIgPD0gMX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17cHJldmlvdXNQYWdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBQcmV2aW91c1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17cGFnZU51bWJlciA+PSBudW1QYWdlc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17bmV4dFBhZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5leHRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvU2lkZWJhcj5cclxuICAgICAgICAgICAgICAgICAgPC9UYWJQYW5lbD4gICAgICAgICAgPC9UYWJWaWV3PlxyXG4gICAgICAgICAgICAgIDwvQm94ID5cclxuICAgICAgICAgICAgKSA6IG51bGwgOiA8PjwvPixcclxuICAgIHJlbmRlclJvd0FjdGlvbnM6ICh7IGNlbGwsIHJvdywgdGFibGUgfSkgPT4gKFxyXG4gICAgICAvLyA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMXJlbScgfX0+XHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInAtYnV0dG9uc2V0IGZsZXggcC0xXCI+XHJcbiAgICAgICAgPEJ1dHRvbiBzaXplPSdzbWFsbCcgaWNvbj1cInBpIHBpLXBlbmNpbFwiIG9uQ2xpY2s9eygpID0+IHsgdGFibGUuc2V0RWRpdGluZ1Jvdyhyb3cpOyBzZXRFZGl0VmlzaWJsZSh0cnVlKSwgY29uc29sZS5sb2coXCJlZGl0aW5nIHJvdyAuLi5cIik7IH19IHJvdW5kZWQgb3V0bGluZWQgLz5cclxuICAgICAgICA8QnV0dG9uIHNpemU9J3NtYWxsJyBpY29uPVwicGkgcGktdHJhc2hcIiByb3VuZGVkIG91dGxpbmVkXHJcbiAgICAgICAgICBvbkNsaWNrPXsoZXZlbnQpID0+IGNvbmZpcm1Qb3B1cCh7XHJcbiAgICAgICAgICAgIHRhcmdldDogZXZlbnQuY3VycmVudFRhcmdldCxcclxuICAgICAgICAgICAgbWVzc2FnZTogJ1ZvdWxlei12b3VzIHN1cHByaW1lciBjZXR0ZSBsaWduZT8nLFxyXG4gICAgICAgICAgICBpY29uOiAncGkgcGktaW5mby1jaXJjbGUnLFxyXG4gICAgICAgICAgICAvLyBkZWZhdWx0Rm9jdXM6ICdyZWplY3QnLFxyXG4gICAgICAgICAgICBhY2NlcHRDbGFzc05hbWU6ICdwLWJ1dHRvbi1kYW5nZXInLFxyXG4gICAgICAgICAgICBhY2NlcHRMYWJlbDogJ091aScsXHJcbiAgICAgICAgICAgIHJlamVjdExhYmVsOiAnTm9uJyxcclxuICAgICAgICAgICAgYWNjZXB0LFxyXG4gICAgICAgICAgICByZWplY3RcclxuICAgICAgICAgIH0pfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPENvbmZpcm1Qb3B1cCAvPlxyXG4gICAgICA8L3NwYW4+XHJcbiAgICAgIC8vIDwvQm94PlxyXG4gICAgKSxcclxuICAgIC8vIG1ydFRoZW1lOiAodGhlbWUpID0+ICh7XHJcbiAgICAvLyAgIGJhc2VCYWNrZ3JvdW5kQ29sb3I6IFwiIzFmMjkzN1wiLFxyXG4gICAgLy8gICBkcmFnZ2luZ0JvcmRlckNvbG9yOiB0aGVtZS5wYWxldHRlLnNlY29uZGFyeS5tYWluLFxyXG4gICAgLy8gfSksXHJcblxyXG4gIH0pO1xyXG4gIC8vIGNvbnNvbGUubG9nKGRhdGFfLmlzTG9hZGluZylcclxuICAvL25vdGU6IHlvdSBjYW4gYWxzbyBwYXNzIHRhYmxlIG9wdGlvbnMgYXMgcHJvcHMgZGlyZWN0bHkgdG8gPE1hdGVyaWFsUmVhY3RUYWJsZSAvPiBpbnN0ZWFkIG9mIHVzaW5nIHVzZU1hdGVyaWFsUmVhY3RUYWJsZVxyXG4gIC8vYnV0IHRoZSB1c2VNYXRlcmlhbFJlYWN0VGFibGUgaG9vayB3aWxsIGJlIHRoZSBtb3N0IHJlY29tbWVuZGVkIHdheSB0byBkZWZpbmUgdGFibGUgb3B0aW9uc1xyXG4gIHJldHVybiA8PjxNYXRlcmlhbFJlYWN0VGFibGUgdGFibGU9e3RhYmxlfSAvPjxUb2FzdCByZWY9e3RvYXN0fSAvPjwvPjtcclxufVxyXG4iXSwibmFtZXMiOlsiQm94IiwiRGlhbG9nQWN0aW9ucyIsIkRpYWxvZ0NvbnRlbnQiLCJTdGFjayIsIk1SVF9FZGl0QWN0aW9uQnV0dG9ucyIsIk1hdGVyaWFsUmVhY3RUYWJsZSIsInVzZU1hdGVyaWFsUmVhY3RUYWJsZSIsIk1SVF9Mb2NhbGl6YXRpb25fRlIiLCJCdXR0b24iLCJDb25maXJtUG9wdXAiLCJjb25maXJtUG9wdXAiLCJTaWRlYmFyIiwiVGFiUGFuZWwiLCJUYWJWaWV3IiwiVGFnIiwiVG9hc3QiLCJ1c2VNZW1vIiwidXNlUmVmIiwidXNlU3RhdGUiLCJwYXJzZSIsIkVkaXRvciIsInVzZUFwaVByb2Nlc3NDcmVhdGUiLCJ1c2VBcGlQcm9jZXNzRGVzdHJveSIsInVzZUFwaVByb2Nlc3NVcGRhdGUiLCJHZW5lcmljVGFibGUiLCJkYXRhXyIsInRvYXN0IiwibXV0YXRlIiwiY3JlYXRlUHJvY2VzcyIsInVwZGF0ZVByb2Nlc3MiLCJkZWxldGVQcm9jZXNzIiwidmlzaWJsZSIsInNldFZpc2libGUiLCJlZGl0VmlzaWJsZSIsInNldEVkaXRWaXNpYmxlIiwiY3JlYXRlVmlzaWJsZSIsInNldENyZWF0ZVZpc2libGUiLCJvblBhZ2luYXRpb25DaGFuZ2UiLCJzdGF0ZSIsImNvbnNvbGUiLCJsb2ciLCJwYWdpbmF0aW9uIiwic2V0IiwibnVtUGFnZXMiLCJzZXROdW1QYWdlcyIsInBhZ2VOdW1iZXIiLCJzZXRQYWdlTnVtYmVyIiwiYWNjZXB0IiwiY3VycmVudCIsInNob3ciLCJzZXZlcml0eSIsInN1bW1hcnkiLCJkZXRhaWwiLCJsaWZlIiwicmVqZWN0Iiwib25Eb2N1bWVudExvYWRTdWNjZXNzIiwiY2hhbmdlUGFnZSIsIm9mZnNldCIsInByZXZQYWdlTnVtYmVyIiwicHJldmlvdXNQYWdlIiwibmV4dFBhZ2UiLCJhbmNob3JFbCIsInNldEFuY2hvckVsIiwicm93QWN0aW9uRW5hYmxlZCIsInNldFJvd0FjdGlvbkVuYWJsZWQiLCJvcGVuIiwiQm9vbGVhbiIsImhhbmRsZUNsaWNrIiwiZXZlbnQiLCJjdXJyZW50VGFyZ2V0IiwiY29sdW1ucyIsIk9iamVjdCIsImVudHJpZXMiLCJkYXRhX3R5cGUiLCJwcm9wZXJ0aWVzIiwiZmlsdGVyIiwiaW5kZXgiLCJrZXkiLCJ2YWx1ZSIsImluY2x1ZGVzIiwibWFwIiwiaGVhZGVyIiwidGl0bGUiLCJhY2Nlc3NvcktleSIsImlkIiwiQ2VsbCIsImNlbGwiLCJkaXYiLCJnZXRWYWx1ZSIsIkVkaXQiLCJjb2x1bW4iLCJyb3ciLCJ0YWJsZSIsImluaXRpYWxWYWx1ZSIsIm9yaWdpbmFsIiwidGlueW1jZVNjcmlwdFNyYyIsImFwaUtleSIsImluaXQiLCJoZWlnaHQiLCJtZW51YmFyIiwicGx1Z2lucyIsInRvb2xiYXIiLCJtdWlUYWJsZUhlYWRDZWxsUHJvcHMiLCJhbGlnbiIsIm11aVRhYmxlQm9keUNlbGxQcm9wcyIsIm11aVRhYmxlRm9vdGVyQ2VsbFByb3BzIiwiZm9ybWF0IiwiYWNjZXNzb3JGbiIsIkRhdGUiLCJjcmVhdGVkIiwibW9kaWZpZWQiLCJmaWx0ZXJWYXJpYW50IiwiZmlsdGVyRm4iLCJzb3J0aW5nRm4iLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJlZGl0U2VsZWN0T3B0aW9ucyIsImFsbE9mIiwiZW51bSIsIm11aUVkaXRUZXh0RmllbGRQcm9wcyIsInNlbGVjdCIsInZhcmlhbnQiLCJTZWxlY3RQcm9wcyIsIm11bHRpcGxlIiwiY29kZSIsImNoaWxkcmVuIiwiZGF0YSIsImVycm9yIiwicmVzdWx0cyIsInJvd0NvdW50IiwiY291bnQiLCJlbmFibGVSb3dTZWxlY3Rpb24iLCJlbmFibGVDb2x1bW5PcmRlcmluZyIsImVuYWJsZUdsb2JhbEZpbHRlciIsImVuYWJsZUdyb3VwaW5nIiwiZW5hYmxlUm93QWN0aW9ucyIsImVuYWJsZVJvd1Bpbm5pbmciLCJlbmFibGVTdGlja3lIZWFkZXIiLCJlbmFibGVTdGlja3lGb290ZXIiLCJlbmFibGVDb2x1bW5QaW5uaW5nIiwiZW5hYmxlQ29sdW1uUmVzaXppbmciLCJlbmFibGVSb3dOdW1iZXJzIiwiZW5hYmxlRXhwYW5kQWxsIiwiZW5hYmxlRWRpdGluZyIsImVuYWJsZUV4cGFuZGluZyIsIm1hbnVhbFBhZ2luYXRpb24iLCJpbml0aWFsU3RhdGUiLCJwYWdlU2l6ZSIsInBhZ2VJbmRleCIsImNvbHVtblZpc2liaWxpdHkiLCJjcmVhdGVkX2J5IiwibW9kZmllZF9ieSIsIm1vZGlmaWVkX2J5Iiwic3RhZmYiLCJhc3Npc3RhbnRzIiwiZG9jdW1lbnQiLCJkZW5zaXR5Iiwic2hvd0dsb2JhbEZpbHRlciIsInNvcnRpbmciLCJkZXNjIiwicGFnaSIsImlzTG9hZGluZyIsImxvY2FsaXphdGlvbiIsImRpc3BsYXlDb2x1bW5EZWZPcHRpb25zIiwiZW5hYmxlSGlkaW5nIiwic2l6ZSIsImRlZmF1bHRDb2x1bW4iLCJncm93IiwiZW5hYmxlTXVsdGlTb3J0IiwibXVpVGFibGVQYXBlclByb3BzIiwiY2xhc3NOYW1lIiwiY2xhc3NlcyIsInJvb3QiLCJzeCIsImJhY2tncm91bmRDb2xvciIsImZvbnRGYW1pbHkiLCJmb250RmVhdHVyZVNldHRpbmdzIiwiY29sb3IiLCJlZGl0RGlzcGxheU1vZGUiLCJjcmVhdGVEaXNwbGF5TW9kZSIsIm9uRWRpdGluZ1Jvd1NhdmUiLCJ2YWx1ZXMiLCJzZXRFZGl0aW5nUm93Iiwib25FZGl0aW5nUm93Q2FuY2VsIiwib25DcmVhdGluZ1Jvd1NhdmUiLCJzZXRDcmVhdGluZ1JvdyIsIm9uQ3JlYXRpbmdSb3dDYW5jZWwiLCJtdWlFZGl0Um93RGlhbG9nUHJvcHMiLCJkaXNwbGF5IiwiekluZGV4IiwibXVpVGFibGVGb290ZXJQcm9wcyIsIm11aVRhYmxlQ29udGFpbmVyUHJvcHMiLCJnZXRTdGF0ZSIsImlzRnVsbFNjcmVlbiIsInJlZnMiLCJ0b3BUb29sYmFyUmVmIiwib2Zmc2V0SGVpZ2h0IiwiYm90dG9tVG9vbGJhclJlZiIsIm11aVBhZ2luYXRpb25Qcm9wcyIsIm11aVRvcFRvb2xiYXJQcm9wcyIsIm11aVRhYmxlQm9keVByb3BzIiwicmVuZGVyVG9wVG9vbGJhckN1c3RvbUFjdGlvbnMiLCJkaXJlY3Rpb24iLCJzcGFjaW5nIiwiaWNvbiIsInJvdW5kZWQiLCJhcmlhLWNvbnRyb2xzIiwidW5kZWZpbmVkIiwiYXJpYS1oYXNwb3B1cCIsImFyaWEtZXhwYW5kZWQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJnZXRJc1NvbWVSb3dzU2VsZWN0ZWQiLCJhcmlhLWxhYmVsIiwibXVpRGV0YWlsUGFuZWxQcm9wcyIsInRoZW1lIiwicGFsZXR0ZSIsIm1vZGUiLCJyZW5kZXJDcmVhdGVSb3dEaWFsb2dDb250ZW50IiwiaW50ZXJuYWxFZGl0Q29tcG9uZW50cyIsInN0eWxlIiwicG9zaXRpb24iLCJzcGFuIiwibmFtZSIsIm9uSGlkZSIsImZsZXhEaXJlY3Rpb24iLCJnYXAiLCJyZW5kZXJFZGl0Um93RGlhbG9nQ29udGVudCIsInJlbmRlckRldGFpbFBhbmVsIiwiZGVzY3JpcHRpb24iLCJjb250ZW50IiwibGVuZ3RoIiwibWFyZ2luIiwid2lkdGgiLCJsZWZ0SWNvbiIsInVsIiwidXNlciIsImlkeCIsImEiLCJocmVmIiwiZW1haWwiLCJsaSIsImxhc3RfbmFtZSIsImZpcnN0X25hbWUiLCJyaWdodEljb24iLCJoMiIsIkRvY3VtZW50IiwiZmlsZSIsIm9uTG9hZFN1Y2Nlc3MiLCJQYWdlIiwicCIsInR5cGUiLCJyZW5kZXJSb3dBY3Rpb25zIiwib3V0bGluZWQiLCJ0YXJnZXQiLCJtZXNzYWdlIiwiYWNjZXB0Q2xhc3NOYW1lIiwiYWNjZXB0TGFiZWwiLCJyZWplY3RMYWJlbCIsInJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/processes/(components)/GenericTAble.tsx\n"));

/***/ })

});