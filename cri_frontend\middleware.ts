import { NextRequest, NextResponse } from 'next/server'
import { auth } from './lib/auth-custom'
import { createAbilityForUser } from './app/ability'

// Define public routes that don't require authentication
const publicRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/api/auth',
]

// Define admin-only routes
const adminRoutes = [
  '/admin',
  '/users/manage',
  '/roles',
  '/permissions',
]

// Define route-to-subject mapping for CASL permissions
const routePermissions: Record<string, { action: string; subject: string }> = {
  '/missions': { action: 'read', subject: 'Mission' },
  '/missions/create': { action: 'create', subject: 'Mission' },
  '/missions/edit': { action: 'update', subject: 'Mission' },
  '/recommendations': { action: 'read', subject: 'Recommendation' },
  '/recommendations/create': { action: 'create', subject: 'Recommendation' },
  '/plans': { action: 'read', subject: 'Plan' },
  '/plans/create': { action: 'create', subject: 'Plan' },
  '/themes': { action: 'read', subject: 'Theme' },
  '/themes/create': { action: 'create', subject: 'Theme' },
  '/users': { action: 'read', subject: 'User' },
  '/users/create': { action: 'create', subject: 'User' },
  '/arbitrations': { action: 'read', subject: 'Arbitration' },
  '/documents': { action: 'read', subject: 'MissionDocument' },
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for API routes, static files, and other excluded paths
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/static/') ||
    pathname.includes('.')
  ) {
    return NextResponse.next()
  }

  // Allow access to public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next()
  }

  try {
    // Get session from NextAuth
    const session = await auth(request)

    // If no session and trying to access protected route, redirect to login
    if (!session?.user) {
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('callbackUrl', pathname)
      return NextResponse.redirect(loginUrl)
    }

    const user = session.user

    // Check if user is active
    if (!user.isActive) {
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('error', 'account-disabled')
      return NextResponse.redirect(loginUrl)
    }

    // Check admin routes
    if (adminRoutes.some(route => pathname.startsWith(route))) {
      if (!user.isSuperuser && !user.isStaff) {
        return NextResponse.redirect(new URL('/unauthorized', request.url))
      }
    }

    // Check CASL permissions for specific routes
    const ability = createAbilityForUser(user)

    // Find matching route permission
    const matchedRoute = Object.keys(routePermissions).find(route =>
      pathname.startsWith(route)
    )

    if (matchedRoute) {
      const permission = routePermissions[matchedRoute]
      if (permission) {
        const { action, subject } = permission

        if (!ability.can(action as any, subject as any)) {
          return NextResponse.redirect(new URL('/unauthorized', request.url))
        }
      }
    }

    // Add user info to headers for downstream use
    const response = NextResponse.next()
    response.headers.set('x-user-id', user.id)
    response.headers.set('x-user-email', user.email)
    response.headers.set('x-user-roles', JSON.stringify({
      isStaff: user.isStaff,
      isSuperuser: user.isSuperuser,
      isActive: user.isActive,
    }))

    return response

  } catch (error) {
    console.error('Middleware error:', error)

    // On error, redirect to login
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('error', 'session-error')
    return NextResponse.redirect(loginUrl)
  }
}


export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files (images, etc.)
     * - file extensions (js, css, png, etc.)
     */
    '/((?!api|_next/static|_next/image|layout|tinymce|themes|demo|favicon.ico|favicon.svg|images|.*\\.).*)',
  ],
}