'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { createAbilityForUser, defineAbilitiesFor, AppAbility, createDefaultAbility } from '@/app/ability';
import { AbilityContext } from '@/app/Can';

interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  isActive: boolean;
  isStaff: boolean;
  isSuperuser: boolean;
}

interface AuthContextType {
  user: User | null;
  ability: AppAbility;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
  updateAbility: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [ability, setAbility] = useState<AppAbility>(createDefaultAbility());
  const [loading, setLoading] = useState(true);

  // Check session on mount
  useEffect(() => {
    checkSession();
  }, []);

  // Update ability when user changes
  const updateAbility = async () => {
    if (user) {
      try {
        // Try async version first (with database permissions)
        const newAbility = await defineAbilitiesFor(user);
        setAbility(newAbility);
      } catch (error) {
        console.error('Error loading user permissions, falling back to basic permissions:', error);
        // Fallback to synchronous version
        const fallbackAbility = createAbilityForUser(user);
        setAbility(fallbackAbility);
      }
    } else {
      setAbility(createDefaultAbility());
    }
  };

  // Update ability when user changes
  useEffect(() => {
    updateAbility();
  }, [user]);

  const checkSession = async () => {
    try {
      const response = await fetch('/api/auth/session', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const sessionData = await response.json();
        if (sessionData?.user) {
          setUser(sessionData.user);
        } else {
          setUser(null);
        }
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Error checking session:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Login failed');
      }

      const data = await response.json();
      if (data.user) {
        setUser(data.user);
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      setUser(null); // Set to null anyway
    }
  };

  const value = {
    user,
    ability,
    login,
    logout,
    loading,
    updateAbility,
  };

  return (
    <AuthContext.Provider value={value}>
      <AbilityContext.Provider value={ability}>
        {children}
      </AbilityContext.Provider>
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function useAbility() {
  return useContext(AbilityContext);
}
