import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/processes/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid process ID' }, { status: 400 })
    }

    const process = await prisma.process.findUnique({
      where: { id },
      include: {
        parent: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        children: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        },
        modifiedByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        }
      },
    })

    if (!process) {
      return NextResponse.json({ error: 'Process not found' }, { status: 404 })
    }

    return NextResponse.json({ data: process })
  } catch (error) {
    console.error('Error fetching process:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/processes/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can update processes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid process ID' }, { status: 400 })
    }

    const body = await request.json()
    const { title, shortTitle, parentId } = body

    // Check if process exists
    const existingProcess = await prisma.process.findUnique({
      where: { id },
    })

    if (!existingProcess) {
      return NextResponse.json({ error: 'Process not found' }, { status: 404 })
    }

    // Check if parent exists (if provided)
    if (parentId && parentId !== existingProcess.parentId) {
      const parent = await prisma.process.findUnique({
        where: { id: parentId }
      })
      
      if (!parent) {
        return NextResponse.json(
          { error: 'Parent process not found' },
          { status: 404 }
        )
      }

      // Prevent circular references
      if (parentId === id) {
        return NextResponse.json(
          { error: 'Process cannot be its own parent' },
          { status: 400 }
        )
      }
    }

    // Update process
    const process = await prisma.process.update({
      where: { id },
      data: {
        ...(title !== undefined && { title }),
        ...(shortTitle !== undefined && { shortTitle }),
        ...(parentId !== undefined && { parentId }),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        parent: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        children: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
          }
        }
      },
    })

    return NextResponse.json({ data: process })
  } catch (error) {
    console.error('Error updating process:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/processes/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can delete processes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid process ID' }, { status: 400 })
    }

    // Check if process exists
    const existingProcess = await prisma.process.findUnique({
      where: { id },
      include: {
        children: true,
        themes: true,
      },
    })

    if (!existingProcess) {
      return NextResponse.json({ error: 'Process not found' }, { status: 404 })
    }

    // Check if process has children or themes
    if (existingProcess.children.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete process with child processes' },
        { status: 400 }
      )
    }

    if (existingProcess.themes.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete process with associated themes' },
        { status: 400 }
      )
    }

    // Delete process
    await prisma.process.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Process deleted successfully' })
  } catch (error) {
    console.error('Error deleting process:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
