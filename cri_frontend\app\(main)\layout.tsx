import { Metadata } from 'next';
import Layout from '../../layout/layout';

interface AppLayoutProps {
    children: React.ReactNode;
}

export const metadata: Metadata = {
    title: 'Suivi des recommendations | CRI/LQS',
    description: 'Suivi des recommendations | CRI/LQS',
    robots: { index: false, follow: false },
    viewport: { initialScale: 1, width: 'device-width' },
    // openGraph: {
    //     type: 'website',
    //     title: 'PrimeReact SAKAI-REACT',
    //     url: 'https://sakai.primereact.org/',
    //     description: 'The ultimate collection of design-agnostic, flexible and accessible React UI Components.',
    //     images: ['https://www.primefaces.org/static/social/sakai-react.png'],
    //     ttl: 604800
    // },
    icons: {
        icon: '/favicon.svg'
    }
};

export default function AppLayout({ children }: AppLayoutProps) {
    return <Layout>{children}</Layout>;
}
