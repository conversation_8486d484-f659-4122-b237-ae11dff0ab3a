// Simple schemas for table display and form generation
// These schemas define the properties and titles for each model

export const $Plan = {
  properties: {
    id: { title: 'ID' },
    title: { title: 'Titre' },
    exercise: { title: 'Exercice' },
    type: { title: 'Type' },
    created: { title: 'Cré<PERSON>' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $Mission = {
  properties: {
    id: { title: 'ID' },
    code: { title: 'Code' },
    title: { title: 'Titre' },
    type: { title: 'Type' },
    etat: { title: 'État' },
    exercise: { title: 'Exercice' },
    planId: { title: 'Plan' },
    themeId: { title: 'Thème' },
    headId: { title: 'Chef de mission' },
    supervisorId: { title: 'Superviseur' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $User = {
  properties: {
    id: { title: 'ID' },
    username: { title: "Nom d'utilisateur" },
    email: { title: 'Email' },
    firstName: { title: 'Prénom' },
    lastName: { title: 'Nom' },
    isActive: { title: 'Actif' },
    isStaff: { title: 'Staff' },
    isSuperuser: { title: 'Superutilisateur' },
    lastLogin: { title: 'Dernière connexion' },
    dateJoined: { title: 'Date inscription' },
  }
};

export const $Recommendation = {
  properties: {
    id: { title: 'ID' },
    recommendation: { title: 'Recommandation' },
    missionId: { title: 'Mission' },
    concernedStructureId: { title: 'Structure concernée' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $Comment = {
  properties: {
    id: { title: 'ID' },
    comment: { title: 'Commentaire' },
    recommendationId: { title: 'Recommandation' },
    createdById: { title: 'Créé par' },
    created: { title: 'Créé' },
    updated: { title: 'Mis à jour' },
  }
};

export const $Action = {
  properties: {
    id: { title: 'ID' },
    description: { title: 'Description' },
    status: { title: 'Statut' },
    progress: { title: 'Progrès' },
    startDate: { title: 'Date de début' },
    endDate: { title: 'Date de fin' },
    jobLeader: { title: 'Responsable' },
    proof: { title: 'Preuve' },
    recommendationId: { title: 'Recommandation' },
    dependencies: { title: 'Dépendances' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $Arbitration = {
  properties: {
    id: { title: 'ID' },
    plan: { title: 'Plan' },
    report: { title: 'Rapport' },
    team : {title :'Membres'},
    // created: { title: 'Créé' },
    // modified: { title: 'Modifié' },
    // createdBy: { title: 'Créé par' },
    // modifiedBy: { title: 'Modifié par' },
  }
};

export const $Theme = {
  properties: {
    id: { title: 'ID' },
    title: { title: 'Titre' },
    code: { title: 'Code' },
    validated: { title: 'Validé' },
    proposedBy: { title: 'Proposé par' },
    monthStart: { title: 'Mois début' },
    monthEnd: { title: 'Mois fin' },
    domain: { title: 'Domaine' },
    process: { title: 'Processus' },
    proposingStructures: { title: 'Structures proposantes' },
    concernedStructures: { title: 'Structures concernées' },
    risks: { title: 'Risques' },
    goals: { title: 'Objectifs' },
    arbitratedThemes: { title: 'Thèmes arbitrés' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $ArbitratedTheme = {
  properties: {
    id: { title: 'ID' },
    arbitrationId: { title: 'ID Arbitrage' },
    themeId: { title: 'ID Thème' },
    note: { title: 'Note' },
    arbitration: { title: 'Arbitrage' },
    theme: { title: 'Thème' },
    missions: { title: 'Missions' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $Domain = {
  properties: {
    id: { title: 'ID' },
    title: { title: 'Titre' },
    shortTitle: { title: 'Titre court' },
    parentId: { title: 'ID Parent' },
    parent: { title: 'Domaine parent' },
    children: { title: 'Sous-domaines' },
    type: { title: 'Type' },
    observation: { title: 'Observation' },
    themes: { title: 'Thèmes' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $Process = {
  properties: {
    id: { title: 'ID' },
    title: { title: 'Titre' },
    shortTitle: { title: 'Titre court' },
    parentId: { title: 'ID Parent' },
    parent: { title: 'Processus parent' },
    children: { title: 'Sous-processus' },
    themes: { title: 'Thèmes' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $Risk = {
  properties: {
    id: { title: 'ID' },
    title: { title: 'Titre' },
    description: { title: 'Description' },
    validated: { title: 'Validé' },
    themes: { title: 'Thèmes' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $Goal = {
  properties: {
    id: { title: 'ID' },
    title: { title: 'Titre' },
    description: { title: 'Description' },
    themes: { title: 'Thèmes' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};

export const $CriStructview = {
  properties: {
    id: { title: 'ID' },
    codeStru: { title: 'Code Structure' },
    libellStru: { title: 'Libellé Structure' },
    codeUnit: { title: 'Code Unité' },
    codeMnemonique: { title: 'Code Mnémonique' },
    structureCorrespondents: { title: 'Correspondants' },
    structureInterim: { title: 'Intérimaires' },
    themeProposing: { title: 'Thèmes proposés' },
    themeConcerned: { title: 'Thèmes concernés' },
    recommendations: { title: 'Recommandations' },
  }
};

export const $Structure = {
  properties: {
    id: { title: 'ID' },
    name: { title: 'Nom' },
    code: { title: 'Code' },
    abbreviation: { title: 'Abréviation' },
    type: { title: 'Type' },
    parentId: { title: 'Structure parente' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
  }
};

export const $Document = {
  properties: {
    id: { title: 'ID' },
    title: { title: 'Titre' },
    filename: { title: 'Nom du fichier' },
    filesize: { title: 'Taille' },
    mimetype: { title: 'Type MIME' },
    uploadedById: { title: 'Téléchargé par' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
  }
};

export const $MissionDocument = {
  properties: {
    id: { title: 'ID' },
    missionId: { title: 'Mission' },
    documentId: { title: 'Document' },
    created: { title: 'Créé' },
  }
};

export const $Account = {
  properties: {
    id: { title: 'ID' },
    userId: { title: 'Utilisateur' },
    provider: { title: 'Fournisseur' },
    providerId: { title: 'ID Fournisseur' },
    password: { title: 'Mot de passe' },
    created: { title: 'Créé' },
    updated: { title: 'Mis à jour' },
  }
};

export const $Session = {
  properties: {
    id: { title: 'ID' },
    userId: { title: 'Utilisateur' },
    token: { title: 'Token' },
    expiresAt: { title: 'Expire le' },
    created: { title: 'Créé' },
    updated: { title: 'Mis à jour' },
  }
};

export const $Constat = {
  properties: {
    id: { title: 'ID' },
    title: { title: 'Titre' },
    description: { title: 'Description' },
    content: { title: 'Contenu' },
    type: { title: 'Type' },
    parent: { title: 'Parent' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
    createdBy: { title: 'Créé par' },
    modifiedBy: { title: 'Modifié par' },
  }
};



export const $StructureLQS = {
  properties: {
    id: { title: 'ID' },
    name: { title: 'Nom' },
    code: { title: 'Code' },
    abbrev: { title: 'Abréviation' },
    type: { title: 'Type' },
    correspondents: { title: 'Correspondants' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
  }
};

export const $StructureLQSInterim = {
  properties: {
    id: { title: 'ID' },
    name: { title: 'Nom' },
    code: { title: 'Code' },
    abbrev: { title: 'Abréviation' },
    type: { title: 'Type' },
    created: { title: 'Créé' },
    modified: { title: 'Modifié' },
  }
};

// Export all schemas as a collection for easy access
export const schemas = {
  Plan: $Plan,
  Mission: $Mission,
  User: $User,
  Recommendation: $Recommendation,
  Comment: $Comment,
  Action: $Action,
  Arbitration: $Arbitration,
  Theme: $Theme,
  ArbitratedTheme: $ArbitratedTheme,
  Domain: $Domain,
  Process: $Process,
  Risk: $Risk,
  Goal: $Goal,
  CriStructview: $CriStructview,
  Structure: $Structure,
  Document: $Document,
  MissionDocument: $MissionDocument,
  Account: $Account,
  Session: $Session,
  Constat: $Constat,
  StructureLQS: $StructureLQS,
  StructureLQSInterim: $StructureLQSInterim,
};

// Helper function to get schema by model name
export function getSchema(modelName: string) {
  return schemas[modelName as keyof typeof schemas];
}
