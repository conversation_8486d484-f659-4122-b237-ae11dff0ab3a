'use client'
import { Box, DialogActions, DialogContent, Stack } from '@mui/material';
import {
  MRT_EditActionButtons,
  MRT_RowData,
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef
} from 'material-react-table';
import { MRT_Localization_FR } from 'material-react-table/locales/fr';
import { Button } from 'primereact/button';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useMemo, useRef, useState } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { Chip } from 'primereact/chip';
import { Dropdown } from 'primereact/dropdown';
import { getCookie } from 'cookies-next';
import { Chips } from 'primereact/chips';
import { Can } from '@/app/Can';
import { useApiArbitratedThemeCreate, useApiArbitratedThemeUpdate, useApiArbitrationList, useApiThemeList } from '@/hooks/useNextApi';
import { Arbitration, Theme } from '@prisma/client';

export default function GenericTable<T extends MRT_RowData>(data_: { isLoading: any; data_: any, error: any, data_type: any | undefined, pagination: any }) {
  const user = JSON.parse(getCookie('user')?.toString() || '{}')

  const [theme_id, setThemeId] = useState(0);
  const [visible, setVisible] = useState(false);
  const [rowTobe, setRowTobe] = useState({});
  const [editVisible, setEditVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [rowActionEnabled, setRowActionEnabled] = useState(false);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };


  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const toast = useRef<Toast | null>(null);

  const { data: arbitrations, isLoading, error } = useApiArbitrationList();
  const { data: themes, isLoading: isLoading_themes, error: error_themes } = useApiThemeList()

  const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = useApiArbitratedThemeCreate()
  const { data: data_update, error: error_update, isPending: isMutating_update, mutate: trigger_update } = useApiArbitratedThemeUpdate()

  const getSeverity = (str: string) => {
    switch (str) {
      case 'Vice Président':
        return 'success';
      case 'Contrôle Interne':
        return 'warning';
      case 'Audit Interne':
        return 'warning';
      case 'Structures':
        return 'danger';

      default:
        return null;
    }
  };

  function onPaginationChange(state: any) {
    console.log(data_.pagination);
    data_.pagination.set(state)
  };

  const accept = () => {
    toast.current?.show({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 });
  };

  const reject = () => {
    toast.current?.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
  };

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setPageNumber(1);
  }

  function changePage(offset: number) {
    setPageNumber(prevPageNumber => prevPageNumber + offset);
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }

  const columns = useMemo<MRT_ColumnDef<T>[]>(
    () =>
      [
        {
          header: "ID",
          accessorKey: "id",
          size: 70,
          Edit: () => null,
        },
        {
          header: "Aribtrage",
          accessorKey: "arbitration",
          muiTableHeadCellProps: {
            align: 'center',
          },
          muiTableBodyCellProps: {
            align: 'center',
          },
          muiTableFooterCellProps: {
            align: 'center',
          },
          id: "arbitration",
          Cell: ({ cell, row }) => <Tag className='w-11rem text-sm' severity={row.original.arbitration.plan.code.includes('Audit') ? 'danger' : 'info'} value={row.original.arbitration.plan.code} />,
          Edit: ({ row }) => <Dropdown
            filter
            onChange={(e) => {
              console.log(e);
              setRowTobe({ ...rowTobe, arbitration: arbitrations?.data?.results?.find((arbi: any) => arbi.id === e.value.code) });
              row._valuesCache = { ...row._valuesCache, arbitration: arbitrations?.data?.results?.find((arbi: any) => arbi.id === e.value.code) }
            }}
            optionLabel="name"
            placeholder="Choisir arbitrage"
            value={{ name: row._valuesCache.arbitration?.id || null, code: row._valuesCache.arbitration?.id || null }}
            options={arbitrations?.data?.results?.map((arbi: any) => { return { code: arbi.id, name: arbi.plan?.type || `Arbitration ${arbi.id}` } }) || []}>

          </Dropdown>
        },
        {
          header: "Proposé par",
          accessorKey: "theme",
          id: "theme_proposed_by",
          muiTableHeadCellProps: {
            align: 'center',
          },
          muiTableBodyCellProps: {
            align: 'center',
          },
          muiTableFooterCellProps: {
            align: 'center',
          },

          Edit: () => null,
          Cell: ({ cell, row }) => <Tag
            className='w-9rem text-sm'
            key={row.original.code + row.original.created}
            severity={getSeverity(cell.getValue<Theme>().proposedBy)}
            value={cell.getValue<Theme>().proposedBy}
          />
        },
        {
          header: "Structures proposantes",
          accessorKey: "theme",
          muiTableHeadCellProps: {
            align: 'left',
          },
          muiTableBodyCellProps: {
            align: 'left',
          },
          muiTableFooterCellProps: {
            align: 'center',
          },
          id: "proposing_structures",
          Cell: ({ cell, row }) => {
            const theme = cell.getValue() as any;
            console.log(theme.proposingStructures)
            return <Stack direction={'row'} spacing={1}>
              {theme.proposingStructures?.map((val: any, idx: number) => <Chip key={`thm${row.original.theme.id}_ps${idx}`} style={{ backgroundColor: 'green', color: 'white' }} label={val.codeMnemonique || val.code_mnemonique}></Chip>) || []}</Stack>
          },
          Edit: () => null
        },
        {
          header: "Structures concernées",
          accessorKey: "theme",
          muiTableHeadCellProps: {
            align: 'left',
          },
          muiTableBodyCellProps: {
            align: 'left',
          },
          muiTableFooterCellProps: {
            align: 'center',
          },
          id: "concerned_structures",
          Cell: ({ cell, row }) => {
            const theme = cell.getValue() as any;
            return <Stack direction={'row'} spacing={1}>
              {theme.concernedStructures?.map((val: any, idx: number) => <Chip key={`thm${row.original.theme.id}_cs${idx}`} style={{ backgroundColor: 'green', color: 'white' }} label={val.codeMnemonique || val.code_mnemonique}></Chip>) || []}
            </Stack>
          },
          Edit: () => null
        },
        {
          header: "Domaine",
          accessorKey: "domain",
          id: "domain",
          Cell: ({ cell, row }) => row.original.theme.domain.title,
          Edit: () => null
        },
        {
          header: "Processus",
          accessorKey: "process",
          id: "process",
          Cell: ({ cell, row }) => row.original.theme.process.title,
          Edit: () => null
        },
        {
          header: "Intitulé",
          accessorKey: "theme",
          id: "title",
          Cell: ({ cell, row }) => <div className='white-space-normal'>{row.original.theme.title}</div>,
          Edit: ({ row }) => <Dropdown
            filter
            onChange={(e) => {
              console.log(e);
              setRowTobe({ ...rowTobe, theme: themes?.data?.results?.find((thm: any) => thm.id === e.value.code) });
              row._valuesCache = { ...row._valuesCache, theme: themes?.data?.results?.find((thm: any) => thm.id === e.value.code) }
            }}
            optionLabel="name"
            placeholder="Choisir un théme"
            value={{ name: row._valuesCache.theme?.title || null, code: row._valuesCache.theme?.id || null }}
            options={themes?.data?.results?.map((thm: any) => { return { code: thm.id, name: thm.title } }) || []}>
          </Dropdown>
        },
        {
          header: "Remarque",
          accessorKey: "note",
          id: "note",
          Cell: ({ cell, row }) => <div className='white-space-normal'>{row.original.note}</div>,
          Edit: ({ row }) => <Editor
            id='note'
            initialValue={row.original.note}
            tinymceScriptSrc="http://localhost:3000/tinymce/tinymce.min.js"
            apiKey='none'
            init={{
              height: 500,
              menubar: true,
              plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'print', 'preview', 'anchor',
                'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'paste', 'code', 'help', 'wordcount'
              ],
              toolbar:
                'undo redo | formatselect | bold italic backcolor | \
                                      alignleft aligncenter alignright alignjustify | \
                                      bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen'
            }}
            onChange={(e) => { row._valuesCache = { ...row._valuesCache, note: e.target.getContent() } }}
          />
        }
      ], [isLoading, isLoading_themes]
  );

  const table = useMaterialReactTable({
    columns,
    data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [data_.data_.data], //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,
    enableRowSelection: true, //enable some features
    enableColumnOrdering: true, //enable a feature for all columns
    enableGlobalFilter: true, //turn off a feature
    enableGrouping: true,
    enableRowActions: true,
    enableRowPinning: true,
    enableStickyHeader: true,
    enableStickyFooter: true,
    enableColumnPinning: true,
    enableColumnResizing: true,
    enableRowNumbers: true,
    enableEditing: true,
    manualPagination: true,
    initialState: {
      pagination: { pageSize: 5, pageIndex: 1 },
      columnVisibility: { created_by: false, created: false, modfied_by: false, modified: false, modified_by: false, },
      density: 'compact',
      showGlobalFilter: true,
      sorting: [{ id: 'id', desc: false }],
    },
    state: {
      pagination: data_.pagination.pagi,
      isLoading: data_.isLoading, //cell skeletons and loading overlay
      //showProgressBars: isLoading, //progress bars while refetching
      // isSaving: isSavingTodos, //progress bars and save button spinners
    },
    localization: MRT_Localization_FR,
    onPaginationChange: onPaginationChange,
    displayColumnDefOptions: {
      'mrt-row-pin': {
        enableHiding: true,
      },
      'mrt-row-expand': {
        enableHiding: true,
      },
        'mrt-row-numbers': {
        enableHiding: true, //now row numbers are hidable too
      },
    },
    defaultColumn: {
      grow: true,
      enableMultiSort: true,
    },
    muiTablePaperProps: ({ table }) => ({

      className: "p-datatable-gridlines text-900 font-medium text-xl",
      classes: { root: 'p-datatable-gridlines text-900 font-medium text-xl' },

      sx: {
        height: `calc(100vh - 9rem)`,
        // height: `calc(100vh - 200px)`,
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        "& .MuiTablePagination-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
        "& .MuiBox-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
      },
    }),
    editDisplayMode: 'modal',
    createDisplayMode: 'modal',
    onEditingRowSave: ({ table, row, values }) => {
      setThemeId(row.original.id)
      //validate data
      //save data to api
      console.log("onEditingRowSave", values)
      const { theme, note, arbitration, ...rest } = values
      let update_values = { theme: theme.id, note: note, arbitration: arbitration.id }
      trigger_update({id:rest.id ,data: update_values},
        {

          onSuccess: () => {
            toast.current?.show({ severity: 'info', summary: 'Modification', detail: 'Thème modifié' });
            table.setCreatingRow(null);
          },
          onError: (err) => {
            toast.current?.show({ severity: 'error', life: 10000, summary: 'Création', detail: `${err.response?.data.message || err.response?.data.non_field_errors}` });
            console.log("onCreatingRowSave", err.message);
            row._valuesCache = { error: err.message, ...row._valuesCache };
            return;
          },
        }
      )

    },
    onEditingRowCancel: () => {
      //clear any validation errors
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });

    },
    onCreatingRowSave: ({ table, row, values }) => {
      //validate data
      //save data to api
      console.log("onCreatingRowSave", values)
      const { theme, note, arbitration, ...rest } = values
      let insert_values = { theme: theme.id, note: note, arbitration: arbitration.id }
      trigger_create(insert_values,
        {

          onSuccess: () => {
            toast.current?.show({ severity: 'info', summary: 'Création', detail: 'Enregistrement créé' });
            table.setCreatingRow(null);
          },
          onError: (err) => {
            toast.current?.show({ severity: 'error', life: 10000, summary: 'Création', detail: `${err.response?.data.message || err.response?.data.non_field_errors}` });
            console.log("onCreatingRowSave", err.message);
            row._valuesCache = { error: err.message, ...row._valuesCache };
            return;
          },
        }
      )
    },
    onCreatingRowCancel: ({ table }) => {
      //clear any validation errors
      table.setCreatingRow(null);
    },
    muiEditRowDialogProps: ({ row, table }) => ({
      //optionally customize the dialog
      //about:"edit modal",
      // open: editVisible || createVisible,
      maxWidth: 'md',
      // sx: {
      //   //  '& .MuiDialog-root': {
      //   //    width :'70vw'

      //   //  },
      //   // '& .MuiDialog-container': {
      //   //   width :'70vw'

      //   // },
      //   zIndex: 1100,

      // }
    }),
    muiTableFooterProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        "& .MuiTableFooter-root": {
          backgroundColor: "var(--surface-card) !important",
        },
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableContainerProps: ({ table }) => ({
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        height: table.getState().isFullScreen ? `calc(100vh)` : `calc(100vh - 9rem - ${table.refs.topToolbarRef.current?.offsetHeight}px - ${table.refs.bottomToolbarRef.current?.offsetHeight}px)`

      },
    }),
    muiPaginationProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {

        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableHeadCellProps: {
      sx: {
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTopToolbarProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important"
      },

    },
    muiTableBodyProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        //stripe the rows, make odd rows a darker color
        '& tr:nth-of-type(odd) > td': {
          backgroundColor: 'var(--surface-card)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
        '& tr:nth-of-type(even) > td': {
          backgroundColor: 'var(--surface-border)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
      },
    },
    renderTopToolbarCustomActions: ({ table }) => (
      <Stack direction={"row"} spacing={1}>
        <Can I='add' a='ArbitratedTheme'>
        <Button
          icon="pi pi-plus"
          rounded
          aria-controls={open ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={(event) => {
            table.setCreatingRow(true); setCreateVisible(true), console.log("creating row ...");
          }}
          size="small"
        >
        </Button>
        </Can>
      </Stack>
    ),
    muiDetailPanelProps: () => ({
      sx: (theme) => ({
        backgroundColor:
          theme.palette.mode === 'dark'
            ? 'rgba(255,210,244,0.1)'
            : 'rgba(0,0,0,0.1)',
      }),
    }),

    // renderCreateRowDialogContent: ThemeEditForm,
    // renderEditRowDialogContent: ThemeEditForm,
    // renderDetailPanel: ({ row }) =>
    //   row.original.description ? parse(row.original.description) :
    //     row.original.content ? parse(row.original.content) :
    //       row.original.staff ?
    //         row.original.staff.length > 0 ? (
    //           <Box
    //             sx={{
    //               display: 'grid',
    //               margin: 'auto',
    //               //gridTemplateColumns: '1fr 1fr',
    //               width: '100vw',
    //             }}
    //           >
    //             <TabView>

    //               <TabPanel header={data_.data_type.properties["staff"].title} leftIcon="pi pi-user mr-2">

    //                 <ul>{row.original.staff.map((user, idx) => <a key={user.email + row.original.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>
    //               </TabPanel>
    //               <TabPanel header={data_.data_type.properties["assistants"].title} rightIcon="pi pi-user ml-2">

    //                 <ul>{row.original.assistants.map((user, idx) => <a key={user.email + row.original.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>

    //               </TabPanel>
    //               <TabPanel header="Lettre" leftIcon="pi pi-file-word mr-2" rightIcon="pi pi-file-pdf ml-2">
    //                 <Button icon="pi pi-check" rounded onClick={() => setVisible(true)} disabled={row.original.document === null} />
    //                 <Sidebar key={row.original.id} header={<h2>Lettre de mission : {row.original.code}</h2>} visible={visible} onHide={() => setVisible(false)} className="w-full md:w-9 lg:w-8">

    //                   <div className="flex flex-column	align-items-center justify-content-center gap-1">
    //                     {row.original.document !== null ?
    //                       <Document
    //                         file={row.original.document}
    //                         onLoadSuccess={onDocumentLoadSuccess}
    //                       >
    //                         <Page pageNumber={pageNumber} />
    //                       </Document> : <p>No Document</p>}
    //                     <div className='flex flex-column	align-items-center justify-content-center gap-1' >
    //                       <p>
    //                         Page {pageNumber || (numPages ? 1 : '--')} of {numPages || '--'}
    //                       </p>
    //                       <div className='flex flex-row	align-items-center justify-content-center gap-1' >
    //                         <Button
    //                           type="button"
    //                           disabled={pageNumber <= 1}
    //                           onClick={previousPage}
    //                         >
    //                           Previous
    //                         </Button>
    //                         <Button
    //                           type="button"
    //                           disabled={pageNumber >= numPages}
    //                           onClick={nextPage}
    //                         >
    //                           Next
    //                         </Button>
    //                       </div>
    //                     </div>
    //                   </div>
    //                 </Sidebar>
    //               </TabPanel>          </TabView>
    //           </Box >
    //         ) : null : <></>,
    // renderRowActions: ({ cell, row, table }) => (
    //   // <Box sx={{ display: 'flex', gap: '1rem' }}>
    //   <span className="p-buttonset flex p-1">
    //     <Button size='small' icon="pi pi-pencil" onClick={() => { table.setEditingRow(row); setEditVisible(true), console.log("editing row ..."); }} rounded outlined />
    //     <Button size='small' icon="pi pi-trash" rounded outlined
    //       onClick={(event) => confirmPopup({
    //         target: event.currentTarget,
    //         message: 'Voulez-vous supprimer cette ligne?',
    //         icon: 'pi pi-info-circle',
    //         // defaultFocus: 'reject',
    //         acceptClassName: 'p-button-danger',
    //         acceptLabel: 'Oui',
    //         rejectLabel: 'Non',
    //         accept,
    //         reject
    //       })}
    //     />
    //     <ConfirmPopup />
    //   </span>
    //   // </Box>
    // ),
    // mrtTheme: (theme) => ({
    //   baseBackgroundColor: "#1f2937",
    //   draggingBorderColor: theme.palette.secondary.main,
    // }),

  });
  if (isLoading) return (<div></div>)
  console.log("-----------------------------------------",arbitrations)
  //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable
  //but the useMaterialReactTable hook will be the most recommended way to define table options
  return <><MaterialReactTable table={table} /><Toast ref={toast} /></>;
}
