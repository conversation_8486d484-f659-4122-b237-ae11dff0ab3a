"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-client)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RootLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../layout/context/layoutcontext */ \"(app-client)/./layout/context/layoutcontext.tsx\");\n/* harmony import */ var primereact_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/api */ \"(app-client)/./node_modules/primereact/api/api.esm.js\");\n/* harmony import */ var primereact_resources_primereact_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! primereact/resources/primereact.css */ \"(app-client)/./node_modules/primereact/resources/primereact.css\");\n/* harmony import */ var primeflex_primeflex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! primeflex/primeflex.css */ \"(app-client)/./node_modules/primeflex/primeflex.css\");\n/* harmony import */ var primeicons_primeicons_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! primeicons/primeicons.css */ \"(app-client)/./node_modules/primeicons/primeicons.css\");\n/* harmony import */ var _styles_layout_layout_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../styles/layout/layout.scss */ \"(app-client)/./styles/layout/layout.scss\");\n/* harmony import */ var _styles_demo_Demos_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../styles/demo/Demos.scss */ \"(app-client)/./styles/demo/Demos.scss\");\n/* harmony import */ var _contexts_CustomAuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/CustomAuthContext */ \"(app-client)/./contexts/CustomAuthContext.tsx\");\n/* harmony import */ var _utilities_hooks_useSpinner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utilities/hooks/useSpinner */ \"(app-client)/./utilities/hooks/useSpinner.tsx\");\n/* harmony import */ var _utilities_hooks_useToast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utilities/hooks/useToast */ \"(app-client)/./utilities/hooks/useToast.tsx\");\n/* harmony import */ var _utilities_service_fr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utilities/service/fr */ \"(app-client)/./utilities/service/fr.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(app-client)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.QueryClient();\nfunction RootLayout(param) {\n    let { children } = param;\n    (0,primereact_api__WEBPACK_IMPORTED_MODULE_12__.addLocale)(\"fr\", _utilities_service_fr__WEBPACK_IMPORTED_MODULE_10__[\"default\"][\"fr\"]);\n    (0,primereact_api__WEBPACK_IMPORTED_MODULE_12__.locale)(\"fr\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        id: \"theme-css\",\n                        href: \"/themes/lara-light-indigo/theme.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"/tinymce/tinymce.min.js\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_api__WEBPACK_IMPORTED_MODULE_12__.PrimeReactProvider, {\n                    value: {\n                        locale: \"fr\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.QueryClientProvider, {\n                        client: queryClient,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CustomAuthContext__WEBPACK_IMPORTED_MODULE_7__.AuthProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utilities_hooks_useToast__WEBPACK_IMPORTED_MODULE_9__.ToastContextProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utilities_hooks_useSpinner__WEBPACK_IMPORTED_MODULE_8__.LoadingSpinnerProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_1__.LayoutProvider, {\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_14__.ReactQueryDevtools, {}, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 9\n    }, this);\n}\n_c = RootLayout;\nvar _c;\n$RefreshReg$(_c, \"RootLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/layout.tsx\n"));

/***/ })

});