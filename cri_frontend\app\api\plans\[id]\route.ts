import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const planId = parseInt(params.id)

    const plan = await prisma.plan.findUnique({
      where: { id: planId },
      include: {
        missions: {
          select: {
            id: true,
            code: true,
            title: true,
            type: true,
            etat: true,
          }
        },
        arbitrations: {
          include: {
            arbitratedThemes: {
              include: {
                theme: {
                  select: {
                    id: true,
                    title: true,
                    validated: true,
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!plan) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 })
    }

    return NextResponse.json(plan)
  } catch (error) {
    console.error('Plan fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to update plans (staff only)
    if (!session.user.isStaff) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const planId = parseInt(params.id)
    const body = await request.json()

    // Check if plan exists
    const existingPlan = await prisma.plan.findUnique({
      where: { id: planId }
    })

    if (!existingPlan) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 })
    }

    // Update plan
    const updatedPlan = await prisma.plan.update({
      where: { id: planId },
      data: {
        title: body.title,
        exercise: body.exercise,
        type: body.type,
        modified: new Date(),
        modifiedBy: session.user.id,
      },
      include: {
        missions: {
          select: {
            id: true,
            code: true,
            title: true,
            type: true,
            etat: true,
          }
        },
        arbitrations: {
          include: {
            arbitratedThemes: {
              include: {
                theme: {
                  select: {
                    id: true,
                    title: true,
                    validated: true,
                  }
                }
              }
            }
          }
        }
      }
    })

    return NextResponse.json(updatedPlan)
  } catch (error) {
    console.error('Plan update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to delete plans (staff only)
    if (!session.user.isStaff) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const planId = parseInt(params.id)

    // Check if plan exists
    const existingPlan = await prisma.plan.findUnique({
      where: { id: planId }
    })

    if (!existingPlan) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 404 })
    }

    // Delete plan
    await prisma.plan.delete({
      where: { id: planId }
    })

    return NextResponse.json({ message: 'Plan deleted successfully' })
  } catch (error) {
    console.error('Plan deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
