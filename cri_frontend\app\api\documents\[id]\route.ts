import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { unlink } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

// GET /api/documents/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    const document = await prisma.missionDocument.findUnique({
      where: { id },
      include: {
        mission: {
          select: {
            id: true,
            code: true,
            type: true,
          }
        }
      }
    })
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }
    
    // Convert BigInt to string for JSON serialization
    const serializedDocument = {
      ...document,
      size: document.size?.toString() || '0',
    }
    
    return NextResponse.json(serializedDocument)
  } catch (error) {
    console.error('Error fetching document:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/documents/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    
    // Get document info before deletion
    const document = await prisma.missionDocument.findUnique({
      where: { id }
    })
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }
    
    // Delete file from filesystem
    const filePath = join(process.cwd(), 'public', document.document)
    if (existsSync(filePath)) {
      try {
        await unlink(filePath)
      } catch (fileError) {
        console.warn('Could not delete file from filesystem:', fileError)
        // Continue with database deletion even if file deletion fails
      }
    }
    
    // Delete document record from database
    await prisma.missionDocument.delete({
      where: { id }
    })
    
    return NextResponse.json({ 
      message: 'Document deleted successfully',
      deletedDocument: {
        id: document.id,
        name: document.name,
      }
    })
  } catch (error) {
    console.error('Error deleting document:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/documents/[id] - Update document metadata
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const body = await request.json()
    
    const { name, description, context } = body
    
    // Prepare update data
    const updateData: any = {}
    
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (context !== undefined) updateData.context = context
    
    const document = await prisma.missionDocument.update({
      where: { id },
      data: updateData,
      include: {
        mission: {
          select: {
            id: true,
            code: true,
            type: true,
          }
        }
      }
    })
    
    // Convert BigInt to string for JSON serialization
    const serializedDocument = {
      ...document,
      size: document.size?.toString() || '0',
    }
    
    return NextResponse.json(serializedDocument)
  } catch (error) {
    console.error('Error updating document:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
