# Schema Definitions

This file contains simple schema definitions for all models in the application. These schemas are used for:

- **Table column generation** in GenericTable components
- **Form field generation**
- **Display titles** and labels
- **Type safety** for table data

## Usage Examples

### 1. Basic Usage in Components

```typescript
import { $Plan, $Mission, $User } from '@/lib/schemas';

// Use in GenericTable
<GenericTable<Plan>
  data_={plans}
  isLoading={isLoading}
  error={error}
  data_type={$Plan}
  pagination={{ "set": setPagination, "pagi": pagination }}
/>
```

### 2. Dynamic Schema Access

```typescript
import { getSchema } from '@/lib/schemas';

// Get schema by model name
const planSchema = getSchema('Plan');
const missionSchema = getSchema('Mission');
```

### 3. Available Schemas

| Model | Schema | Description |
|-------|--------|-------------|
| Plan | `$Plan` | Plans d'audit |
| Mission | `$Mission` | Missions d'audit |
| User | `$User` | Utilisateurs |
| Recommendation | `$Recommendation` | Recommandations |
| Comment | `$Comment` | Commentaires |
| Action | `$Action` | Actions |
| Arbitration | `$Arbitration` | Arbitrages |
| Theme | `$Theme` | Thèmes |
| ArbitratedTheme | `$ArbitratedTheme` | Thèmes arbitrés |
| Structure | `$Structure` | Structures |
| Document | `$Document` | Documents |
| MissionDocument | `$MissionDocument` | Documents de mission |
| Account | `$Account` | Comptes utilisateur |
| Session | `$Session` | Sessions |
| Constat | `$Constat` | Constats |
| Risk | `$Risk` | Risques |
| Domain | `$Domain` | Domaines |
| Process | `$Process` | Processus |
| StructureLQS | `$StructureLQS` | Structures LQS |
| StructureLQSInterim | `$StructureLQSInterim` | Structures LQS Intérim |

### 4. Schema Structure

Each schema follows this pattern:

```typescript
export const $ModelName = {
  properties: {
    fieldName: { title: 'Display Title' },
    // ... more fields
  }
};
```

### 5. Adding New Schemas

To add a new schema:

1. **Define the schema** in `/lib/schemas.ts`:
```typescript
export const $NewModel = {
  properties: {
    id: { title: 'ID' },
    name: { title: 'Nom' },
    // ... other fields
  }
};
```

2. **Add to schemas collection**:
```typescript
export const schemas = {
  // ... existing schemas
  NewModel: $NewModel,
};
```

3. **Use in components**:
```typescript
import { $NewModel } from '@/lib/schemas';

<GenericTable data_type={$NewModel} ... />
```

### 6. Field Types and Formatting

The GenericTable components automatically handle different field types:

- **Date fields**: `created`, `modified` → Formatted as French dates
- **Rich text fields**: `description`, `content`, `report` → Rendered with HTML parser
- **Relation fields**: `parent`, `concernedStructure` → Display related object properties
- **Enum fields**: `type`, `etat` → Rendered as colored tags
- **Boolean fields**: `validated`, `isActive` → Rendered as checkboxes/switches

### 7. Migration from OpenAPI Schemas

**Before** (using generated OpenAPI schemas):
```typescript
import { $Plan } from '@/services/openapi_client/schemas.gen';
```

**After** (using centralized schemas):
```typescript
import { $Plan } from '@/lib/schemas';
```

### 8. Benefits

- ✅ **Centralized**: All schemas in one place
- ✅ **Consistent**: Same structure across all models
- ✅ **Maintainable**: Easy to update field titles
- ✅ **Type-safe**: TypeScript support
- ✅ **Flexible**: Easy to extend with new properties
- ✅ **Independent**: No dependency on external API schemas

## Notes

- Field titles are in French to match the application language
- The `id` field is included in all schemas for consistency
- Audit fields (`created`, `modified`, `createdBy`, `modifiedBy`) are included where applicable
- Relation fields use descriptive titles (e.g., "Plan" instead of "planId")
