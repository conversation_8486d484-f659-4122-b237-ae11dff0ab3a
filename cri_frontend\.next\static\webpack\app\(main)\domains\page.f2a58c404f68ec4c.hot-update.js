"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/domains/page",{

/***/ "(app-client)/./app/(main)/domains/(components)/GenericTAble.tsx":
/*!**********************************************************!*\
  !*** ./app/(main)/domains/(components)/GenericTAble.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    _s();\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { mutate: createDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainCreate)();\n    const { mutate: updateDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainUpdate)();\n    const { mutate: deleteDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainDestroy)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [parentDropDown, setParentDropDown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: (0,html_react_parser__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"parent\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"parent.title\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        var _row__valuesCache_parent, _data__data__data_results, _data__data__data;\n                        const onChange = (event)=>{\n                            var _data__data__data_results, _data__data__data;\n                            console.log(\"################AAAAAAAAAAAAAAAAAAAA####################\", event.value);\n                            row._valuesCache.parentId = event.value.code;\n                            row._valuesCache.parent = (_data__data__data = data_.data_.data) === null || _data__data__data === void 0 ? void 0 : (_data__data__data_results = _data__data__data.results) === null || _data__data__data_results === void 0 ? void 0 : _data__data__data_results.find((dom)=>dom.id === event.value.code);\n                            setParentDropDown(event.value);\n                        };\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_5__.Dropdown, {\n                            optionLabel: \"name\",\n                            value: {\n                                code: row._valuesCache.parentId || null,\n                                name: \"\".concat((_row__valuesCache_parent = row._valuesCache.parent) === null || _row__valuesCache_parent === void 0 ? void 0 : _row__valuesCache_parent.title) || null\n                            },\n                            onChange: onChange,\n                            filter: true,\n                            options: ((_data__data__data = data_.data_.data) === null || _data__data__data === void 0 ? void 0 : (_data__data__data_results = _data__data__data.results) === null || _data__data__data_results === void 0 ? void 0 : _data__data__data_results.map(function(val) {\n                                return {\n                                    \"code\": val.id,\n                                    \"name\": val.title\n                                };\n                            })) || []\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 22\n                        }, this);\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title3;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0][\"$ref\"] ? data_.data_type.properties[key].allOf[0][\"$ref\"].enum : [],\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                            severity: cell.getValue() === \"VP\" ? \"danger\" : cell.getValue() === \"STRUCT\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"VP\" ? \"Vice Pr\\xe9sident\" : cell.getValue() === \"STRUCT\" ? \"Structures\" : \"Contr\\xf4le Interne\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                            severity: cell.getValue() === \"NS\" ? \"danger\" : cell.getValue() === \"EC\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"NS\" ? \"Non lanc\\xe9e\" : cell.getValue() === \"SP\" ? \"Suspendue\" : cell.getValue() === \"EC\" ? \"En cours\" : \"Cl\\xf4tur\\xe9e\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title5;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key][\"$ref\"] && data_.data_type.properties[key][\"$ref\"].enum) {\n                console.log(\"#######enum##########\", key, value);\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+\" ,\"; }, \"\"):\"\",\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.row.original[key];\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        children: data_.data_type.properties[key][\"$ref\"].enum,\n                        SelectProps: {\n                        }\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title7, _data__data_type_properties_key_title8;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    console.log(\"############## data from api \", data_);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_7__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_8__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values } = param;\n            var _toast_current;\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            table.setEditingRow(null); //exit editing mode\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Sauvegarde en cours\"\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            delete values.id;\n            createDomain(values);\n            table.setCreatingRow(null); //exit creating mode\n        },\n        onCreatingRowCancel: ()=>{\n        //clear any validation errors\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        rounded: true,\n                        disabled: table.getIsSomeRowsSelected(),\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: handleClick,\n                        icon: \"pi pi-trash\",\n                        // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        icon: \"pi pi-bell\",\n                        rounded: true,\n                        color: rowActionEnabled ? \"secondary\" : \"primary\",\n                        size: \"small\",\n                        \"aria-label\": \"edit\",\n                        onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            zIndex: \"1302 !important\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_11__.Sidebar, {\n                            position: \"right\",\n                            header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"align-content-center \",\n                                        children: [\n                                            \"Cr\\xe9ation \",\n                                            data_.data_type.name,\n                                            \" \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_7__.MRT_EditActionButtons, {\n                                            variant: \"text\",\n                                            table: table,\n                                            row: row\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            visible: createVisible,\n                            onHide: ()=>{\n                                table.setCreatingRow(null);\n                                setCreateVisible(false);\n                            },\n                            className: \"w-full md:w-9 lg:w-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: [\n                                    internalEditComponents,\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 7\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        zIndex: \"1302 !important\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_11__.Sidebar, {\n                        position: \"right\",\n                        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"align-content-center \",\n                                    children: [\n                                        \"Editer \",\n                                        data_.data_type.name,\n                                        \" \",\n                                        row.original.code\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_7__.MRT_EditActionButtons, {\n                                        variant: \"text\",\n                                        table: table,\n                                        row: row\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        visible: editVisible,\n                        onHide: ()=>{\n                            table.setEditingRow(null);\n                            setEditVisible(false);\n                        },\n                        className: \"w-full md:w-9 lg:w-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                gap: \"1.5rem\"\n                            },\n                            children: [\n                                internalEditComponents,\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 79\n                }, this)\n            }, void 0, false);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return row.original.description ? (0,html_react_parser__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(row.original.description) : row.original.content ? (0,html_react_parser__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(row.original.content) : row.original.staff ? row.original.staff.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    display: \"grid\",\n                    margin: \"auto\",\n                    //gridTemplateColumns: '1fr 1fr',\n                    width: \"100vw\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_15__.TabView, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_15__.TabPanel, {\n                            header: data_.data_type.properties[\"staff\"].title,\n                            leftIcon: \"pi pi-user mr-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.staff.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 134\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 64\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_15__.TabPanel, {\n                            header: data_.data_type.properties[\"assistants\"].title,\n                            rightIcon: \"pi pi-user ml-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.assistants.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 139\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 69\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_15__.TabPanel, {\n                            header: \"Lettre\",\n                            leftIcon: \"pi pi-file-word mr-2\",\n                            rightIcon: \"pi pi-file-pdf ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                    icon: \"pi pi-check\",\n                                    rounded: true,\n                                    onClick: ()=>setVisible(true),\n                                    disabled: row.original.document === null\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_11__.Sidebar, {\n                                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: [\n                                            \"Lettre de mission : \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    visible: visible,\n                                    onHide: ()=>setVisible(false),\n                                    className: \"w-full md:w-9 lg:w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                        children: [\n                                            row.original.document !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Document, {\n                                                file: row.original.document,\n                                                onLoadSuccess: onDocumentLoadSuccess,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Page, {\n                                                    pageNumber: pageNumber\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 27\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No Document\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Page \",\n                                                            pageNumber || (numPages ? 1 : \"--\"),\n                                                            \" of \",\n                                                            numPages || \"--\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row align-items-center justify-content-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber <= 1,\n                                                                onClick: previousPage,\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber >= numPages,\n                                                                onClick: nextPage,\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 23\n                                    }, this)\n                                }, row.original.id, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 19\n                        }, this),\n                        \"          \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 655,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 647,\n                columnNumber: 15\n            }, this) : null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n        },\n        renderRowActions: (param)=>// <Box sx={{ display: 'flex', gap: '1rem' }}>\n        /*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_16__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_16__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 706,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    // console.log(data_.isLoading)\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_7__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 734,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_17__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 734,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"9HKR67+FhLwe1E0D4xNcptDwHDs=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainUpdate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainDestroy,\n        material_react_table__WEBPACK_IMPORTED_MODULE_7__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/domains/(components)/GenericTAble.tsx\n"));

/***/ })

});