import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/plans
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const exercise = searchParams.get('exercise')
    const type = searchParams.get('type')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (exercise) {
      where.exercise = parseInt(exercise)
    }
    
    if (type) {
      where.type = type
    }
    
    const [plans, total] = await Promise.all([
      prisma.plan.findMany({
        where,
        skip,
        take: limit,
        include: {
          missions: {
            select: {
              id: true,
              code: true,
              type: true,
              etat: true,
            },
          },
          arbitrations: {
            include: {
              arbitratedThemes: {
                include: {
                  theme: {
                    select: {
                      id: true,
                      title: true,
                      validated: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: { exercise: 'desc' },
      }),
      prisma.plan.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: plans,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching plans:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/plans
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const { exercise, type } = body
    
    const plan = await prisma.plan.create({
      data: {
        exercise,
        type,
      },
      include: {
        missions: true,
        arbitrations: true,
      },
    })
    
    return NextResponse.json(plan, { status: 201 })
  } catch (error) {
    console.error('Error creating plan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
