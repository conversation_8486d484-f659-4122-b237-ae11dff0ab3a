const  locales = {
    "fr": {
      "accept": "Oui",
      "addRule": "Ajouter une règle",
      "am": "am",
      "apply": "Appliquer",
      "cancel": "Annuler",
      "choose": "<PERSON><PERSON>",
      "chooseDate": "<PERSON>sir une date",
      "choose<PERSON>ont<PERSON>": "<PERSON><PERSON> un mois",
      "chooseYear": "Choisir une année",
      "clear": "Effacer",
      "completed": "Terminé",
      "contains": "Contient",
      "custom": "Personnalisé",
      "dateAfter": "Après le",
      "dateBefore": "Avant le",
      "dateFormat": "dd/mm/yy",
      "dateIs": "La date est",
      "dateIsNot": "La date n'est pas",
      "dayNames": [
        "dimanche",
        "lundi",
        "mardi",
        "mercredi",
        "jeudi",
        "vendredi",
        "samedi"
      ],
      "dayNamesMin": [
        "dim",
        "lun",
        "mar",
        "mer",
        "jeu",
        "ven",
        "sam"
      ],
      "dayNamesShort": [
        "dim",
        "lundi",
        "mardi",
        "mercr",
        "jeudi",
        "vendr",
        "sam"
      ],
      "emptyFilterMessage": "Aucun résultat trouvé",
      "emptyMessage": "Aucune option disponible",
      "emptySearchMessage": "Aucun résultat trouvé",
      "emptySelectionMessage": "Aucun élément sélectionné",
      "endsWith": "Se termine par",
      "equals": "Égal à",
      "fileSizeTypes": [
        "o",
        "Ko",
        "Mo",
        "Go",
        "To",
        "Po",
        "Eo",
        "Zo",
        "Yo"
      ],
      "filter": "Filtre",
      "firstDayOfWeek": 1,
      "gt": "Supérieur à",
      "gte": "Supérieur ou égal à",
      "lt": "Inférieur à",
      "lte": "Inférieur ou égal à",
      "matchAll": "Correspond à tous",
      "matchAny": "Au moins un Correspond",
      "medium": "Moyen",
      "monthNames": [
        "janvier",
        "février",
        "mars",
        "avril",
        "mai",
        "juin",
        "juillet",
        "août",
        "septembre",
        "octobre",
        "novembre",
        "décembre"
      ],
      "monthNamesShort": [
        "janv",
        "févr",
        "mars",
        "avr",
        "mai",
        "juin",
        "juill",
        "août",
        "sept",
        "oct",
        "nov",
        "déc"
      ],
      "nextDecade": "Décennie suivante",
      "nextHour": "Heure suivante",
      "nextMinute": "Minute suivante",
      "nextMonth": "Mois suivant",
      "nextSecond": "Seconde suivante",
      "nextYear": "Année suivante",
      "noFilter": "Aucun filtre",
      "notContains": "Ne contient pas",
      "notEquals": "Différent de",
      "now": "Maintenant",
      "passwordPrompt": "Saisissez un mot de passe",
      "pending": "En attente",
      "pm": "pm",
      "prevDecade": "Décennie précédente",
      "prevHour": "Heure précédente",
      "prevMinute": "Minute précédente",
      "prevMonth": "Mois précédent",
      "prevSecond": "Seconde précédente",
      "prevYear": "Année précédente",
      "reject": "Non",
      "removeRule": "Retirer une règle",
      "searchMessage": "{0} résultats disponibles",
      "selectionMessage": "{0} éléments sélectionnés",
      "showMonthAfterYear": false,
      "startsWith": "Commence par",
      "strong": "Fort",
      "today": "Aujourd'hui",
      "upload": "Envoyer",
      "weak": "Faible",
      "weekHeader": "Sem",
      "aria": {
        "cancelEdit": "Annule l'édition",
        "close": "Fermer",
        "collapseLabel": "Effondrement",
        "collapseRow": "Ligne repliée",
        "editRow": "Édite une ligne",
        "expandLabel": "Développer",
        "expandRow": "Ligne dépliée",
        "falseLabel": "Faux",
        "filterConstraint": "Contrainte de filtrage",
        "filterOperator": "Opérateur de filtrage",
        "firstPageLabel": "Première page",
        "gridView": "Vue en grille",
        "hideFilterMenu": "Masque le menu des filtres",
        "jumpToPageDropdownLabel": "Aller à la page",
        "jumpToPageInputLabel": "Aller à la page",
        "lastPageLabel": "Dernière page",
        "listView": "Vue en liste",
        "moveAllToSource": "Tout déplacer vers la source",
        "moveAllToTarget": "Tout déplacer vers la cible",
        "moveBottom": "Déplacer tout en bas",
        "moveDown": "Déplacer vers le bas",
        "moveTop": "Déplacer tout en haut",
        "moveToSource": "Déplacer vers la source",
        "moveToTarget": "Déplacer vers la cible",
        "moveUp": "Déplacer vers le haut",
        "navigation": "Navigation",
        "next": "Suivant",
        "nextPageLabel": "Page suivante",
        "nullLabel": "Aucune sélection",
        "otpLabel": "Veuillez saisir le caractère de mot de passe à usage unique {0}",
        "pageLabel": "Page {page}",
        "passwordHide": "Masquer le mot de passe",
        "passwordShow": "Montrer le mot de passe",
        "previous": "Précédent",
        "previousPageLabel": "Page précédente",
        "removeLabel": "Retirer",
        "rotateLeft": "Tourner vers la gauche",
        "rotateRight": "Tourner vers la droite",
        "rowsPerPageLabel": "Lignes par page",
        "saveEdit": "Sauvegarde l'édition",
        "scrollTop": "Défiler tout en haut",
        "selectAll": "Tous éléments sélectionnés",
        "selectLabel": "Sélectionner",
        "selectRow": "Ligne sélectionnée",
        "showFilterMenu": "Montre le menu des filtres",
        "slide": "Diapositive",
        "slideNumber": "{slideNumber}",
        "star": "1 étoile",
        "stars": "{star} étoiles",
        "trueLabel": "Vrai",
        "unselectAll": "Tous éléments désélectionnés",
        "unselectLabel": "Désélectionner",
        "unselectRow": "Ligne désélectionnée",
        "zoomImage": "Zoomer l'image",
        "zoomIn": "Zoomer",
        "zoomOut": "Dézoomer"
      }
    }
  }

  export default  locales;