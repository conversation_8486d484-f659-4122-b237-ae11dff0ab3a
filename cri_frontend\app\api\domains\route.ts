import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/domains
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const parentId = searchParams.get('parentId')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' as const } },
        { shortTitle: { contains: search, mode: 'insensitive' as const } },
        { observation: { contains: search, mode: 'insensitive' as const } },
      ]
    }
    
    if (parentId) {
      where.parentId = parseInt(parentId)
    }
    
    const [domains, total] = await Promise.all([
      prisma.domain.findMany({
        where,
        skip,
        take: limit,
        include: {
          parent: {
            select: {
              id: true,
              title: true,
              shortTitle: true,
            }
          },
          children: {
            select: {
              id: true,
              title: true,
              shortTitle: true,
            }
          },
          themes: {
            select: {
              id: true,
              title: true,
              validated: true,
            }
          }
        },
        orderBy: { id: 'desc' },
      }),
      prisma.domain.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: domains,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching domains:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/domains
export async function POST(request: NextRequest) {
  try {
    // Check authentication - only staff can create domains
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const body = await request.json()
    
    const {
      title,
      shortTitle,
      parentId,
      type,
      observation,
    } = body
    
    // Validate required fields
    if (!title || !shortTitle) {
      return NextResponse.json(
        { error: 'title and shortTitle are required' },
        { status: 400 }
      )
    }

    // Check if parent exists (if provided)
    if (parentId) {
      const parent = await prisma.domain.findUnique({
        where: { id: parentId }
      })
      
      if (!parent) {
        return NextResponse.json(
          { error: 'Parent domain not found' },
          { status: 404 }
        )
      }
    }

    const domain = await prisma.domain.create({
      data: {
        title,
        shortTitle,
        parentId,
        type,
        observation,
        createdBy: session.user.id.toString(),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        parent: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        children: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
          }
        }
      },
    })

    return NextResponse.json({ data: domain }, { status: 201 })
  } catch (error) {
    console.error('Error creating domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
