import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/causes
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const constatId = searchParams.get('constatId')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.content = {
        contains: search,
        mode: 'insensitive' as const
      }
    }
    
    if (constatId) {
      where.constatId = parseInt(constatId)
    }
    
    const [causes, total] = await Promise.all([
      prisma.cause.findMany({
        where,
        skip,
        take: limit,
        include: {
          constat: {
            include: {
              mission: {
                select: {
                  id: true,
                  code: true,
                  type: true,
                }
              }
            }
          },
          recommendations: {
            select: {
              id: true,
              recommendation: true,
              priority: true,
              status: true,
            }
          },
          createdByUser: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
            }
          },
          modifiedByUser: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
            }
          }
        },
        orderBy: { id: 'desc' },
      }),
      prisma.cause.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: causes,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching causes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/causes
export async function POST(request: NextRequest) {
  try {
    // Check authentication - only staff can create causes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const body = await request.json()
    
    const {
      numcause = 1,
      content,
      constatId,
    } = body
    
    // Validate required fields
    if (!content || !constatId) {
      return NextResponse.json(
        { error: 'content and constatId are required' },
        { status: 400 }
      )
    }

    // Check if constat exists
    const constat = await prisma.constat.findUnique({
      where: { id: constatId }
    })
    
    if (!constat) {
      return NextResponse.json(
        { error: 'Constat not found' },
        { status: 404 }
      )
    }

    const cause = await prisma.cause.create({
      data: {
        numcause,
        content,
        constatId,
        createdBy: session.user.id,
        modifiedBy: session.user.id,
      },
      include: {
        constat: {
          include: {
            mission: {
              select: {
                id: true,
                code: true,
                type: true,
              }
            }
          }
        },
        recommendations: {
          select: {
            id: true,
            recommendation: true,
            priority: true,
            status: true,
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        },
        modifiedByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        }
      },
    })

    return NextResponse.json({ data: cause }, { status: 201 })
  } catch (error) {
    console.error('Error creating cause:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
