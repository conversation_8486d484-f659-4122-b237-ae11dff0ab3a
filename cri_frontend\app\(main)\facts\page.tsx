'use client';

import GenericTable from '@/utilities/components/GenericTAble';
import { useApiFactList } from '@/services/api/api/api';
import { $FACT, FACT } from '@/services/openapi_client';
import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import { getCookie } from 'cookies-next';

const ThemesTable = () => {
  const user = JSON.parse(getCookie('user')?.toString() || '{}')

    const { data: facts, isLoading: isLoading, error: error } = useApiFactList({},{
        axios: { headers: { Authorization: `Token ${user?.token}` } } });

    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex: 0,
        pageSize: 5, //customize the default page size
    });
    if (isLoading) return (<div></div>)
    return (
        <div className="grid">

            <div className="col-12">
                {/* <div className="card"> */}
                {/* <h5>Missions</h5> */}
                <GenericTable<FACT> data_={facts} isLoading={isLoading} error={error} data_type={$FACT} pagination={{ "set": setPagination, "pagi": pagination }}></GenericTable>
                {/* </div> */}
            </div>
        </div>
    );
};

export default ThemesTable;
