import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

// GET /api/missions/[id]/documents - Get all documents for a mission
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const missionId = parseInt(params.id)
    
    // Verify mission exists
    const mission = await prisma.mission.findUnique({
      where: { id: missionId }
    })

    if (!mission) {
      return NextResponse.json(
        { error: 'Mission not found' },
        { status: 404 }
      )
    }
    
    const documents = await prisma.missionDocument.findMany({
      where: { missionId },
      orderBy: { created: 'desc' }
    })
    
    // Convert BigInt to string for JSON serialization
    const serializedDocuments = documents.map(doc => ({
      ...doc,
      size: doc.size?.toString() || '0',
    }))
    
    return NextResponse.json({
      data: {
        results: serializedDocuments,
        count: serializedDocuments.length,
      }
    })
  } catch (error) {
    console.error('Error fetching mission documents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/missions/[id]/documents - Upload documents for a specific mission
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const missionId = parseInt(params.id)
    const formData = await request.formData()
    const context = formData.get('context') as string || 'MISSION'
    const description = formData.get('description') as string || ''
    
    // Verify mission exists
    const mission = await prisma.mission.findUnique({
      where: { id: missionId }
    })

    if (!mission) {
      return NextResponse.json(
        { error: 'Mission not found' },
        { status: 404 }
      )
    }

    const uploadedDocuments = []

    // Process each file
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('document') && value instanceof File) {
        const file = value as File
        
        if (file.size === 0) continue

        // Create upload directory if it doesn't exist
        const uploadDir = join(process.cwd(), 'public', 'uploads', 'missions', missionId.toString())
        if (!existsSync(uploadDir)) {
          await mkdir(uploadDir, { recursive: true })
        }

        // Generate unique filename
        const timestamp = Date.now()
        const fileExtension = file.name.split('.').pop()
        const fileName = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`
        const filePath = join(uploadDir, fileName)
        const relativePath = `/uploads/missions/${missionId}/${fileName}`

        // Save file to disk
        const bytes = await file.arrayBuffer()
        const buffer = Buffer.from(bytes)
        await writeFile(filePath, buffer)

        // Save document record to database
        const document = await prisma.missionDocument.create({
          data: {
            document: relativePath,
            size: BigInt(file.size),
            name: file.name,
            type: file.type,
            missionId: missionId,
            context: context as any, // DocumentType enum
            description,
          }
        })

        uploadedDocuments.push({
          ...document,
          size: document.size.toString(), // Convert BigInt to string for JSON
        })
      }
    }

    if (uploadedDocuments.length === 0) {
      return NextResponse.json(
        { error: 'No valid files were uploaded' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      message: 'Documents uploaded successfully',
      documents: uploadedDocuments,
    }, { status: 201 })

  } catch (error) {
    console.error('Error uploading mission documents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
