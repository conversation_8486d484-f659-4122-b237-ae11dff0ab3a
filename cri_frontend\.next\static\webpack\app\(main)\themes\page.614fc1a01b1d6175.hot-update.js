"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx":
/*!*********************************************************!*\
  !*** ./app/(main)/themes/(components)/GenericTAble.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_chip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/chip */ \"(app-client)/./node_modules/primereact/chip/chip.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const [theme_id, setThemeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: arbitrations, isLoading, error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList)();\n    const { data: themes, isLoading: isLoading_themes, error: error_themes } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList)();\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate)();\n    const { data: data_update, error: error_update, isPending: isMutating_update, mutate: trigger_update } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate)();\n    const getSeverity = (str)=>{\n        switch(str){\n            case \"Vice Pr\\xe9sident\":\n                return \"success\";\n            case \"Contr\\xf4le Interne\":\n                return \"warning\";\n            case \"Audit Interne\":\n                return \"warning\";\n            case \"Structures\":\n                return \"danger\";\n            default:\n                return null;\n        }\n    };\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                header: \"ID\",\n                accessorKey: \"id\",\n                size: 70,\n                Edit: ()=>null\n            },\n            {\n                header: \"Aribtrage\",\n                accessorKey: \"arbitration\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"arbitration\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-11rem text-sm\",\n                        severity: row.original.arbitration.plan.code.includes(\"Audit\") ? \"danger\" : \"info\",\n                        value: row.original.arbitration.plan.code\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_arbitration, _row__valuesCache_arbitration1, _arbitrations;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _arbitrations, _arbitrations1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                arbitration: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data.results.find((arbi)=>arbi.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                arbitration: (_arbitrations1 = arbitrations) === null || _arbitrations1 === void 0 ? void 0 : _arbitrations1.data.results.find((arbi)=>arbi.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un\",\n                        value: {\n                            name: ((_row__valuesCache_arbitration = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration === void 0 ? void 0 : _row__valuesCache_arbitration.id) || null,\n                            code: ((_row__valuesCache_arbitration1 = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration1 === void 0 ? void 0 : _row__valuesCache_arbitration1.id) || null\n                        },\n                        options: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data.results.map((arbi)=>{\n                            return {\n                                code: arbi.id,\n                                name: arbi.id\n                            };\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Propos\\xe9 par\",\n                accessorKey: \"theme\",\n                id: \"theme_proposed_by\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                Edit: ()=>null,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-9rem text-sm\",\n                        severity: getSeverity(cell.getValue().proposedBy),\n                        value: cell.getValue().proposedBy\n                    }, row.original.code + row.original.created, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 36\n                    }, this);\n                }\n            },\n            {\n                header: \"Structures proposantes\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"proposing_structures\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    console.log(cell.getValue().proposing_structures);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: cell.getValue().proposing_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                style: {\n                                    backgroundColor: \"green\",\n                                    color: \"white\"\n                                },\n                                label: val.code_mnemonique\n                            }, \"thm\".concat(row.original.theme.id, \"_ps\").concat(idx), false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 20\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Structures concern\\xe9es\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"concerned_structures\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [\n                            \" \",\n                            cell.getValue().concerned_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                    style: {\n                                        backgroundColor: \"green\",\n                                        color: \"white\"\n                                    },\n                                    label: val.code_mnemonique\n                                }, \"thm\".concat(row.original.theme.id, \"_cs\").concat(idx), false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 137\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Domaine\",\n                accessorKey: \"domain\",\n                id: \"domain\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.domain.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Processus\",\n                accessorKey: \"process\",\n                id: \"process\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.process.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Intitul\\xe9\",\n                accessorKey: \"theme\",\n                id: \"title\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.theme.title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_theme, _row__valuesCache_theme1, _themes;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _themes, _themes1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                theme: (_themes = themes) === null || _themes === void 0 ? void 0 : _themes.data.results.find((thm)=>thm.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                theme: (_themes1 = themes) === null || _themes1 === void 0 ? void 0 : _themes1.data.results.find((thm)=>thm.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un\",\n                        value: {\n                            name: ((_row__valuesCache_theme = row._valuesCache.theme) === null || _row__valuesCache_theme === void 0 ? void 0 : _row__valuesCache_theme.title) || null,\n                            code: ((_row__valuesCache_theme1 = row._valuesCache.theme) === null || _row__valuesCache_theme1 === void 0 ? void 0 : _row__valuesCache_theme1.id) || null\n                        },\n                        options: (_themes = themes) === null || _themes === void 0 ? void 0 : _themes.data.results.map((thm)=>{\n                            return {\n                                code: thm.id,\n                                name: thm.title\n                            };\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Remarque\",\n                accessorKey: \"note\",\n                id: \"note\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.note\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>/*#__PURE__*/ {\n                    let { row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                        id: \"note\",\n                        initialValue: row.original.note,\n                        tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                        apiKey: \"none\",\n                        init: {\n                            height: 500,\n                            menubar: true,\n                            plugins: [\n                                \"advlist\",\n                                \"autolink\",\n                                \"lists\",\n                                \"link\",\n                                \"image\",\n                                \"charmap\",\n                                \"print\",\n                                \"preview\",\n                                \"anchor\",\n                                \"searchreplace\",\n                                \"visualblocks\",\n                                \"code\",\n                                \"fullscreen\",\n                                \"insertdatetime\",\n                                \"media\",\n                                \"table\",\n                                \"paste\",\n                                \"code\",\n                                \"help\",\n                                \"wordcount\"\n                            ],\n                            toolbar: \"undo redo | formatselect | bold italic backcolor |                                       alignleft aligncenter alignright alignjustify |                                       bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                        },\n                        onChange: (e)=>{\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                note: e.target.getContent()\n                            };\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 30\n                    }, this);\n                }\n            }\n        ], [\n        arbitrations,\n        themes\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableEditing: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            setThemeId(row.original.id);\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let update_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_update({\n                id: rest.id,\n                data: update_values\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"Th\\xe8me modifi\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.message);\n                    row._valuesCache = {\n                        error: err.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, row, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let insert_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_create(insert_values, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.message);\n                    row._valuesCache = {\n                        error: err.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: (param)=>{\n            let { table } = param;\n            //clear any validation errors\n            table.setCreatingRow(null);\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                //about:\"edit modal\",\n                // open: editVisible || createVisible,\n                maxWidth: \"md\"\n            };\n        // sx: {\n        //   //  '& .MuiDialog-root': {\n        //   //    width :'70vw'\n        //   //  },\n        //   // '& .MuiDialog-container': {\n        //   //   width :'70vw'\n        //   // },\n        //   zIndex: 1100,\n        // }\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_4__.Can, {\n                    I: \"add\",\n                    a: \"ArbitratedTheme\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            })\n    });\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n        lineNumber: 601,\n        columnNumber: 26\n    }, this);\n    console.log(\"-----------------------------------------\", arbitrations);\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_10__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 605,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_13__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 605,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"bDGdo1+71fnDMhQHJ0dTBFzwcyk=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx\n"));

/***/ })

});