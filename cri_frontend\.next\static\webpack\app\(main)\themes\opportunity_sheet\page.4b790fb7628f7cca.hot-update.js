"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/opportunity_sheet/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/editForm.tsx":
/*!*****************************************************!*\
  !*** ./app/(main)/themes/(components)/editForm.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_inputtext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/inputtext */ \"(app-client)/./node_modules/primereact/inputtext/inputtext.esm.js\");\n/* harmony import */ var primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/inputtextarea */ \"(app-client)/./node_modules/primereact/inputtextarea/inputtextarea.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var _mui_material_Stepper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Stepper */ \"(app-client)/./node_modules/@mui/material/Stepper/Stepper.js\");\n/* harmony import */ var _mui_material_Step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Step */ \"(app-client)/./node_modules/@mui/material/Step/Step.js\");\n/* harmony import */ var _mui_material_StepLabel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/StepLabel */ \"(app-client)/./node_modules/@mui/material/StepLabel/StepLabel.js\");\n/* harmony import */ var primereact_calendar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! primereact/calendar */ \"(app-client)/./node_modules/primereact/calendar/calendar.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* harmony import */ var primereact_togglebutton__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/togglebutton */ \"(app-client)/./node_modules/primereact/togglebutton/togglebutton.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var primereact_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/card */ \"(app-client)/./node_modules/primereact/card/card.esm.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var _lib_enums__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/enums */ \"(app-client)/./lib/enums.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// import { useApiDomainList, useApiGoalList, useApiMissionCreate, useApiMissionDestroy, useApiMissionList, useApiPlanList, useApiProcessList, useApiRiskList, useApiStructurelqsList, useApiThemeList, useApiUsersList } from '@/services/api/api/api';\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ThemeEditForm = (props)=>{\n    var _getCookie, _structures_lqs_data, _structures_lqs, _risks_data, _risks, _goals_data, _goals, _structures_lqs_data1, _structures_lqs1, _structures_lqs_error, _props_row_original, _props_row__valuesCache_error_data, _props_row__valuesCache_error, _props_row__valuesCache_error_data1, _props_row__valuesCache_error1, _props_row__valuesCache_error_data2, _props_row__valuesCache_error2, _props_row__valuesCache_error_data3, _props_row__valuesCache_error3, _props_row__valuesCache_error_data4, _props_row__valuesCache_error4, _props_row__valuesCache_error_data5, _props_row__valuesCache_error5, _props_row__valuesCache_error_data6, _props_row__valuesCache_error6, _props_row__valuesCache_error_data7, _props_row__valuesCache_error7, _props_row__valuesCache_error_data8, _props_row__valuesCache_error8, _props_row__valuesCache_error_data9, _props_row__valuesCache_error9, _props_row__valuesCache_error_data10, _props_row__valuesCache_error10, _props_row__valuesCache_error_data11, _props_row__valuesCache_error11, _props_row__valuesCache_error_data12, _props_row__valuesCache_error12, _props_row__valuesCache_error_data13, _props_row__valuesCache_error13, _props_row__valuesCache_error_data14, _props_row__valuesCache_error14, _domains_data_results, _domains_data, _domains, _props_row__valuesCache_error_data15, _props_row__valuesCache_error15, _props_row__valuesCache_error_data16, _props_row__valuesCache_error16, _props_row__valuesCache_error_data17, _props_row__valuesCache_error17, _processes_data_results, _processes_data, _processes, _props_row__valuesCache_error_data18, _props_row__valuesCache_error18, _props_row__valuesCache_error_data19, _props_row__valuesCache_error19, _props_row__valuesCache_error_data20, _props_row__valuesCache_error20, _props_row__valuesCache_error_data21, _props_row__valuesCache_error21, _props_row__valuesCache_error_data22, _props_row__valuesCache_error22, _props_row__valuesCache_error_data23, _props_row__valuesCache_error23, _props_row__valuesCache_error_data24, _props_row__valuesCache_error24, _props_row__valuesCache_error_data25, _props_row__valuesCache_error25, _props_row__valuesCache_error_data26, _props_row__valuesCache_error26, _props_row__valuesCache_error_data27, _props_row__valuesCache_error27, _props_row__valuesCache_error_data28, _props_row__valuesCache_error28, _props_row__valuesCache_error_data29, _props_row__valuesCache_error29, _props_row__valuesCache_error_data30, _props_row__valuesCache_error30, _props_row__valuesCache_error_data31, _props_row__valuesCache_error31, _props_row__valuesCache_error_data32, _props_row__valuesCache_error32, _props_row__valuesCache_error_data33, _props_row__valuesCache_error33, _props_row__valuesCache_error_data34, _props_row__valuesCache_error34;\n    _s();\n    ///////////////////////////////////////////////////////////////////////////////\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_2__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    // Fetch data using specific hooks\n    const { data: users, isLoading, error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiUserList)({\n        limit: 100\n    });\n    const { data: plans, isLoading: plan_isLoading, error: plan_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiPlanList)({\n        limit: 100\n    });\n    const { data: themes, isLoading: themes_isLoading, error: themes_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiThemeList)({\n        limit: 100\n    });\n    const { data: risks, isLoading: risks_isLoading, error: risks_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiRiskList)({\n        limit: 100\n    });\n    const { data: goals, isLoading: goals_isLoading, error: goals_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiGoalList)({\n        limit: 100\n    });\n    const { data: domains, isLoading: domains_isLoading, error: domains_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainList)({\n        limit: 100\n    });\n    const { data: processes, isLoading: processes_isLoading, error: processes_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiProcessList)({\n        limit: 100\n    });\n    const { data: structures_lqs, isLoading: structures_lqs_isLoading, error: structures_lqs_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiStructurelqsList)({\n        limit: 100\n    });\n    ///////////////////////////Stepper functions///////////////////////////////////\n    const [activeStep, setActiveStep] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const [skipped, setSkipped] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    const isStepOptional = (step)=>{\n        return step === 1;\n    };\n    const isStepSkipped = (step)=>{\n        return skipped.has(step);\n    };\n    const handleNext = ()=>{\n        let newSkipped = skipped;\n        if (isStepSkipped(activeStep)) {\n            newSkipped = new Set(newSkipped.values());\n            newSkipped.delete(activeStep);\n        }\n        setActiveStep((prevActiveStep)=>prevActiveStep + 1);\n        setSkipped(newSkipped);\n    };\n    const handleBack = ()=>{\n        setActiveStep((prevActiveStep)=>prevActiveStep - 1);\n    };\n    const handleSkip = ()=>{\n        if (!isStepOptional(activeStep)) {\n            // You probably want to guard against something like this,\n            // it should never occur unless someone's actively trying to break something.\n            throw new Error(\"You can't skip a step that isn't optional.\");\n        }\n        setActiveStep((prevActiveStep)=>prevActiveStep + 1);\n        setSkipped((prevSkipped)=>{\n            const newSkipped = new Set(prevSkipped.values());\n            newSkipped.add(activeStep);\n            return newSkipped;\n        });\n    };\n    const handleReset = ()=>{\n        setActiveStep(0);\n    };\n    ///////////////////////////Stepper functions///////////////////////////////////\n    ///////////////////////////////////////////////////////////////////////////////\n    const steps = [\n        \"Th\\xe8me\",\n        \"Risques\",\n        \"Objectifs\"\n    ]; //'Structures Proposantes', 'Structures conernées',\n    ///////////////////////////////////////////////////////////////////////////////\n    const [theme_data, setThemeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"domainId\": props.row.id === \"mrt-row-create\" ? null : props.row.original.domain.id,\n        \"processId\": props.row.id === \"mrt-row-create\" ? null : props.row.original.process.id,\n        \"risks\": props.row.id === \"mrt-row-create\" ? [] : props.row.original.risks.map((risk)=>risk.id),\n        \"goals\": props.row.id === \"mrt-row-create\" ? [] : props.row.original.goals.map((goal)=>goal.id),\n        \"proposingStructures\": props.row.id === \"mrt-row-create\" ? [] : props.row.original.proposingStructures.map((struct)=>struct.id),\n        \"concernedStructures\": props.row.id === \"mrt-row-create\" ? [] : props.row.original.concernedStructures.map((struct)=>struct.id),\n        \"validated\": props.row.id === \"mrt-row-create\" ? false : props.row.original.validated,\n        \"code\": props.row.id === \"mrt-row-create\" ? \"\" : props.row.original.code,\n        \"title\": props.row.id === \"mrt-row-create\" ? \"\" : props.row.original.title,\n        \"proposedBy\": props.row.id === \"mrt-row-create\" ? null : props.row.original.proposedVy,\n        \"monthStart\": props.row.id === \"mrt-row-create\" ? null : props.row.original.monthStart,\n        \"monthEnd\": props.row.id === \"mrt-row-create\" ? null : props.row.original.monthEnd,\n        \"id\": props.row.id === \"mrt-row-create\" ? null : props.row.original.id\n    });\n    const handleTheme = (field, event)=>{\n        const theme_new = {\n            ...theme_data,\n            ...{\n                [field]: event\n            }\n        };\n        props.row._valuesCache = theme_new;\n        console.log(theme_new);\n        setThemeData(theme_new);\n    };\n    // const { data: users,            isLoading, error                                                  } = useApiUsersList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }});\n    // const { data: plans,            isLoading: plan_isLoading, error: plan_error                          } = useApiPlanList()\n    // const { data: risks,            isLoading: risks_isLoading, error: risks_error                   } = useApiRiskList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    // const { data: themes,           isLoading: themes_isLoading, error: themes_error                  } = useApiThemeList()\n    // const { data: goals,            isLoading: goals_isLoading, error: goals_error                       } = useApiGoalList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    // const { data: domains,          isLoading: domains_isLoading, error: domains_error               } = useApiDomainList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    // const { data: processes,        isLoading: processes_isLoading, error: processes_error               } = useApiProcessList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    // const { data: structures_lqs,   isLoading: structures_lqs_isLoading, error: structures_lqs_error } = useApiStructurelqsList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    const [editDialogVisible, setEditDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [picklistSourceValueProposingStructures, setPicklistSourceValueProposingStructures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_structures_lqs = structures_lqs) === null || _structures_lqs === void 0 ? void 0 : (_structures_lqs_data = _structures_lqs.data) === null || _structures_lqs_data === void 0 ? void 0 : _structures_lqs_data.results);\n    const [picklistTargetValueProposingStructures, setPicklistTargetValueProposingStructures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? [] : props.row.original.proposingStructures);\n    const [picklistSourceValueRisks, setPicklistSourceValueRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_risks = risks) === null || _risks === void 0 ? void 0 : (_risks_data = _risks.data) === null || _risks_data === void 0 ? void 0 : _risks_data.results);\n    const [picklistTargetValueRisks, setPicklistTargetValueRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? [] : props.row.original.risks);\n    const [picklistSourceValueGoals, setPicklistSourceValueGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_goals = goals) === null || _goals === void 0 ? void 0 : (_goals_data = _goals.data) === null || _goals_data === void 0 ? void 0 : _goals_data.results);\n    const [picklistTargetValueGoals, setPicklistTargetValueGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? [] : props.row.original.goals);\n    const [picklistSourceValueConcernedStructures, setPicklistSourceValueConcernedStructures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_structures_lqs1 = structures_lqs) === null || _structures_lqs1 === void 0 ? void 0 : (_structures_lqs_data1 = _structures_lqs1.data) === null || _structures_lqs_data1 === void 0 ? void 0 : _structures_lqs_data1.results);\n    const [picklistTargetValueConcernedStructures, setPicklistTargetValueConcernedStructures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? [] : props.row.original.proposingStructures);\n    const [dropdownItemDomain, setDropdownItemDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : {\n        \"name\": props.row.original.domain.title,\n        \"code\": props.row.original.domain.id\n    });\n    const [dropdownItemProcess, setDropdownItemProcess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : {\n        \"name\": props.row.original.process.title,\n        \"code\": props.row.original.process.id\n    });\n    const [dropdownItemProposedBy, setDropdownItemProposedBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : {\n        \"name\": props.row.original.proposed_by,\n        \"code\": props.row.original.proposed_by\n    });\n    const [theme_validated, setThemeValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? false : props.row.original.validated);\n    const [theme_code, setThemeCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : props.row.original.code);\n    const [theme_title, setThemeTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? \"\" : props.row.original.title);\n    const [theme_end_date, setThemeEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? new Date() : new Date(props.row.original.month_end));\n    const [theme_start_date, setThemeStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? new Date() : new Date(props.row.original.month_start));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _structures_lqs_data, _structures_lqs, _structures_lqs_data_results, _structures_lqs_data1, _structures_lqs1, _structures_lqs_data2, _structures_lqs2, _structures_lqs_data_results1, _structures_lqs_data3, _structures_lqs3, _risks_data, _risks, _risks_data_results, _risks_data1, _risks1, _goals_data, _goals, _goals_data_results, _goals_data1, _goals1;\n        setPicklistSourceValueConcernedStructures(props.row.id === \"mrt-row-create\" ? (_structures_lqs = structures_lqs) === null || _structures_lqs === void 0 ? void 0 : (_structures_lqs_data = _structures_lqs.data) === null || _structures_lqs_data === void 0 ? void 0 : _structures_lqs_data.results : (_structures_lqs1 = structures_lqs) === null || _structures_lqs1 === void 0 ? void 0 : (_structures_lqs_data1 = _structures_lqs1.data) === null || _structures_lqs_data1 === void 0 ? void 0 : (_structures_lqs_data_results = _structures_lqs_data1.results) === null || _structures_lqs_data_results === void 0 ? void 0 : _structures_lqs_data_results.filter((struct)=>!props.row.original.concernedStructures.map((struct_)=>struct_.id).includes(struct.id)));\n        setPicklistTargetValueConcernedStructures(props.row.id === \"mrt-row-create\" ? [] : props.row.original.concernedStructures);\n        setPicklistSourceValueProposingStructures(props.row.id === \"mrt-row-create\" ? (_structures_lqs2 = structures_lqs) === null || _structures_lqs2 === void 0 ? void 0 : (_structures_lqs_data2 = _structures_lqs2.data) === null || _structures_lqs_data2 === void 0 ? void 0 : _structures_lqs_data2.results : (_structures_lqs3 = structures_lqs) === null || _structures_lqs3 === void 0 ? void 0 : (_structures_lqs_data3 = _structures_lqs3.data) === null || _structures_lqs_data3 === void 0 ? void 0 : (_structures_lqs_data_results1 = _structures_lqs_data3.results) === null || _structures_lqs_data_results1 === void 0 ? void 0 : _structures_lqs_data_results1.filter((struct)=>!props.row.original.proposingStructures.map((struct_)=>struct_.id).includes(struct.id)));\n        setPicklistTargetValueProposingStructures(props.row.id === \"mrt-row-create\" ? [] : props.row.original.proposingStructures);\n        setPicklistSourceValueRisks(props.row.id === \"mrt-row-create\" ? (_risks = risks) === null || _risks === void 0 ? void 0 : (_risks_data = _risks.data) === null || _risks_data === void 0 ? void 0 : _risks_data.results : (_risks1 = risks) === null || _risks1 === void 0 ? void 0 : (_risks_data1 = _risks1.data) === null || _risks_data1 === void 0 ? void 0 : (_risks_data_results = _risks_data1.results) === null || _risks_data_results === void 0 ? void 0 : _risks_data_results.filter((risk)=>!props.row.original.risks.map((risk_)=>risk_.id).includes(risk.id)));\n        setPicklistTargetValueRisks(props.row.id === \"mrt-row-create\" ? [] : props.row.original.risks);\n        setPicklistSourceValueGoals(props.row.id === \"mrt-row-create\" ? (_goals = goals) === null || _goals === void 0 ? void 0 : (_goals_data = _goals.data) === null || _goals_data === void 0 ? void 0 : _goals_data.results : (_goals1 = goals) === null || _goals1 === void 0 ? void 0 : (_goals_data1 = _goals1.data) === null || _goals_data1 === void 0 ? void 0 : (_goals_data_results = _goals_data1.results) === null || _goals_data_results === void 0 ? void 0 : _goals_data_results.filter((goal)=>!props.row.original.goals.map((goal_)=>goal_.id).includes(goal.id)));\n        setPicklistTargetValueGoals(props.row.id === \"mrt-row-create\" ? [] : props.row.original.goals);\n    }, [\n        structures_lqs\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // setDropdownItemDomain(props.row.id === 'mrt-row-create' ? null : { \"name\": props.row.original.domain.title, \"code\": props.row.original.domain.id })\n        props.row._valuesCache = {\n            ...theme_data\n        };\n    }, []);\n    if (plan_isLoading && structures_lqs_isLoading && domains_isLoading && processes_isLoading && risks_isLoading && goals_isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_5__.ProgressSpinner, {\n        style: {\n            width: \"50px\",\n            height: \"50px\"\n        },\n        strokeWidth: \"8\",\n        fill: \"var(--surface-ground)\",\n        animationDuration: \".5s\"\n    }, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n        lineNumber: 181,\n        columnNumber: 143\n    }, undefined);\n    if (structures_lqs_error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: (_structures_lqs_error = structures_lqs_error) === null || _structures_lqs_error === void 0 ? void 0 : _structures_lqs_error.message\n    }, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n        lineNumber: 182,\n        columnNumber: 39\n    }, undefined);\n    console.log(\"STRUCTLQS\", structures_lqs);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                zIndex: \"1302 !important\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_6__.Sidebar, {\n                position: \"right\",\n                header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"align-content-center \",\n                            children: [\n                                props.row.id === \"mrt-row-create\" ? \"Nouveau th\\xe8me\" : \"Editer th\\xe9me :\",\n                                \" \",\n                                (_props_row_original = props.row.original) === null || _props_row_original === void 0 ? void 0 : _props_row_original.code\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        ((_props_row__valuesCache_error = props.row._valuesCache.error) === null || _props_row__valuesCache_error === void 0 ? void 0 : (_props_row__valuesCache_error_data = _props_row__valuesCache_error.data) === null || _props_row__valuesCache_error_data === void 0 ? void 0 : _props_row__valuesCache_error_data[\"non_field_errors\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                            className: \"p-error\",\n                            children: (_props_row__valuesCache_error1 = props.row._valuesCache.error) === null || _props_row__valuesCache_error1 === void 0 ? void 0 : (_props_row__valuesCache_error_data1 = _props_row__valuesCache_error1.data) === null || _props_row__valuesCache_error_data1 === void 0 ? void 0 : _props_row__valuesCache_error_data1[\"non_field_errors\"][0]\n                        }, void 0, false, void 0, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_8__.MRT_EditActionButtons, {\n                                variant: \"text\",\n                                table: props.table,\n                                row: props.row\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    ]\n                }, void 0, true, void 0, void 0),\n                visible: editDialogVisible,\n                onHide: ()=>{\n                //  props.table.setEditingRow(null); setEditDialogVisible(false)\n                },\n                className: \"w-full md:w-9 lg:w-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: \"0.7rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stepper__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                activeStep: activeStep,\n                                sx: {\n                                    paddingY: \"0.7rem\"\n                                },\n                                children: steps.map((label, index)=>{\n                                    const stepProps = {};\n                                    const labelProps = {};\n                                    if (isStepOptional(index)) {\n                                        labelProps.optional = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            variant: \"caption\",\n                                            children: \"Optional\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 41\n                                        }, undefined);\n                                    }\n                                    if (isStepSkipped(index)) {\n                                        stepProps.completed = false;\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Step__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        ...stepProps,\n                                        sx: {\n                                            \"& .MuiStepLabel-root .Mui-completed\": {\n                                                color: \"secondary.dark\"\n                                            },\n                                            \"& .MuiStepLabel-label.Mui-completed.MuiStepLabel-alternativeLabel\": {\n                                                color: \"white\"\n                                            },\n                                            \"& .MuiStepLabel-root .Mui-active\": {\n                                                color: \"var(--primary-color)\"\n                                            },\n                                            \"& .MuiStepLabel-label.Mui-active.MuiStepLabel-alternativeLabel\": {\n                                                color: \"white\"\n                                            },\n                                            \"& .MuiStepLabel-root .Mui-active .MuiStepIcon-text\": {\n                                                fill: \"white\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_StepLabel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            ...labelProps,\n                                            children: label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, label, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 37\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 25\n                            }, undefined),\n                            activeStep === steps.length ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        sx: {\n                                            mt: 2,\n                                            mb: 1\n                                        },\n                                        children: \"All steps completed - you're finished\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            flexDirection: \"row\",\n                                            pt: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                sx: {\n                                                    flex: \"1 1 auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                onClick: handleReset,\n                                                children: \"Reset\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 29\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                children: [\n                                    activeStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_card__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-fluid formgrid grid\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"title\",\n                                                            children: \"Th\\xe9matique\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_17__.InputTextarea, {\n                                                            className: ((_props_row__valuesCache_error2 = props.row._valuesCache.error) === null || _props_row__valuesCache_error2 === void 0 ? void 0 : (_props_row__valuesCache_error_data2 = _props_row__valuesCache_error2.data) === null || _props_row__valuesCache_error_data2 === void 0 ? void 0 : _props_row__valuesCache_error_data2[\"title\"]) ? \"p-invalid\" : \"\",\n                                                            id: \"title\",\n                                                            defaultValue: theme_title,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"title\", e.target.value);\n                                                                setThemeTitle(e.target.value);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error3 = props.row._valuesCache.error) === null || _props_row__valuesCache_error3 === void 0 ? void 0 : (_props_row__valuesCache_error_data3 = _props_row__valuesCache_error3.data) === null || _props_row__valuesCache_error_data3 === void 0 ? void 0 : _props_row__valuesCache_error_data3[\"title\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error4 = props.row._valuesCache.error) === null || _props_row__valuesCache_error4 === void 0 ? void 0 : (_props_row__valuesCache_error_data4 = _props_row__valuesCache_error4.data) === null || _props_row__valuesCache_error_data4 === void 0 ? void 0 : _props_row__valuesCache_error_data4[\"title\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 99\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"code\",\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_18__.InputText, {\n                                                            className: ((_props_row__valuesCache_error5 = props.row._valuesCache.error) === null || _props_row__valuesCache_error5 === void 0 ? void 0 : (_props_row__valuesCache_error_data5 = _props_row__valuesCache_error5.data) === null || _props_row__valuesCache_error_data5 === void 0 ? void 0 : _props_row__valuesCache_error_data5[\"code\"]) ? \"p-invalid\" : \"\",\n                                                            id: \"code\",\n                                                            type: \"text\",\n                                                            defaultValue: theme_code,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"code\", e.target.value);\n                                                                setThemeCode(e.target.value);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error6 = props.row._valuesCache.error) === null || _props_row__valuesCache_error6 === void 0 ? void 0 : (_props_row__valuesCache_error_data6 = _props_row__valuesCache_error6.data) === null || _props_row__valuesCache_error_data6 === void 0 ? void 0 : _props_row__valuesCache_error_data6[\"code\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error7 = props.row._valuesCache.error) === null || _props_row__valuesCache_error7 === void 0 ? void 0 : (_props_row__valuesCache_error_data7 = _props_row__valuesCache_error7.data) === null || _props_row__valuesCache_error_data7 === void 0 ? void 0 : _props_row__valuesCache_error_data7[\"code\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 98\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"proposed_by\",\n                                                            children: \"Propos\\xe9 par\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_19__.Dropdown, {\n                                                            className: ((_props_row__valuesCache_error8 = props.row._valuesCache.error) === null || _props_row__valuesCache_error8 === void 0 ? void 0 : (_props_row__valuesCache_error_data8 = _props_row__valuesCache_error8.data) === null || _props_row__valuesCache_error_data8 === void 0 ? void 0 : _props_row__valuesCache_error_data8[\"proposed_by\"]) ? \"p-invalid\" : \"\",\n                                                            filter: true,\n                                                            id: \"proposed_by\",\n                                                            value: dropdownItemProposedBy,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"proposed_by\", e.value.name);\n                                                                setDropdownItemProposedBy(e.value);\n                                                            },\n                                                            options: _lib_enums__WEBPACK_IMPORTED_MODULE_4__.$ProposedByEnum.enum.map(function(val) {\n                                                                return {\n                                                                    \"name\": val,\n                                                                    \"code\": val\n                                                                };\n                                                            }),\n                                                            optionLabel: \"name\",\n                                                            placeholder: \"Choisir un\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error9 = props.row._valuesCache.error) === null || _props_row__valuesCache_error9 === void 0 ? void 0 : (_props_row__valuesCache_error_data9 = _props_row__valuesCache_error9.data) === null || _props_row__valuesCache_error_data9 === void 0 ? void 0 : _props_row__valuesCache_error_data9[\"proposedBy\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error10 = props.row._valuesCache.error) === null || _props_row__valuesCache_error10 === void 0 ? void 0 : (_props_row__valuesCache_error_data10 = _props_row__valuesCache_error10.data) === null || _props_row__valuesCache_error_data10 === void 0 ? void 0 : _props_row__valuesCache_error_data10[\"proposedBy\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 104\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"validated\",\n                                                            children: \"Valid\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_togglebutton__WEBPACK_IMPORTED_MODULE_20__.ToggleButton, {\n                                                            className: ((_props_row__valuesCache_error11 = props.row._valuesCache.error) === null || _props_row__valuesCache_error11 === void 0 ? void 0 : (_props_row__valuesCache_error_data11 = _props_row__valuesCache_error11.data) === null || _props_row__valuesCache_error_data11 === void 0 ? void 0 : _props_row__valuesCache_error_data11[\"validated\"]) ? \"p-invalid\" : \"\",\n                                                            onLabel: \"Oui\",\n                                                            offLabel: \"Non\",\n                                                            color: \"green\",\n                                                            id: \"validated\",\n                                                            checked: theme_validated,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"validated\", e.value);\n                                                                setThemeValidated(e.value);\n                                                            },\n                                                            placeholder: \"Choisir un\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error12 = props.row._valuesCache.error) === null || _props_row__valuesCache_error12 === void 0 ? void 0 : (_props_row__valuesCache_error_data12 = _props_row__valuesCache_error12.data) === null || _props_row__valuesCache_error_data12 === void 0 ? void 0 : _props_row__valuesCache_error_data12[\"validated\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error13 = props.row._valuesCache.error) === null || _props_row__valuesCache_error13 === void 0 ? void 0 : (_props_row__valuesCache_error_data13 = _props_row__valuesCache_error13.data) === null || _props_row__valuesCache_error_data13 === void 0 ? void 0 : _props_row__valuesCache_error_data13[\"validated\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 103\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"domain\",\n                                                            children: \"Domaine\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_19__.Dropdown, {\n                                                            className: ((_props_row__valuesCache_error14 = props.row._valuesCache.error) === null || _props_row__valuesCache_error14 === void 0 ? void 0 : (_props_row__valuesCache_error_data14 = _props_row__valuesCache_error14.data) === null || _props_row__valuesCache_error_data14 === void 0 ? void 0 : _props_row__valuesCache_error_data14[\"domain\"]) ? \"p-invalid\" : \"\",\n                                                            filter: true,\n                                                            id: \"domain\",\n                                                            value: dropdownItemDomain,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"domain\", e.value.code);\n                                                                setDropdownItemDomain(e.value);\n                                                            },\n                                                            options: (_domains = domains) === null || _domains === void 0 ? void 0 : (_domains_data = _domains.data) === null || _domains_data === void 0 ? void 0 : (_domains_data_results = _domains_data.results) === null || _domains_data_results === void 0 ? void 0 : _domains_data_results.map(function(val) {\n                                                                return {\n                                                                    \"name\": val.title,\n                                                                    \"code\": val.id\n                                                                };\n                                                            }),\n                                                            optionLabel: \"name\",\n                                                            placeholder: \"Choisir un\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error15 = props.row._valuesCache.error) === null || _props_row__valuesCache_error15 === void 0 ? void 0 : (_props_row__valuesCache_error_data15 = _props_row__valuesCache_error15.data) === null || _props_row__valuesCache_error_data15 === void 0 ? void 0 : _props_row__valuesCache_error_data15[\"domain\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error16 = props.row._valuesCache.error) === null || _props_row__valuesCache_error16 === void 0 ? void 0 : (_props_row__valuesCache_error_data16 = _props_row__valuesCache_error16.data) === null || _props_row__valuesCache_error_data16 === void 0 ? void 0 : _props_row__valuesCache_error_data16[\"domain\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 100\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"process\",\n                                                            children: \"Processus\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_19__.Dropdown, {\n                                                            className: ((_props_row__valuesCache_error17 = props.row._valuesCache.error) === null || _props_row__valuesCache_error17 === void 0 ? void 0 : (_props_row__valuesCache_error_data17 = _props_row__valuesCache_error17.data) === null || _props_row__valuesCache_error_data17 === void 0 ? void 0 : _props_row__valuesCache_error_data17[\"process\"]) ? \"p-invalid\" : \"\",\n                                                            filter: true,\n                                                            id: \"process\",\n                                                            value: dropdownItemProcess,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"process\", e.value.code);\n                                                                setDropdownItemProcess(e.value);\n                                                            },\n                                                            options: (_processes = processes) === null || _processes === void 0 ? void 0 : (_processes_data = _processes.data) === null || _processes_data === void 0 ? void 0 : (_processes_data_results = _processes_data.results) === null || _processes_data_results === void 0 ? void 0 : _processes_data_results.map(function(val) {\n                                                                return {\n                                                                    \"name\": val.title,\n                                                                    \"code\": val.id\n                                                                };\n                                                            }),\n                                                            optionLabel: \"name\",\n                                                            placeholder: \"Choisir un\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error18 = props.row._valuesCache.error) === null || _props_row__valuesCache_error18 === void 0 ? void 0 : (_props_row__valuesCache_error_data18 = _props_row__valuesCache_error18.data) === null || _props_row__valuesCache_error_data18 === void 0 ? void 0 : _props_row__valuesCache_error_data18[\"process\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error19 = props.row._valuesCache.error) === null || _props_row__valuesCache_error19 === void 0 ? void 0 : (_props_row__valuesCache_error_data19 = _props_row__valuesCache_error19.data) === null || _props_row__valuesCache_error_data19 === void 0 ? void 0 : _props_row__valuesCache_error_data19[\"process\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 101\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"start_date\",\n                                                            children: \"Date D\\xe9but\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_calendar__WEBPACK_IMPORTED_MODULE_21__.Calendar, {\n                                                            className: ((_props_row__valuesCache_error20 = props.row._valuesCache.error) === null || _props_row__valuesCache_error20 === void 0 ? void 0 : (_props_row__valuesCache_error_data20 = _props_row__valuesCache_error20.data) === null || _props_row__valuesCache_error_data20 === void 0 ? void 0 : _props_row__valuesCache_error_data20[\"month_start\"]) ? \"p-invalid\" : \"\",\n                                                            id: \"month_start\",\n                                                            value: new Date(theme_start_date),\n                                                            onChange: (e)=>{\n                                                                var _e_value;\n                                                                handleTheme(\"month_start\", (_e_value = e.value) === null || _e_value === void 0 ? void 0 : _e_value.toISOString().split(\"T\")[0]);\n                                                                setThemeStartDate(e.value);\n                                                            },\n                                                            locale: \"fr\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error21 = props.row._valuesCache.error) === null || _props_row__valuesCache_error21 === void 0 ? void 0 : (_props_row__valuesCache_error_data21 = _props_row__valuesCache_error21.data) === null || _props_row__valuesCache_error_data21 === void 0 ? void 0 : _props_row__valuesCache_error_data21[\"month_start\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error22 = props.row._valuesCache.error) === null || _props_row__valuesCache_error22 === void 0 ? void 0 : (_props_row__valuesCache_error_data22 = _props_row__valuesCache_error22.data) === null || _props_row__valuesCache_error_data22 === void 0 ? void 0 : _props_row__valuesCache_error_data22[\"month_start\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 105\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"end_date\",\n                                                            children: \"Date Fin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_calendar__WEBPACK_IMPORTED_MODULE_21__.Calendar, {\n                                                            className: ((_props_row__valuesCache_error23 = props.row._valuesCache.error) === null || _props_row__valuesCache_error23 === void 0 ? void 0 : (_props_row__valuesCache_error_data23 = _props_row__valuesCache_error23.data) === null || _props_row__valuesCache_error_data23 === void 0 ? void 0 : _props_row__valuesCache_error_data23[\"month_end\"]) ? \"p-invalid\" : \"\",\n                                                            id: \"month_end\",\n                                                            value: new Date(theme_end_date),\n                                                            onChange: (e)=>{\n                                                                var _e_value;\n                                                                handleTheme(\"month_end\", (_e_value = e.value) === null || _e_value === void 0 ? void 0 : _e_value.toISOString().split(\"T\")[0]);\n                                                                setThemeEndDate(e.value);\n                                                            },\n                                                            locale: \"fr\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error24 = props.row._valuesCache.error) === null || _props_row__valuesCache_error24 === void 0 ? void 0 : (_props_row__valuesCache_error_data24 = _props_row__valuesCache_error24.data) === null || _props_row__valuesCache_error_data24 === void 0 ? void 0 : _props_row__valuesCache_error_data24[\"month_end\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error25 = props.row._valuesCache.error) === null || _props_row__valuesCache_error25 === void 0 ? void 0 : (_props_row__valuesCache_error_data25 = _props_row__valuesCache_error25.data) === null || _props_row__valuesCache_error_data25 === void 0 ? void 0 : _props_row__valuesCache_error_data25[\"month_end\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 103\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"picklist_concerned_structrures\",\n                                                            children: \"Structures Concern\\xe9es\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"card\",\n                                                            style: {\n                                                                borderColor: ((_props_row__valuesCache_error26 = props.row._valuesCache.error) === null || _props_row__valuesCache_error26 === void 0 ? void 0 : (_props_row__valuesCache_error_data26 = _props_row__valuesCache_error26.data) === null || _props_row__valuesCache_error_data26 === void 0 ? void 0 : _props_row__valuesCache_error_data26[\"concerned_structrures\"]) ? \"#e24c4c\" : \"\"\n                                                            },\n                                                            children: [\n                                                                ((_props_row__valuesCache_error27 = props.row._valuesCache.error) === null || _props_row__valuesCache_error27 === void 0 ? void 0 : (_props_row__valuesCache_error_data27 = _props_row__valuesCache_error27.data) === null || _props_row__valuesCache_error_data27 === void 0 ? void 0 : _props_row__valuesCache_error_data27[\"concerned_structrures\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    className: \"p-error\",\n                                                                    children: (_props_row__valuesCache_error28 = props.row._valuesCache.error) === null || _props_row__valuesCache_error28 === void 0 ? void 0 : (_props_row__valuesCache_error_data28 = _props_row__valuesCache_error28.data) === null || _props_row__valuesCache_error_data28 === void 0 ? void 0 : _props_row__valuesCache_error_data28[\"concerned_structrures\"][0]\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 119\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_22__.PickList, {\n                                                                    id: \"picklist_concerned_structrures\",\n                                                                    source: picklistSourceValueConcernedStructures,\n                                                                    target: picklistTargetValueConcernedStructures,\n                                                                    sourceHeader: \"De\",\n                                                                    targetHeader: \"A\",\n                                                                    itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm\",\n                                                                            children: [\n                                                                                item.libellStru,\n                                                                                \" | \",\n                                                                                item.codeMnemonique\n                                                                            ]\n                                                                        }, void 0, true, void 0, void 0),\n                                                                    onChange: (e)=>{\n                                                                        setPicklistSourceValueConcernedStructures(e.source);\n                                                                        setPicklistTargetValueConcernedStructures(e.target);\n                                                                        handleTheme(\"concernedStructures\", e.target.map((struct)=>struct.id));\n                                                                    },\n                                                                    sourceStyle: {\n                                                                        height: \"200px\"\n                                                                    },\n                                                                    targetStyle: {\n                                                                        height: \"200px\"\n                                                                    },\n                                                                    filter: true,\n                                                                    filterBy: \"libellStru,codeMnemonique\",\n                                                                    filterMatchMode: \"contains\",\n                                                                    sourceFilterPlaceholder: \"Recherche\",\n                                                                    targetFilterPlaceholder: \"Recherche\",\n                                                                    className: ((_props_row__valuesCache_error29 = props.row._valuesCache.error) === null || _props_row__valuesCache_error29 === void 0 ? void 0 : (_props_row__valuesCache_error_data29 = _props_row__valuesCache_error29.data) === null || _props_row__valuesCache_error_data29 === void 0 ? void 0 : _props_row__valuesCache_error_data29[\"concerned_structrures\"]) ? \"p-invalid\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 53\n                                                                }, undefined),\n                                                                ((_props_row__valuesCache_error30 = props.row._valuesCache.error) === null || _props_row__valuesCache_error30 === void 0 ? void 0 : (_props_row__valuesCache_error_data30 = _props_row__valuesCache_error30.data) === null || _props_row__valuesCache_error_data30 === void 0 ? void 0 : _props_row__valuesCache_error_data30[\"concerned_structrures\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    className: \"p-error\",\n                                                                    children: (_props_row__valuesCache_error31 = props.row._valuesCache.error) === null || _props_row__valuesCache_error31 === void 0 ? void 0 : (_props_row__valuesCache_error_data31 = _props_row__valuesCache_error31.data) === null || _props_row__valuesCache_error_data31 === void 0 ? void 0 : _props_row__valuesCache_error_data31[\"concerned_structrures\"][0]\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 119\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-6 text-center \",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"picklist_proposingStructures\",\n                                                            children: \"Structures Proposantes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"card\",\n                                                            style: {\n                                                                borderColor: ((_props_row__valuesCache_error32 = props.row._valuesCache.error) === null || _props_row__valuesCache_error32 === void 0 ? void 0 : (_props_row__valuesCache_error_data32 = _props_row__valuesCache_error32.data) === null || _props_row__valuesCache_error_data32 === void 0 ? void 0 : _props_row__valuesCache_error_data32[\"proposingStructures\"]) ? \"#e24c4c\" : \"\"\n                                                            },\n                                                            children: [\n                                                                ((_props_row__valuesCache_error33 = props.row._valuesCache.error) === null || _props_row__valuesCache_error33 === void 0 ? void 0 : (_props_row__valuesCache_error_data33 = _props_row__valuesCache_error33.data) === null || _props_row__valuesCache_error_data33 === void 0 ? void 0 : _props_row__valuesCache_error_data33[\"proposingStructures\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    className: \"p-error w-full text-sm \",\n                                                                    children: (_props_row__valuesCache_error34 = props.row._valuesCache.error) === null || _props_row__valuesCache_error34 === void 0 ? void 0 : (_props_row__valuesCache_error_data34 = _props_row__valuesCache_error34.data) === null || _props_row__valuesCache_error_data34 === void 0 ? void 0 : _props_row__valuesCache_error_data34[\"proposingStructures\"][0]\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 117\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_22__.PickList, {\n                                                                    id: \"picklist_proposingStructures\",\n                                                                    source: picklistSourceValueProposingStructures,\n                                                                    target: picklistTargetValueProposingStructures,\n                                                                    sourceHeader: \"De\",\n                                                                    targetHeader: \"A\",\n                                                                    itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm\",\n                                                                            children: [\n                                                                                item.codeMnemonique,\n                                                                                \" | \",\n                                                                                item.libellStru\n                                                                            ]\n                                                                        }, void 0, true, void 0, void 0),\n                                                                    onChange: (e)=>{\n                                                                        setPicklistSourceValueProposingStructures(e.source);\n                                                                        setPicklistTargetValueProposingStructures(e.target);\n                                                                        handleTheme(\"proposingStructures\", e.target.map((struct)=>struct.id));\n                                                                    },\n                                                                    sourceStyle: {\n                                                                        height: \"200px\"\n                                                                    },\n                                                                    targetStyle: {\n                                                                        height: \"200px\"\n                                                                    },\n                                                                    filter: true,\n                                                                    filterBy: \"libellStru,codeMnemonique\",\n                                                                    filterMatchMode: \"contains\",\n                                                                    sourceFilterPlaceholder: \"Recherche\",\n                                                                    targetFilterPlaceholder: \"Recherche\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 53\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    activeStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"field col-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"picklist_risks\",\n                                                    children: \"Risques\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_22__.PickList, {\n                                                        id: \"picklist_risks\",\n                                                        source: picklistSourceValueRisks,\n                                                        target: picklistTargetValueRisks,\n                                                        sourceHeader: \"Disponibles\",\n                                                        targetHeader: \"S\\xe9l\\xe9ctionn\\xe9s\",\n                                                        itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: item.description\n                                                            }, void 0, false, void 0, void 0),\n                                                        onChange: (e)=>{\n                                                            setPicklistSourceValueRisks(e.source);\n                                                            setPicklistTargetValueRisks(e.target);\n                                                            handleTheme(\"risks\", e.target.map((risk)=>risk.id));\n                                                        },\n                                                        sourceStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        targetStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        filter: true,\n                                                        filterBy: \"content\",\n                                                        filterMatchMode: \"contains\",\n                                                        sourceFilterPlaceholder: \"Recherche\",\n                                                        targetFilterPlaceholder: \"Recherche\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    activeStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"field col-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"picklist_goals\",\n                                                    children: \"Objectifs\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_22__.PickList, {\n                                                        id: \"picklist_goals\",\n                                                        source: picklistSourceValueGoals,\n                                                        target: picklistTargetValueGoals,\n                                                        sourceHeader: \"Disponibles\",\n                                                        targetHeader: \"S\\xe9l\\xe9ctionn\\xe9s\",\n                                                        itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: item.description\n                                                            }, void 0, false, void 0, void 0),\n                                                        onChange: (e)=>{\n                                                            setPicklistSourceValueGoals(e.source);\n                                                            setPicklistTargetValueGoals(e.target);\n                                                            handleTheme(\"goals\", e.target.map((goal)=>goal.id));\n                                                        },\n                                                        sourceStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        targetStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        filter: true,\n                                                        filterBy: \"content\",\n                                                        filterMatchMode: \"contains\",\n                                                        sourceFilterPlaceholder: \"Recherche\",\n                                                        targetFilterPlaceholder: \"Recherche\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            flexDirection: \"row\",\n                                            pt: 2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                color: \"inherit\",\n                                                disabled: activeStep === 0,\n                                                onClick: handleBack,\n                                                sx: {\n                                                    mr: 1\n                                                },\n                                                children: \"Back\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                sx: {\n                                                    flex: \"1 1 auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            isStepOptional(activeStep) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                color: \"inherit\",\n                                                onClick: handleSkip,\n                                                children: \"Skip\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                onClick: handleNext,\n                                                children: activeStep === steps.length - 1 ? \"Finish\" : \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                lineNumber: 187,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 186,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s(ThemeEditForm, \"yyOlDDE9uXUXubp/2JlX1gPyBvo=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiThemeList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiRiskList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiGoalList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiProcessList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiStructurelqsList\n    ];\n});\n_c = ThemeEditForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ThemeEditForm);\nvar _c;\n$RefreshReg$(_c, \"ThemeEditForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/editForm.tsx\n"));

/***/ })

});