import { PrismaClient } from '@prisma/client'
import seedEntities from './seed-entities'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding entities only...')
  
  try {
    await seedEntities()
    console.log('✅ Entity seeding completed successfully!')
  } catch (error) {
    console.error('❌ Error seeding entities:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Error:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
