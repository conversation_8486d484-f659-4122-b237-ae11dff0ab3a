from django.db import models
from django.db.models import FilteredRelation, Q
from django.db.models.signals import m2m_changed, post_save, pre_save
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from month.models import MonthField

# from tinymce.models import HTMLField
from field_history.tracker import FieldHistoryTracker
from .enums import *
from .fields import *

# from river.models.fields.state import StateField
from django.utils.safestring import mark_safe
from django.contrib.contenttypes.fields import GenericRelation

# from django_extensions.db.models import   TimeStampedModel
from django_extensions.db.fields import (
    AutoSlugField,
    CreationDateTimeField,
    ModificationDateTimeField,
)
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

import html2text
from django.core.validators import FileExtensionValidator

from django.db.models import F, Q
from django.db.models.functions import Now, TruncDate
from django.core.validators import MaxV<PERSON>ueValidator, MinValueValidator
from django.dispatch import receiver
from django_currentuser.middleware import (
    get_current_user,
    get_current_authenticated_user,
)
from django.contrib.auth import get_user_model


h = html2text.HTML2Text()
h.ignore_links = True


class CustomQuerySetManager(models.Manager):
    """A re-usable Manager to access a custom QuerySet"""

    def __getattr__(self, attr, *args):
        try:
            return getattr(self.__class__, attr, *args)
        except AttributeError:
            # don't delegate internal methods to the queryset
            if attr.startswith("__") and attr.endswith("__"):
                raise
            return getattr(self.get_query_set(), attr, *args)

    def get_query_set(self):
        return self.model.QuerySet(self.model, using=self._db)


class TimeStampedModel(models.Model):
    """
    TimeStampedModel

    An abstract base class model that provides self-managed "created" and
    "modified" fields.
    """

    created = CreationDateTimeField(_("created"))
    modified = ModificationDateTimeField(_("modified"))
    created_by = models.ForeignKey(
        get_user_model(),
        on_delete=models.DO_NOTHING,
        related_name="%(app_label)s_%(class)s_created_by",
        blank=True,
        editable=False,
        verbose_name="créé par",
    )
    modified_by = models.ForeignKey(
        get_user_model(),
        on_delete=models.DO_NOTHING,
        related_name="%(app_label)s_%(class)s_modified_by",
        blank=True,
        null=True,
        editable=False,
        verbose_name="mis à jour par",
    )

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None
    ):
        if self.modified is None and self.created is None:
            # print("self.modified > self.created")
            self.created_by = get_current_authenticated_user() or User.objects.get(id=1)
            self.modified_by = None
        if self.modified is not None and self.created is not None:
            # print("self.modified == self.created")
            self.modified_by = get_current_authenticated_user() or User.objects.get(
                id=1
            )
        super().save(
            force_insert=force_insert,
            force_update=force_update,
            using=using,
            update_fields=update_fields,
        )

    class Meta:
        get_latest_by = "modified"
        abstract = True


class CriAgents(models.Model):
    ID_STRUCT = models.BigIntegerField(blank=True, null=True)
    matric_agnt = models.CharField(max_length=18, primary_key=True)
    nom_agnt = models.CharField(max_length=120, blank=True, null=True)
    prenom_agnt = models.CharField(max_length=203, blank=True, null=True)
    intitu_fonc = models.CharField(max_length=159, blank=True, null=True)
    libell_stru = models.CharField(max_length=150, blank=True, null=True)
    code_unit = models.CharField(max_length=9, blank=True, null=True)
    code_stru = models.CharField(max_length=27, blank=True, null=True)
    code_ser = models.CharField(max_length=60, blank=True, null=True)
    code_mnemonique = models.CharField(max_length=90, blank=True, null=True)

    def __str__(self) -> str:
        return f"{self.code_mnemonique},{self.ID_STRUCT}"

    class Meta:
        managed = True
        # db_table = '"CRI_DEV"."CRI_AGENTS"'


class UserProfile(TimeStampedModel):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="user_profile"
    )
    agent = models.OneToOneField(
        CriAgents,
        on_delete=models.CASCADE,
        related_name="user_profile_agnt",
        blank=True,
        null=True,
    )

    # serial = models.CharField(max_length=25, verbose_name="matricule")
    # department = models.CharField(max_length=100)
    # unit = models.CharField(max_length=100,verbose_name="Unité/Complexe")
    # title = models.CharField(max_length=100)
    def __str__(self) -> str:
        return self.user.get_full_name()

    class Meta:
        verbose_name = "Profile utilisateur"
        verbose_name_plural = "Profiles utilisateurs"


class CriStructview(models.Model):
    id = models.BigIntegerField(blank=True, primary_key=True)
    code_stru = models.CharField(max_length=27)
    libell_stru = models.CharField(max_length=150, blank=True, null=True)
    code_unit = models.CharField(max_length=18, blank=True, null=True)
    code_mnemonique = models.CharField(max_length=90, blank=True, null=True)

    def __str__(self) -> str:
        return f"{self.libell_stru},{self.code_unit}"

    class Meta:
        managed = True
        # db_table = '"CRI_DEV"."CRI_STRUCTVIEW"'


# TODO on post save add the user to the concerned group permission
#  and create a shceduled task to remove on end date
class StructureLQSInterim(TimeStampedModel):
    structure = models.ForeignKey(
        "CriStructview",
        on_delete=models.DO_NOTHING,
        related_name="struct_interim",
        verbose_name="structure",
    )
    interim = models.ForeignKey(
        User,
        on_delete=models.DO_NOTHING,
        related_name="struct_interim_users",
        verbose_name="intérim",
    )
    start = models.DateTimeField(verbose_name="début")
    end = models.DateTimeField(verbose_name="fin")

    def __str__(self) -> str:
        return "%s / %s-%s / %s" % (self.structure, self.start, self.end, self.interim)

    class Meta:
        verbose_name = "Intérim Structure LQS"
        verbose_name_plural = "Intérims Structures LQS"
        constraints = [
            # models.CheckConstraint(
            #     check=Q(start__lte=TruncDate(F("end")), start__gte=TruncDate(Now())),
            #     name="correct_datetime",
            #     violation_error_message="Date début doit être inférieure date fin",
            # ),
        ]


class StructureLQSCorrespondents(TimeStampedModel):
    correspondents = models.ManyToManyField(User)
    structure = models.ForeignKey(
        "CriStructview",
        on_delete=models.DO_NOTHING,
        related_name="struct_correspondents",
        verbose_name="structure",
    )

    # suppliant = models.ForeignKey(User,on_delete=models.DO_NOTHING)
    def __str__(self) -> str:
        return self.structure.code_mnemonique

    class Meta:
        verbose_name = "Structure LQS correspondents"
        verbose_name_plural = "Structures LQS correspondents"


# def correspondents_changed(sender, **kwargs):
#     if kwargs["instance"].correspondents.count() > 2:
#         raise ValidationError("You can't assign more than 2 correspondents")

# m2m_changed.connect(correspondents_changed, sender=StructureLQS.correspondents.through)


class Theme(TimeStampedModel):
    # This is valid for opportunity sheet ,after validation , it'll become a theme
    validated = models.BooleanField(default=False, verbose_name="Validé")
    code = models.CharField(max_length=30, verbose_name="code", blank=True, null=True)
    domain = models.ForeignKey(
        "Domain",
        on_delete=models.DO_NOTHING,
        verbose_name="Domaine",
        blank=True,
        null=True,
    )
    process = models.ForeignKey(
        "Process",
        on_delete=models.DO_NOTHING,
        verbose_name="Processus",
        blank=True,
        null=True,
    )
    title = models.CharField(max_length=200, verbose_name="Intitulé")
    proposed_by = models.CharField(
        choices=THEME_PROPOSING_ENTITY, max_length=100, verbose_name="Proposé par"
    )
    proposing_structures = models.ManyToManyField(
        CriStructview,
        related_name="structures_proposing_theme",
        blank=True,
        verbose_name="Structures proposantes",
    )
    concerned_structures = models.ManyToManyField(
        CriStructview,
        related_name="structures_concerned_theme",
        verbose_name="Structures concernées",
    )
    # priority = models.IntegerField(default=1,verbose_name="Priorité")

    month_start = MonthField("Période Début", help_text="Mois début")
    month_end = MonthField("Période Fin", help_text="Mois fin")
    risks = models.ManyToManyField(
        "Risk", related_name="risks_theme", verbose_name="Risques"
    )
    goals = models.ManyToManyField(
        "Goal", related_name="goals_theme", verbose_name="Objéctifs"
    )
    field_history = FieldHistoryTracker(
        ["validated", "domain", "title", "month_start", "month_end"]
    )

    def __str__(self) -> str:
        if self.proposed_by == "Structures":
            return "%s | %s" % (
                self.title,
                ", ".join(
                    list(
                        self.proposing_structures.all().values_list(
                            "code_mnemonique", flat=True
                        )
                    )
                ),
            )
        else:
            return "%s | %s" % (self.title, self.proposed_by)

    class Meta:
        verbose_name = _("Thème")
        verbose_name_plural = _("Vivier de Thèmes")
        permissions = [
            ("change_theme_status", "Can change the status of themes"),
            ("close_theme", "Can remove a theme by setting its status as closed"),
        ]


class Domain(TimeStampedModel):  # and sub-domain
    title = models.CharField(max_length=100, verbose_name=_("Intitulé"))
    short_title = models.CharField(max_length=50, verbose_name=_("Abbréviation"))
    parent = models.ForeignKey(
        "self",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
        verbose_name=_("Domaine parent"),
        help_text="",
    )
    type = models.CharField(max_length=300, null=True, blank=True)
    observation = Varchar2(max_length=4000, null=True, blank=True)

    def __str__(self) -> str:
        return self.title

    class Meta:
        verbose_name = _("Domaine")
        verbose_name_plural = _("Domaines")
        unique_together = [["title", "parent"]]


class Process(TimeStampedModel):  # and sub-process
    title = models.CharField(max_length=100, verbose_name=_("Intitulé"))
    short_title = models.CharField(max_length=10, verbose_name=_("Abbréviation"))
    parent = models.ForeignKey(
        "self",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
        verbose_name=_("Processus parent"),
        help_text="",
    )

    def __str__(self) -> str:
        return self.title

    class Meta:
        verbose_name = "Processus"
        verbose_name_plural = "Processus"


class Arbitration(TimeStampedModel):
    plan = models.ForeignKey("Plan", on_delete=models.DO_NOTHING)
    report = Varchar2(blank=True, null=True, verbose_name="Rapport", max_length=4000)
    team = models.ManyToManyField(User, blank=True)

    def __str__(self) -> str:
        return "ARBITRAGE|%s" % (self.plan)

    class Meta:
        verbose_name = "Arbitrage"
        verbose_name_plural = "Arbitrages"


class ArbitratedTheme(TimeStampedModel):
    arbitration = models.ForeignKey(
        Arbitration, on_delete=models.DO_NOTHING, verbose_name="Arbitrage"
    )
    theme = models.ForeignKey(
        Theme,
        on_delete=models.DO_NOTHING,
        related_name="arbitrated_themes",
        verbose_name="thème",
    )
    note = Varchar2(blank=True, null=True, verbose_name="remarque", max_length=4000)

    # flag retenu - non-retenu
    def __str__(self) -> str:
        return "%s|%s" % (self.arbitration, self.theme)

    class Meta:
        verbose_name = "Thème arbitré"
        verbose_name_plural = "Thèmes arbitrés"
        unique_together = [["arbitration", "theme"]]


class Plan(TimeStampedModel):
    exercise = models.IntegerField(
        choices=year_choices, default=current_year, verbose_name="Année"
    )
    type = models.CharField(choices=PlanType, max_length=16, verbose_name="Type")

    # themes = models.ManyToManyField(Theme,blank=True,verbose_name="Thèmes")
    # period_estimated = models.CharField(max_length=20,verbose_name="Période estimée")
    # controlled_structures = models.ManyToManyField(StructureLQS,verbose_name="Structures contrôlées",blank=True)
    @property
    def code(self):
        return "%s|%s" % (self.exercise, self.type)

    def __str__(self) -> str:
        return "%s|%s" % (self.exercise, self.type)

    class Meta:
        verbose_name = "PLan"
        verbose_name_plural = "Plans"


class MissionDocument(TimeStampedModel):
    document = models.FileField(
        upload_to="missions/",
        verbose_name="Lettre",
        validators=[
            FileExtensionValidator(
                allowed_extensions=["pdf", "docx", "doc", "xls", "xlsx", "image", "csv"]
            )
        ],
    )
    size = models.BigIntegerField(default=0, blank=True, null=True)
    name = models.CharField(max_length=500, blank=True, null=True)
    type = models.CharField(max_length=200, blank=True, null=True)
    mission = models.ForeignKey(
        "Mission", on_delete=models.CASCADE, related_name="mission_docs"
    )    
    context = models.CharField(max_length=100,choices=DocumentTypeEnum)
    description = models.TextField(verbose_name="Description",blank=True,null=True)
    objects = CustomQuerySetManager()
    class QuerySet(models.query.QuerySet):
        def active_for_user(self, user, *args, **kwargs):
            # Récupération des noms des groupes de l'utilisateur
            roles = user.groups.values_list("name", flat=True)
            if "CRI_MANAGERS" in roles or "ADMINS" in roles or user.username == "admin":
                return self.all()

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            return self.none()

class Mission(TimeStampedModel):
    plan = models.ForeignKey(
        "Plan",
        on_delete=models.DO_NOTHING,
        verbose_name="plan",
        related_name="plan_missions",
        blank=True,
        null=True,
    )
    exercise = models.CharField(
        verbose_name="Exercice", blank=True, null=True, max_length=4
    )
    type = models.CharField(
        choices=MissionType, max_length=15, verbose_name="Type"
    )  # ,default=MissionType.PLANIFIED)
    code = models.CharField(
        max_length=100, verbose_name="code", unique=True, blank=True
    )
    etat = models.CharField(
        choices=MissionEtat, max_length=10, verbose_name="état"
    )  # ,default=MissionEtat.NS)
    start_date = models.DateField(verbose_name="date début")
    end_date = models.DateField(verbose_name="date fin")
    theme = models.ForeignKey(
        ArbitratedTheme,
        on_delete=models.RESTRICT,
        verbose_name="thème",
        related_name="mission",
        null=True,
        blank=True,
    )
    head = models.ForeignKey(
        User,
        on_delete=models.DO_NOTHING,
        verbose_name="Chef de mission",
        related_name="mission_head",
        blank=True,
        null=True,
    )
    supervisor = models.ForeignKey(
        User,
        on_delete=models.DO_NOTHING,
        verbose_name="Supervisuer",
        related_name="mission_supervisor",
        blank=True,
        null=True,
    )
    staff = models.ManyToManyField(
        User,
        blank=True,
        verbose_name="Auditeurs/Controleurs",
        related_name="mission_auditors_controllers",
    )
    assistants = models.ManyToManyField(
        User, verbose_name="Assistants", blank=True, related_name="mission_assistants"
    )
    objects = CustomQuerySetManager()
    class QuerySet(models.query.QuerySet):
        def active_for_user(self, user, *args, **kwargs):
            # Récupération des noms des groupes de l'utilisateur
            roles = user.groups.values_list("name", flat=True)
            if "CRI_MANAGERS" in roles or "ADMINS" in roles or user.username == "admin":
                return self.all()

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            return self.none()
    # phases : Prep Realisation Conclusion
    def __str__(self) -> str:
        return self.code



    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["plan", "code", "theme"], name="uniq_miss_thm_pln"
            )
        ]

        verbose_name = "Mission"
        verbose_name_plural = "Missions"


class Action(TimeStampedModel):
    description = Varchar2(verbose_name="Description", max_length=4000)
    job_leader = models.ForeignKey(User, on_delete=models.RESTRICT)
    start_date = models.DateTimeField(verbose_name="Début")
    end_date = models.DateTimeField(verbose_name="Fin")
    validated = models.BooleanField(default=False, verbose_name="Validée")  # ASR
    status = models.CharField(
        choices=ActionEtat, max_length=50, verbose_name="Statut"
    )  # Concerned
    progress = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="Progrès",
    )
    accepted = models.BooleanField(
        default=False, verbose_name="Acceptée"
    )  # Concerned pour saisie du plan d'action recommendations
    proof = models.FileField(upload_to="media/action", null=True, blank=True)
    recommendation = models.ForeignKey(
        "Recommendation", on_delete=models.CASCADE, verbose_name="recommandation"
    )

    def __str__(self) -> str:
        return (
            h.handle("%s ... " % self.description)
            .replace("*", "")
            .replace("\\-", "")
            .replace("#", "")[0:60]
        )
    objects = CustomQuerySetManager()
    class QuerySet(models.query.QuerySet):
        def active_for_user(self, user, *args, **kwargs):
            # Récupération des noms des groupes de l'utilisateur
            roles = user.groups.values_list("name", flat=True)
            if "CRI_MANAGERS" in roles or "ADMINS" in roles or user.username == "admin":
                return self.all()

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            return self.none()
    class Meta:
        verbose_name = "Action"
        verbose_name_plural = "Actions"


class Notification(TimeStampedModel):
    user = models.ForeignKey(
        User, on_delete=models.DO_NOTHING, verbose_name="utilisateur"
    )
    read = models.BooleanField(default=False)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey("content_type", "object_id")
    objects = CustomQuerySetManager()
    class QuerySet(models.query.QuerySet):
        def active_for_user(self, user, *args, **kwargs):
            # Récupération des noms des groupes de l'utilisateur
            roles = user.groups.values_list("name", flat=True)
            if "CRI_MANAGERS" in roles or "ADMINS" in roles or user.username == "admin":
                return self.all()

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            return self.none()
    class Meta:
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
        ]


class Comment(TimeStampedModel):

    recommendation = models.ForeignKey(
        "Recommendation",
        on_delete=models.DO_NOTHING,
        related_name="recommendation_discussions",
        null=True,
    )
    action = models.ForeignKey(
        "Action",
        on_delete=models.DO_NOTHING,
        related_name="action_discussions",
        null=True,
    )
    comment = Varchar2(verbose_name="commentaire", max_length=4000)
    type = models.CharField(choices=RecommendationActionTypeEnum,max_length=50,null=True,default=None)
    value = models.CharField(choices=ActionStateEnum,max_length=50,blank=True)
    validMeraci = models.CharField(
        choices=ValidationEnum,
        max_length=20,
        verbose_name="validation Meraci",
        default=ValidationEnum.NO,
    )
    validDirecteur = models.CharField(
        choices=ValidationEnum,
        max_length=20,
        verbose_name="validation Directeur",
        default=ValidationEnum.NO,
    )
    objects = CustomQuerySetManager()
    class QuerySet(models.query.QuerySet):
        def active_for_user(self, user, *args, **kwargs):
            # Récupération des noms des groupes de l'utilisateur
            roles = user.groups.values_list("name", flat=True)
            if "CRI_MANAGERS" in roles or "ADMINS" in roles or user.username == "admin":
                return self.all()

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            return self.none()

    def __str__(self) -> str:
        return (
            h.handle("%s ... " % self.comment)
            .replace("*", "")
            .replace("\\-", "")
            .replace("#", "")[0:60]
        )

    class Meta:
        verbose_name = "Commentaire"
        verbose_name_plural = "Commentaires"

class CommentDocument(TimeStampedModel):
    
    comment = models.ForeignKey(
        "Comment",
        on_delete=models.DO_NOTHING,
        related_name="comment_files",
    )

    document = models.FileField(
        upload_to="missions/comments/",
        verbose_name="Lettre",
        validators=[
            FileExtensionValidator(
                allowed_extensions=["pdf", "docx", "doc", "xls", "xlsx", "image", "csv"]
            )
        ],
    )
    size = models.BigIntegerField(default=0, blank=True, null=True)
    name = models.CharField(max_length=500, blank=True, null=True)
    type = models.CharField(max_length=200, blank=True, null=True)
    description = models.TextField(verbose_name="Description",blank=True,null=True)

    def __str__(self) -> str:
        return (
            h.handle("%s ... " % self.comment)
            .replace("*", "")
            .replace("\\-", "")
            .replace("#", "")[0:60]
        )
    objects = CustomQuerySetManager()
    class QuerySet(models.query.QuerySet):
        def active_for_user(self, user, *args, **kwargs):
            # Récupération des noms des groupes de l'utilisateur
            roles = user.groups.values_list("name", flat=True)
            if "CRI_MANAGERS" in roles or "ADMINS" in roles or user.username == "admin":
                return self.all()

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    theme__theme__concerned_structures__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            return self.none()
    class Meta:
        verbose_name = "Commentaire"
        verbose_name_plural = "Commentaires"

class Constat(TimeStampedModel):
    numconstat = models.IntegerField(default=1, verbose_name="N° Constat")
    content = Varchar2(verbose_name="Contenu", max_length=4000)
    mission = models.ForeignKey(
        Mission, on_delete=models.DO_NOTHING, verbose_name="mission_constat"
    )

    def __str__(self) -> str:
        return "%s|%s" % (self.mission.code, self.numconstat)

    class Meta:
        verbose_name = "Constat"
        verbose_name_plural = "Constats"


class FACT(TimeStampedModel):
    code = models.CharField(max_length=100)
    description = Varchar2(max_length=4000, verbose_name="Description")
    constat = models.ForeignKey(
        Constat, on_delete=models.DO_NOTHING, related_name="fact_constat"
    )

    def __str__(self) -> str:
        return self.code

    class Meta:
        verbose_name = "Fait"
        verbose_name_plural = "Faits"


class Cause(TimeStampedModel):
    numcause = models.IntegerField(default=1, verbose_name="N° Cause")
    content = Varchar2(verbose_name="Contenu", max_length=4000)
    constat = models.ForeignKey(
        Constat, on_delete=models.DO_NOTHING, null=False, related_name="cause_constat"
    )

    def __str__(self) -> str:
        return (
            h.handle("%s ... " % self.content)
            .replace("*", "")
            .replace("\\-", "")
            .replace("#", "")[0:60]
        )

    def __repr__(self) -> str:
        return (
            h.handle("%s ... " % self.content)
            .replace("*", "")
            .replace("\\-", "")
            .replace("#", "")[0:60]
        )

    class Meta:
        verbose_name = "Cause"
        verbose_name_plural = "Causes"


class Consequence(TimeStampedModel):
    numconsequence = models.IntegerField(default=1, verbose_name="N° Conséquence")
    code = models.CharField(max_length=100)
    content = Varchar2("Contenu", max_length=4000)
    constat = models.ForeignKey(
        Constat, on_delete=models.DO_NOTHING, related_name="constat_consequences"
    )

    def __str__(self) -> str:
        return (
            h.handle("%s ... " % self.content)
            .replace("*", "")
            .replace("\\-", "")
            .replace("#", "")[0:60]
        )

    class Meta:
        verbose_name = "Conséquence"
        verbose_name_plural = "Conséquences"


class Recommendation(TimeStampedModel):
    numrecommandation = models.IntegerField(default=1, verbose_name="N° Recommandation")
    recommendation = Varchar2(
        max_length=4000, verbose_name="Description de la recommendation"
    )
    concerned_structure = models.ForeignKey(
        CriStructview,
        related_name="structures_concerned_recommendation",
        on_delete=models.RESTRICT,
    )

    priority = models.CharField(
        choices=RecommendationPriorityChoice, verbose_name="Priorité", max_length=10
    )

    status = models.CharField(
        choices=RecommendationEtat, max_length=50, blank=True,verbose_name="Situation prise en charge"
    )  # Concerned

    validated = models.BooleanField(default=False, verbose_name="Validée")  # ASR

    accepted = models.BooleanField(
        default=None, verbose_name="Acceptée", null=True
    )  # Concerned pour saisie du plan d'action recommendations

    responsible = models.CharField(max_length=100)

    causes = models.ManyToManyField("Cause", verbose_name="Causes", blank=True)

    mission = models.ForeignKey(
        Mission, on_delete=models.DO_NOTHING, verbose_name="mission"
    )

    field_history = FieldHistoryTracker(["validated", "accepted", "status"])
    # objectif = 
    # action_type=
    # dispositif_type=
    # lifecycle = 
    objects = CustomQuerySetManager()
    class QuerySet(models.query.QuerySet):
        def active_for_user(self, user, *args, **kwargs):
            # Récupération des noms des groupes de l'utilisateur
            roles = user.groups.values_list("name", flat=True)
            if "CRI_MANAGERS" in roles or "ADMINS" in roles or user.username == "admin":
                return self.all()

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    concerned_structure__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            elif (
                "CRI_STAFF" in roles
                or "CRI_Auditeurs" in roles
                or "CRI_Audites" in roles
            ):
                qs = self.filter(
                    concerned_structure__in=[
                        user.user_profile.agent.ID_STRUCT
                    ]
                )
                if qs.exists():
                    return qs

            return self.none()
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["id", "concerned_structure", "mission"],
                name="uniq_miss_recomm_followup",
            )
        ]
        verbose_name = "Recommendations"
        verbose_name_plural = "Recommendations"


class ActionResponsable(TimeStampedModel):
    recommendation = models.ForeignKey(
        "Recommendation",
        on_delete=models.DO_NOTHING,
        related_name="recommandation_action_responsable",
        null=True,
    )
    action = models.ForeignKey(
        "Action",
        on_delete=models.DO_NOTHING,
        related_name="action_action_responsable",
        null=True,
    )
    structure = models.ForeignKey(
        CriStructview, related_name="structure_approbation", on_delete=models.DO_NOTHING
    )

    type = models.CharField(
        choices=ActionTypeEnum, verbose_name="Type Action", max_length=20
    )

    value = models.CharField(
        choices=ActionStateEnum, verbose_name="valeur", max_length=20
    )

    class Meta:
        verbose_name = "ActionResponsable"
        verbose_name_plural = "ActionResponsables"


class Risk(TimeStampedModel):
    consequence = models.ForeignKey(
        "Consequence",
        on_delete=models.RESTRICT,
        verbose_name="conséquence",
        related_name="risk_consequences",
    )
    validated = models.BooleanField(default=False, verbose_name="Validé")
    title = models.CharField(max_length=100, verbose_name="Intitulé")
    description = Varchar2(max_length=4000, verbose_name="Description")
    impacts = models.ManyToManyField(
        "RiskImpact", blank=True, related_name="risk_impacts"
    )
    # recommendations = GenericRelation("Recommendation")
    field_history = FieldHistoryTracker(
        ["validated", "impacts", "title", "description"]
    )

    def __str__(self) -> str:
        return self.title

    class Meta:
        verbose_name = "Risque"
        verbose_name_plural = "Risques"


class RiskImpact(TimeStampedModel):
    description = Varchar2(max_length=4000, verbose_name="Description")
    degree = models.CharField(choices=DegreeImpact, max_length=20, verbose_name="Degré")
    occur_prob = models.CharField(
        choices=OccurProbImpact, max_length=20, verbose_name="Probabilité d'occurence"
    )

    def __str__(self) -> str:
        return (
            h.handle("%s" % self.description)
            .replace("*", "")
            .replace("\\-", "")
            .replace("#", "")[0:60]
        )

    class Meta:
        verbose_name = "Impact du risque"
        verbose_name_plural = "Impacts des risques"


class Goal(TimeStampedModel):
    validated = models.BooleanField(default=False, verbose_name="Validé")
    title = models.CharField(max_length=100, verbose_name="Intitulé")
    description = Varchar2(verbose_name="Description", max_length=4000)
    field_history = FieldHistoryTracker(["validated", "title", "description"])

    def __str__(self) -> str:
        return self.title

    class Meta:
        verbose_name = "Objectif"
        verbose_name_plural = "Objectifs"
