import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const recommendationId = searchParams.get('recommendationId')
    const status = searchParams.get('status')

    // Build where clause
    const where: any = {}

    if (search) {
      where.OR = [
        {
          description: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          status: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ]
    }

    if (recommendationId) {
      where.recommendationId = parseInt(recommendationId)
    }

    if (status) {
      where.status = status
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Fetch actions with relations
    const actions = await prisma.action.findMany({
      where,
      include: {
        recommendation: {
          select: {
            id: true,
            recommendation: true,
            mission: {
              select: {
                id: true,
                code: true,
                title: true,
              }
            }
          }
        },
        jobLeader: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        comments: {
          select: {
            id: true,
            comment: true,
            created: true,
            createdBy: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
              }
            }
          }
        }
      },
      orderBy: {
        created: 'desc'
      },
      skip,
      take: limit,
    })

    // Get total count for pagination
    const total = await prisma.action.count({ where })

    return NextResponse.json({
      data: {
        results: actions,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Actions fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to create actions (staff only)
    if (!session.user.isStaff) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const {
      description,
      status,
      progress,
      startDate,
      endDate,
      jobLeaderId,
      recommendationId,
      proof
    } = body

    if (!description || !recommendationId) {
      return NextResponse.json(
        { error: 'Description and recommendation ID are required' },
        { status: 400 }
      )
    }

    // Create action
    const newAction = await prisma.action.create({
      data: {
        description,
        status: status || 'PENDING',
        progress: progress || 0,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        jobLeaderId: jobLeaderId || null,
        recommendationId: parseInt(recommendationId),
        proof: proof || null,
        createdBy: session.user.id,
      },
      include: {
        recommendation: {
          select: {
            id: true,
            recommendation: true,
            mission: {
              select: {
                id: true,
                code: true,
                title: true,
              }
            }
          }
        },
        jobLeader: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    })

    return NextResponse.json(newAction, { status: 201 })
  } catch (error) {
    console.error('Action creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
