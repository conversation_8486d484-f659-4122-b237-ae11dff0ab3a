/**
 * Example component demonstrating how to use ArbitratedTheme hooks
 * This shows different ways to use the ArbitratedTheme API hooks
 */

import React, { useState } from 'react';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
import { 
  useApiArbitratedThemeList, 
  useApiArbitratedThemeCreate, 
  useApiArbitratedThemeUpdate, 
  useApiArbitratedThemePartialUpdate,
  useApiArbitratedThemeDestroy,
  useApiArbitratedThemeRetrieve,
  useApiArbitrationList,
  useApiThemeList
} from '@/hooks/useNextApi';
import { $ArbitratedTheme } from '@/lib/schemas';

export default function ArbitratedThemeHooksExample() {
  const [selectedArbitrationId, setSelectedArbitrationId] = useState<number | null>(null);
  const [selectedThemeId, setSelectedThemeId] = useState<number | null>(null);
  const [selectedArbitratedThemeId, setSelectedArbitratedThemeId] = useState<number | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [selectedArbitratedTheme, setSelectedArbitratedTheme] = useState<any>(null);
  const [formData, setFormData] = useState({
    arbitrationId: null,
    themeId: null,
    note: '',
  });

  const toast = useRef<Toast>(null);

  // Fetch data
  const { data: arbitratedThemesResponse, isLoading: arbitratedThemesLoading, refetch: refetchArbitratedThemes } = useApiArbitratedThemeList({
    arbitrationId: selectedArbitrationId || undefined,
    themeId: selectedThemeId || undefined,
    limit: 50
  });

  const { data: arbitratedThemeDetails, isLoading: arbitratedThemeDetailsLoading } = useApiArbitratedThemeRetrieve(
    selectedArbitratedThemeId || 0
  );

  const { data: arbitrationsResponse } = useApiArbitrationList({ limit: 100 });
  const { data: themesResponse } = useApiThemeList({ limit: 100 });

  // Mutations
  const createArbitratedTheme = useApiArbitratedThemeCreate();
  const updateArbitratedTheme = useApiArbitratedThemeUpdate();
  const partialUpdateArbitratedTheme = useApiArbitratedThemePartialUpdate();
  const deleteArbitratedTheme = useApiArbitratedThemeDestroy();

  const arbitratedThemes = Array.isArray(arbitratedThemesResponse?.data?.results) ? arbitratedThemesResponse.data.results : [];
  const arbitrations = Array.isArray(arbitrationsResponse?.data?.results) ? arbitrationsResponse.data.results : [];
  const themes = Array.isArray(themesResponse?.data?.results) ? themesResponse.data.results : [];

  const handleCreate = async () => {
    try {
      await createArbitratedTheme.mutateAsync(formData);
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Thème arbitré créé avec succès' 
      });
      
      setShowCreateDialog(false);
      resetForm();
      refetchArbitratedThemes();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la création' 
      });
    }
  };

  const handleUpdate = async () => {
    try {
      await updateArbitratedTheme.mutateAsync({
        id: selectedArbitratedTheme.id,
        data: formData,
      });
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Thème arbitré mis à jour avec succès' 
      });
      
      setShowEditDialog(false);
      resetForm();
      refetchArbitratedThemes();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la mise à jour' 
      });
    }
  };

  const handlePartialUpdate = async (arbitratedThemeId: number, updates: any) => {
    try {
      await partialUpdateArbitratedTheme.mutateAsync({
        id: arbitratedThemeId,
        data: updates,
      });
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Thème arbitré mis à jour' 
      });
      
      refetchArbitratedThemes();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la mise à jour' 
      });
    }
  };

  const handleDelete = async (arbitratedThemeId: number) => {
    try {
      await deleteArbitratedTheme.mutateAsync(arbitratedThemeId);
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Thème arbitré supprimé avec succès' 
      });
      
      refetchArbitratedThemes();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la suppression' 
      });
    }
  };

  const resetForm = () => {
    setFormData({
      arbitrationId: null,
      themeId: null,
      note: '',
    });
  };

  const openCreateDialog = () => {
    resetForm();
    setShowCreateDialog(true);
  };

  const openEditDialog = (arbitratedTheme: any) => {
    setSelectedArbitratedTheme(arbitratedTheme);
    setFormData({
      arbitrationId: arbitratedTheme.arbitrationId,
      themeId: arbitratedTheme.themeId,
      note: arbitratedTheme.note || '',
    });
    setShowEditDialog(true);
  };

  const openDetailsDialog = (arbitratedTheme: any) => {
    setSelectedArbitratedThemeId(arbitratedTheme.id);
    setShowDetailsDialog(true);
  };

  // Column templates
  const arbitrationBodyTemplate = (rowData: any) => {
    return rowData.arbitration?.plan?.title || `Arbitrage #${rowData.arbitrationId}`;
  };

  const themeBodyTemplate = (rowData: any) => {
    return rowData.theme?.title || `Thème #${rowData.themeId}`;
  };

  const missionsBodyTemplate = (rowData: any) => {
    const missionCount = rowData.missions?.length || 0;
    return missionCount > 0 ? 
      <Tag value={`${missionCount} mission(s)`} severity="info" /> : 
      <Tag value="Aucune mission" severity="warning" />;
  };

  const actionsBodyTemplate = (rowData: any) => {
    return (
      <div className="flex gap-2">
        <Button 
          icon="pi pi-eye" 
          size="small" 
          onClick={() => openDetailsDialog(rowData)}
          tooltip="Voir détails"
        />
        <Button 
          icon="pi pi-pencil" 
          size="small" 
          onClick={() => openEditDialog(rowData)}
          tooltip="Modifier"
        />
        <Button 
          icon="pi pi-trash" 
          size="small" 
          severity="danger"
          onClick={() => handleDelete(rowData.id)}
          tooltip="Supprimer"
        />
      </div>
    );
  };

  return (
    <div className="arbitrated-theme-hooks-example">
      <Toast ref={toast} />
      
      <div className="card">
        <h4>Exemple d'utilisation des hooks ArbitratedTheme</h4>
        
        {/* Filters */}
        <div className="grid mb-3">
          <div className="col-6">
            <label htmlFor="arbitration-filter">Filtrer par arbitrage:</label>
            <Dropdown
              id="arbitration-filter"
              value={selectedArbitrationId}
              options={arbitrations.map((arb: any) => ({
                label: arb.plan?.title || `Arbitrage #${arb.id}`,
                value: arb.id
              }))}
              onChange={(e) => setSelectedArbitrationId(e.value)}
              placeholder="Sélectionner un arbitrage"
              showClear
              className="w-full"
            />
          </div>
          <div className="col-6">
            <label htmlFor="theme-filter">Filtrer par thème:</label>
            <Dropdown
              id="theme-filter"
              value={selectedThemeId}
              options={themes.map((theme: any) => ({
                label: theme.title,
                value: theme.id
              }))}
              onChange={(e) => setSelectedThemeId(e.value)}
              placeholder="Sélectionner un thème"
              showClear
              className="w-full"
            />
          </div>
        </div>

        {/* ArbitratedThemes table */}
        <div className="flex justify-content-between align-items-center mb-3">
          <h5>Thèmes Arbitrés</h5>
          <Button 
            label="Nouveau Thème Arbitré" 
            icon="pi pi-plus" 
            onClick={openCreateDialog}
          />
        </div>

        <DataTable 
          value={arbitratedThemes} 
          loading={arbitratedThemesLoading}
          paginator 
          rows={10}
          emptyMessage="Aucun thème arbitré trouvé"
        >
          <Column field="id" header={$ArbitratedTheme.properties.id.title} />
          <Column field="arbitration" header={$ArbitratedTheme.properties.arbitration.title} body={arbitrationBodyTemplate} />
          <Column field="theme" header={$ArbitratedTheme.properties.theme.title} body={themeBodyTemplate} />
          <Column field="note" header={$ArbitratedTheme.properties.note.title} style={{ maxWidth: '200px' }} />
          <Column field="missions" header={$ArbitratedTheme.properties.missions.title} body={missionsBodyTemplate} />
          <Column field="created" header={$ArbitratedTheme.properties.created.title} body={(data) => 
            new Date(data.created).toLocaleDateString('fr')
          } />
          <Column header="Actions" body={actionsBodyTemplate} />
        </DataTable>
      </div>

      {/* Create Dialog */}
      <Dialog 
        header="Créer un Thème Arbitré" 
        visible={showCreateDialog} 
        onHide={() => setShowCreateDialog(false)}
        style={{ width: '500px' }}
      >
        <div className="field">
          <label htmlFor="arbitrationId">Arbitrage *</label>
          <Dropdown
            id="arbitrationId"
            value={formData.arbitrationId}
            options={arbitrations.map((arb: any) => ({
              label: arb.plan?.title || `Arbitrage #${arb.id}`,
              value: arb.id
            }))}
            onChange={(e) => setFormData({ ...formData, arbitrationId: e.value })}
            placeholder="Sélectionner un arbitrage"
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="themeId">Thème *</label>
          <Dropdown
            id="themeId"
            value={formData.themeId}
            options={themes.map((theme: any) => ({
              label: theme.title,
              value: theme.id
            }))}
            onChange={(e) => setFormData({ ...formData, themeId: e.value })}
            placeholder="Sélectionner un thème"
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="note">Note</label>
          <InputTextarea
            id="note"
            value={formData.note}
            onChange={(e) => setFormData({ ...formData, note: e.target.value })}
            rows={3}
            className="w-full"
          />
        </div>

        <div className="flex justify-content-end gap-2 mt-4">
          <Button 
            label="Annuler" 
            severity="secondary" 
            onClick={() => setShowCreateDialog(false)} 
          />
          <Button 
            label="Créer" 
            onClick={handleCreate}
            loading={createArbitratedTheme.isPending}
            disabled={!formData.arbitrationId || !formData.themeId}
          />
        </div>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog 
        header="Modifier le Thème Arbitré" 
        visible={showEditDialog} 
        onHide={() => setShowEditDialog(false)}
        style={{ width: '500px' }}
      >
        <div className="field">
          <label htmlFor="edit-note">Note</label>
          <InputTextarea
            id="edit-note"
            value={formData.note}
            onChange={(e) => setFormData({ ...formData, note: e.target.value })}
            rows={3}
            className="w-full"
          />
        </div>

        <div className="flex justify-content-end gap-2 mt-4">
          <Button 
            label="Annuler" 
            severity="secondary" 
            onClick={() => setShowEditDialog(false)} 
          />
          <Button 
            label="Mettre à jour" 
            onClick={handleUpdate}
            loading={updateArbitratedTheme.isPending}
          />
        </div>
      </Dialog>

      {/* Details Dialog */}
      <Dialog 
        header="Détails du Thème Arbitré" 
        visible={showDetailsDialog} 
        onHide={() => setShowDetailsDialog(false)}
        style={{ width: '700px' }}
      >
        {arbitratedThemeDetailsLoading ? (
          <div className="text-center">Chargement...</div>
        ) : arbitratedThemeDetails?.data ? (
          <div className="arbitrated-theme-details">
            <div className="grid">
              <div className="col-6">
                <strong>ID:</strong> {arbitratedThemeDetails.data.id}
              </div>
              <div className="col-6">
                <strong>Créé:</strong> {new Date(arbitratedThemeDetails.data.created).toLocaleDateString('fr')}
              </div>
              <div className="col-12">
                <strong>Arbitrage:</strong> {arbitratedThemeDetails.data.arbitration?.plan?.title || 'N/A'}
              </div>
              <div className="col-12">
                <strong>Thème:</strong> {arbitratedThemeDetails.data.theme?.title || 'N/A'}
              </div>
              <div className="col-12">
                <strong>Note:</strong> {arbitratedThemeDetails.data.note || 'Aucune note'}
              </div>
              <div className="col-12">
                <strong>Missions associées:</strong>
                {arbitratedThemeDetails.data.missions?.length > 0 ? (
                  <ul>
                    {arbitratedThemeDetails.data.missions.map((mission: any) => (
                      <li key={mission.id}>
                        {mission.code} - {mission.title} ({mission.type})
                      </li>
                    ))}
                  </ul>
                ) : (
                  <span> Aucune mission associée</span>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div>Aucun détail disponible</div>
        )}
      </Dialog>
    </div>
  );
}
