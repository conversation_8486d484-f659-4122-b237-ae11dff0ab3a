"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/page",{

/***/ "(app-client)/./hooks/useNextApi.ts":
/*!*****************************!*\
  !*** ./hooks/useNextApi.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApiActionCreate: function() { return /* binding */ useApiActionCreate; },\n/* harmony export */   useApiActionDestroy: function() { return /* binding */ useApiActionDestroy; },\n/* harmony export */   useApiActionList: function() { return /* binding */ useApiActionList; },\n/* harmony export */   useApiActionPartialUpdate: function() { return /* binding */ useApiActionPartialUpdate; },\n/* harmony export */   useApiActionRetrieve: function() { return /* binding */ useApiActionRetrieve; },\n/* harmony export */   useApiActionUpdate: function() { return /* binding */ useApiActionUpdate; },\n/* harmony export */   useApiArbitratedThemeCreate: function() { return /* binding */ useApiArbitratedThemeCreate; },\n/* harmony export */   useApiArbitratedThemeDestroy: function() { return /* binding */ useApiArbitratedThemeDestroy; },\n/* harmony export */   useApiArbitratedThemeList: function() { return /* binding */ useApiArbitratedThemeList; },\n/* harmony export */   useApiArbitratedThemePartialUpdate: function() { return /* binding */ useApiArbitratedThemePartialUpdate; },\n/* harmony export */   useApiArbitratedThemeRetrieve: function() { return /* binding */ useApiArbitratedThemeRetrieve; },\n/* harmony export */   useApiArbitratedThemeUpdate: function() { return /* binding */ useApiArbitratedThemeUpdate; },\n/* harmony export */   useApiArbitrationCreate: function() { return /* binding */ useApiArbitrationCreate; },\n/* harmony export */   useApiArbitrationDestroy: function() { return /* binding */ useApiArbitrationDestroy; },\n/* harmony export */   useApiArbitrationList: function() { return /* binding */ useApiArbitrationList; },\n/* harmony export */   useApiArbitrationPartialUpdate: function() { return /* binding */ useApiArbitrationPartialUpdate; },\n/* harmony export */   useApiArbitrationRetrieve: function() { return /* binding */ useApiArbitrationRetrieve; },\n/* harmony export */   useApiCommentCreate: function() { return /* binding */ useApiCommentCreate; },\n/* harmony export */   useApiCommentDestroy: function() { return /* binding */ useApiCommentDestroy; },\n/* harmony export */   useApiCommentList: function() { return /* binding */ useApiCommentList; },\n/* harmony export */   useApiCommentPartialUpdate: function() { return /* binding */ useApiCommentPartialUpdate; },\n/* harmony export */   useApiCommentRetrieve: function() { return /* binding */ useApiCommentRetrieve; },\n/* harmony export */   useApiDocsCreate: function() { return /* binding */ useApiDocsCreate; },\n/* harmony export */   useApiDocsDestroy: function() { return /* binding */ useApiDocsDestroy; },\n/* harmony export */   useApiDocsUpdate: function() { return /* binding */ useApiDocsUpdate; },\n/* harmony export */   useApiDocumentsList: function() { return /* binding */ useApiDocumentsList; },\n/* harmony export */   useApiDomainCreate: function() { return /* binding */ useApiDomainCreate; },\n/* harmony export */   useApiDomainDestroy: function() { return /* binding */ useApiDomainDestroy; },\n/* harmony export */   useApiDomainList: function() { return /* binding */ useApiDomainList; },\n/* harmony export */   useApiDomainRetrieve: function() { return /* binding */ useApiDomainRetrieve; },\n/* harmony export */   useApiDomainUpdate: function() { return /* binding */ useApiDomainUpdate; },\n/* harmony export */   useApiGoalCreate: function() { return /* binding */ useApiGoalCreate; },\n/* harmony export */   useApiGoalDestroy: function() { return /* binding */ useApiGoalDestroy; },\n/* harmony export */   useApiGoalList: function() { return /* binding */ useApiGoalList; },\n/* harmony export */   useApiGoalRetrieve: function() { return /* binding */ useApiGoalRetrieve; },\n/* harmony export */   useApiGoalUpdate: function() { return /* binding */ useApiGoalUpdate; },\n/* harmony export */   useApiMissionCreate: function() { return /* binding */ useApiMissionCreate; },\n/* harmony export */   useApiMissionDestroy: function() { return /* binding */ useApiMissionDestroy; },\n/* harmony export */   useApiMissionDocsCreate: function() { return /* binding */ useApiMissionDocsCreate; },\n/* harmony export */   useApiMissionDocumentsList: function() { return /* binding */ useApiMissionDocumentsList; },\n/* harmony export */   useApiMissionList: function() { return /* binding */ useApiMissionList; },\n/* harmony export */   useApiMissionPartialUpdate: function() { return /* binding */ useApiMissionPartialUpdate; },\n/* harmony export */   useApiMissionRetrieve: function() { return /* binding */ useApiMissionRetrieve; },\n/* harmony export */   useApiPlanCreate: function() { return /* binding */ useApiPlanCreate; },\n/* harmony export */   useApiPlanDestroy: function() { return /* binding */ useApiPlanDestroy; },\n/* harmony export */   useApiPlanList: function() { return /* binding */ useApiPlanList; },\n/* harmony export */   useApiPlanRetrieve: function() { return /* binding */ useApiPlanRetrieve; },\n/* harmony export */   useApiPlanUpdate: function() { return /* binding */ useApiPlanUpdate; },\n/* harmony export */   useApiProcessCreate: function() { return /* binding */ useApiProcessCreate; },\n/* harmony export */   useApiProcessDestroy: function() { return /* binding */ useApiProcessDestroy; },\n/* harmony export */   useApiProcessList: function() { return /* binding */ useApiProcessList; },\n/* harmony export */   useApiProcessRetrieve: function() { return /* binding */ useApiProcessRetrieve; },\n/* harmony export */   useApiProcessUpdate: function() { return /* binding */ useApiProcessUpdate; },\n/* harmony export */   useApiRecommendationCreate: function() { return /* binding */ useApiRecommendationCreate; },\n/* harmony export */   useApiRecommendationDestroy: function() { return /* binding */ useApiRecommendationDestroy; },\n/* harmony export */   useApiRecommendationList: function() { return /* binding */ useApiRecommendationList; },\n/* harmony export */   useApiRecommendationPartialUpdate: function() { return /* binding */ useApiRecommendationPartialUpdate; },\n/* harmony export */   useApiRecommendationRetrieve: function() { return /* binding */ useApiRecommendationRetrieve; },\n/* harmony export */   useApiRiskCreate: function() { return /* binding */ useApiRiskCreate; },\n/* harmony export */   useApiRiskDestroy: function() { return /* binding */ useApiRiskDestroy; },\n/* harmony export */   useApiRiskList: function() { return /* binding */ useApiRiskList; },\n/* harmony export */   useApiRiskRetrieve: function() { return /* binding */ useApiRiskRetrieve; },\n/* harmony export */   useApiRiskUpdate: function() { return /* binding */ useApiRiskUpdate; },\n/* harmony export */   useApiStructurelqsList: function() { return /* binding */ useApiStructurelqsList; },\n/* harmony export */   useApiStructurelqsRetrieve: function() { return /* binding */ useApiStructurelqsRetrieve; },\n/* harmony export */   useApiThemeCreate: function() { return /* binding */ useApiThemeCreate; },\n/* harmony export */   useApiThemeDestroy: function() { return /* binding */ useApiThemeDestroy; },\n/* harmony export */   useApiThemeList: function() { return /* binding */ useApiThemeList; },\n/* harmony export */   useApiThemePartialUpdate: function() { return /* binding */ useApiThemePartialUpdate; },\n/* harmony export */   useApiThemeRetrieve: function() { return /* binding */ useApiThemeRetrieve; },\n/* harmony export */   useApiThemeUpdate: function() { return /* binding */ useApiThemeUpdate; },\n/* harmony export */   useApiUserCreate: function() { return /* binding */ useApiUserCreate; },\n/* harmony export */   useApiUserList: function() { return /* binding */ useApiUserList; },\n/* harmony export */   useApiUserRetrieve: function() { return /* binding */ useApiUserRetrieve; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api/nextApi */ \"(app-client)/./services/api/nextApi.ts\");\n// React hooks for Next.js API to replace Django API hooks\n\n\n// Mission hooks\nfunction useApiMissionList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"missions\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMissions(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiMissionRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"missions\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMission(id),\n        enabled: !!id\n    });\n}\nfunction useApiMissionCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createMission(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateMission(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteMission(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// Recommendation hooks\nfunction useApiRecommendationList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"recommendations\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRecommendations(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiRecommendationRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"recommendations\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRecommendation(id),\n        enabled: !!id\n    });\n}\nfunction useApiRecommendationCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createRecommendation(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRecommendationPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateRecommendation(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRecommendationDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteRecommendation(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// User hooks\nfunction useApiUserList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"users\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUsers(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiUserRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"users\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUser(id),\n        enabled: !!id\n    });\n}\nfunction useApiUserCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createUser(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"users\"\n                ]\n            });\n        }\n    });\n}\n// Plan hooks\nfunction useApiPlanList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"plans\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlans(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiPlanRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"plans\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlan(id),\n        enabled: !!id\n    });\n}\nfunction useApiPlanCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createPlan(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiPlanUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updatePlan(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiPlanDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deletePlan(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n        }\n    });\n}\n// Theme hooks\nfunction useApiThemeList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"themes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getThemes(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiThemeRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"themes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getTheme(id),\n        enabled: !!id\n    });\n}\nfunction useApiThemeCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createTheme(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemeUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemePartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemeDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteTheme(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Arbitration hooks\nfunction useApiArbitrationList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrations\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitrations(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiArbitrationRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrations\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitration(id),\n        enabled: !!id\n    });\n}\nfunction useApiArbitrationCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createArbitration(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitrationPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitration(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitrationDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteArbitration(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\n// Comment hooks\nfunction useApiCommentList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"comments\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComments(params),\n        staleTime: 2 * 60 * 1000\n    });\n}\nfunction useApiCommentRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"comments\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComment(id),\n        enabled: !!id\n    });\n}\nfunction useApiCommentCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createComment(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiCommentPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateComment(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiCommentDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteComment(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// Document hooks\nfunction useApiDocsCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].uploadDocument(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDocsCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { missionId, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].uploadMissionDocuments(missionId, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\",\n                    variables.missionId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\",\n                    variables.missionId\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDocumentsList(missionId) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"mission-documents\",\n            missionId\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMissionDocuments(missionId),\n        enabled: !!missionId\n    });\n}\nfunction useApiDocumentsList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"documents\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getDocuments(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiDocsDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteDocument(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiDocsUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateDocument(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\"\n                ]\n            });\n        }\n    });\n}\n// Action hooks\nfunction useApiActionList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"actions\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getActions(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiActionRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"actions\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAction(id),\n        enabled: !!id\n    });\n}\nfunction useApiActionCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createAction(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateAction(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateAction(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteAction(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\n// ArbitratedTheme hooks\nfunction useApiArbitratedThemeList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrated-themes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitratedThemes(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiArbitratedThemeRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrated-themes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitratedTheme(id),\n        enabled: !!id\n    });\n}\nfunction useApiArbitratedThemeCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createArbitratedTheme(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemeUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitratedTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemePartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitratedTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemeDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteArbitratedTheme(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Domain hooks\nfunction useApiDomainList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"domains\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getDomains(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiDomainRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"domains\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getDomain(id),\n        enabled: !!id\n    });\n}\nfunction useApiDomainCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createDomain(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"domains\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiDomainUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateDomain(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"domains\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"domains\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiDomainDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteDomain(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"domains\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Process hooks\nfunction useApiProcessList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"processes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getProcesses(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiProcessRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"processes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getProcess(id),\n        enabled: !!id\n    });\n}\nfunction useApiProcessCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createProcess(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"processes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiProcessUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateProcess(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"processes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"processes\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiProcessDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteProcess(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"processes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Risk hooks\nfunction useApiRiskList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"risks\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRisks(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiRiskRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"risks\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRisk(id),\n        enabled: !!id\n    });\n}\nfunction useApiRiskCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createRisk(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"risks\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRiskUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateRisk(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"risks\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"risks\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRiskDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteRisk(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"risks\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Goal hooks\nfunction useApiGoalList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"goals\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getGoals(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiGoalRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"goals\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getGoal(id),\n        enabled: !!id\n    });\n}\nfunction useApiGoalCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createGoal(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"goals\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiGoalUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateGoal(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"goals\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"goals\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiGoalDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteGoal(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"goals\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Structure hooks\nfunction useApiStructurelqsList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"structures\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getStructures(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiStructurelqsRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"structures\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getStructure(id),\n        enabled: !!id\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./hooks/useNextApi.ts\n"));

/***/ })

});