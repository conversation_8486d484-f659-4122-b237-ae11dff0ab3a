/* eslint-disable @next/next/no-img-element */
'use client'
import Link from 'next/link';
import { getCookie, deleteCookie } from "cookies-next";

import { classNames } from 'primereact/utils';
import React, { forwardRef, useContext, useImperativeHandle, useMemo, useRef } from 'react';
import { AppTopbarRef, LayoutState } from '@/types';
import { LayoutContext } from './context/layoutcontext';
import { Menu } from 'primereact/menu';
import { Toast } from 'primereact/toast';
import { useRouter } from 'next/navigation';
import { Badge } from 'primereact/badge';
import sanitizeHtml from 'sanitize-html';
import parse from 'html-react-parser';
import { OverlayPanel } from 'primereact/overlaypanel';
import { DataView } from 'primereact/dataview';
import { Rating } from 'primereact/rating';
import { Tag } from 'primereact/tag';
import { Button } from 'primereact/button';
import router from 'next/router';
import { Avatar } from 'primereact/avatar';
import { ProgressSpinner } from 'primereact/progressspinner';
import { useLoadingSpinner } from '../utilities/hooks/useSpinner';
// import { Inbox } from '@novu/react';
//TODO Notifications for Action Plan recommendations
const AppTopbar = forwardRef<AppTopbarRef>((props, ref) => {
    const {loadingSpinner} = useLoadingSpinner()
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    const toast = useRef(null);
    const router = useRouter();
    const getSeverity = (comment: Comment) => {
        // switch (comment.inventoryStatus) {
        //     case 'INSTOCK':
        //         return 'success';

        //     case 'LOWSTOCK':
        //         return 'warning';

        //     case 'OUTOFSTOCK':
        //         return 'danger';

        //     default:
        return null;
        // }
    };
    const itemTemplate = (comment: any, index: number) => {
        return (
            <div className="col-12" key={comment.id}>
                <div className={classNames('flex flex-column xl:flex-row xl:align-items-start p-4 gap-4', { 'border-top-1 surface-border': index !== 0 })}>
                    {/* <img className="w-9 sm:w-16rem xl:w-10rem shadow-2 block xl:block mx-auto border-round" src={`https://primefaces.org/cdn/primereact/images/product/${product.image}`} alt={product.name} /> */}
                    <div className="flex flex-column sm:flex-row justify-content-between align-items-center xl:align-items-start flex-1 gap-4">
                        <div className="flex flex-column align-items-center sm:align-items-start gap-3">
                            <div className="flex align-items-center gap-3">
                                <Tag value={"comment.recommendation.mission"} severity={'info'}></Tag>
                                <Tag value={"comment.recommendation.concerned_structure.abbrev"} severity={'warning'}></Tag>
                                <Tag value={"comment.recommendation.recommendation.code"} severity={'success'}></Tag>
                            </div>
                            <div className=" font-bold ">{parse(sanitizeHtml(comment.comment.substring(0, 250)))}{comment.comment.length >= 250 && '...'}</div>
                            {/* <Rating value={product.rating} readOnly cancel={false}></Rating> */}
                            <div className="flex align-items-center gap-3">
                                <div className=" font-semibold ">{new Date(comment.created).toLocaleDateString('fr')} {new Date(comment.created).toLocaleTimeString('fr')}</div>
                                <span className="flex align-items-center gap-2">
                                    <i className="pi pi-user"></i>
                                    <span className="font-italic">{comment.created_by.first_name} {comment.created_by.last_name}</span>
                                </span></div>
                        </div>

                    </div>

                </div>
            </div>
        );
    };
    const listTemplate = (items: CommentRead[]) => {
        if (!items || items.length === 0) return null;

        let list = items.map((comment: CommentRead, index: number) => {
            return itemTemplate(comment, index);
        });

        return <div className="grid grid-nogutter">{list}</div>;
    };
    const items =useMemo(() => [
        {
            label: 'Profile',
            items: [
                {
                    key: "profile_menu_apptopbar_1",
                    label: `${user.user?.last_name} ${user.user?.first_name} (${user.user?.username})`,
                    icon: 'pi pi-user'
                },
                {
                    key: "profile_menu_apptopbar",
                    label: `${user.user?.email}`,
                    icon: 'pi pi-envelope'
                },

                {
                    key: "signout_menu_apptopbar",
                    label: 'Quitter',
                    icon: 'pi pi-sign-out',
                    command: () => {
                        //toast.current.show({ severity: 'success', summary: 'Success', detail: 'File created', life: 3000 });
                        deleteCookie('user')
                        router.push('/')
                    }
                }
            ]
        }
    ],[user]);
    const menuRight = useRef(null);
    const notifMenuRight = useRef(null);
    const { layoutConfig, layoutState, setLayoutState, onMenuToggle, showProfileSidebar } = useContext(LayoutContext);
    const menubuttonRef = useRef(null);
    const topbarmenuRef = useRef(null);
    const topbarmenubuttonRef = useRef(null);
    const onConfigButtonClick = () => {
        setLayoutState((prevState: LayoutState) => ({ ...prevState, configSidebarVisible: true }));
    };
    useImperativeHandle(ref, () => ({
        menubutton: menubuttonRef.current,
        topbarmenu: topbarmenuRef.current,
        topbarmenubutton: topbarmenubuttonRef.current
    }));

    return (
        <div className="layout-topbar">
            <Link href="/" className="layout-topbar-logo">
                <img src={`/layout/images/logo-${layoutConfig.colorScheme !== 'light' ? 'white' : 'dark'}.svg`} width="47.22px" height={'35px'} alt="logo" />
                <span>CRI LQS | <i className="pi pi-eye" style={{ fontSize: '2rem' }}></i> </span>
            </Link>

            <button ref={menubuttonRef} type="button" className="p-link layout-menu-button layout-topbar-button" onClick={onMenuToggle}>
                <i className="pi pi-bars" />
            </button>

            <button ref={topbarmenubuttonRef} type="button" className="p-link layout-topbar-menu-button layout-topbar-button" onClick={showProfileSidebar}>
                <i className="pi pi-ellipsis-v" />
            </button>

            <div ref={topbarmenuRef} className={classNames('layout-topbar-menu', { 'layout-topbar-menu-mobile-active': layoutState.profileSidebarVisible })}>
                {/* <Inbox
                    applicationIdentifier="YOUR_APPLICATION_IDENTIFIER"
                    subscriberId="YOUR_SUBSCRIBER_ID"
                    routerPush={(path: string) => router.push(path)}
                /> */}
                {loadingSpinner && <ProgressSpinner className='align-self-center' style={{width: '20px', height: '20px'}} strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s"/>}
                <button type="button" className="p-link layout-topbar-button" onClick={(event) => notifMenuRight.current?.toggle(event)}>
                    <i className="pi pi-bell p-overlay-badge">
                        <Badge
                            style={{ display: "block !important" }}
                            value={`${ 0}`}
                            severity="danger"
                            // data?.data.count ||
                        />
                    </i>
                    <span>Notifications</span>
                </button>
                <OverlayPanel ref={notifMenuRight} closeOnEscape>
                    {/* {data?.data.results.map((comment)=>{
                    console.log(comment);
                    return <div>
                        <span>{comment.id}</span>
                        <span>{parse(sanitizeHtml(comment.comment))}</span>
                        </div>                   
                })} */}
                    <DataView emptyMessage='Pas de notifications !' layout='list' value={data?.data.results.toReversed()} itemTemplate={itemTemplate} paginator rows={2} className='w-30rem' />

                </OverlayPanel>
                {/* <Menu model={data?.data.results.map((comment)=>{console.log(comment);return {key :comment.id,label: parse(sanitizeHtml(comment.comment)),icon: 'pi pi-refresh'}} )} popup ref={notifMenuRight} id="popup_menu_notifications" popupAlignment="right" /> */}
                <button type="button" className="p-link layout-topbar-button" onClick={(event) => menuRight.current?.toggle(event)}>
                    <i className="pi pi-user"></i>
                    <span>Profile</span>
                </button>
                <Menu aria-haspopup model={items} className='w-2' popup ref={menuRight} id="popup_menu_profile" popupAlignment="right" />
                <Toast ref={toast} />
                <button type="button" className="p-link layout-topbar-button" onClick={onConfigButtonClick}>
                    <i className="pi pi-cog"></i>
                    <span>Settings</span>
                </button>
            </div>
        </div>
    );
});

AppTopbar.displayName = 'AppTopbar';

export default AppTopbar;
