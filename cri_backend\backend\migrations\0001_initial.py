# Generated by Django 5.0.6 on 2025-05-18 20:02

import backend.enums
import backend.fields
import django.core.validators
import django.db.models.deletion
import django_extensions.db.fields
import month.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CriAgents',
            fields=[
                ('ID_STRUCT', models.BigIntegerField(blank=True, null=True)),
                ('matric_agnt', models.CharField(max_length=18, primary_key=True, serialize=False)),
                ('nom_agnt', models.CharField(blank=True, max_length=120, null=True)),
                ('prenom_agnt', models.CharField(blank=True, max_length=203, null=True)),
                ('intitu_fonc', models.Char<PERSON>ield(blank=True, max_length=159, null=True)),
                ('libell_stru', models.CharField(blank=True, max_length=150, null=True)),
                ('code_unit', models.CharField(blank=True, max_length=9, null=True)),
                ('code_stru', models.CharField(blank=True, max_length=27, null=True)),
                ('code_ser', models.CharField(blank=True, max_length=60, null=True)),
                ('code_mnemonique', models.CharField(blank=True, max_length=90, null=True)),
            ],
            options={
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='CriStructview',
            fields=[
                ('id', models.BigIntegerField(blank=True, primary_key=True, serialize=False)),
                ('code_stru', models.CharField(max_length=27)),
                ('libell_stru', models.CharField(blank=True, max_length=150, null=True)),
                ('code_unit', models.CharField(blank=True, max_length=18, null=True)),
                ('code_mnemonique', models.CharField(blank=True, max_length=90, null=True)),
            ],
            options={
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Action',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('description', backend.fields.Varchar2(max_length=4000, verbose_name='Description')),
                ('start_date', models.DateTimeField(verbose_name='Début')),
                ('end_date', models.DateTimeField(verbose_name='Fin')),
                ('validated', models.BooleanField(default=False, verbose_name='Validée')),
                ('status', models.CharField(choices=[('Réalisée', 'Accomplished'), ('Non Réalisée', 'Notaccomplished'), ('En cours', 'Inprogress')], max_length=50, verbose_name='Statut')),
                ('progress', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Progrès')),
                ('accepted', models.BooleanField(default=False, verbose_name='Acceptée')),
                ('proof', models.FileField(blank=True, null=True, upload_to='media/action')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('job_leader', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'Action',
                'verbose_name_plural': 'Actions',
            },
        ),
        migrations.CreateModel(
            name='Arbitration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('report', backend.fields.Varchar2(blank=True, max_length=4000, null=True, verbose_name='Rapport')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('team', models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Arbitrage',
                'verbose_name_plural': 'Arbitrages',
            },
        ),
        migrations.CreateModel(
            name='ArbitratedTheme',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('note', backend.fields.Varchar2(blank=True, max_length=4000, null=True, verbose_name='remarque')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('arbitration', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='backend.arbitration', verbose_name='Arbitrage')),
            ],
            options={
                'verbose_name': 'Thème arbitré',
                'verbose_name_plural': 'Thèmes arbitrés',
            },
        ),
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('comment', backend.fields.Varchar2(max_length=4000, verbose_name='commentaire')),
                ('type', models.CharField(choices=[('Retenue', 'Accepted'), ('Non Retenue', 'Not Accepted'), ('Non Concerné', 'Not Concerned')], default=None, max_length=50, null=True)),
                ('value', models.CharField(blank=True, choices=[('Validé', 'Valide'), ('Non Validé', 'Nonvalide'), ('Accepté', 'Accepte'), ('Non Accepté', 'Nonaccepte')], max_length=50)),
                ('validMeraci', models.CharField(choices=[('Validé', 'Yes'), ('Non Validé', 'No'), ('Accepté', 'Ac'), ('Non Accepté', 'Na')], default='Non Validé', max_length=20, verbose_name='validation Meraci')),
                ('validDirecteur', models.CharField(choices=[('Validé', 'Yes'), ('Non Validé', 'No'), ('Accepté', 'Ac'), ('Non Accepté', 'Na')], default='Non Validé', max_length=20, verbose_name='validation Directeur')),
                ('action', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='action_discussions', to='backend.action')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'Commentaire',
                'verbose_name_plural': 'Commentaires',
            },
        ),
        migrations.CreateModel(
            name='CommentDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('document', models.FileField(upload_to='missions/comments/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'docx', 'doc', 'xls', 'xlsx', 'image', 'csv'])], verbose_name='Lettre')),
                ('size', models.BigIntegerField(blank=True, default=0, null=True)),
                ('name', models.CharField(blank=True, max_length=500, null=True)),
                ('type', models.CharField(blank=True, max_length=200, null=True)),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('comment', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='comment_files', to='backend.comment')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'Commentaire',
                'verbose_name_plural': 'Commentaires',
            },
        ),
        migrations.CreateModel(
            name='Constat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('numconstat', models.IntegerField(default=1, verbose_name='N° Constat')),
                ('content', backend.fields.Varchar2(max_length=4000, verbose_name='Contenu')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'Constat',
                'verbose_name_plural': 'Constats',
            },
        ),
        migrations.CreateModel(
            name='Consequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('numconsequence', models.IntegerField(default=1, verbose_name='N° Conséquence')),
                ('code', models.CharField(max_length=100)),
                ('content', backend.fields.Varchar2(max_length=4000, verbose_name='Contenu')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('constat', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='constat_consequences', to='backend.constat')),
            ],
            options={
                'verbose_name': 'Conséquence',
                'verbose_name_plural': 'Conséquences',
            },
        ),
        migrations.CreateModel(
            name='Cause',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('numcause', models.IntegerField(default=1, verbose_name='N° Cause')),
                ('content', backend.fields.Varchar2(max_length=4000, verbose_name='Contenu')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('constat', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='cause_constat', to='backend.constat')),
            ],
            options={
                'verbose_name': 'Cause',
                'verbose_name_plural': 'Causes',
            },
        ),
        migrations.CreateModel(
            name='Domain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('title', models.CharField(max_length=100, verbose_name='Intitulé')),
                ('short_title', models.CharField(max_length=50, verbose_name='Abbréviation')),
                ('type', models.CharField(blank=True, max_length=300, null=True)),
                ('observation', backend.fields.Varchar2(blank=True, max_length=4000, null=True)),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='backend.domain', verbose_name='Domaine parent')),
            ],
            options={
                'verbose_name': 'Domaine',
                'verbose_name_plural': 'Domaines',
                'unique_together': {('title', 'parent')},
            },
        ),
        migrations.CreateModel(
            name='FACT',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('code', models.CharField(max_length=100)),
                ('description', backend.fields.Varchar2(max_length=4000, verbose_name='Description')),
                ('constat', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='fact_constat', to='backend.constat')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'Fait',
                'verbose_name_plural': 'Faits',
            },
        ),
        migrations.CreateModel(
            name='Goal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('validated', models.BooleanField(default=False, verbose_name='Validé')),
                ('title', models.CharField(max_length=100, verbose_name='Intitulé')),
                ('description', backend.fields.Varchar2(max_length=4000, verbose_name='Description')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'Objectif',
                'verbose_name_plural': 'Objectifs',
            },
        ),
        migrations.CreateModel(
            name='Mission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('exercise', models.CharField(blank=True, max_length=4, null=True, verbose_name='Exercice')),
                ('type', models.CharField(choices=[('Commandée', 'Commanded'), ('Planifiée', 'Planified'), ('Avis & Conseils', 'Avis Conseil')], max_length=15, verbose_name='Type')),
                ('code', models.CharField(blank=True, max_length=100, unique=True, verbose_name='code')),
                ('etat', models.CharField(choices=[('Non Lancée', 'Notsarted'), ('Suspendue', 'Suspended'), ('En cours', 'Inprogress'), ('Clôturée', 'Closed')], max_length=10, verbose_name='état')),
                ('start_date', models.DateField(verbose_name='date début')),
                ('end_date', models.DateField(verbose_name='date fin')),
                ('assistants', models.ManyToManyField(blank=True, related_name='mission_assistants', to=settings.AUTH_USER_MODEL, verbose_name='Assistants')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('head', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='mission_head', to=settings.AUTH_USER_MODEL, verbose_name='Chef de mission')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('staff', models.ManyToManyField(blank=True, related_name='mission_auditors_controllers', to=settings.AUTH_USER_MODEL, verbose_name='Auditeurs/Controleurs')),
                ('supervisor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='mission_supervisor', to=settings.AUTH_USER_MODEL, verbose_name='Supervisuer')),
                ('theme', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='mission', to='backend.arbitratedtheme', verbose_name='thème')),
            ],
            options={
                'verbose_name': 'Mission',
                'verbose_name_plural': 'Missions',
            },
        ),
        migrations.AddField(
            model_name='constat',
            name='mission',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='backend.mission', verbose_name='mission_constat'),
        ),
        migrations.CreateModel(
            name='MissionDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('document', models.FileField(upload_to='missions/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'docx', 'doc', 'xls', 'xlsx', 'image', 'csv'])], verbose_name='Lettre')),
                ('size', models.BigIntegerField(blank=True, default=0, null=True)),
                ('name', models.CharField(blank=True, max_length=500, null=True)),
                ('type', models.CharField(blank=True, max_length=200, null=True)),
                ('context', models.CharField(choices=[('MISSION', 'Mission'), ('ACTION', 'Action'), ('RECOMMENDATION', 'Recommendation')], max_length=100)),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('mission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mission_docs', to='backend.mission')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Plan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('exercise', models.IntegerField(choices=backend.enums.year_choices, default=backend.enums.current_year, verbose_name='Année')),
                ('type', models.CharField(choices=[('Audit Interne', 'Audit Intern'), ('Contrôle Interne', 'Ctrl Intern'), ('Hors Plan', 'Hors Plan')], max_length=16, verbose_name='Type')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'PLan',
                'verbose_name_plural': 'Plans',
            },
        ),
        migrations.AddField(
            model_name='mission',
            name='plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='plan_missions', to='backend.plan', verbose_name='plan'),
        ),
        migrations.AddField(
            model_name='arbitration',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='backend.plan'),
        ),
        migrations.CreateModel(
            name='Process',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('title', models.CharField(max_length=100, verbose_name='Intitulé')),
                ('short_title', models.CharField(max_length=10, verbose_name='Abbréviation')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='backend.process', verbose_name='Processus parent')),
            ],
            options={
                'verbose_name': 'Processus',
                'verbose_name_plural': 'Processus',
            },
        ),
        migrations.CreateModel(
            name='Recommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('numrecommandation', models.IntegerField(default=1, verbose_name='N° Recommandation')),
                ('recommendation', backend.fields.Varchar2(max_length=4000, verbose_name='Description de la recommendation')),
                ('priority', models.CharField(choices=[('FAIBLE', 'Low'), ('NORMALE', 'Normal'), ('ELEVEE', 'High')], max_length=10, verbose_name='Priorité')),
                ('status', models.CharField(blank=True, choices=[('Réalisée', 'Accomplished'), ('Non Réalisée', 'Notaccomplished'), ('En cours', 'Inprogress')], max_length=50, verbose_name='Situation prise en charge')),
                ('validated', models.BooleanField(default=False, verbose_name='Validée')),
                ('accepted', models.BooleanField(default=None, null=True, verbose_name='Acceptée')),
                ('responsible', models.CharField(max_length=100)),
                ('causes', models.ManyToManyField(blank=True, to='backend.cause', verbose_name='Causes')),
                ('concerned_structure', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='structures_concerned_recommendation', to='backend.cristructview')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('mission', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='backend.mission', verbose_name='mission')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'Recommendations',
                'verbose_name_plural': 'Recommendations',
            },
        ),
        migrations.AddField(
            model_name='comment',
            name='recommendation',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='recommendation_discussions', to='backend.recommendation'),
        ),
        migrations.CreateModel(
            name='ActionResponsable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('type', models.CharField(choices=[('Validation', 'Validation'), ('Approbation', 'Approbation'), ('Acceptation', 'Acceptation')], max_length=20, verbose_name='Type Action')),
                ('value', models.CharField(choices=[('Validé', 'Valide'), ('Non Validé', 'Nonvalide'), ('Accepté', 'Accepte'), ('Non Accepté', 'Nonaccepte')], max_length=20, verbose_name='valeur')),
                ('action', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='action_action_responsable', to='backend.action')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('structure', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='structure_approbation', to='backend.cristructview')),
                ('recommendation', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='recommandation_action_responsable', to='backend.recommendation')),
            ],
            options={
                'verbose_name': 'ActionResponsable',
                'verbose_name_plural': 'ActionResponsables',
            },
        ),
        migrations.AddField(
            model_name='action',
            name='recommendation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='backend.recommendation', verbose_name='recommandation'),
        ),
        migrations.CreateModel(
            name='RiskImpact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('description', backend.fields.Varchar2(max_length=4000, verbose_name='Description')),
                ('degree', models.CharField(choices=[('Modéré', 'Moderate'), ('Critique', 'Critical'), ('Faible', 'Low'), ('Elevé', 'High')], max_length=20, verbose_name='Degré')),
                ('occur_prob', models.CharField(choices=[('Rare', 'Rare'), ('Occasionel', 'Occas'), ('Fréquent', 'Freq'), ('Imp', 'Improb')], max_length=20, verbose_name="Probabilité d'occurence")),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
            ],
            options={
                'verbose_name': 'Impact du risque',
                'verbose_name_plural': 'Impacts des risques',
            },
        ),
        migrations.CreateModel(
            name='Risk',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('validated', models.BooleanField(default=False, verbose_name='Validé')),
                ('title', models.CharField(max_length=100, verbose_name='Intitulé')),
                ('description', backend.fields.Varchar2(max_length=4000, verbose_name='Description')),
                ('consequence', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='risk_consequences', to='backend.consequence', verbose_name='conséquence')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('impacts', models.ManyToManyField(blank=True, related_name='risk_impacts', to='backend.riskimpact')),
            ],
            options={
                'verbose_name': 'Risque',
                'verbose_name_plural': 'Risques',
            },
        ),
        migrations.CreateModel(
            name='StructureLQSCorrespondents',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('correspondents', models.ManyToManyField(to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('structure', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='struct_correspondents', to='backend.cristructview', verbose_name='structure')),
            ],
            options={
                'verbose_name': 'Structure LQS correspondents',
                'verbose_name_plural': 'Structures LQS correspondents',
            },
        ),
        migrations.CreateModel(
            name='StructureLQSInterim',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('start', models.DateTimeField(verbose_name='début')),
                ('end', models.DateTimeField(verbose_name='fin')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('interim', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='struct_interim_users', to=settings.AUTH_USER_MODEL, verbose_name='intérim')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('structure', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='struct_interim', to='backend.cristructview', verbose_name='structure')),
            ],
            options={
                'verbose_name': 'Intérim Structure LQS',
                'verbose_name_plural': 'Intérims Structures LQS',
            },
        ),
        migrations.CreateModel(
            name='Theme',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('validated', models.BooleanField(default=False, verbose_name='Validé')),
                ('code', models.CharField(blank=True, max_length=30, null=True, verbose_name='code')),
                ('title', models.CharField(max_length=200, verbose_name='Intitulé')),
                ('proposed_by', models.CharField(choices=[('Vice Président', 'Vice Président'), ('Contrôle Interne', 'Contrôle Interne'), ('Audit Interne', 'Audit Interne'), ('Structures', 'Structures')], max_length=100, verbose_name='Proposé par')),
                ('month_start', month.models.MonthField(help_text='Mois début', verbose_name='Période Début')),
                ('month_end', month.models.MonthField(help_text='Mois fin', verbose_name='Période Fin')),
                ('concerned_structures', models.ManyToManyField(related_name='structures_concerned_theme', to='backend.cristructview', verbose_name='Structures concernées')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('domain', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='backend.domain', verbose_name='Domaine')),
                ('goals', models.ManyToManyField(related_name='goals_theme', to='backend.goal', verbose_name='Objéctifs')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('process', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='backend.process', verbose_name='Processus')),
                ('proposing_structures', models.ManyToManyField(blank=True, related_name='structures_proposing_theme', to='backend.cristructview', verbose_name='Structures proposantes')),
                ('risks', models.ManyToManyField(related_name='risks_theme', to='backend.risk', verbose_name='Risques')),
            ],
            options={
                'verbose_name': 'Thème',
                'verbose_name_plural': 'Vivier de Thèmes',
                'permissions': [('change_theme_status', 'Can change the status of themes'), ('close_theme', 'Can remove a theme by setting its status as closed')],
            },
        ),
        migrations.AddField(
            model_name='arbitratedtheme',
            name='theme',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='arbitrated_themes', to='backend.theme', verbose_name='thème'),
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('agent', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='user_profile_agnt', to='backend.criagents')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='user_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Profile utilisateur',
                'verbose_name_plural': 'Profiles utilisateurs',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('read', models.BooleanField(default=False)),
                ('object_id', models.PositiveIntegerField()),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('created_by', models.ForeignKey(blank=True, editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_created_by', to=settings.AUTH_USER_MODEL, verbose_name='créé par')),
                ('modified_by', models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_modified_by', to=settings.AUTH_USER_MODEL, verbose_name='mis à jour par')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to=settings.AUTH_USER_MODEL, verbose_name='utilisateur')),
            ],
            options={
                'indexes': [models.Index(fields=['content_type', 'object_id'], name='backend_not_content_a9214f_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='mission',
            constraint=models.UniqueConstraint(fields=('plan', 'code', 'theme'), name='uniq_miss_thm_pln'),
        ),
        migrations.AddConstraint(
            model_name='recommendation',
            constraint=models.UniqueConstraint(fields=('id', 'concerned_structure', 'mission'), name='uniq_miss_recomm_followup'),
        ),
        migrations.AlterUniqueTogether(
            name='arbitratedtheme',
            unique_together={('arbitration', 'theme')},
        ),
    ]
