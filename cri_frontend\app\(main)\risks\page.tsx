'use client';

import GenericTable from '@/utilities/components/GenericTAble';
import {  useApiRiskList } from '@/services/api/api/api';
import { $Risk, Risk } from '@/services/openapi_client';
import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';

const ThemesTable = () => {

    const { data: themes, isLoading:isLoading ,error:error} = useApiRiskList();
    const { data: themes_valid, isLoading:isLoadingValid ,error:errorValid} = useApiRiskList({validated:true});
    const { data: themes_no_valid, isLoading:isLoadingNoValid ,error:errorNoValid} = useApiRiskList({validated:false});
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex:0,
        pageSize: 5, //customize the default page size
    });
    if( isLoading )  return (<div></div>)
    return   (        
        <div className="grid">
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Risques</span>
                            <div className="text-900 font-medium text-xl">{themes?.data.count! ?? 0 }</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-blue-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-briefcase text-blue-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">24 new </span>
                    <span className="text-500">since last visit</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Commandées</span>
                            <div className="text-900 font-medium text-xl">{themes?.data.count ?? 0 }</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-orange-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-microsoft text-orange-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">{cmd_missions?.data.count ?? 0}</span>
                    <span className="text-500">since last week</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Planifiées</span>
                            <div className="text-900 font-medium text-xl">{themes?.data.count ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-cyan-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-clock text-cyan-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">520 </span>
                    <span className="text-500">newly registered</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">En cours</span>
                            <div className="text-900 font-medium text-xl">{themes?.data.count ?? 0 }</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-bolt text-green-500 text-xl" />                            

                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">85 </span>
                    <span className="text-500">responded</span> */}
                </div>
            </div>
            <div className="col-12">
                {/* <div className="card"> */}
                    {/* <h5>Missions</h5> */}
                    <GenericTable<Risk> data_={themes} isLoading={isLoading} error={error} data_type={$Risk} pagination={{"set":setPagination,"pagi":pagination}}></GenericTable>                    
                {/* </div> */}
            </div>
        </div>
    );
};

export default ThemesTable;
