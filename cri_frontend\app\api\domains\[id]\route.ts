import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/domains/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid domain ID' }, { status: 400 })
    }

    const domain = await prisma.domain.findUnique({
      where: { id },
      include: {
        parent: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        children: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        },
        modifiedByUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          }
        }
      },
    })

    if (!domain) {
      return NextResponse.json({ error: 'Domain not found' }, { status: 404 })
    }

    return NextResponse.json({ data: domain })
  } catch (error) {
    console.error('Error fetching domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/domains/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can update domains
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid domain ID' }, { status: 400 })
    }

    const body = await request.json()
    const { title, shortTitle, parentId, type, observation } = body

    // Check if domain exists
    const existingDomain = await prisma.domain.findUnique({
      where: { id },
    })

    if (!existingDomain) {
      return NextResponse.json({ error: 'Domain not found' }, { status: 404 })
    }

    // Check if parent exists (if provided)
    if (parentId && parentId !== existingDomain.parentId) {
      const parent = await prisma.domain.findUnique({
        where: { id: parentId }
      })
      
      if (!parent) {
        return NextResponse.json(
          { error: 'Parent domain not found' },
          { status: 404 }
        )
      }

      // Prevent circular references
      if (parentId === id) {
        return NextResponse.json(
          { error: 'Domain cannot be its own parent' },
          { status: 400 }
        )
      }
    }

    // Update domain
    const domain = await prisma.domain.update({
      where: { id },
      data: {
        ...(title !== undefined && { title }),
        ...(shortTitle !== undefined && { shortTitle }),
        ...(parentId !== undefined && { parentId }),
        ...(type !== undefined && { type }),
        ...(observation !== undefined && { observation }),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        parent: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        children: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
          }
        }
      },
    })

    return NextResponse.json({ data: domain })
  } catch (error) {
    console.error('Error updating domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/domains/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can delete domains
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid domain ID' }, { status: 400 })
    }

    // Check if domain exists
    const existingDomain = await prisma.domain.findUnique({
      where: { id },
      include: {
        children: true,
        themes: true,
      },
    })

    if (!existingDomain) {
      return NextResponse.json({ error: 'Domain not found' }, { status: 404 })
    }

    // Check if domain has children or themes
    if (existingDomain.children.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete domain with child domains' },
        { status: 400 }
      )
    }

    if (existingDomain.themes.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete domain with associated themes' },
        { status: 400 }
      )
    }

    // Delete domain
    await prisma.domain.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Domain deleted successfully' })
  } catch (error) {
    console.error('Error deleting domain:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
