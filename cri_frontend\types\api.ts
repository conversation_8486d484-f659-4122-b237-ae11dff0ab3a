/**
 * TypeScript types for API responses and requests
 * These types provide type safety for all API hooks
 */

import {
  User, Mission, Recommendation, Comment, Action, Plan, Theme, Arbitration,
  Domain, Process, Risk, Goal, CriStructview, Document, MissionDocument,
  Cause, Constat, Consequence,
  PlanType, MissionType, MissionEtat, ThemeProposingEntity,
  RecommendationPriority, RecommendationEtat, ActionEtat, DocumentType
} from '@prisma/client'

// Base API Response structure
export interface ApiResponse<T> {
  data: T
  message?: string
  error?: string
}

// Paginated response structure
export interface PaginatedResponse<T> {
  results: T[]
  count: number
  page: number
  limit: number
  totalPages: number
}

// API Response types for each entity
export type ApiResponseData<T> = ApiResponse<T>
export type ApiPaginatedResponse<T> = ApiResponse<PaginatedResponse<T>>

// Extended types with relations
export interface UserWithRelations extends User {
  missions?: Mission[]
  recommendations?: Recommendation[]
  comments?: Comment[]
  actions?: Action[]
  createdPlans?: Plan[]
  modifiedPlans?: Plan[]
}

export interface MissionWithRelations extends Mission {
  plan?: Plan
  recommendations?: Recommendation[]
  documents?: MissionDocument[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface RecommendationWithRelations extends Recommendation {
  mission?: Mission
  actions?: Action[]
  comments?: Comment[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface CommentWithRelations extends Comment {
  recommendation?: Recommendation
  mission?: Mission
  user?: User
}

export interface ActionWithRelations extends Action {
  recommendation?: Recommendation
  createdByUser?: User
  modifiedByUser?: User
}

export interface PlanWithRelations extends Plan {
  arbitrations?: Arbitration[]
  missions?: Mission[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface ThemeWithRelations extends Theme {
  domain?: Domain
  process?: Process
  risks?: Risk[]
  goals?: Goal[]
  proposingStructures?: CriStructview[]
  concernedStructures?: CriStructview[]
  arbitratedThemes?: ArbitratedTheme[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface ArbitrationWithRelations extends Arbitration {
  plan?: Plan
  arbitratedThemes?: ArbitratedTheme[]
  missions?: Mission[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface ArbitratedTheme {
  id: number
  arbitrationId: number
  themeId: number
  note?: string
  arbitration?: ArbitrationWithRelations
  theme?: ThemeWithRelations
  missions?: Mission[]
  created: Date
  modified: Date
  createdBy: string
  modifiedBy: string
  createdByUser?: User
  modifiedByUser?: User
}

export interface DomainWithRelations extends Domain {
  parent?: Domain
  children?: Domain[]
  themes?: Theme[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface ProcessWithRelations extends Process {
  parent?: Process
  children?: Process[]
  themes?: Theme[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface RiskWithRelations extends Risk {
  themes?: Theme[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface GoalWithRelations extends Goal {
  themes?: Theme[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface CriStructviewWithRelations extends CriStructview {
  structureCorrespondents?: any[]
  structureInterim?: any[]
  themeProposing?: Theme[]
  themeConcerned?: Theme[]
  recommendations?: Recommendation[]
}

export interface CauseWithRelations extends Cause {
  constat?: ConstatWithRelations
  recommendations?: RecommendationWithRelations[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface ConstatWithRelations extends Constat {
  mission?: MissionWithRelations
  facts?: any[] // FACT model
  causes?: CauseWithRelations[]
  consequences?: ConsequenceWithRelations[]
  recommendations?: RecommendationWithRelations[]
  createdByUser?: User
  modifiedByUser?: User
}

export interface ConsequenceWithRelations extends Consequence {
  constat?: ConstatWithRelations
  createdByUser?: User
  modifiedByUser?: User
}

export interface DocumentWithRelations extends Document {
  createdByUser?: User
  modifiedByUser?: User
}

export interface MissionDocumentWithRelations extends MissionDocument {
  mission?: Mission
  document?: Document
}

// Request parameter types
export interface BaseListParams {
  page?: number
  limit?: number
  search?: string
}

export interface MissionListParams extends BaseListParams {
  planId?: number
  type?: MissionType
  etat?: MissionEtat
}

export interface RecommendationListParams extends BaseListParams {
  missionId?: number
  priority?: RecommendationPriority
  etat?: RecommendationEtat
}

export interface CommentListParams extends BaseListParams {
  recommendationId?: number
  missionId?: number
  userId?: number
}

export interface ActionListParams extends BaseListParams {
  recommendationId?: number
  etat?: ActionEtat
}

export interface PlanListParams extends BaseListParams {
  exercise?: number
  type?: PlanType
}

export interface ThemeListParams extends BaseListParams {
  validated?: boolean
  domainId?: number
  processId?: number
  proposedBy?: ThemeProposingEntity
}

export interface ArbitrationListParams extends BaseListParams {
  planId?: number
}

export interface ArbitratedThemeListParams extends BaseListParams {
  arbitrationId?: number
  themeId?: number
}

export interface DomainListParams extends BaseListParams {
  parentId?: number
}

export interface ProcessListParams extends BaseListParams {
  parentId?: number
}

export interface RiskListParams extends BaseListParams {
  validated?: boolean
}

export interface GoalListParams extends BaseListParams {}

export interface StructureListParams extends BaseListParams {}

export interface DocumentListParams extends BaseListParams {
  missionId?: number
  context?: string
  type?: DocumentType
}

export interface CauseListParams extends BaseListParams {
  constatId?: number
}

export interface ConstatListParams extends BaseListParams {
  missionId?: number
}

export interface ConsequenceListParams extends BaseListParams {
  constatId?: number
}

// Create/Update request types
export interface CreateMissionRequest {
  title: string
  code: string
  planId: number
  type: MissionType
  etat?: MissionEtat
  startDate?: Date
  endDate?: Date
  description?: string
}

export interface UpdateMissionRequest extends Partial<CreateMissionRequest> {}

export interface CreateRecommendationRequest {
  title: string
  missionId: number
  priority: RecommendationPriority
  etat?: RecommendationEtat
  description?: string
  dueDate?: Date
}

export interface UpdateRecommendationRequest extends Partial<CreateRecommendationRequest> {}

export interface CreateCommentRequest {
  content: string
  recommendationId?: number
  missionId?: number
}

export interface UpdateCommentRequest extends Partial<CreateCommentRequest> {}

export interface CreateActionRequest {
  title: string
  recommendationId: number
  etat?: ActionEtat
  description?: string
  dueDate?: Date
}

export interface UpdateActionRequest extends Partial<CreateActionRequest> {}

export interface CreatePlanRequest {
  title: string
  exercise: number
  type: PlanType
  description?: string
}

export interface UpdatePlanRequest extends Partial<CreatePlanRequest> {}

export interface CreateThemeRequest {
  title: string
  code: string
  validated?: boolean
  proposedBy: ThemeProposingEntity
  domainId?: number
  processId?: number
  monthStart?: Date
  monthEnd?: Date
  description?: string
  riskIds?: number[]
  goalIds?: number[]
  proposingStructureIds?: number[]
  concernedStructureIds?: number[]
}

export interface UpdateThemeRequest extends Partial<CreateThemeRequest> {}

export interface CreateArbitrationRequest {
  planId: number
  description?: string
}

export interface UpdateArbitrationRequest extends Partial<CreateArbitrationRequest> {}

export interface CreateArbitratedThemeRequest {
  arbitrationId: number
  themeId: number
  note?: string
}

export interface UpdateArbitratedThemeRequest extends Partial<CreateArbitratedThemeRequest> {}

export interface CreateDomainRequest {
  title: string
  shortTitle: string
  parentId?: number
  type?: string
  observation?: string
}

export interface UpdateDomainRequest extends Partial<CreateDomainRequest> {}

export interface CreateProcessRequest {
  title: string
  shortTitle: string
  parentId?: number
}

export interface UpdateProcessRequest extends Partial<CreateProcessRequest> {}

export interface CreateRiskRequest {
  title: string
  description?: string
  validated?: boolean
}

export interface UpdateRiskRequest extends Partial<CreateRiskRequest> {}

export interface CreateGoalRequest {
  title: string
  description?: string
}

export interface UpdateGoalRequest extends Partial<CreateGoalRequest> {}

export interface CreateUserRequest {
  username: string
  email: string
  firstName?: string
  lastName?: string
  isStaff?: boolean
  isActive?: boolean
}

export interface UpdateUserRequest extends Partial<CreateUserRequest> {}

export interface CreateCauseRequest {
  numcause?: number
  content: string
  constatId: number
}

export interface UpdateCauseRequest extends Partial<CreateCauseRequest> {}

export interface CreateConstatRequest {
  numconstat?: number
  content: string
  missionId: number
}

export interface UpdateConstatRequest extends Partial<CreateConstatRequest> {}

export interface CreateConsequenceRequest {
  numconsequence?: number
  code: string
  content: string
  constatId: number
}

export interface UpdateConsequenceRequest extends Partial<CreateConsequenceRequest> {}

// Mutation parameter types
export interface MutationParams<T> {
  id: number
  data: T
}

// Error types
export interface ApiError {
  message: string
  field?: string
  code?: string
}

export interface ValidationError {
  [field: string]: string[]
}
