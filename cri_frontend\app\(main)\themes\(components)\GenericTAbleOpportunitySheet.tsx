'use client'
import { Box, Chip, Stack } from '@mui/material';
import { Editor } from '@tinymce/tinymce-react';
import parse from 'html-react-parser';
import {
  MRT_RowData,
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef
} from 'material-react-table';
import { MRT_Localization_FR } from 'material-react-table/locales/fr';
import { useRouter } from 'next/navigation';
import { Button } from 'primereact/button';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { Dialog } from 'primereact/dialog';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useEffect, useMemo, useRef, useState } from 'react';

import RecommendationEditForm from './editForm';
import { useApiThemeCreate, useApiThemeDestroy, useApiThemeUpdate } from '@/services/api/api/api';
import { Recommendation } from '@/services/openapi_client';
import { getCookie } from 'cookies-next';
import { CriStructview, Domain, Process, Theme } from '@/services/schemas';
import ability from '@/app/ability';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckSquare, faXmarkCircle } from '@fortawesome/free-regular-svg-icons';
import { Can } from '@/app/Can';

export default function GenericTableOpportunitySheet<T extends MRT_RowData>(data_: { isLoading: any; data_: any, error: any, data_type: any | undefined, pagination: any }) {
  const user = JSON.parse(getCookie('user')?.toString() || '{}')
  const toast = useRef<any>(null);
  const [theme_id, setThemeId] = useState(0);
  const { push } = useRouter();

  const [visible, setVisible] = useState(false);

  const { data: data_create, error: error_create, isMutating: isMutating_create, trigger: trigger_create } = useApiThemeCreate({
    axios: { headers: { Authorization: `Token ${user.token}` } }, swr: {

      revalidate: true,
      populateCache: true,
    }
  })
  const { data: data_modify, error: error_modify, isMutating: isMutating_modify, trigger: trigger_modify } = useApiThemeUpdate(theme_id, {
    axios: { headers: { Authorization: `Token ${user.token}` } }, swr: {

      revalidate: true,
      populateCache: true,
    }
  })
  const { data: data_delete, error: error_delete, isMutating: isMutating_delete, trigger: trigger_delete } = useApiThemeDestroy(theme_id, {
    axios: { headers: { Authorization: `Token ${user.token}` } }, swr: {

      revalidate: true,
      populateCache: true,
    }
  })
  const [detailsDialogVisible, setDetailsDialogVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  function onPaginationChange(state: any) {
    console.log("PAGINATION", data_.pagination);
    data_.pagination.set(state)
  };
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState(1);

  const getSeverity = (str: string) => {
    switch (str) {
      case 'Vice Président':
        return 'success';

      case 'Contrôle Interne':
        return 'warning';
      case 'Audit Interne':
        return 'warning';
      case 'Structures':
        return 'danger';

      default:
        return null;
    }
  };
  const accept = () => {
    trigger_delete(
      {},
      {

        onSuccess: () => toast.current?.show({ severity: 'info', summary: 'Suppression', detail: 'Enregistrement supprimé' }),
        onError: (error) => toast.current?.show({ severity: 'info', summary: 'Suppression', detail: `${error.code}` }),
      }
    )
  };

  const reject = () => {
    toast.current.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
  };

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setPageNumber(1);
  }
  function changePage(offset: number) {
    setPageNumber(prevPageNumber => prevPageNumber + offset);
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [rowActionEnabled, setRowActionEnabled] = useState(false);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  useEffect(() => {
    console.log("theme_id", theme_id);
  }, [theme_id]);
  const columns = useMemo<MRT_ColumnDef<T>[]>(
    () =>
      Object.entries(data_.data_type.properties).filter(([key, value], index) => !['modified_by', 'created_by', 'risks', 'goals'].includes(key)).map(([key, value], index) => {
        if (['report', 'content', 'note', 'order', 'comment', 'description'].includes(key)) {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Cell: ({ cell }) => { if (["description", "content", "report"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },
            Edit: ({ cell, column, row, table }) => {
              return <Editor
                onChange={(e) => { row._valuesCache.content = e.target.getContent() }}
                initialValue={row.original[key]}
                tinymceScriptSrc="http://localhost:3000/tinymce/tinymce.min.js"
                apiKey='none'
                init={{
                  height: 500,
                  menubar: true,
                  plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'print', 'preview', 'anchor',
                    'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'paste', 'code', 'help', 'wordcount'
                  ],
                  toolbar:
                    'undo redo | formatselect | bold italic backcolor | \
                        alignleft aligncenter alignright alignjustify | \
                        bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen'
                  ,


                }}
              />
                ;
            },
          }
        }
        if (data_.data_type.properties[key].format === 'date-time' || data_.data_type.properties[key].format === 'date') {
          return {
            accessorFn: (row) => new Date(row[key]),
            header: data_.data_type.properties[key].title ?? key,
            filterVariant: 'date',
            filterFn: 'lessThan',
            sortingFn: 'datetime',
            accessorKey: key,
            Cell: ({ cell }) => (new Date(cell.getValue<string>())).toLocaleDateString('fr'),
            id: key,
          }
        }
        if (key === "concerned_structures") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => cell.getValue<CriStructview[]>().length > 0 ? <Stack direction={'row'} spacing={1}>{cell.getValue<CriStructview[]>().map((struct: CriStructview) => <Chip style={{ backgroundColor: 'var(--pink-300)', color: 'black', fontWeight: 'bold' }} label={struct?.code_mnemonique ?? struct?.code_stru}></Chip>)}</Stack> : '/'

          }
        }

        if (key === "proposing_structures") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => cell.getValue<CriStructview[]>().length > 0 ? <Stack direction={'row'} spacing={1}>{cell.getValue<CriStructview[]>().map((struct: CriStructview) => <Chip style={{ backgroundColor: 'var(--cyan-300)', color: 'black', fontWeight: 'bold' }} label={struct?.code_mnemonique ?? struct?.code_stru}></Chip>)}</Stack> : '/'


          }
        }
        if (key === "proposed_by") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Tag
              className='w-11rem text-sm'
              key={row.original.code + row.original.created}
              severity={getSeverity(cell.getValue<string>())}
              value={cell.getValue<string>()}
            >

            </Tag>
          }
        }
        if (key === "validated") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableFooterCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            size: 120,
            id: key,
            Cell: ({ cell, row }) => cell.getValue<boolean>() ? <FontAwesomeIcon className='text-green-500' size='2x' icon={faCheckSquare} /> : <FontAwesomeIcon className='text-red-500' size='2x' icon={faXmarkCircle} />

          }
        }
        if (key === "etat") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,
            muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',
              // children: data_.data_type.properties[key]['$ref'].enum,
              SelectProps: {
                // multiple: true,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            id: key,
            Cell: ({ cell, row }) => <Tag
              key={row.original.code + row.original.created}
              severity={cell.getValue<string>() === "Non lancée" ?
                "danger" : cell.getValue<string>() === "En cours" ? "success" : "info"
              }
              value={cell.getValue<string>()}
            >

            </Tag>
          }
        }
        if (key === "type") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },

            editSelectOptions: data_.data_type.properties[key]['$ref'].enum
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                key={row.original.code + row.original.created}
                severity={
                  cell.getValue<string>() === "Commandée" ?
                    "danger" : cell.getValue<string>() === "Planifiée" ? "warning" : cell.getValue<string>() === 'AI' ? "info" : 'success'
                }
                value={cell.getValue<string>()}
              >

              </Tag>
          }

        }
        if (key === "exercise") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },

            editSelectOptions: data_.data_type.properties[key]['$ref'].enum
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                key={row.original.code + row.original.created}
                severity={
                  'success'
                }
                value={cell.getValue<string>()}
              >

              </Tag>
          }

        }
        if (key === "title") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'left',
            },
            muiTableBodyCellProps: {
              align: 'left',
            },
            // editSelectOptions: data_.data_type.properties[key]['$ref'].enum , 
            muiEditTextFieldProps: {
              // select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <div className='white-space-normal'>{cell.getValue()}</div>

          }
        }
        if (key === 'domain') {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableFooterCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => cell.getValue<Domain>()?.title ?? '/'
          }
        }
        if (key === 'process') {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableFooterCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => cell.getValue<Process>()?.title ?? '/'
          }
        }
        else {
          if (key === "id") return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Edit: () => null,
          }; else
            return {
              header: data_.data_type.properties[key].title ?? key,
              accessorKey: key,
              id: key,
              // Edit: () => null,
            };

        }
      }),
    [],
  );

  const table = useMaterialReactTable<T>({
    columns,
    data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [data_.data_.data], //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,
    enableRowSelection: true, //enable some features
    enableColumnOrdering: true, //enable a feature for all columns
    enableGlobalFilter: true, //turn off a feature
    enableGrouping: true,
    enableRowActions: true,
    enableRowPinning: true,
    enableStickyHeader: true,
    enableStickyFooter: true,
    enableColumnPinning: true,
    enableColumnResizing: true,
    enableRowNumbers: true,
    enableExpandAll: true,
    enableEditing: true,
    enableExpanding: false,
    manualPagination: true,
    initialState: {
      pagination: { pageSize: 5, pageIndex: 1 },
      columnVisibility: { created_by: false, created: false, modfied_by: false, modified: false, modified_by: false, staff: false, assistants: false, id: false, document: false },
      density: 'compact',
      showGlobalFilter: true,
      sorting: [{ id: 'id', desc: false }],
    },
    state: {
      pagination: data_.pagination.pagi,
      isLoading: data_.isLoading, //cell skeletons and loading overlay
      //showProgressBars: isLoading, //progress bars while refetching
      isSaving: isMutating_create, //progress bars and save button spinners
    },
    localization: MRT_Localization_FR,
    onPaginationChange: onPaginationChange,
    displayColumnDefOptions: {
      'mrt-row-pin': {
        enableHiding: true,
      },
      'mrt-row-expand': {
        enableHiding: true,
      },
      'mrt-row-actions': {
        size: 140,
        enableHiding: true,
        grow: true
      },
      'mrt-row-numbers': {
        enableHiding: true, //now row numbers are hidable too
      },
    },
    defaultColumn: {
      grow: true,
      enableMultiSort: true,
    },
    muiTablePaperProps: ({ table }) => ({
      //elevation: 0, //change the mui box shadow
      //customize paper styles
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      classes: { root: 'p-datatable-gridlines text-900 font-medium text-xl' },
      sx: {
        height: `calc(100vh - 9rem)`,
        // height: `calc(100vh - 200px)`,
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        "& .MuiTablePagination-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
        "& .MuiSvgIcon-root": {
          color: "var(--surface-900) !important",
        },
        "& .MuiInputBase-input": {
          color: "var(--surface-900) !important",
        },
        "& .MuiTableSortLabel-root": {
          color: "var(--surface-900) !important",
        },
        "& .MuiBox-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
      },
    }),
    // editDisplayMode: 'modal',
    // createDisplayMode: 'modal',
    onEditingRowSave: ({ table, row, values }) => {
      console.log("onEditingRowSave", values)
      const { created, modified, id, ...rest } = values;
      setThemeId(id)
      trigger_modify(
        rest,
        {
          revalidate: true,
          populateCache: true,
          onSuccess: () => {
            table.setEditingRow(null); //exit editing mode
            toast.current?.show({ severity: 'success', summary: 'Modification', detail: 'Enregistrement modifié' });
          },
          onError: (error) => {
            toast.current?.show({ severity: 'error', summary: 'Modification', detail: `${error.response?.statusText}` });
            console.log("onEditingRowSave", error.response);
            row._valuesCache = { error: error.response, ...row._valuesCache };
            return;
          },
        }
      )

    },
    onEditingRowCancel: () => {
      //clear any validation errors
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });
    },
    onCreatingRowSave: ({ table, values, row }) => {
      console.log("onCreatingRowSave", values)
      const { created, modified, id, ...rest } = values;
      trigger_create(
        rest,
        {
          revalidate: true,
          populateCache: true,
          onSuccess: () => {
            table.setCreatingRow(null);
            toast.current?.show({ severity: 'success', summary: 'Création', detail: 'Enregistrement créé' });
          },
          onError: (err) => {
            toast.current?.show({ severity: 'error', summary: 'Création', detail: `${err.response?.statusText}` });
            console.log("onCreatingRowSave", err.response);
            row._valuesCache = { error: err.response, ...row._valuesCache };
            return;
          },
        }
      )
    },
    onCreatingRowCancel: () => {
      //clear any validation errors
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });
    },
    muiEditRowDialogProps: ({ row, table }) => ({
      //optionally customize the dialog
      // about:"edit modal",
      open: editVisible || createVisible,
      sx: {
        '& .MuiDialog-root': {
          // display: 'none'

        },
        '& .MuiDialog-container': {
          //  display: 'none',
          "& .MuiPaper-root": {
            maxWidth: '60vw'
          }

        },
        zIndex: 1100

      }
    }),
    muiTableFooterProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        "& .MuiTableFooter-root": {
          backgroundColor: "var(--surface-card) !important",
        },
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableContainerProps: ({ table }) => ({
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        height: table.getState().isFullScreen ? `calc(100vh)` : `calc(100vh - 9rem - ${table.refs.topToolbarRef.current?.offsetHeight}px - ${table.refs.bottomToolbarRef.current?.offsetHeight}px)`

      },
    }),
    muiPaginationProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {

        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableHeadCellProps: {
      sx: {
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTopToolbarProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important"
      },

    },
    muiTableBodyProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        //stripe the rows, make odd rows a darker color
        '& tr:nth-of-type(odd) > td': {
          backgroundColor: 'var(--surface-card)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
        '& tr:nth-of-type(even) > td': {
          backgroundColor: 'var(--surface-border)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
      },
    },
    renderTopToolbarCustomActions: ({ table }) => (
      <Stack direction={"row"} spacing={1}>
        <Can I='add' a='theme'>
          <Button
            icon="pi pi-plus"
            rounded
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={(event) => {
              table.setCreatingRow(true); setCreateVisible(true), console.log("creating row ...");
            }}
            size="small"
          />
        </Can>
        <Can I='delete' a='theme'>
          <Button
            rounded
            disabled={table.getIsSomeRowsSelected()}
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
            icon="pi pi-trash"
            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}
            size="small"
          />
        </Can>
        <Can I='add' a='arbitratedtheme'>
          <Button icon="pi pi-bell" rounded color={rowActionEnabled ? "secondary" : "primary"} size="small"
            aria-label="edit" onClick={() => setRowActionEnabled(!rowActionEnabled)} />
        </Can>
      </Stack>
    ),
    muiDetailPanelProps: () => ({
      sx: (theme) => ({
        backgroundColor:
          theme.palette.mode === 'dark'
            ? 'rgba(255,210,244,0.1)'
            : 'rgba(0,0,0,0.1)',
      }),
    }),
    renderCreateRowDialogContent: RecommendationEditForm,
    renderEditRowDialogContent: RecommendationEditForm,
    // renderDetailPanel: ({ row }) =>
    //   row.original.staff ?
    //     (
    //       <Box
    //         sx={{
    //           display: 'grid',
    //           margin: 'auto',
    //           //gridTemplateColumns: '1fr 1fr',
    //           width: '100vw',
    //         }}
    //       >
    //         <TabView>

    //           <TabPanel header={data_.data_type.properties["staff"].title} leftIcon="pi pi-user mr-2">

    //             <ul>{row.original.staff.staff.map((user, idx) => <a key={user.email + row.original.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>
    //           </TabPanel>
    //           <TabPanel header={"Assistants"} rightIcon="pi pi-user ml-2">

    //             <ul>{row.original.staff.assistants.map((user, idx) => <a key={user.email + row.original.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul>

    //           </TabPanel>
    //           <TabPanel header="Lettre" leftIcon="pi pi-file-word mr-2" rightIcon="pi pi-file-pdf ml-2">
    //             <Button icon="pi pi-check" rounded onClick={() => setVisible(true)} disabled={row.original.document === null} />
    //             <Sidebar key={row.original.id} header={<h2>Lettre de mission : {row.original.code}</h2>} visible={visible} onHide={() => setVisible(false)} className="w-full md:w-9 lg:w-8">

    //               <div className="flex flex-column	align-items-center justify-content-center gap-1">
    //                 {row.original.document !== null ?
    //                   <Document
    //                     file={row.original.document}
    //                     onLoadSuccess={onDocumentLoadSuccess}
    //                   >
    //                     <Page pageNumber={pageNumber} />
    //                   </Document> : <p>No Document</p>}
    //                 <div className='flex flex-column	align-items-center justify-content-center gap-1' >
    //                   <p>
    //                     Page {pageNumber || (numPages ? 1 : '--')} of {numPages || '--'}
    //                   </p>
    //                   <div className='flex flex-row	align-items-center justify-content-center gap-1' >
    //                     <Button
    //                       type="button"
    //                       disabled={pageNumber <= 1}
    //                       onClick={previousPage}
    //                     >
    //                       Previous
    //                     </Button>
    //                     <Button
    //                       type="button"
    //                       disabled={pageNumber >= numPages}
    //                       onClick={nextPage}
    //                     >
    //                       Next
    //                     </Button>
    //                   </div>
    //                 </div>
    //               </div>
    //             </Sidebar>
    //           </TabPanel>          </TabView>
    //       </Box >
    //     ) : <></>,
    renderRowActions: ({ cell, row, table }) => (
      <span className="p-buttonset flex p-1">
        <Button size='small' icon="pi pi-eye" onClick={() => { setThemeId(row.original.id); setDetailsDialogVisible(true) }} rounded outlined />
        {ability.can('change','theme') && 
        <Button size='small' icon="pi pi-pencil" onClick={() => { setThemeId(row.original.id); table.setEditingRow(row); setEditVisible(true), console.log("editing row ..."); }} rounded outlined />
         } 
        {ability.can('delete','theme') && <Button size='small' icon="pi pi-trash" rounded outlined
          onClick={(event) => {
            setThemeId(row.original.id); confirmPopup({
              target: event.currentTarget,
              message: 'Voulez-vous supprimer cette ligne?',
              icon: 'pi pi-info-circle',
              // defaultFocus: 'reject',
              acceptClassName: 'p-button-danger',
              acceptLabel: 'Oui',
              rejectLabel: 'Non',
              accept,
              reject
            })
          }}
        />}
        <ConfirmPopup />
      </span>
      // </Box>
    ),
    // mrtTheme: (theme) => ({
    //   baseBackgroundColor: "#1f2937",
    //   draggingBorderColor: theme.palette.secondary.main,
    // }),

  });

  return (<>
    <MaterialReactTable table={table} />
    <Toast ref={toast} />
    <Dialog maximizable header={`THEME ID : ${theme_id}`} visible={detailsDialogVisible} style={{ width: '90vw' }} onHide={() => { if (!detailsDialogVisible) return; setDetailsDialogVisible(false); }}>
      {/* <RecommendationDetails params={{ recommendation_id: recommendation_id }} /> */}
    </Dialog>
  </>);
}
