"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx":
/*!*******************************************************************!*\
  !*** ./app/(main)/plans/(components)/GenericTAbleArbitration.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_hooks_useBaseData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/hooks/useBaseData */ \"(app-client)/./utilities/hooks/useBaseData.tsx\");\n/* harmony import */ var _utilities_functions_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utilities/functions/utils */ \"(app-client)/./utilities/functions/utils.tsx\");\n/* harmony import */ var primereact_editor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/editor */ \"(app-client)/./node_modules/primereact/editor/editor.esm.js\");\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// import { Editor } from '@tinymce/tinymce-react';\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie, _users_data, _users_data1, _plans_data, _arbitrations_data;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { plans, users, arbitrations } = (0,_utilities_hooks_useBaseData__WEBPACK_IMPORTED_MODULE_4__.usebaseData)();\n    const users_data = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _users_data;\n        return (_users_data = users.data) === null || _users_data === void 0 ? void 0 : _users_data.data;\n    }, [\n        (_users_data = users.data) === null || _users_data === void 0 ? void 0 : _users_data.data\n    ]);\n    const [arbitrationID, setArbitrationID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [picklistTargetValueTeam, setPicklistTargetValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [picklistSourceValueTeam, setPicklistSourceValueTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(users_data);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { mutate: arbitration_create_trigger, isPending: isCreateMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__.useApiArbitrationCreate)();\n    const { mutate: arbitration_patch_trigger, isPending: isPatchMutating } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__.useApiArbitrationPartialUpdate)();\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const accept_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject_row_deletion = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\",\n                \"created\",\n                \"modified\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    // Cell: ({ cell }) => <div>{parse(cell.getValue<string>())}</div>,\n                    // Cell: ({ cell }) => { if ([\"description\", \"content\",\"report\"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Rapport\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_editor__WEBPACK_IMPORTED_MODULE_8__.Editor, {\n                                    // initialValue={row.original[key]}\n                                    // tinymceScriptSrc=\"http://localhost:3000/tinymce/tinymce.min.js\"\n                                    // apiKey='none'\n                                    value: row.original.report,\n                                    // onChange={(e) => { row._valuesCache.report = e.target.getContent() }}\n                                    onTextChange: (e)=>{\n                                        row._valuesCache.report = e.htmlValue;\n                                    },\n                                    style: {\n                                        height: \"320px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (key === \"plan\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"plan\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_9__.Tag, {\n                            className: \"w-11rem text-sm\",\n                            children: cell.getValue().code\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 38\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { row } = param;\n                        var _row__valuesCache_plan, _row__valuesCache_plan1, _plans_data, _plans;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"font-bold\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n                                    optionLabel: \"name\",\n                                    placeholder: \"Choisir un plan\",\n                                    onChange: (e)=>{\n                                        var _plans_data, _plans, _plans_data1, _plans1;\n                                        console.log(e);\n                                        setRowTobe({\n                                            ...rowTobe,\n                                            plan: (_plans = plans) === null || _plans === void 0 ? void 0 : (_plans_data = _plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.data.results.find((plan)=>plan.id === e.value.code)\n                                        });\n                                        row._valuesCache = {\n                                            ...row._valuesCache,\n                                            plan: (_plans1 = plans) === null || _plans1 === void 0 ? void 0 : (_plans_data1 = _plans1.data) === null || _plans_data1 === void 0 ? void 0 : _plans_data1.data.results.find((plan)=>plan.id === e.value.code)\n                                        };\n                                    },\n                                    value: {\n                                        code: ((_row__valuesCache_plan = row._valuesCache.plan) === null || _row__valuesCache_plan === void 0 ? void 0 : _row__valuesCache_plan.id) || null,\n                                        name: ((_row__valuesCache_plan1 = row._valuesCache.plan) === null || _row__valuesCache_plan1 === void 0 ? void 0 : _row__valuesCache_plan1.code) || null\n                                    },\n                                    options: (_plans = plans) === null || _plans === void 0 ? void 0 : (_plans_data = _plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.data.results.map((plan)=>{\n                                        return {\n                                            code: plan.id,\n                                            name: plan.code\n                                        };\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            var _data__data_type_properties_key_title3;\n            if (key === \"id\") return {\n                header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                accessorKey: key,\n                id: key,\n                Edit: ()=>null\n            };\n            var _data__data_type_properties_key_title4;\n            if (key === \"team\") return {\n                header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                accessorKey: key,\n                id: key,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: cell.getValue().map((usr)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: (0,_utilities_functions_utils__WEBPACK_IMPORTED_MODULE_5__.getUserFullname)(usr)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    console.log(\"[ARBITRATION]\", row._valuesCache.team);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"font-bold\",\n                                children: \"Membres\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_11__.PickList, {\n                                source: picklistTargetValueTeam.length === 0 ? picklistSourceValueTeam : picklistSourceValueTeam.filter((user)=>picklistTargetValueTeam.map((user)=>user.username).includes(user.username)),\n                                id: \"picklist_team\",\n                                target: picklistTargetValueTeam.length > 0 ? picklistTargetValueTeam : row._valuesCache.team,\n                                sourceHeader: \"De\",\n                                targetHeader: \"A\",\n                                itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            item.first_name,\n                                            \" \",\n                                            item.last_name\n                                        ]\n                                    }, item.username, true, void 0, void 0),\n                                onChange: (e)=>{\n                                    console.log(\"source Team\", e.source);\n                                    setPicklistSourceValueTeam([\n                                        ...e.source\n                                    ]);\n                                    setPicklistTargetValueTeam([\n                                        ...e.target\n                                    ]);\n                                    row._valuesCache.team = e.target;\n                                },\n                                sourceStyle: {\n                                    height: \"200px\"\n                                },\n                                targetStyle: {\n                                    height: \"200px\"\n                                },\n                                filter: true,\n                                filterBy: \"username,email,first_name,last_name\",\n                                filterMatchMode: \"contains\",\n                                sourceFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\",\n                                targetFilterPlaceholder: \"Rechercher par nom & pr\\xe9nom\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true);\n                }\n            };\n            else {\n                var _data__data_type_properties_key_title5;\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), [\n        (_users_data1 = users.data) === null || _users_data1 === void 0 ? void 0 : _users_data1.data,\n        (_plans_data = plans.data) === null || _plans_data === void 0 ? void 0 : _plans_data.data,\n        (_arbitrations_data = arbitrations.data) === null || _arbitrations_data === void 0 ? void 0 : _arbitrations_data.data\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_12__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data ? data_.data_.data : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data ? data_.data_.data.length : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                report: false,\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_13__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onEditingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_patch_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setEditingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" mis \\xe0 ajour\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values, row } = param;\n            var _values_team;\n            console.log(\"onCreatingRowSave\", values);\n            const { id, ...rest } = values;\n            rest.plan = values.plan.id;\n            rest.team = ((_values_team = values.team) === null || _values_team === void 0 ? void 0 : _values_team.map((user)=>user.id)) || [];\n            arbitration_create_trigger(rest, {\n                revalidate: true,\n                onSuccess: ()=>{\n                    table.setCreatingRow(null); //exit creating mode\n                    toast.current.show({\n                        severity: \"success\",\n                        summary: \"Info\",\n                        detail: \"Arbitrage \".concat(values.plan.code, \" cr\\xe9\\xe9\")\n                    });\n                },\n                onError: (error)=>{\n                    var _error_response;\n                    toast.current.show({\n                        severity: \"error\",\n                        summary: \"Info\",\n                        detail: \"\".concat((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.statusText)\n                    });\n                    //console.log(\"onEditingRowSave\", error.response);\n                    row._valuesCache = {\n                        error: error.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: ()=>{\n            var _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_6__.Can, {\n                        I: \"add\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            icon: \"pi pi-plus\",\n                            rounded: true,\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: (event)=>{\n                                table.setCreatingRow(true);\n                                setCreateVisible(true), console.log(\"creating row ...\");\n                            },\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_6__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            rounded: true,\n                            disabled: table.getIsSomeRowsSelected(),\n                            // id=\"basic-button\"\n                            \"aria-controls\": open ? \"basic-menu\" : undefined,\n                            \"aria-haspopup\": \"true\",\n                            \"aria-expanded\": open ? \"true\" : undefined,\n                            onClick: handleClick,\n                            icon: \"pi pi-trash\",\n                            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                            size: \"small\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"align-content-center \",\n                                children: \"Cr\\xe9ation nouveau arbitrage\"\n                            }, void 0, false, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: createVisible,\n                    onHide: ()=>{\n                        table.setCreatingRow(null);\n                        setCreateVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: internalEditComponents\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 491,\n                columnNumber: 7\n            }, this);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    zIndex: \"1302 !important\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_16__.Sidebar, {\n                    position: \"right\",\n                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"align-content-center \",\n                                children: [\n                                    \"Editer l'arbitrage n\\xb0 \",\n                                    row.original.id\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MRT_EditActionButtons, {\n                                    variant: \"text\",\n                                    table: table,\n                                    row: row\n                                }, void 0, false, void 0, void 0)\n                            }, void 0, false, void 0, void 0)\n                        ]\n                    }, void 0, true, void 0, void 0),\n                    visible: editVisible,\n                    onHide: ()=>{\n                        table.setEditingRow(null);\n                        setEditVisible(false);\n                    },\n                    className: \"w-full md:w-9 lg:w-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"1.5rem\"\n                        },\n                        children: [\n                            internalEditComponents,\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                    lineNumber: 517,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return (0,html_react_parser__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(row.original.report);\n        },\n        renderRowActions: (param)=>/*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_6__.Can, {\n                        I: \"update\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-pencil\",\n                            onClick: ()=>{\n                                setArbitrationID(row.original.id);\n                                table.setEditingRow(row);\n                                setEditVisible(true);\n                                console.log(\"editing row ...\");\n                            },\n                            rounded: true,\n                            outlined: true\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_6__.Can, {\n                        I: \"delete\",\n                        a: \"arbitration\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            size: \"small\",\n                            icon: \"pi pi-trash\",\n                            rounded: true,\n                            outlined: true,\n                            onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__.confirmPopup)({\n                                    target: event.currentTarget,\n                                    message: \"Voulez-vous supprimer cette ligne?\",\n                                    icon: \"pi pi-info-circle\",\n                                    // defaultFocus: 'reject',\n                                    acceptClassName: \"p-button-danger\",\n                                    acceptLabel: \"Oui\",\n                                    rejectLabel: \"Non\",\n                                    accept: accept_row_deletion,\n                                    reject: reject_row_deletion\n                                })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_19__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 542,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_12__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 570,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_20__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\(components)\\\\GenericTAbleArbitration.tsx\",\n                lineNumber: 570,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"DTmNspyLLrTWiRWa5mJCpRSYQwI=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__.useApiArbitrationCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_7__.useApiArbitrationPartialUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_12__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1jbGllbnQpLy4vYXBwLyhtYWluKS9wbGFucy8oY29tcG9uZW50cykvR2VuZXJpY1RBYmxlQXJiaXRyYXRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3lFO0FBTzNDO0FBQ3dDO0FBQzNCO0FBQzBCO0FBQ3hCO0FBRVI7QUFDSTtBQUNTO0FBQ1o7QUFDdEMsbURBQW1EO0FBQ0o7QUFDQTtBQUNOO0FBQ21CO0FBQ0U7QUFDbkI7QUFDWDtBQUM2RDtBQUc5RSxTQUFTMEIsYUFBb0NDLEtBQThGO1FBQ2hJUixZQUs0QlMsYUF5TGpEQSxjQUFrQkMsYUFBa0JDOztJQTlMdkMsTUFBTUMsT0FBT0MsS0FBS2hCLEtBQUssQ0FBQ0csRUFBQUEsYUFBQUEsdURBQVNBLENBQUMscUJBQVZBLGlDQUFBQSxXQUFtQmMsUUFBUSxPQUFNO0lBRXpELE1BQU1DLFFBQVFwQiw2Q0FBTUEsQ0FBZTtJQUNuQyxNQUFNLEVBQUVlLEtBQUssRUFBRUQsS0FBSyxFQUFFRSxZQUFZLEVBQUUsR0FBR1YseUVBQVdBO0lBRWxELE1BQU1lLGFBQWF0Qiw4Q0FBT0EsQ0FBQztZQUFNZTtnQkFBQUEsY0FBQUEsTUFBTVEsSUFBSSxjQUFWUixrQ0FBQUEsWUFBWVEsSUFBSTtPQUFFO1NBQUNSLGNBQUFBLE1BQU1RLElBQUksY0FBVlIsa0NBQUFBLFlBQVlRLElBQUk7S0FBQztJQUNyRSxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHdkIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDd0IsYUFBYUMsZUFBZSxHQUFHekIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDMEIsZUFBZUMsaUJBQWlCLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUM0Qix5QkFBeUJDLDJCQUEyQixHQUFHN0IsK0NBQVFBLENBQUMsRUFBRTtJQUN6RSxNQUFNLENBQUM4Qix5QkFBeUJDLDJCQUEyQixHQUFHL0IsK0NBQVFBLENBQUNvQjtJQUN2RSxNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBR2pDLCtDQUFRQSxDQUFDLENBQUM7SUFDeEMsTUFBTSxFQUFFa0MsUUFBUUMsMEJBQTBCLEVBQUVDLFdBQVdDLGdCQUFnQixFQUFFLEdBQUc1QiwwRUFBdUJBO0lBQ25HLE1BQU0sRUFBRXlCLFFBQVFJLHlCQUF5QixFQUFFRixXQUFXRyxlQUFlLEVBQUUsR0FBRzdCLGlGQUE4QkE7SUFDeEcsTUFBTSxDQUFDOEIsVUFBVUMsWUFBWSxHQUFHekMsK0NBQVFBLENBQWdCO0lBQ3hELE1BQU0sQ0FBQzBDLFlBQVlDLGNBQWMsR0FBRzNDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQzRDLFVBQVVDLFlBQVksR0FBRzdDLCtDQUFRQSxDQUFxQjtJQUM3RCxNQUFNLENBQUM4QyxrQkFBa0JDLG9CQUFvQixHQUFHL0MsK0NBQVFBLENBQUM7SUFDekQsTUFBTWdELE9BQU9DLFFBQVFMO0lBRXJCLFNBQVNNLG1CQUFtQkMsS0FBVTtRQUNwQ0MsUUFBUUMsR0FBRyxDQUFDekMsTUFBTTBDLFVBQVU7UUFDNUIxQyxNQUFNMEMsVUFBVSxDQUFDQyxHQUFHLENBQUNKO0lBQ3ZCO0lBQ0EsU0FBU0ssc0JBQXNCLEtBQWtDO1lBQWxDLEVBQUVoQixRQUFRLEVBQXdCLEdBQWxDO1FBQzdCQyxZQUFZRDtRQUNaRyxjQUFjO0lBQ2hCO0lBQ0EsU0FBU2MsV0FBV0MsTUFBYztRQUNoQ2YsY0FBY2dCLENBQUFBLGlCQUFrQkEsaUJBQWlCRDtJQUNuRDtJQUNBLFNBQVNFO1FBQ1BILFdBQVcsQ0FBQztJQUNkO0lBQ0EsU0FBU0k7UUFDUEosV0FBVztJQUNiO0lBQ0EsTUFBTUssc0JBQXNCO1lBQzFCM0M7U0FBQUEsaUJBQUFBLE1BQU00QyxPQUFPLGNBQWI1QyxxQ0FBQUEsZUFBZTZDLElBQUksQ0FBQztZQUFFQyxVQUFVO1lBQVFDLFNBQVM7WUFBYUMsUUFBUTtZQUFxQkMsTUFBTTtRQUFLO0lBQ3hHO0lBQ0EsTUFBTUMsc0JBQXNCO1lBQzFCbEQ7U0FBQUEsaUJBQUFBLE1BQU00QyxPQUFPLGNBQWI1QyxxQ0FBQUEsZUFBZTZDLElBQUksQ0FBQztZQUFFQyxVQUFVO1lBQVFDLFNBQVM7WUFBWUMsUUFBUTtZQUFxQkMsTUFBTTtRQUFLO0lBQ3ZHO0lBRUEsTUFBTUUsY0FBYyxDQUFDQztRQUNuQjFCLFlBQVkwQixNQUFNQyxhQUFhO0lBQ2pDO0lBRUEsTUFBTUMsVUFBVTNFLDhDQUFPQSxDQUNyQixJQUNFNEUsT0FBT0MsT0FBTyxDQUFDL0QsTUFBTWdFLFNBQVMsQ0FBQ0MsVUFBVSxFQUFFQyxNQUFNLENBQUMsUUFBZUM7Z0JBQWQsQ0FBQ0MsS0FBS0MsTUFBTTttQkFBWSxDQUFDO2dCQUFDO2dCQUFlO2dCQUFjO2dCQUFXO2FBQVksQ0FBQ0MsUUFBUSxDQUFDRjtRQUFHLEdBQUdHLEdBQUcsQ0FBQyxRQUFlSjtnQkFBZCxDQUFDQyxLQUFLQyxNQUFNO1lBQ2hLLElBQUk7Z0JBQUM7Z0JBQVU7Z0JBQVc7Z0JBQVE7Z0JBQVM7Z0JBQVc7YUFBYyxDQUFDQyxRQUFRLENBQUNGLE1BQU07b0JBRXhFcEU7Z0JBRFYsT0FBTztvQkFDTHdFLFFBQVF4RSxDQUFBQSx3Q0FBQUEsTUFBTWdFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckN6RSxtREFBQUEsd0NBQXlDb0U7b0JBQ2pETSxhQUFhTjtvQkFDYk8sSUFBSVA7b0JBQ0osbUVBQW1FO29CQUNuRSx1SkFBdUo7b0JBQ3ZKUSxNQUFNOzRCQUFDLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFFQyxHQUFHLEVBQUVDLEtBQUssRUFBRTt3QkFDakMscUJBQ0U7OzhDQUNFLDhEQUFDQztvQ0FBTUMsV0FBVTs4Q0FBWTs7Ozs7OzhDQUM3Qiw4REFBQ3ZGLHFEQUFNQTtvQ0FDTCxtQ0FBbUM7b0NBQ25DLGtFQUFrRTtvQ0FDbEUsZ0JBQWdCO29DQUNoQjBFLE9BQU9VLElBQUlJLFFBQVEsQ0FBQ0MsTUFBTTtvQ0FDMUIsd0VBQXdFO29DQUN4RUMsY0FBYyxDQUFDQzt3Q0FBUVAsSUFBSVEsWUFBWSxDQUFDSCxNQUFNLEdBQUdFLEVBQUVFLFNBQVM7b0NBQUM7b0NBQzdEQyxPQUFPO3dDQUFFQyxRQUFRO29DQUFROzs7Ozs7OztvQkFrQmpDO2dCQUNGO1lBQ0Y7WUFDQSxJQUFJdEIsUUFBUSxRQUFRO29CQUVScEU7Z0JBRFYsT0FBTztvQkFDTHdFLFFBQVF4RSxDQUFBQSx5Q0FBQUEsTUFBTWdFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckN6RSxvREFBQUEseUNBQXlDb0U7b0JBQ2pETSxhQUFhO29CQUViaUIsdUJBQXVCO3dCQUNyQkMsT0FBTztvQkFDVDtvQkFDQUMsdUJBQXVCO3dCQUNyQkQsT0FBTztvQkFDVDtvQkFFQUUseUJBQXlCO3dCQUN2QkYsT0FBTztvQkFDVDtvQkFDQUcsTUFBTTs0QkFBQyxFQUFFbEIsSUFBSSxFQUFFRSxHQUFHLEVBQUU7K0JBQUssOERBQUMvRiwrQ0FBR0E7NEJBQUNrRyxXQUFVO3NDQUFtQkwsS0FBS21CLFFBQVEsR0FBU0MsSUFBSTs7Ozs7O29CQUFNO29CQUMzRnJCLE1BQU07NEJBQUMsRUFBRUcsR0FBRyxFQUFFOzRCQVdPQSx3QkFBeUNBLHlCQUMvQzdFLGFBQUFBOzZDQVhiOzs4Q0FDRSw4REFBQytFO29DQUFNQyxXQUFVOzhDQUFZOzs7Ozs7OENBQzdCLDhEQUFDM0YsMERBQVFBO29DQUNQMkcsYUFBWTtvQ0FDWkMsYUFBWTtvQ0FDWkMsVUFBVSxDQUFDZDs0Q0FFc0JwRixhQUFBQSxRQUNpQkEsY0FBQUE7d0NBRmhEc0MsUUFBUUMsR0FBRyxDQUFDNkM7d0NBQ1pqRSxXQUFXOzRDQUFFLEdBQUdELE9BQU87NENBQUVpRixJQUFJLEdBQUVuRyxTQUFBQSxtQkFBQUEsOEJBQUFBLGNBQUFBLE9BQU9PLElBQUksY0FBWFAsa0NBQUFBLFlBQWFPLElBQUksQ0FBQzZGLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLENBQUNGLE9BQWVBLEtBQUsxQixFQUFFLEtBQUtXLEVBQUVqQixLQUFLLENBQUM0QixJQUFJO3dDQUFFO3dDQUN4R2xCLElBQUlRLFlBQVksR0FBRzs0Q0FBRSxHQUFHUixJQUFJUSxZQUFZOzRDQUFFYyxJQUFJLEdBQUVuRyxVQUFBQSxtQkFBQUEsK0JBQUFBLGVBQUFBLFFBQU9PLElBQUksY0FBWFAsbUNBQUFBLGFBQWFPLElBQUksQ0FBQzZGLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDLENBQUNGLE9BQWVBLEtBQUsxQixFQUFFLEtBQUtXLEVBQUVqQixLQUFLLENBQUM0QixJQUFJO3dDQUFFO29DQUMzSDtvQ0FDQTVCLE9BQU87d0NBQUU0QixNQUFNbEIsRUFBQUEseUJBQUFBLElBQUlRLFlBQVksQ0FBQ2MsSUFBSSxjQUFyQnRCLDZDQUFBQSx1QkFBdUJKLEVBQUUsS0FBSTt3Q0FBTTZCLE1BQU16QixFQUFBQSwwQkFBQUEsSUFBSVEsWUFBWSxDQUFDYyxJQUFJLGNBQXJCdEIsOENBQUFBLHdCQUF1QmtCLElBQUksS0FBSTtvQ0FBSztvQ0FDNUZRLE9BQU8sR0FBRXZHLFNBQUFBLG1CQUFBQSw4QkFBQUEsY0FBQUEsT0FBT08sSUFBSSxjQUFYUCxrQ0FBQUEsWUFBYU8sSUFBSSxDQUFDNkYsT0FBTyxDQUFDL0IsR0FBRyxDQUFDLENBQUM4Qjt3Q0FBaUIsT0FBTzs0Q0FBRUosTUFBTUksS0FBSzFCLEVBQUU7NENBQUU2QixNQUFNSCxLQUFLSixJQUFJO3dDQUFDO29DQUFFOzs7Ozs7Ozs7Z0JBSzNHO1lBQ0Y7WUFDQSxJQUFJakcsTUFBTWdFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNzQyxNQUFNLEtBQUssZUFBZTFHLE1BQU1nRSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDc0MsTUFBTSxLQUFLLFFBQVE7b0JBSXJHMUc7Z0JBRlYsT0FBTztvQkFDTDJHLFlBQVksQ0FBQzVCLE1BQVEsSUFBSTZCLEtBQUt4QyxPQUFPLFlBQVlXLElBQUk4QixPQUFPLEdBQUc5QixJQUFJK0IsUUFBUTtvQkFDM0V0QyxRQUFReEUsQ0FBQUEseUNBQUFBLE1BQU1nRSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDekUsb0RBQUFBLHlDQUF5Q29FO29CQUNqRDJDLGVBQWU7b0JBQ2ZDLFVBQVU7b0JBQ1ZDLFdBQVc7b0JBQ1h2QyxhQUFhTjtvQkFDYjJCLE1BQU07NEJBQUMsRUFBRWxCLElBQUksRUFBRTs0QkFBS0E7Z0NBQUFBLGlCQUFBQSxLQUFLbUIsUUFBUSxnQkFBYm5CLHFDQUFBQSxlQUF1QnFDLGtCQUFrQixDQUFDOztvQkFDOUR2QyxJQUFJUDtvQkFDSlEsTUFBTSxJQUFNO2dCQUNkO1lBQ0Y7Z0JBR1U1RTtZQURWLElBQUlvRSxRQUFRLE1BQU0sT0FBTztnQkFDdkJJLFFBQVF4RSxDQUFBQSx5Q0FBQUEsTUFBTWdFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckN6RSxvREFBQUEseUNBQXlDb0U7Z0JBQ2pETSxhQUFhTjtnQkFDYk8sSUFBSVA7Z0JBQ0pRLE1BQU0sSUFBTTtZQUNkO2dCQUVVNUU7WUFEVixJQUFJb0UsUUFBUSxRQUFRLE9BQU87Z0JBQ3pCSSxRQUFReEUsQ0FBQUEseUNBQUFBLE1BQU1nRSxTQUFTLENBQUNDLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSyxLQUFLLGNBQXJDekUsb0RBQUFBLHlDQUF5Q29FO2dCQUNqRE0sYUFBYU47Z0JBQ2JPLElBQUlQO2dCQUNKMkIsTUFBTTt3QkFBQyxFQUFFbEIsSUFBSSxFQUFFRSxHQUFHLEVBQUU7MkJBQUssOERBQUNvQztrQ0FBSXRDLEtBQUttQixRQUFRLEdBQVd6QixHQUFHLENBQUMsQ0FBQzZDLG9CQUFRLDhEQUFDQzswQ0FBSTNILDJFQUFlQSxDQUFDMEg7Ozs7Ozs7Ozs7O2dCQUFnQjtnQkFDeEd4QyxNQUFNO3dCQUFDLEVBQUVHLEdBQUcsRUFBRTtvQkFDWnZDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJzQyxJQUFJUSxZQUFZLENBQUMrQixJQUFJO29CQUNsRCxxQkFBUTs7MENBQ04sOERBQUNyQztnQ0FBTUMsV0FBVTswQ0FBWTs7Ozs7OzBDQUM3Qiw4REFBQzVGLDBEQUFRQTtnQ0FDUGlJLFFBQVF2Ryx3QkFBd0J3RyxNQUFNLEtBQUssSUFBSXRHLDBCQUEwQkEsd0JBQXdCZ0QsTUFBTSxDQUFDOUQsQ0FBQUEsT0FBUVksd0JBQXdCdUQsR0FBRyxDQUFDbkUsQ0FBQUEsT0FBUUEsS0FBS3FILFFBQVEsRUFBRW5ELFFBQVEsQ0FBQ2xFLEtBQUtxSCxRQUFRO2dDQUN6TDlDLElBQUc7Z0NBQ0grQyxRQUFRMUcsd0JBQXdCd0csTUFBTSxHQUFHLElBQUl4RywwQkFBMEIrRCxJQUFJUSxZQUFZLENBQUMrQixJQUFJO2dDQUM1RkssY0FBYTtnQ0FDYkMsY0FBYTtnQ0FDYkMsY0FBYyxDQUFDQyxxQkFBUyw4REFBQ0M7OzRDQUF5QkQsS0FBS0UsVUFBVTs0Q0FBQzs0Q0FBRUYsS0FBS0csU0FBUzs7dUNBQWhESCxLQUFLTCxRQUFRO2dDQUMvQ3JCLFVBQVUsQ0FBQ2Q7b0NBQ1Q5QyxRQUFRQyxHQUFHLENBQUMsZUFBZTZDLEVBQUVpQyxNQUFNO29DQUNuQ3BHLDJCQUEyQjsyQ0FBSW1FLEVBQUVpQyxNQUFNO3FDQUFDO29DQUN4Q3RHLDJCQUEyQjsyQ0FBSXFFLEVBQUVvQyxNQUFNO3FDQUFDO29DQUN4QzNDLElBQUlRLFlBQVksQ0FBQytCLElBQUksR0FBR2hDLEVBQUVvQyxNQUFNO2dDQUNsQztnQ0FDQVEsYUFBYTtvQ0FBRXhDLFFBQVE7Z0NBQVE7Z0NBQy9CeUMsYUFBYTtvQ0FBRXpDLFFBQVE7Z0NBQVE7Z0NBQy9CeEIsTUFBTTtnQ0FBQ2tFLFVBQVM7Z0NBQ2hCQyxpQkFBZ0I7Z0NBQ2hCQyx5QkFBd0I7Z0NBQThCQyx5QkFBd0I7Ozs7Ozs7O2dCQUtwRjtZQUNGO2lCQUNLO29CQUVPdkk7Z0JBRFYsT0FBTztvQkFDTHdFLFFBQVF4RSxDQUFBQSx5Q0FBQUEsTUFBTWdFLFNBQVMsQ0FBQ0MsVUFBVSxDQUFDRyxJQUFJLENBQUNLLEtBQUssY0FBckN6RSxvREFBQUEseUNBQXlDb0U7b0JBQ2pETSxhQUFhTjtvQkFDYk8sSUFBSVA7Z0JBRU47WUFFRjtRQUNGLElBRUY7U0FBQ25FLGVBQUFBLE1BQU1RLElBQUksY0FBVlIsbUNBQUFBLGFBQVlRLElBQUk7U0FBRVAsY0FBQUEsTUFBTU8sSUFBSSxjQUFWUCxrQ0FBQUEsWUFBWU8sSUFBSTtTQUFFTixxQkFBQUEsYUFBYU0sSUFBSSxjQUFqQk4seUNBQUFBLG1CQUFtQk0sSUFBSTtLQUFDO0lBRy9ELE1BQU11RSxRQUFRdEcsNEVBQXFCQSxDQUFDO1FBQ2xDbUY7UUFDQXBELE1BQU1ULE1BQU13SSxLQUFLLEdBQUcsRUFBRSxHQUFHeEksTUFBTUEsS0FBSyxDQUFDUyxJQUFJLEdBQUdULE1BQU1BLEtBQUssQ0FBQ1MsSUFBSSxHQUFHO1lBQUNULE1BQU1BLEtBQUssQ0FBQ1MsSUFBSTtTQUFDO1FBQ2pGZ0ksVUFBVXpJLE1BQU13SSxLQUFLLEdBQUcsSUFBSXhJLE1BQU1BLEtBQUssQ0FBQ1MsSUFBSSxHQUFHVCxNQUFNQSxLQUFLLENBQUNTLElBQUksQ0FBQytHLE1BQU0sR0FBRztRQUN6RWtCLG9CQUFvQjtRQUNwQkMsc0JBQXNCO1FBQ3RCQyxvQkFBb0I7UUFDcEJDLGdCQUFnQjtRQUNoQkMsa0JBQWtCO1FBQ2xCQyxrQkFBa0I7UUFDbEJDLG9CQUFvQjtRQUNwQkMsb0JBQW9CO1FBQ3BCQyxxQkFBcUI7UUFDckJDLHNCQUFzQjtRQUN0QkMsa0JBQWtCO1FBQ2xCQyxpQkFBaUI7UUFDakJDLGVBQWU7UUFDZkMsaUJBQWlCO1FBQ2pCQyxrQkFBa0I7UUFDbEJDLGNBQWM7WUFDWi9HLFlBQVk7Z0JBQUVnSCxVQUFVO2dCQUFHQyxXQUFXO1lBQUU7WUFDeENDLGtCQUFrQjtnQkFBRXhFLFFBQVE7Z0JBQU95RSxZQUFZO2dCQUFPaEQsU0FBUztnQkFBT2lELFlBQVk7Z0JBQU9oRCxVQUFVO2dCQUFPaUQsYUFBYTtnQkFBT0MsT0FBTztnQkFBT0MsWUFBWTtnQkFBT3RGLElBQUk7Z0JBQU91RixVQUFVO1lBQU07WUFDMUxDLFNBQVM7WUFDVEMsa0JBQWtCO1lBQ2xCQyxTQUFTO2dCQUFDO29CQUFFMUYsSUFBSTtvQkFBTTJGLE1BQU07Z0JBQU07YUFBRTtRQUN0QztRQUNBL0gsT0FBTztZQUNMRyxZQUFZMUMsTUFBTTBDLFVBQVUsQ0FBQzZILElBQUk7WUFDakNDLFdBQVd4SyxNQUFNd0ssU0FBUztRQUc1QjtRQUNBQyxjQUFjOUwsaUZBQW1CQTtRQUNqQzJELG9CQUFvQkE7UUFDcEJvSSx5QkFBeUI7WUFDdkIsZUFBZTtnQkFDYkMsY0FBYztZQUNoQjtZQUNBLGtCQUFrQjtnQkFDaEJBLGNBQWM7WUFDaEI7WUFDQSxtQkFBbUI7Z0JBQ2pCLCtDQUErQztnQkFDL0NDLE1BQU07Z0JBQ05ELGNBQWM7WUFNaEI7WUFDQSxtQkFBbUI7Z0JBQ2pCQSxjQUFjO1lBQ2hCO1FBQ0Y7UUFDQUUsZUFBZTtZQUNiQyxNQUFNO1lBQ05DLGlCQUFpQjtRQUNuQjtRQUNBQyxvQkFBb0I7Z0JBQUMsRUFBRWhHLEtBQUssRUFBRTttQkFBTTtnQkFDbEMsMkNBQTJDO2dCQUMzQyx3QkFBd0I7Z0JBQ3hCRSxXQUFXO2dCQUNYK0YsU0FBUztvQkFBRUMsTUFBTTtnQkFBcUQ7Z0JBRXRFQyxJQUFJO29CQUNGekYsUUFBUztvQkFDVCxpQ0FBaUM7b0JBQ2pDLHFCQUFxQjtvQkFDckIsZ0NBQWdDO29CQUNoQzBGLGlCQUFpQjtvQkFDakJDLFlBQVk7b0JBQ1pDLHFCQUFxQjtvQkFDckIsK0JBQStCO3dCQUM3QkYsaUJBQWlCO3dCQUNqQkMsWUFBWTt3QkFDWkMscUJBQXFCO3dCQUNyQkMsT0FBTztvQkFDVDtvQkFDQSxtQkFBbUI7d0JBQ2pCSCxpQkFBaUI7d0JBQ2pCQyxZQUFZO3dCQUNaQyxxQkFBcUI7d0JBQ3JCQyxPQUFPO29CQUNUO2dCQUNGO1lBQ0Y7UUFBQTtRQUNBQyx1QkFBdUI7Z0JBQUMsRUFBRXpHLEdBQUcsRUFBRUMsS0FBSyxFQUFFO21CQUFNO2dCQUMxQzVDLE1BQU14QixlQUFlRTtnQkFDckJxSyxJQUFJO29CQUNGLHFCQUFxQjt3QkFDbkJNLFNBQVM7b0JBQ1g7b0JBQ0EsMEJBQTBCO3dCQUN4QkEsU0FBUztvQkFDWDtvQkFDQUMsUUFBUTtnQkFFVjtZQUNGO1FBQUE7UUFDQUMsaUJBQWlCO1FBQ2pCQyxtQkFBbUI7UUFDbkJDLGtCQUFrQjtnQkFBQyxFQUFFN0csS0FBSyxFQUFFOEcsTUFBTSxFQUFFL0csR0FBRyxFQUFFO2dCQUkzQitHO1lBSFp0SixRQUFRQyxHQUFHLENBQUMsb0JBQW9CcUo7WUFDaEMsTUFBTSxFQUFFbkgsRUFBRSxFQUFFLEdBQUdvSCxNQUFNLEdBQUdEO1lBQ3hCQyxLQUFLMUYsSUFBSSxHQUFHeUYsT0FBT3pGLElBQUksQ0FBQzFCLEVBQUU7WUFDMUJvSCxLQUFLekUsSUFBSSxHQUFHd0UsRUFBQUEsZUFBQUEsT0FBT3hFLElBQUksY0FBWHdFLG1DQUFBQSxhQUFhdkgsR0FBRyxDQUFDbkUsQ0FBQUEsT0FBUUEsS0FBS3VFLEVBQUUsTUFBSyxFQUFFO1lBQ25EakQsMEJBQTBCcUssTUFBTTtnQkFDOUJDLFlBQVk7Z0JBQ1pDLFdBQVc7b0JBQ1RqSCxNQUFNa0gsYUFBYSxDQUFDLE9BQU8sb0JBQW9CO29CQUMvQzNMLE1BQU00QyxPQUFPLENBQUVDLElBQUksQ0FBQzt3QkFBRUMsVUFBVTt3QkFBV0MsU0FBUzt3QkFBUUMsUUFBUSxhQUE4QixPQUFqQnVJLE9BQU96RixJQUFJLENBQUNKLElBQUksRUFBQztvQkFBYztnQkFDbEg7Z0JBQ0FrRyxTQUFTLENBQUMzRDt3QkFDNkRBO29CQUFyRWpJLE1BQU00QyxPQUFPLENBQUVDLElBQUksQ0FBQzt3QkFBRUMsVUFBVTt3QkFBU0MsU0FBUzt3QkFBUUMsUUFBUSxHQUE4QixRQUEzQmlGLGtCQUFBQSxNQUFNNEQsUUFBUSxjQUFkNUQsc0NBQUFBLGdCQUFnQjZELFVBQVU7b0JBQUc7b0JBQ2xHLGtEQUFrRDtvQkFDbER0SCxJQUFJUSxZQUFZLEdBQUc7d0JBQUVpRCxPQUFPQSxNQUFNNEQsUUFBUTt3QkFBRSxHQUFHckgsSUFBSVEsWUFBWTtvQkFBQztvQkFDaEU7Z0JBQ0Y7WUFDRjtRQUNGO1FBQ0ErRyxvQkFBb0I7Z0JBQ2xCLDZCQUE2QjtZQUM3Qi9MO2FBQUFBLGlCQUFBQSxNQUFNNEMsT0FBTyxjQUFiNUMscUNBQUFBLGVBQWU2QyxJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQVFDLFNBQVM7Z0JBQVFDLFFBQVE7WUFBYTtRQUNoRjtRQUNBZ0osbUJBQW1CO2dCQUFDLEVBQUV2SCxLQUFLLEVBQUU4RyxNQUFNLEVBQUUvRyxHQUFHLEVBQUU7Z0JBSTVCK0c7WUFIWnRKLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJxSjtZQUNqQyxNQUFNLEVBQUVuSCxFQUFFLEVBQUUsR0FBR29ILE1BQU0sR0FBR0Q7WUFDeEJDLEtBQUsxRixJQUFJLEdBQUd5RixPQUFPekYsSUFBSSxDQUFDMUIsRUFBRTtZQUMxQm9ILEtBQUt6RSxJQUFJLEdBQUd3RSxFQUFBQSxlQUFBQSxPQUFPeEUsSUFBSSxjQUFYd0UsbUNBQUFBLGFBQWF2SCxHQUFHLENBQUNuRSxDQUFBQSxPQUFRQSxLQUFLdUUsRUFBRSxNQUFLLEVBQUU7WUFDbkRwRCwyQkFBMkJ3SyxNQUFNO2dCQUMvQkMsWUFBWTtnQkFDWkMsV0FBVztvQkFDVGpILE1BQU13SCxjQUFjLENBQUMsT0FBTyxvQkFBb0I7b0JBQ2hEak0sTUFBTTRDLE9BQU8sQ0FBRUMsSUFBSSxDQUFDO3dCQUFFQyxVQUFVO3dCQUFXQyxTQUFTO3dCQUFRQyxRQUFRLGFBQThCLE9BQWpCdUksT0FBT3pGLElBQUksQ0FBQ0osSUFBSSxFQUFDO29CQUFPO2dCQUMzRztnQkFDQWtHLFNBQVMsQ0FBQzNEO3dCQUM2REE7b0JBQXJFakksTUFBTTRDLE9BQU8sQ0FBRUMsSUFBSSxDQUFDO3dCQUFFQyxVQUFVO3dCQUFTQyxTQUFTO3dCQUFRQyxRQUFRLEdBQThCLFFBQTNCaUYsa0JBQUFBLE1BQU00RCxRQUFRLGNBQWQ1RCxzQ0FBQUEsZ0JBQWdCNkQsVUFBVTtvQkFBRztvQkFDbEcsa0RBQWtEO29CQUNsRHRILElBQUlRLFlBQVksR0FBRzt3QkFBRWlELE9BQU9BLE1BQU00RCxRQUFRO3dCQUFFLEdBQUdySCxJQUFJUSxZQUFZO29CQUFDO29CQUNoRTtnQkFDRjtZQUNGO1FBQ0Y7UUFDQWtILHFCQUFxQjtnQkFDbkJsTTthQUFBQSxpQkFBQUEsTUFBTTRDLE9BQU8sY0FBYjVDLHFDQUFBQSxlQUFlNkMsSUFBSSxDQUFDO2dCQUFFQyxVQUFVO2dCQUFRQyxTQUFTO2dCQUFRQyxRQUFRO1lBQWE7UUFDaEY7UUFDQW1KLHFCQUFxQjtZQUNuQnhILFdBQVc7WUFDWGlHLElBQUk7Z0JBQ0YsMEJBQTBCO29CQUN4QkMsaUJBQWlCO2dCQUNuQjtnQkFDQUEsaUJBQWlCO2dCQUNqQkcsT0FBTztnQkFDUEYsWUFBWTtnQkFDWkMscUJBQXFCO1lBQ3ZCO1FBQ0Y7UUFDQXFCLHdCQUF3QjtnQkFBQyxFQUFFM0gsS0FBSyxFQUFFO2dCQVFpREEsbUNBQXNEQTttQkFSakc7Z0JBQ3RDRSxXQUFXO2dCQUNYaUcsSUFBSTtvQkFDRkUsWUFBWTtvQkFDWkMscUJBQXFCO29CQUNyQixxQkFBcUI7b0JBQ3JCLGdDQUFnQztvQkFDaENGLGlCQUFpQjtvQkFDakIxRixRQUFRVixNQUFNNEgsUUFBUSxHQUFHQyxZQUFZLEdBQUksZ0JBQWUsK0JBQXVCN0gsb0NBQUFBLE1BQU04SCxJQUFJLENBQUNDLGFBQWEsQ0FBQzVKLE9BQU8sY0FBaEM2Qix3REFBQUEsa0NBQWtDZ0ksWUFBWSxFQUFDLFNBQXlELFFBQWxEaEksdUNBQUFBLE1BQU04SCxJQUFJLENBQUNHLGdCQUFnQixDQUFDOUosT0FBTyxjQUFuQzZCLDJEQUFBQSxxQ0FBcUNnSSxZQUFZLEVBQUM7Z0JBRXpMO1lBQ0Y7O1FBQ0FFLG9CQUFvQjtZQUNsQmhJLFdBQVc7WUFDWGlHLElBQUk7Z0JBRUYscUJBQXFCO2dCQUNyQixnQ0FBZ0M7Z0JBQ2hDQyxpQkFBaUI7Z0JBQ2pCRyxPQUFPO2dCQUNQRixZQUFZO2dCQUNaQyxxQkFBcUI7WUFDdkI7UUFDRjtRQUNBM0YsdUJBQXVCO1lBQ3JCd0YsSUFBSTtnQkFDRixxQkFBcUI7Z0JBQ3JCLGdDQUFnQztnQkFDaENDLGlCQUFpQjtnQkFDakJHLE9BQU87Z0JBQ1BGLFlBQVk7Z0JBQ1pDLHFCQUFxQjtZQUN2QjtRQUNGO1FBQ0E2QixvQkFBb0I7WUFDbEJqSSxXQUFXO1lBQ1hpRyxJQUFJO2dCQUNGRSxZQUFZO2dCQUNaQyxxQkFBcUI7Z0JBQ3JCLHFCQUFxQjtnQkFDckIsZ0NBQWdDO2dCQUNoQ0YsaUJBQWlCO2dCQUNqQkcsT0FBTztZQUNUO1FBRUY7UUFDQTZCLG1CQUFtQjtZQUNqQmxJLFdBQVc7WUFDWGlHLElBQUk7Z0JBQ0ZFLFlBQVk7Z0JBQ1pDLHFCQUFxQjtnQkFDckIsK0NBQStDO2dCQUMvQyw4QkFBOEI7b0JBQzVCRixpQkFBaUI7b0JBQ2pCRyxPQUFPO29CQUNQRixZQUFZO29CQUNaQyxxQkFBcUI7Z0JBQ3ZCO2dCQUNBLCtCQUErQjtvQkFDN0JGLGlCQUFpQjtvQkFDakJHLE9BQU87b0JBQ1BGLFlBQVk7b0JBQ1pDLHFCQUFxQjtnQkFDdkI7WUFDRjtRQUNGO1FBQ0ErQiwrQkFBK0I7Z0JBQUMsRUFBRXJJLEtBQUssRUFBRTttQkFDdkMsOERBQUN6RyxzREFBS0E7Z0JBQUMrTyxXQUFXO2dCQUFPQyxTQUFTOztrQ0FDaEMsOERBQUMzTix5Q0FBR0E7d0JBQUM0TixHQUFFO3dCQUFNQyxHQUFFO2tDQUNiLDRFQUFDN08sc0RBQU1BOzRCQUNMOE8sTUFBSzs0QkFDTEMsT0FBTzs0QkFDUCxvQkFBb0I7NEJBQ3BCQyxpQkFBZXhMLE9BQU8sZUFBZXlMOzRCQUNyQ0MsaUJBQWM7NEJBQ2RDLGlCQUFlM0wsT0FBTyxTQUFTeUw7NEJBQy9CRyxTQUFTLENBQUNySztnQ0FDUnFCLE1BQU13SCxjQUFjLENBQUM7Z0NBQU96TCxpQkFBaUIsT0FBT3lCLFFBQVFDLEdBQUcsQ0FBQzs0QkFDbEU7NEJBQ0FtSSxNQUFLOzs7Ozs7Ozs7OztrQ0FJVCw4REFBQ2hMLHlDQUFHQTt3QkFBQzROLEdBQUU7d0JBQVNDLEdBQUU7a0NBQ2hCLDRFQUFDN08sc0RBQU1BOzRCQUNMK08sT0FBTzs0QkFDUE0sVUFBVWpKLE1BQU1rSixxQkFBcUI7NEJBQ3JDLG9CQUFvQjs0QkFDcEJOLGlCQUFleEwsT0FBTyxlQUFleUw7NEJBQ3JDQyxpQkFBYzs0QkFDZEMsaUJBQWUzTCxPQUFPLFNBQVN5TDs0QkFDL0JHLFNBQVN0Szs0QkFDVGdLLE1BQUs7NEJBQ0wsb0ZBQW9GOzRCQUNwRjlDLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBR0o7UUFFVHVELHFCQUFxQixJQUFPO2dCQUMxQmhELElBQUksQ0FBQ2lELFFBQVc7d0JBQ2RoRCxpQkFDRWdELE1BQU1DLE9BQU8sQ0FBQ0MsSUFBSSxLQUFLLFNBQ25CLDBCQUNBO29CQUNSO1lBQ0Y7UUFDQUMsOEJBQThCO2dCQUFDLEVBQUVDLHNCQUFzQixFQUFFekosR0FBRyxFQUFFQyxLQUFLLEVBQUU7bUJBQ25FLDhEQUFDK0M7Z0JBQUl0QyxPQUFPO29CQUFFaUcsUUFBUTtnQkFBa0I7MEJBQ3RDLDRFQUFDM00sd0RBQU9BO29CQUFDMFAsVUFBUztvQkFDaEJqSyxzQkFDRSw4REFBQ3VEO3dCQUFJN0MsV0FBVTs7MENBQ2IsOERBQUN3SjtnQ0FBS3hKLFdBQVU7MENBQXdCOzswQ0FDeEMsOERBQUM3RyxzREFBYUE7MENBQ1osNEVBQUNHLHdFQUFxQkE7b0NBQUNtUSxTQUFRO29DQUFPM0osT0FBT0E7b0NBQU9ELEtBQUtBOzs7OztvQkFJL0Q2SixTQUFTOU47b0JBQ1QrTixRQUFRO3dCQUFRN0osTUFBTXdILGNBQWMsQ0FBQzt3QkFBT3pMLGlCQUFpQjtvQkFBTztvQkFDcEVtRSxXQUFVOzhCQUNWLDRFQUFDNUcsc0RBQWFBO3dCQUNaNk0sSUFBSTs0QkFDRk0sU0FBUzs0QkFDVHFELGVBQWU7NEJBQ2ZDLEtBQUs7d0JBQ1A7a0NBRUNQOzs7Ozs7Ozs7Ozs7Ozs7O1FBR0Y7UUFDUFEsNEJBQTRCO2dCQUFDLEVBQUVSLHNCQUFzQixFQUFFekosR0FBRyxFQUFFQyxLQUFLLEVBQUU7bUJBQ2pFLDhEQUFDK0M7Z0JBQUl0QyxPQUFPO29CQUFFaUcsUUFBUTtnQkFBa0I7MEJBQ3RDLDRFQUFDM00sd0RBQU9BO29CQUFDMFAsVUFBUztvQkFDaEJqSyxzQkFDRSw4REFBQ3VEO3dCQUFJN0MsV0FBVTs7MENBQ2IsOERBQUMrSjtnQ0FBRy9KLFdBQVU7O29DQUF3QjtvQ0FBdUJILElBQUlJLFFBQVEsQ0FBQ1IsRUFBRTs7OzBDQUM1RSw4REFBQ3RHLHNEQUFhQTswQ0FDWiw0RUFBQ0csd0VBQXFCQTtvQ0FBQ21RLFNBQVE7b0NBQU8zSixPQUFPQTtvQ0FBT0QsS0FBS0E7Ozs7O29CQUkvRDZKLFNBQVNoTztvQkFDVGlPLFFBQVE7d0JBQ043SixNQUFNa0gsYUFBYSxDQUFDO3dCQUNwQnJMLGVBQWU7b0JBQ2pCO29CQUNBcUUsV0FBVTs4QkFDViw0RUFBQzVHLHNEQUFhQTt3QkFDWjZNLElBQUk7NEJBQUVNLFNBQVM7NEJBQVFxRCxlQUFlOzRCQUFVQyxLQUFLO3dCQUFTOzs0QkFFN0RQOzRCQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFHekI7UUFDUFUsbUJBQW1CO2dCQUFDLEVBQUVuSyxHQUFHLEVBQUU7bUJBQ3pCMUYsNkRBQUtBLENBQUMwRixJQUFJSSxRQUFRLENBQUNDLE1BQU07UUFBQTtRQUMzQitKLGtCQUFrQjtnQkFBQyxFQUFFdEssSUFBSSxFQUFFRSxHQUFHLEVBQUVDLEtBQUssRUFBRTttQkFDckMsOERBQUMwSjtnQkFBS3hKLFdBQVU7O2tDQUNkLDhEQUFDdEYseUNBQUdBO3dCQUFDNE4sR0FBRTt3QkFBU0MsR0FBRTtrQ0FDaEIsNEVBQUM3TyxzREFBTUE7NEJBQUNnTSxNQUFLOzRCQUFROEMsTUFBSzs0QkFBZU0sU0FBUztnQ0FDaERyTixpQkFBaUJvRSxJQUFJSSxRQUFRLENBQUNSLEVBQUU7Z0NBQ2hDSyxNQUFNa0gsYUFBYSxDQUFDbkg7Z0NBQ3BCbEUsZUFBZTtnQ0FDZjJCLFFBQVFDLEdBQUcsQ0FBQzs0QkFDZDs0QkFBR2tMLE9BQU87NEJBQUN5QixRQUFROzs7Ozs7Ozs7OztrQ0FFckIsOERBQUN4UCx5Q0FBR0E7d0JBQUM0TixHQUFFO3dCQUFTQyxHQUFFO2tDQUNoQiw0RUFBQzdPLHNEQUFNQTs0QkFBQ2dNLE1BQUs7NEJBQVE4QyxNQUFLOzRCQUFjQyxPQUFPOzRCQUFDeUIsUUFBUTs0QkFDdERwQixTQUFTLENBQUNySyxRQUFVN0Usc0VBQVlBLENBQUM7b0NBQy9CNEksUUFBUS9ELE1BQU1DLGFBQWE7b0NBQzNCeUwsU0FBUztvQ0FDVDNCLE1BQU07b0NBQ04sMEJBQTBCO29DQUMxQjRCLGlCQUFpQjtvQ0FDakJDLGFBQWE7b0NBQ2JDLGFBQWE7b0NBQ2JDLFFBQVF2TTtvQ0FDUndNLFFBQVFqTTtnQ0FDVjs7Ozs7Ozs7Ozs7a0NBR0osOERBQUM1RSxrRUFBWUE7Ozs7Ozs7Ozs7O1FBQ1Q7SUFFVjtJQUNBLHFCQUFPOzswQkFBRSw4REFBQ0oscUVBQWtCQTtnQkFBQ3VHLE9BQU9BOzs7Ozs7MEJBQVMsOERBQUMvRixvREFBS0E7Z0JBQUMwUSxLQUFLcFA7Ozs7Ozs7O0FBQzNEO0dBN2hCd0JSOztRQWFzREYsc0VBQXVCQTtRQUN6QkMsNkVBQThCQTtRQW9MMUZwQix3RUFBcUJBOzs7S0FsTWJxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvKG1haW4pL3BsYW5zLyhjb21wb25lbnRzKS9HZW5lcmljVEFibGVBcmJpdHJhdGlvbi50c3g/Y2Q5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IHsgQm94LCBEaWFsb2dBY3Rpb25zLCBEaWFsb2dDb250ZW50LCBTdGFjayB9IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xyXG5pbXBvcnQge1xyXG4gIE1SVF9FZGl0QWN0aW9uQnV0dG9ucyxcclxuICBNUlRfUm93RGF0YSxcclxuICBNYXRlcmlhbFJlYWN0VGFibGUsXHJcbiAgdXNlTWF0ZXJpYWxSZWFjdFRhYmxlLFxyXG4gIHR5cGUgTVJUX0NvbHVtbkRlZlxyXG59IGZyb20gJ21hdGVyaWFsLXJlYWN0LXRhYmxlJztcclxuaW1wb3J0IHsgTVJUX0xvY2FsaXphdGlvbl9GUiB9IGZyb20gJ21hdGVyaWFsLXJlYWN0LXRhYmxlL2xvY2FsZXMvZnInO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdwcmltZXJlYWN0L2J1dHRvbic7XHJcbmltcG9ydCB7IENvbmZpcm1Qb3B1cCwgY29uZmlybVBvcHVwIH0gZnJvbSAncHJpbWVyZWFjdC9jb25maXJtcG9wdXAnO1xyXG5pbXBvcnQgeyBTaWRlYmFyIH0gZnJvbSAncHJpbWVyZWFjdC9zaWRlYmFyJztcclxuaW1wb3J0IHsgVGFiUGFuZWwsIFRhYlZpZXcgfSBmcm9tICdwcmltZXJlYWN0L3RhYnZpZXcnO1xyXG5pbXBvcnQgeyBUYWcgfSBmcm9tICdwcmltZXJlYWN0L3RhZyc7XHJcbmltcG9ydCB7IFRvYXN0IH0gZnJvbSAncHJpbWVyZWFjdC90b2FzdCc7XHJcbmltcG9ydCB7IHVzZU1lbW8sIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBwYXJzZSBmcm9tICdodG1sLXJlYWN0LXBhcnNlcic7XHJcbi8vIGltcG9ydCB7IEVkaXRvciB9IGZyb20gJ0B0aW55bWNlL3RpbnltY2UtcmVhY3QnO1xyXG5pbXBvcnQgeyBQaWNrTGlzdCB9IGZyb20gJ3ByaW1lcmVhY3QvcGlja2xpc3QnO1xyXG5pbXBvcnQgeyBEcm9wZG93biB9IGZyb20gJ3ByaW1lcmVhY3QvZHJvcGRvd24nO1xyXG5pbXBvcnQgeyBnZXRDb29raWUgfSBmcm9tICdjb29raWVzLW5leHQnO1xyXG5pbXBvcnQgeyB1c2ViYXNlRGF0YSB9IGZyb20gJ0AvdXRpbGl0aWVzL2hvb2tzL3VzZUJhc2VEYXRhJztcclxuaW1wb3J0IHsgZ2V0VXNlckZ1bGxuYW1lIH0gZnJvbSAnQC91dGlsaXRpZXMvZnVuY3Rpb25zL3V0aWxzJztcclxuaW1wb3J0IHsgRWRpdG9yIH0gZnJvbSAncHJpbWVyZWFjdC9lZGl0b3InO1xyXG5pbXBvcnQgeyBDYW4gfSBmcm9tICdAL2FwcC9DYW4nO1xyXG5pbXBvcnQgeyB1c2VBcGlBcmJpdHJhdGlvbkNyZWF0ZSwgdXNlQXBpQXJiaXRyYXRpb25QYXJ0aWFsVXBkYXRlIH0gZnJvbSAnQC9ob29rcy91c2VOZXh0QXBpJztcclxuaW1wb3J0IHsgUGxhbiB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdlbmVyaWNUYWJsZTxUIGV4dGVuZHMgTVJUX1Jvd0RhdGE+KGRhdGFfOiB7IGlzTG9hZGluZzogYW55OyBkYXRhXzogYW55LCBlcnJvcjogYW55LCBkYXRhX3R5cGU6IGFueSB8IHVuZGVmaW5lZCwgcGFnaW5hdGlvbjogYW55IH0pIHtcclxuICBjb25zdCB1c2VyID0gSlNPTi5wYXJzZShnZXRDb29raWUoJ3VzZXInKT8udG9TdHJpbmcoKSB8fCAne30nKVxyXG5cclxuICBjb25zdCB0b2FzdCA9IHVzZVJlZjxUb2FzdCB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHsgcGxhbnMsIHVzZXJzLCBhcmJpdHJhdGlvbnMgfSA9IHVzZWJhc2VEYXRhKClcclxuXHJcbiAgY29uc3QgdXNlcnNfZGF0YSA9IHVzZU1lbW8oKCkgPT4gdXNlcnMuZGF0YT8uZGF0YSwgW3VzZXJzLmRhdGE/LmRhdGFdKVxyXG4gIGNvbnN0IFthcmJpdHJhdGlvbklELCBzZXRBcmJpdHJhdGlvbklEXSA9IHVzZVN0YXRlKDApXHJcbiAgY29uc3QgW2VkaXRWaXNpYmxlLCBzZXRFZGl0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2NyZWF0ZVZpc2libGUsIHNldENyZWF0ZVZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwaWNrbGlzdFRhcmdldFZhbHVlVGVhbSwgc2V0UGlja2xpc3RUYXJnZXRWYWx1ZVRlYW1dID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtwaWNrbGlzdFNvdXJjZVZhbHVlVGVhbSwgc2V0UGlja2xpc3RTb3VyY2VWYWx1ZVRlYW1dID0gdXNlU3RhdGUodXNlcnNfZGF0YSk7XHJcbiAgY29uc3QgW3Jvd1RvYmUsIHNldFJvd1RvYmVdID0gdXNlU3RhdGUoe30pO1xyXG4gIGNvbnN0IHsgbXV0YXRlOiBhcmJpdHJhdGlvbl9jcmVhdGVfdHJpZ2dlciwgaXNQZW5kaW5nOiBpc0NyZWF0ZU11dGF0aW5nIH0gPSB1c2VBcGlBcmJpdHJhdGlvbkNyZWF0ZSgpXHJcbiAgY29uc3QgeyBtdXRhdGU6IGFyYml0cmF0aW9uX3BhdGNoX3RyaWdnZXIsIGlzUGVuZGluZzogaXNQYXRjaE11dGF0aW5nIH0gPSB1c2VBcGlBcmJpdHJhdGlvblBhcnRpYWxVcGRhdGUoKVxyXG4gIGNvbnN0IFtudW1QYWdlcywgc2V0TnVtUGFnZXNdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3BhZ2VOdW1iZXIsIHNldFBhZ2VOdW1iZXJdID0gdXNlU3RhdGUoMSk7XHJcbiAgY29uc3QgW2FuY2hvckVsLCBzZXRBbmNob3JFbF0gPSB1c2VTdGF0ZTxudWxsIHwgSFRNTEVsZW1lbnQ+KG51bGwpO1xyXG4gIGNvbnN0IFtyb3dBY3Rpb25FbmFibGVkLCBzZXRSb3dBY3Rpb25FbmFibGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBvcGVuID0gQm9vbGVhbihhbmNob3JFbCk7XHJcblxyXG4gIGZ1bmN0aW9uIG9uUGFnaW5hdGlvbkNoYW5nZShzdGF0ZTogYW55KSB7XHJcbiAgICBjb25zb2xlLmxvZyhkYXRhXy5wYWdpbmF0aW9uKTtcclxuICAgIGRhdGFfLnBhZ2luYXRpb24uc2V0KHN0YXRlKVxyXG4gIH07XHJcbiAgZnVuY3Rpb24gb25Eb2N1bWVudExvYWRTdWNjZXNzKHsgbnVtUGFnZXMgfTogeyBudW1QYWdlczogbnVtYmVyIH0pIHtcclxuICAgIHNldE51bVBhZ2VzKG51bVBhZ2VzKTtcclxuICAgIHNldFBhZ2VOdW1iZXIoMSk7XHJcbiAgfVxyXG4gIGZ1bmN0aW9uIGNoYW5nZVBhZ2Uob2Zmc2V0OiBudW1iZXIpIHtcclxuICAgIHNldFBhZ2VOdW1iZXIocHJldlBhZ2VOdW1iZXIgPT4gcHJldlBhZ2VOdW1iZXIgKyBvZmZzZXQpO1xyXG4gIH1cclxuICBmdW5jdGlvbiBwcmV2aW91c1BhZ2UoKSB7XHJcbiAgICBjaGFuZ2VQYWdlKC0xKTtcclxuICB9XHJcbiAgZnVuY3Rpb24gbmV4dFBhZ2UoKSB7XHJcbiAgICBjaGFuZ2VQYWdlKDEpO1xyXG4gIH1cclxuICBjb25zdCBhY2NlcHRfcm93X2RlbGV0aW9uID0gKCkgPT4ge1xyXG4gICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnaW5mbycsIHN1bW1hcnk6ICdDb25maXJtZWQnLCBkZXRhaWw6ICdZb3UgaGF2ZSBhY2NlcHRlZCcsIGxpZmU6IDMwMDAgfSk7XHJcbiAgfTtcclxuICBjb25zdCByZWplY3Rfcm93X2RlbGV0aW9uID0gKCkgPT4ge1xyXG4gICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnd2FybicsIHN1bW1hcnk6ICdSZWplY3RlZCcsIGRldGFpbDogJ1lvdSBoYXZlIHJlamVjdGVkJywgbGlmZTogMzAwMCB9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDbGljayA9IChldmVudDogUmVhY3QuTW91c2VFdmVudDxIVE1MQnV0dG9uRWxlbWVudD4pID0+IHtcclxuICAgIHNldEFuY2hvckVsKGV2ZW50LmN1cnJlbnRUYXJnZXQpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGNvbHVtbnMgPSB1c2VNZW1vPE1SVF9Db2x1bW5EZWY8VD5bXT4oXHJcbiAgICAoKSA9PlxyXG4gICAgICBPYmplY3QuZW50cmllcyhkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllcykuZmlsdGVyKChba2V5LCB2YWx1ZV0sIGluZGV4KSA9PiAhWydtb2RpZmllZF9ieScsICdjcmVhdGVkX2J5JywgJ2NyZWF0ZWQnLCAnbW9kaWZpZWQnLF0uaW5jbHVkZXMoa2V5KSkubWFwKChba2V5LCB2YWx1ZV0sIGluZGV4KSA9PiB7XHJcbiAgICAgICAgaWYgKFsncmVwb3J0JywgJ2NvbnRlbnQnLCAnbm90ZScsICdvcmRlcicsICdjb21tZW50JywgJ2Rlc2NyaXB0aW9uJ10uaW5jbHVkZXMoa2V5KSkge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgICAgaWQ6IGtleSxcclxuICAgICAgICAgICAgLy8gQ2VsbDogKHsgY2VsbCB9KSA9PiA8ZGl2PntwYXJzZShjZWxsLmdldFZhbHVlPHN0cmluZz4oKSl9PC9kaXY+LFxyXG4gICAgICAgICAgICAvLyBDZWxsOiAoeyBjZWxsIH0pID0+IHsgaWYgKFtcImRlc2NyaXB0aW9uXCIsIFwiY29udGVudFwiLFwicmVwb3J0XCJdLmluY2x1ZGVzKGtleSkpIHJldHVybiBudWxsOyBlbHNlIHJldHVybiA8ZGl2PntwYXJzZShjZWxsLmdldFZhbHVlPHN0cmluZz4oKSl9PC9kaXY+IH0sXHJcbiAgICAgICAgICAgIEVkaXQ6ICh7IGNlbGwsIGNvbHVtbiwgcm93LCB0YWJsZSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9J2ZvbnQtYm9sZCc+UmFwcG9ydDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgIDxFZGl0b3JcclxuICAgICAgICAgICAgICAgICAgICAvLyBpbml0aWFsVmFsdWU9e3Jvdy5vcmlnaW5hbFtrZXldfVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIHRpbnltY2VTY3JpcHRTcmM9XCJodHRwOi8vbG9jYWxob3N0OjMwMDAvdGlueW1jZS90aW55bWNlLm1pbi5qc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gYXBpS2V5PSdub25lJ1xyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyb3cub3JpZ2luYWwucmVwb3J0fVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIG9uQ2hhbmdlPXsoZSkgPT4geyByb3cuX3ZhbHVlc0NhY2hlLnJlcG9ydCA9IGUudGFyZ2V0LmdldENvbnRlbnQoKSB9fVxyXG4gICAgICAgICAgICAgICAgICAgIG9uVGV4dENoYW5nZT17KGUpID0+IHsgcm93Ll92YWx1ZXNDYWNoZS5yZXBvcnQgPSBlLmh0bWxWYWx1ZSB9fVxyXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogJzMyMHB4JyB9fVxyXG4gICAgICAgICAgICAgICAgICAvLyBpbml0PXt7XHJcbiAgICAgICAgICAgICAgICAgIC8vICAgbGljZW5zZUtleTonZ3BsJyxcclxuICAgICAgICAgICAgICAgICAgLy8gICBoZWlnaHQ6IDUwMCxcclxuICAgICAgICAgICAgICAgICAgLy8gICBtZW51YmFyOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAvLyAgIHBsdWdpbnM6IFtcclxuICAgICAgICAgICAgICAgICAgLy8gICAgICdhZHZsaXN0JywgJ2F1dG9saW5rJywgJ2xpc3RzJywgJ2xpbmsnLCAnaW1hZ2UnLCAnY2hhcm1hcCcsICdwcmludCcsICdwcmV2aWV3JywgJ2FuY2hvcicsXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgICAnc2VhcmNocmVwbGFjZScsICd2aXN1YWxibG9ja3MnLCAnY29kZScsICdmdWxsc2NyZWVuJyxcclxuICAgICAgICAgICAgICAgICAgLy8gICAgICdpbnNlcnRkYXRldGltZScsICdtZWRpYScsICd0YWJsZScsICdwYXN0ZScsICdjb2RlJywgJ2hlbHAnLCAnd29yZGNvdW50J1xyXG4gICAgICAgICAgICAgICAgICAvLyAgIF0sXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgdG9vbGJhcjpcclxuICAgICAgICAgICAgICAgICAgLy8gICAgICd1bmRvIHJlZG8gfCBmb3JtYXRzZWxlY3QgfCBib2xkIGl0YWxpYyBiYWNrY29sb3IgfCBcXFxyXG4gICAgICAgICAgICAgICAgICAvLyAgICAgYWxpZ25sZWZ0IGFsaWduY2VudGVyIGFsaWducmlnaHQgYWxpZ25qdXN0aWZ5IHwgXFxcclxuICAgICAgICAgICAgICAgICAgLy8gICAgIGJ1bGxpc3QgbnVtbGlzdCBvdXRkZW50IGluZGVudCB8IHJlbW92ZWZvcm1hdCB8IGhlbHAgfHZpc3VhbGJsb2NrcyBjb2RlIGZ1bGxzY3JlZW4nXHJcbiAgICAgICAgICAgICAgICAgIC8vIH19XHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoa2V5ID09PSBcInBsYW5cIikge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdwbGFuJyxcclxuXHJcbiAgICAgICAgICAgIG11aVRhYmxlSGVhZENlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnbGVmdCcsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIG11aVRhYmxlQm9keUNlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnbGVmdCcsXHJcbiAgICAgICAgICAgIH0sXHJcblxyXG4gICAgICAgICAgICBtdWlUYWJsZUZvb3RlckNlbGxQcm9wczoge1xyXG4gICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgQ2VsbDogKHsgY2VsbCwgcm93IH0pID0+IDxUYWcgY2xhc3NOYW1lPSd3LTExcmVtIHRleHQtc20nPntjZWxsLmdldFZhbHVlPFBsYW4+KCkuY29kZX08L1RhZz4sXHJcbiAgICAgICAgICAgIEVkaXQ6ICh7IHJvdyB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9J2ZvbnQtYm9sZCc+UGxhbjwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICA8RHJvcGRvd25cclxuICAgICAgICAgICAgICAgICAgb3B0aW9uTGFiZWw9XCJuYW1lXCJcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDaG9pc2lyIHVuIHBsYW5cIlxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRSb3dUb2JlKHsgLi4ucm93VG9iZSwgcGxhbjogcGxhbnM/LmRhdGE/LmRhdGEucmVzdWx0cy5maW5kKChwbGFuOiBQbGFuKSA9PiBwbGFuLmlkID09PSBlLnZhbHVlLmNvZGUpIH0pO1xyXG4gICAgICAgICAgICAgICAgICAgIHJvdy5fdmFsdWVzQ2FjaGUgPSB7IC4uLnJvdy5fdmFsdWVzQ2FjaGUsIHBsYW46IHBsYW5zPy5kYXRhPy5kYXRhLnJlc3VsdHMuZmluZCgocGxhbjogUGxhbikgPT4gcGxhbi5pZCA9PT0gZS52YWx1ZS5jb2RlKSB9XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXt7IGNvZGU6IHJvdy5fdmFsdWVzQ2FjaGUucGxhbj8uaWQgfHwgbnVsbCwgbmFtZTogcm93Ll92YWx1ZXNDYWNoZS5wbGFuPy5jb2RlIHx8IG51bGwgfX1cclxuICAgICAgICAgICAgICAgICAgb3B0aW9ucz17cGxhbnM/LmRhdGE/LmRhdGEucmVzdWx0cy5tYXAoKHBsYW46IFBsYW4pID0+IHsgcmV0dXJuIHsgY29kZTogcGxhbi5pZCwgbmFtZTogcGxhbi5jb2RlIH0gfSl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duPlxyXG4gICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLmZvcm1hdCA9PT0gJ2RhdGUtdGltZScgfHwgZGF0YV8uZGF0YV90eXBlLnByb3BlcnRpZXNba2V5XS5mb3JtYXQgPT09ICdkYXRlJykge1xyXG5cclxuICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yRm46IChyb3cpID0+IG5ldyBEYXRlKGtleSA9PSBcImNyZWF0ZWRcIiA/IHJvdy5jcmVhdGVkIDogcm93Lm1vZGlmaWVkKSxcclxuICAgICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgICAgZmlsdGVyVmFyaWFudDogJ2RhdGUnLFxyXG4gICAgICAgICAgICBmaWx0ZXJGbjogJ2xlc3NUaGFuJyxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAnZGF0ZXRpbWUnLFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBDZWxsOiAoeyBjZWxsIH0pID0+IGNlbGwuZ2V0VmFsdWU8RGF0ZT4oKT8udG9Mb2NhbGVEYXRlU3RyaW5nKCdmcicpLFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICBFZGl0OiAoKSA9PiBudWxsLFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKGtleSA9PT0gXCJpZFwiKSByZXR1cm4ge1xyXG4gICAgICAgICAgaGVhZGVyOiBkYXRhXy5kYXRhX3R5cGUucHJvcGVydGllc1trZXldLnRpdGxlID8/IGtleSxcclxuICAgICAgICAgIGFjY2Vzc29yS2V5OiBrZXksXHJcbiAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgRWRpdDogKCkgPT4gbnVsbCxcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGtleSA9PT0gXCJ0ZWFtXCIpIHJldHVybiB7XHJcbiAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgYWNjZXNzb3JLZXk6IGtleSxcclxuICAgICAgICAgIGlkOiBrZXksXHJcbiAgICAgICAgICBDZWxsOiAoeyBjZWxsLCByb3cgfSkgPT4gPHVsPntjZWxsLmdldFZhbHVlPFVzZXJbXT4oKS5tYXAoKHVzcikgPT4gPGxpPntnZXRVc2VyRnVsbG5hbWUodXNyKX08L2xpPil9PC91bD4sXHJcbiAgICAgICAgICBFZGl0OiAoeyByb3cgfSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnW0FSQklUUkFUSU9OXScsIHJvdy5fdmFsdWVzQ2FjaGUudGVhbSlcclxuICAgICAgICAgICAgcmV0dXJuICg8PlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9J2ZvbnQtYm9sZCc+TWVtYnJlczwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgPFBpY2tMaXN0XHJcbiAgICAgICAgICAgICAgICBzb3VyY2U9e3BpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtLmxlbmd0aCA9PT0gMCA/IHBpY2tsaXN0U291cmNlVmFsdWVUZWFtIDogcGlja2xpc3RTb3VyY2VWYWx1ZVRlYW0uZmlsdGVyKHVzZXIgPT4gcGlja2xpc3RUYXJnZXRWYWx1ZVRlYW0ubWFwKHVzZXIgPT4gdXNlci51c2VybmFtZSkuaW5jbHVkZXModXNlci51c2VybmFtZSkpfVxyXG4gICAgICAgICAgICAgICAgaWQ9J3BpY2tsaXN0X3RlYW0nXHJcbiAgICAgICAgICAgICAgICB0YXJnZXQ9e3BpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtLmxlbmd0aCA+IDAgPyBwaWNrbGlzdFRhcmdldFZhbHVlVGVhbSA6IHJvdy5fdmFsdWVzQ2FjaGUudGVhbX1cclxuICAgICAgICAgICAgICAgIHNvdXJjZUhlYWRlcj1cIkRlXCJcclxuICAgICAgICAgICAgICAgIHRhcmdldEhlYWRlcj1cIkFcIlxyXG4gICAgICAgICAgICAgICAgaXRlbVRlbXBsYXRlPXsoaXRlbSkgPT4gPGRpdiBrZXk9e2l0ZW0udXNlcm5hbWV9PntpdGVtLmZpcnN0X25hbWV9IHtpdGVtLmxhc3RfbmFtZX08L2Rpdj59XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ3NvdXJjZSBUZWFtJywgZS5zb3VyY2UpXHJcbiAgICAgICAgICAgICAgICAgIHNldFBpY2tsaXN0U291cmNlVmFsdWVUZWFtKFsuLi5lLnNvdXJjZV0pXHJcbiAgICAgICAgICAgICAgICAgIHNldFBpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtKFsuLi5lLnRhcmdldF0pXHJcbiAgICAgICAgICAgICAgICAgIHJvdy5fdmFsdWVzQ2FjaGUudGVhbSA9IGUudGFyZ2V0XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgc291cmNlU3R5bGU9e3sgaGVpZ2h0OiAnMjAwcHgnIH19XHJcbiAgICAgICAgICAgICAgICB0YXJnZXRTdHlsZT17eyBoZWlnaHQ6ICcyMDBweCcgfX1cclxuICAgICAgICAgICAgICAgIGZpbHRlciBmaWx0ZXJCeT0ndXNlcm5hbWUsZW1haWwsZmlyc3RfbmFtZSxsYXN0X25hbWUnXHJcbiAgICAgICAgICAgICAgICBmaWx0ZXJNYXRjaE1vZGU9J2NvbnRhaW5zJ1xyXG4gICAgICAgICAgICAgICAgc291cmNlRmlsdGVyUGxhY2Vob2xkZXI9XCJSZWNoZXJjaGVyIHBhciBub20gJiBwcsOpbm9tXCIgdGFyZ2V0RmlsdGVyUGxhY2Vob2xkZXI9XCJSZWNoZXJjaGVyIHBhciBub20gJiBwcsOpbm9tXCJcclxuICAgICAgICAgICAgICA+XHJcblxyXG4gICAgICAgICAgICAgIDwvUGlja0xpc3Q+XHJcbiAgICAgICAgICAgIDwvPilcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBoZWFkZXI6IGRhdGFfLmRhdGFfdHlwZS5wcm9wZXJ0aWVzW2tleV0udGl0bGUgPz8ga2V5LFxyXG4gICAgICAgICAgICBhY2Nlc3NvcktleToga2V5LFxyXG4gICAgICAgICAgICBpZDoga2V5LFxyXG4gICAgICAgICAgICAvLyBFZGl0OiAoKSA9PiBudWxsLFxyXG4gICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgfVxyXG4gICAgICB9KVxyXG4gICAgLFxyXG4gICAgW3VzZXJzLmRhdGE/LmRhdGEsIHBsYW5zLmRhdGE/LmRhdGEsIGFyYml0cmF0aW9ucy5kYXRhPy5kYXRhXSxcclxuICApO1xyXG5cclxuICBjb25zdCB0YWJsZSA9IHVzZU1hdGVyaWFsUmVhY3RUYWJsZSh7XHJcbiAgICBjb2x1bW5zLFxyXG4gICAgZGF0YTogZGF0YV8uZXJyb3IgPyBbXSA6IGRhdGFfLmRhdGFfLmRhdGEgPyBkYXRhXy5kYXRhXy5kYXRhIDogW2RhdGFfLmRhdGFfLmRhdGFdLCAvL211c3QgYmUgbWVtb2l6ZWQgb3Igc3RhYmxlICh1c2VTdGF0ZSwgdXNlTWVtbywgZGVmaW5lZCBvdXRzaWRlIG9mIHRoaXMgY29tcG9uZW50LCBldGMuKVxyXG4gICAgcm93Q291bnQ6IGRhdGFfLmVycm9yID8gMCA6IGRhdGFfLmRhdGFfLmRhdGEgPyBkYXRhXy5kYXRhXy5kYXRhLmxlbmd0aCA6IDEsXHJcbiAgICBlbmFibGVSb3dTZWxlY3Rpb246IHRydWUsIC8vZW5hYmxlIHNvbWUgZmVhdHVyZXNcclxuICAgIGVuYWJsZUNvbHVtbk9yZGVyaW5nOiB0cnVlLCAvL2VuYWJsZSBhIGZlYXR1cmUgZm9yIGFsbCBjb2x1bW5zXHJcbiAgICBlbmFibGVHbG9iYWxGaWx0ZXI6IHRydWUsIC8vdHVybiBvZmYgYSBmZWF0dXJlXHJcbiAgICBlbmFibGVHcm91cGluZzogdHJ1ZSxcclxuICAgIGVuYWJsZVJvd0FjdGlvbnM6IHRydWUsXHJcbiAgICBlbmFibGVSb3dQaW5uaW5nOiB0cnVlLFxyXG4gICAgZW5hYmxlU3RpY2t5SGVhZGVyOiB0cnVlLFxyXG4gICAgZW5hYmxlU3RpY2t5Rm9vdGVyOiB0cnVlLFxyXG4gICAgZW5hYmxlQ29sdW1uUGlubmluZzogdHJ1ZSxcclxuICAgIGVuYWJsZUNvbHVtblJlc2l6aW5nOiB0cnVlLFxyXG4gICAgZW5hYmxlUm93TnVtYmVyczogdHJ1ZSxcclxuICAgIGVuYWJsZUV4cGFuZEFsbDogdHJ1ZSxcclxuICAgIGVuYWJsZUVkaXRpbmc6IHRydWUsXHJcbiAgICBlbmFibGVFeHBhbmRpbmc6IHRydWUsXHJcbiAgICBtYW51YWxQYWdpbmF0aW9uOiB0cnVlLFxyXG4gICAgaW5pdGlhbFN0YXRlOiB7XHJcbiAgICAgIHBhZ2luYXRpb246IHsgcGFnZVNpemU6IDUsIHBhZ2VJbmRleDogMSB9LFxyXG4gICAgICBjb2x1bW5WaXNpYmlsaXR5OiB7IHJlcG9ydDogZmFsc2UsIGNyZWF0ZWRfYnk6IGZhbHNlLCBjcmVhdGVkOiBmYWxzZSwgbW9kZmllZF9ieTogZmFsc2UsIG1vZGlmaWVkOiBmYWxzZSwgbW9kaWZpZWRfYnk6IGZhbHNlLCBzdGFmZjogZmFsc2UsIGFzc2lzdGFudHM6IGZhbHNlLCBpZDogZmFsc2UsIGRvY3VtZW50OiBmYWxzZSB9LFxyXG4gICAgICBkZW5zaXR5OiAnY29tcGFjdCcsXHJcbiAgICAgIHNob3dHbG9iYWxGaWx0ZXI6IHRydWUsXHJcbiAgICAgIHNvcnRpbmc6IFt7IGlkOiAnaWQnLCBkZXNjOiBmYWxzZSB9XSxcclxuICAgIH0sXHJcbiAgICBzdGF0ZToge1xyXG4gICAgICBwYWdpbmF0aW9uOiBkYXRhXy5wYWdpbmF0aW9uLnBhZ2ksXHJcbiAgICAgIGlzTG9hZGluZzogZGF0YV8uaXNMb2FkaW5nLCAvL2NlbGwgc2tlbGV0b25zIGFuZCBsb2FkaW5nIG92ZXJsYXlcclxuICAgICAgLy9zaG93UHJvZ3Jlc3NCYXJzOiBpc0xvYWRpbmcsIC8vcHJvZ3Jlc3MgYmFycyB3aGlsZSByZWZldGNoaW5nXHJcbiAgICAgIC8vIGlzU2F2aW5nOiBpc1NhdmluZ1RvZG9zLCAvL3Byb2dyZXNzIGJhcnMgYW5kIHNhdmUgYnV0dG9uIHNwaW5uZXJzXHJcbiAgICB9LFxyXG4gICAgbG9jYWxpemF0aW9uOiBNUlRfTG9jYWxpemF0aW9uX0ZSLFxyXG4gICAgb25QYWdpbmF0aW9uQ2hhbmdlOiBvblBhZ2luYXRpb25DaGFuZ2UsXHJcbiAgICBkaXNwbGF5Q29sdW1uRGVmT3B0aW9uczoge1xyXG4gICAgICAnbXJ0LXJvdy1waW4nOiB7XHJcbiAgICAgICAgZW5hYmxlSGlkaW5nOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICAnbXJ0LXJvdy1leHBhbmQnOiB7XHJcbiAgICAgICAgZW5hYmxlSGlkaW5nOiB0cnVlLFxyXG4gICAgICB9LFxyXG4gICAgICAnbXJ0LXJvdy1hY3Rpb25zJzoge1xyXG4gICAgICAgIC8vIGhlYWRlcjogJ0VkaXQnLCAvL2NoYW5nZSBcIkFjdGlvbnNcIiB0byBcIkVkaXRcIlxyXG4gICAgICAgIHNpemU6IDEwMCxcclxuICAgICAgICBlbmFibGVIaWRpbmc6IHRydWUsXHJcblxyXG4gICAgICAgIC8vdXNlIGEgdGV4dCBidXR0b24gaW5zdGVhZCBvZiBhIGljb24gYnV0dG9uXHJcbiAgICAgICAgLy8gQ2VsbDogKHsgcm93LCB0YWJsZSB9KSA9PiAoXHJcbiAgICAgICAgLy8gICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHRhYmxlLnNldEVkaXRpbmdSb3cocm93KX0+RWRpdCBDdXN0b21lcjwvQnV0dG9uPlxyXG4gICAgICAgIC8vICksXHJcbiAgICAgIH0sXHJcbiAgICAgICdtcnQtcm93LW51bWJlcnMnOiB7XHJcbiAgICAgICAgZW5hYmxlSGlkaW5nOiB0cnVlLCAvL25vdyByb3cgbnVtYmVycyBhcmUgaGlkYWJsZSB0b29cclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBkZWZhdWx0Q29sdW1uOiB7XHJcbiAgICAgIGdyb3c6IHRydWUsXHJcbiAgICAgIGVuYWJsZU11bHRpU29ydDogdHJ1ZSxcclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZVBhcGVyUHJvcHM6ICh7IHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIC8vZWxldmF0aW9uOiAwLCAvL2NoYW5nZSB0aGUgbXVpIGJveCBzaGFkb3dcclxuICAgICAgLy9jdXN0b21pemUgcGFwZXIgc3R5bGVzXHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBjbGFzc2VzOiB7IHJvb3Q6ICdwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bCcgfSxcclxuXHJcbiAgICAgIHN4OiB7XHJcbiAgICAgICAgaGVpZ2h0OiBgY2FsYygxMDB2aCAtIDlyZW0pYCxcclxuICAgICAgICAvLyBoZWlnaHQ6IGBjYWxjKDEwMHZoIC0gMjAwcHgpYCxcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIFwiJiAuTXVpVGFibGVQYWdpbmF0aW9uLXJvb3QgXCI6IHtcclxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIFwiJiAuTXVpQm94LXJvb3QgXCI6IHtcclxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfSksXHJcbiAgICBtdWlFZGl0Um93RGlhbG9nUHJvcHM6ICh7IHJvdywgdGFibGUgfSkgPT4gKHtcclxuICAgICAgb3BlbjogZWRpdFZpc2libGUgfHwgY3JlYXRlVmlzaWJsZSxcclxuICAgICAgc3g6IHtcclxuICAgICAgICAnJiAuTXVpRGlhbG9nLXJvb3QnOiB7XHJcbiAgICAgICAgICBkaXNwbGF5OiAnbm9uZSdcclxuICAgICAgICB9LFxyXG4gICAgICAgICcmIC5NdWlEaWFsb2ctY29udGFpbmVyJzoge1xyXG4gICAgICAgICAgZGlzcGxheTogJ25vbmUnXHJcbiAgICAgICAgfSxcclxuICAgICAgICB6SW5kZXg6IDExMDBcclxuXHJcbiAgICAgIH1cclxuICAgIH0pLFxyXG4gICAgZWRpdERpc3BsYXlNb2RlOiAnbW9kYWwnLFxyXG4gICAgY3JlYXRlRGlzcGxheU1vZGU6ICdtb2RhbCcsXHJcbiAgICBvbkVkaXRpbmdSb3dTYXZlOiAoeyB0YWJsZSwgdmFsdWVzLCByb3cgfSkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIm9uRWRpdGluZ1Jvd1NhdmVcIiwgdmFsdWVzKVxyXG4gICAgICBjb25zdCB7IGlkLCAuLi5yZXN0IH0gPSB2YWx1ZXNcclxuICAgICAgcmVzdC5wbGFuID0gdmFsdWVzLnBsYW4uaWRcclxuICAgICAgcmVzdC50ZWFtID0gdmFsdWVzLnRlYW0/Lm1hcCh1c2VyID0+IHVzZXIuaWQpIHx8IFtdXHJcbiAgICAgIGFyYml0cmF0aW9uX3BhdGNoX3RyaWdnZXIocmVzdCwge1xyXG4gICAgICAgIHJldmFsaWRhdGU6IHRydWUsXHJcbiAgICAgICAgb25TdWNjZXNzOiAoKSA9PiB7XHJcbiAgICAgICAgICB0YWJsZS5zZXRFZGl0aW5nUm93KG51bGwpOyAvL2V4aXQgY3JlYXRpbmcgbW9kZVxyXG4gICAgICAgICAgdG9hc3QuY3VycmVudCEuc2hvdyh7IHNldmVyaXR5OiAnc3VjY2VzcycsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiBgQXJiaXRyYWdlICR7dmFsdWVzLnBsYW4uY29kZX0gbWlzIMOgIGFqb3VyYCB9KTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgdG9hc3QuY3VycmVudCEuc2hvdyh7IHNldmVyaXR5OiAnZXJyb3InLCBzdW1tYXJ5OiAnSW5mbycsIGRldGFpbDogYCR7ZXJyb3IucmVzcG9uc2U/LnN0YXR1c1RleHR9YCB9KTtcclxuICAgICAgICAgIC8vY29uc29sZS5sb2coXCJvbkVkaXRpbmdSb3dTYXZlXCIsIGVycm9yLnJlc3BvbnNlKTtcclxuICAgICAgICAgIHJvdy5fdmFsdWVzQ2FjaGUgPSB7IGVycm9yOiBlcnJvci5yZXNwb25zZSwgLi4ucm93Ll92YWx1ZXNDYWNoZSB9O1xyXG4gICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuICAgICAgfSlcclxuICAgIH0sXHJcbiAgICBvbkVkaXRpbmdSb3dDYW5jZWw6ICgpID0+IHtcclxuICAgICAgLy9jbGVhciBhbnkgdmFsaWRhdGlvbiBlcnJvcnNcclxuICAgICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnaW5mbycsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiAnQW5udWxhdGlvbicgfSk7XHJcbiAgICB9LFxyXG4gICAgb25DcmVhdGluZ1Jvd1NhdmU6ICh7IHRhYmxlLCB2YWx1ZXMsIHJvdyB9KSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwib25DcmVhdGluZ1Jvd1NhdmVcIiwgdmFsdWVzKVxyXG4gICAgICBjb25zdCB7IGlkLCAuLi5yZXN0IH0gPSB2YWx1ZXNcclxuICAgICAgcmVzdC5wbGFuID0gdmFsdWVzLnBsYW4uaWRcclxuICAgICAgcmVzdC50ZWFtID0gdmFsdWVzLnRlYW0/Lm1hcCh1c2VyID0+IHVzZXIuaWQpIHx8IFtdXHJcbiAgICAgIGFyYml0cmF0aW9uX2NyZWF0ZV90cmlnZ2VyKHJlc3QsIHtcclxuICAgICAgICByZXZhbGlkYXRlOiB0cnVlLFxyXG4gICAgICAgIG9uU3VjY2VzczogKCkgPT4ge1xyXG4gICAgICAgICAgdGFibGUuc2V0Q3JlYXRpbmdSb3cobnVsbCk7IC8vZXhpdCBjcmVhdGluZyBtb2RlXHJcbiAgICAgICAgICB0b2FzdC5jdXJyZW50IS5zaG93KHsgc2V2ZXJpdHk6ICdzdWNjZXNzJywgc3VtbWFyeTogJ0luZm8nLCBkZXRhaWw6IGBBcmJpdHJhZ2UgJHt2YWx1ZXMucGxhbi5jb2RlfSBjcsOpw6lgIH0pO1xyXG4gICAgICAgIH0sXHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICB0b2FzdC5jdXJyZW50IS5zaG93KHsgc2V2ZXJpdHk6ICdlcnJvcicsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiBgJHtlcnJvci5yZXNwb25zZT8uc3RhdHVzVGV4dH1gIH0pO1xyXG4gICAgICAgICAgLy9jb25zb2xlLmxvZyhcIm9uRWRpdGluZ1Jvd1NhdmVcIiwgZXJyb3IucmVzcG9uc2UpO1xyXG4gICAgICAgICAgcm93Ll92YWx1ZXNDYWNoZSA9IHsgZXJyb3I6IGVycm9yLnJlc3BvbnNlLCAuLi5yb3cuX3ZhbHVlc0NhY2hlIH07XHJcbiAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICB9KVxyXG4gICAgfSxcclxuICAgIG9uQ3JlYXRpbmdSb3dDYW5jZWw6ICgpID0+IHtcclxuICAgICAgdG9hc3QuY3VycmVudD8uc2hvdyh7IHNldmVyaXR5OiAnaW5mbycsIHN1bW1hcnk6ICdJbmZvJywgZGV0YWlsOiAnQW5udWxhdGlvbicgfSk7XHJcbiAgICB9LFxyXG4gICAgbXVpVGFibGVGb290ZXJQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBcIiYgLk11aVRhYmxlRm9vdGVyLXJvb3RcIjoge1xyXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXN1cmZhY2UtY2FyZCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgZm9udEZhbWlseTogXCJ2YXIoLS1mb250LWZhbWlseSlcIixcclxuICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIG11aVRhYmxlQ29udGFpbmVyUHJvcHM6ICh7IHRhYmxlIH0pID0+ICh7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgaGVpZ2h0OiB0YWJsZS5nZXRTdGF0ZSgpLmlzRnVsbFNjcmVlbiA/IGBjYWxjKDEwMHZoKWAgOiBgY2FsYygxMDB2aCAtIDlyZW0gLSAke3RhYmxlLnJlZnMudG9wVG9vbGJhclJlZi5jdXJyZW50Py5vZmZzZXRIZWlnaHR9cHggLSAke3RhYmxlLnJlZnMuYm90dG9tVG9vbGJhclJlZi5jdXJyZW50Py5vZmZzZXRIZWlnaHR9cHgpYFxyXG5cclxuICAgICAgfSxcclxuICAgIH0pLFxyXG4gICAgbXVpUGFnaW5hdGlvblByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG5cclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpICFpbXBvcnRhbnRcIixcclxuICAgICAgICBjb2xvcjogXCJ2YXIoLS1zdXJmYWNlLTkwMCkgIWltcG9ydGFudFwiLFxyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZUhlYWRDZWxsUHJvcHM6IHtcclxuICAgICAgc3g6IHtcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgbXVpVG9wVG9vbGJhclByb3BzOiB7XHJcbiAgICAgIGNsYXNzTmFtZTogXCJwLWRhdGF0YWJsZS1ncmlkbGluZXMgdGV4dC05MDAgZm9udC1tZWRpdW0gdGV4dC14bFwiLFxyXG4gICAgICBzeDoge1xyXG4gICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICAvLyBib3JkZXJSYWRpdXM6ICcwJyxcclxuICAgICAgICAvLyBib3JkZXI6ICcxcHggZGFzaGVkICNlMGUwZTAnLFxyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1zdXJmYWNlLWNhcmQpXCIsXHJcbiAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIlxyXG4gICAgICB9LFxyXG5cclxuICAgIH0sXHJcbiAgICBtdWlUYWJsZUJvZHlQcm9wczoge1xyXG4gICAgICBjbGFzc05hbWU6IFwicC1kYXRhdGFibGUtZ3JpZGxpbmVzIHRleHQtOTAwIGZvbnQtbWVkaXVtIHRleHQteGxcIixcclxuICAgICAgc3g6IHtcclxuICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgIGZvbnRGZWF0dXJlU2V0dGluZ3M6IFwidmFyKC0tZm9udC1mZWF0dXJlLXNldHRpbmdzLCBub3JtYWwpXCIsXHJcbiAgICAgICAgLy9zdHJpcGUgdGhlIHJvd3MsIG1ha2Ugb2RkIHJvd3MgYSBkYXJrZXIgY29sb3JcclxuICAgICAgICAnJiB0cjpudGgtb2YtdHlwZShvZGQpID4gdGQnOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1zdXJmYWNlLWNhcmQpJyxcclxuICAgICAgICAgIGNvbG9yOiBcInZhcigtLXN1cmZhY2UtOTAwKSAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICBmb250RmFtaWx5OiBcInZhcigtLWZvbnQtZmFtaWx5KVwiLFxyXG4gICAgICAgICAgZm9udEZlYXR1cmVTZXR0aW5nczogXCJ2YXIoLS1mb250LWZlYXR1cmUtc2V0dGluZ3MsIG5vcm1hbClcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgICcmIHRyOm50aC1vZi10eXBlKGV2ZW4pID4gdGQnOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1zdXJmYWNlLWJvcmRlciknLFxyXG4gICAgICAgICAgY29sb3I6IFwidmFyKC0tc3VyZmFjZS05MDApICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgIGZvbnRGYW1pbHk6IFwidmFyKC0tZm9udC1mYW1pbHkpXCIsXHJcbiAgICAgICAgICBmb250RmVhdHVyZVNldHRpbmdzOiBcInZhcigtLWZvbnQtZmVhdHVyZS1zZXR0aW5ncywgbm9ybWFsKVwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgcmVuZGVyVG9wVG9vbGJhckN1c3RvbUFjdGlvbnM6ICh7IHRhYmxlIH0pID0+IChcclxuICAgICAgPFN0YWNrIGRpcmVjdGlvbj17XCJyb3dcIn0gc3BhY2luZz17MX0+XHJcbiAgICAgICAgPENhbiBJPVwiYWRkXCIgYT0nYXJiaXRyYXRpb24nPlxyXG4gICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICBpY29uPVwicGkgcGktcGx1c1wiXHJcbiAgICAgICAgICAgIHJvdW5kZWRcclxuICAgICAgICAgICAgLy8gaWQ9XCJiYXNpYy1idXR0b25cIlxyXG4gICAgICAgICAgICBhcmlhLWNvbnRyb2xzPXtvcGVuID8gJ2Jhc2ljLW1lbnUnIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICBhcmlhLWhhc3BvcHVwPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgIGFyaWEtZXhwYW5kZWQ9e29wZW4gPyAndHJ1ZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eyhldmVudCkgPT4ge1xyXG4gICAgICAgICAgICAgIHRhYmxlLnNldENyZWF0aW5nUm93KHRydWUpOyBzZXRDcmVhdGVWaXNpYmxlKHRydWUpLCBjb25zb2xlLmxvZyhcImNyZWF0aW5nIHJvdyAuLi5cIik7XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICA8L0Nhbj5cclxuICAgICAgICA8Q2FuIEk9XCJkZWxldGVcIiBhPSdhcmJpdHJhdGlvbic+XHJcbiAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgIHJvdW5kZWRcclxuICAgICAgICAgICAgZGlzYWJsZWQ9e3RhYmxlLmdldElzU29tZVJvd3NTZWxlY3RlZCgpfVxyXG4gICAgICAgICAgICAvLyBpZD1cImJhc2ljLWJ1dHRvblwiXHJcbiAgICAgICAgICAgIGFyaWEtY29udHJvbHM9e29wZW4gPyAnYmFzaWMtbWVudScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgIGFyaWEtaGFzcG9wdXA9XCJ0cnVlXCJcclxuICAgICAgICAgICAgYXJpYS1leHBhbmRlZD17b3BlbiA/ICd0cnVlJyA6IHVuZGVmaW5lZH1cclxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2xpY2t9XHJcbiAgICAgICAgICAgIGljb249XCJwaSBwaS10cmFzaFwiXHJcbiAgICAgICAgICAgIC8vIHN0eWxlPXt7ICBib3JkZXJSYWRpdXM6ICcwJywgYm94U2hhZG93OiAnbm9uZScsIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JyB9fVxyXG4gICAgICAgICAgICBzaXplPVwic21hbGxcIlxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0Nhbj5cclxuICAgICAgPC9TdGFjaz5cclxuICAgICksXHJcbiAgICBtdWlEZXRhaWxQYW5lbFByb3BzOiAoKSA9PiAoe1xyXG4gICAgICBzeDogKHRoZW1lKSA9PiAoe1xyXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjpcclxuICAgICAgICAgIHRoZW1lLnBhbGV0dGUubW9kZSA9PT0gJ2RhcmsnXHJcbiAgICAgICAgICAgID8gJ3JnYmEoMjU1LDIxMCwyNDQsMC4xKSdcclxuICAgICAgICAgICAgOiAncmdiYSgwLDAsMCwwLjEpJyxcclxuICAgICAgfSksXHJcbiAgICB9KSxcclxuICAgIHJlbmRlckNyZWF0ZVJvd0RpYWxvZ0NvbnRlbnQ6ICh7IGludGVybmFsRWRpdENvbXBvbmVudHMsIHJvdywgdGFibGUgfSkgPT5cclxuICAgICAgPGRpdiBzdHlsZT17eyB6SW5kZXg6ICcxMzAyICFpbXBvcnRhbnQnIH19PlxyXG4gICAgICAgIDxTaWRlYmFyIHBvc2l0aW9uPSdyaWdodCdcclxuICAgICAgICAgIGhlYWRlcj17XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtcm93IHctZnVsbCBmbGV4LXdyYXAganVzdGlmeS1jb250ZW50LWJldHdlZW4nPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nYWxpZ24tY29udGVudC1jZW50ZXIgJz5DcsOpYXRpb24gbm91dmVhdSBhcmJpdHJhZ2U8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPERpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgICAgICA8TVJUX0VkaXRBY3Rpb25CdXR0b25zIHZhcmlhbnQ9XCJ0ZXh0XCIgdGFibGU9e3RhYmxlfSByb3c9e3Jvd30gLz5cclxuICAgICAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdmlzaWJsZT17Y3JlYXRlVmlzaWJsZX1cclxuICAgICAgICAgIG9uSGlkZT17KCkgPT4geyB0YWJsZS5zZXRDcmVhdGluZ1JvdyhudWxsKTsgc2V0Q3JlYXRlVmlzaWJsZShmYWxzZSkgfX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTkgbGc6dy04XCI+XHJcbiAgICAgICAgICA8RGlhbG9nQ29udGVudFxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcclxuICAgICAgICAgICAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcclxuICAgICAgICAgICAgICBnYXA6ICcxLjVyZW0nXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHtpbnRlcm5hbEVkaXRDb21wb25lbnRzfVxyXG4gICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICAgIDwvU2lkZWJhcj5cclxuICAgICAgPC9kaXY+LFxyXG4gICAgcmVuZGVyRWRpdFJvd0RpYWxvZ0NvbnRlbnQ6ICh7IGludGVybmFsRWRpdENvbXBvbmVudHMsIHJvdywgdGFibGUgfSkgPT5cclxuICAgICAgPGRpdiBzdHlsZT17eyB6SW5kZXg6ICcxMzAyICFpbXBvcnRhbnQnIH19PlxyXG4gICAgICAgIDxTaWRlYmFyIHBvc2l0aW9uPSdyaWdodCdcclxuICAgICAgICAgIGhlYWRlcj17XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGZsZXgtcm93IHctZnVsbCBmbGV4LXdyYXAganVzdGlmeS1jb250ZW50LWJldHdlZW4nPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9J2FsaWduLWNvbnRlbnQtY2VudGVyICc+RWRpdGVyIGwnYXJiaXRyYWdlIG7CsCB7cm93Lm9yaWdpbmFsLmlkfTwvaDM+XHJcbiAgICAgICAgICAgICAgPERpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgICAgICA8TVJUX0VkaXRBY3Rpb25CdXR0b25zIHZhcmlhbnQ9XCJ0ZXh0XCIgdGFibGU9e3RhYmxlfSByb3c9e3Jvd30gLz5cclxuICAgICAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdmlzaWJsZT17ZWRpdFZpc2libGV9XHJcbiAgICAgICAgICBvbkhpZGU9eygpID0+IHtcclxuICAgICAgICAgICAgdGFibGUuc2V0RWRpdGluZ1JvdyhudWxsKTtcclxuICAgICAgICAgICAgc2V0RWRpdFZpc2libGUoZmFsc2UpXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctOSBsZzp3LThcIj5cclxuICAgICAgICAgIDxEaWFsb2dDb250ZW50XHJcbiAgICAgICAgICAgIHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsIGdhcDogJzEuNXJlbScgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAge2ludGVybmFsRWRpdENvbXBvbmVudHN9IHsvKiBvciByZW5kZXIgY3VzdG9tIGVkaXQgY29tcG9uZW50cyBoZXJlICovfVxyXG4gICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICAgIDwvU2lkZWJhcj5cclxuICAgICAgPC9kaXY+LFxyXG4gICAgcmVuZGVyRGV0YWlsUGFuZWw6ICh7IHJvdyB9KSA9PlxyXG4gICAgICBwYXJzZShyb3cub3JpZ2luYWwucmVwb3J0KSxcclxuICAgIHJlbmRlclJvd0FjdGlvbnM6ICh7IGNlbGwsIHJvdywgdGFibGUgfSkgPT4gKFxyXG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJwLWJ1dHRvbnNldCBmbGV4IHAtMVwiPlxyXG4gICAgICAgIDxDYW4gST1cInVwZGF0ZVwiIGE9J2FyYml0cmF0aW9uJz5cclxuICAgICAgICAgIDxCdXR0b24gc2l6ZT0nc21hbGwnIGljb249XCJwaSBwaS1wZW5jaWxcIiBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgIHNldEFyYml0cmF0aW9uSUQocm93Lm9yaWdpbmFsLmlkKTtcclxuICAgICAgICAgICAgdGFibGUuc2V0RWRpdGluZ1Jvdyhyb3cpO1xyXG4gICAgICAgICAgICBzZXRFZGl0VmlzaWJsZSh0cnVlKTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJlZGl0aW5nIHJvdyAuLi5cIik7XHJcbiAgICAgICAgICB9fSByb3VuZGVkIG91dGxpbmVkIC8+XHJcbiAgICAgICAgPC9DYW4+XHJcbiAgICAgICAgPENhbiBJPVwiZGVsZXRlXCIgYT0nYXJiaXRyYXRpb24nPlxyXG4gICAgICAgICAgPEJ1dHRvbiBzaXplPSdzbWFsbCcgaWNvbj1cInBpIHBpLXRyYXNoXCIgcm91bmRlZCBvdXRsaW5lZFxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoZXZlbnQpID0+IGNvbmZpcm1Qb3B1cCh7XHJcbiAgICAgICAgICAgICAgdGFyZ2V0OiBldmVudC5jdXJyZW50VGFyZ2V0LFxyXG4gICAgICAgICAgICAgIG1lc3NhZ2U6ICdWb3VsZXotdm91cyBzdXBwcmltZXIgY2V0dGUgbGlnbmU/JyxcclxuICAgICAgICAgICAgICBpY29uOiAncGkgcGktaW5mby1jaXJjbGUnLFxyXG4gICAgICAgICAgICAgIC8vIGRlZmF1bHRGb2N1czogJ3JlamVjdCcsXHJcbiAgICAgICAgICAgICAgYWNjZXB0Q2xhc3NOYW1lOiAncC1idXR0b24tZGFuZ2VyJyxcclxuICAgICAgICAgICAgICBhY2NlcHRMYWJlbDogJ091aScsXHJcbiAgICAgICAgICAgICAgcmVqZWN0TGFiZWw6ICdOb24nLFxyXG4gICAgICAgICAgICAgIGFjY2VwdDogYWNjZXB0X3Jvd19kZWxldGlvbixcclxuICAgICAgICAgICAgICByZWplY3Q6IHJlamVjdF9yb3dfZGVsZXRpb25cclxuICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvQ2FuPlxyXG4gICAgICAgIDxDb25maXJtUG9wdXAgLz5cclxuICAgICAgPC9zcGFuPlxyXG4gICAgKSxcclxuICB9KTtcclxuICByZXR1cm4gPD48TWF0ZXJpYWxSZWFjdFRhYmxlIHRhYmxlPXt0YWJsZX0gLz48VG9hc3QgcmVmPXt0b2FzdH0gLz48Lz47XHJcbn1cclxuIl0sIm5hbWVzIjpbIkRpYWxvZ0FjdGlvbnMiLCJEaWFsb2dDb250ZW50IiwiU3RhY2siLCJNUlRfRWRpdEFjdGlvbkJ1dHRvbnMiLCJNYXRlcmlhbFJlYWN0VGFibGUiLCJ1c2VNYXRlcmlhbFJlYWN0VGFibGUiLCJNUlRfTG9jYWxpemF0aW9uX0ZSIiwiQnV0dG9uIiwiQ29uZmlybVBvcHVwIiwiY29uZmlybVBvcHVwIiwiU2lkZWJhciIsIlRhZyIsIlRvYXN0IiwidXNlTWVtbyIsInVzZVJlZiIsInVzZVN0YXRlIiwicGFyc2UiLCJQaWNrTGlzdCIsIkRyb3Bkb3duIiwiZ2V0Q29va2llIiwidXNlYmFzZURhdGEiLCJnZXRVc2VyRnVsbG5hbWUiLCJFZGl0b3IiLCJDYW4iLCJ1c2VBcGlBcmJpdHJhdGlvbkNyZWF0ZSIsInVzZUFwaUFyYml0cmF0aW9uUGFydGlhbFVwZGF0ZSIsIkdlbmVyaWNUYWJsZSIsImRhdGFfIiwidXNlcnMiLCJwbGFucyIsImFyYml0cmF0aW9ucyIsInVzZXIiLCJKU09OIiwidG9TdHJpbmciLCJ0b2FzdCIsInVzZXJzX2RhdGEiLCJkYXRhIiwiYXJiaXRyYXRpb25JRCIsInNldEFyYml0cmF0aW9uSUQiLCJlZGl0VmlzaWJsZSIsInNldEVkaXRWaXNpYmxlIiwiY3JlYXRlVmlzaWJsZSIsInNldENyZWF0ZVZpc2libGUiLCJwaWNrbGlzdFRhcmdldFZhbHVlVGVhbSIsInNldFBpY2tsaXN0VGFyZ2V0VmFsdWVUZWFtIiwicGlja2xpc3RTb3VyY2VWYWx1ZVRlYW0iLCJzZXRQaWNrbGlzdFNvdXJjZVZhbHVlVGVhbSIsInJvd1RvYmUiLCJzZXRSb3dUb2JlIiwibXV0YXRlIiwiYXJiaXRyYXRpb25fY3JlYXRlX3RyaWdnZXIiLCJpc1BlbmRpbmciLCJpc0NyZWF0ZU11dGF0aW5nIiwiYXJiaXRyYXRpb25fcGF0Y2hfdHJpZ2dlciIsImlzUGF0Y2hNdXRhdGluZyIsIm51bVBhZ2VzIiwic2V0TnVtUGFnZXMiLCJwYWdlTnVtYmVyIiwic2V0UGFnZU51bWJlciIsImFuY2hvckVsIiwic2V0QW5jaG9yRWwiLCJyb3dBY3Rpb25FbmFibGVkIiwic2V0Um93QWN0aW9uRW5hYmxlZCIsIm9wZW4iLCJCb29sZWFuIiwib25QYWdpbmF0aW9uQ2hhbmdlIiwic3RhdGUiLCJjb25zb2xlIiwibG9nIiwicGFnaW5hdGlvbiIsInNldCIsIm9uRG9jdW1lbnRMb2FkU3VjY2VzcyIsImNoYW5nZVBhZ2UiLCJvZmZzZXQiLCJwcmV2UGFnZU51bWJlciIsInByZXZpb3VzUGFnZSIsIm5leHRQYWdlIiwiYWNjZXB0X3Jvd19kZWxldGlvbiIsImN1cnJlbnQiLCJzaG93Iiwic2V2ZXJpdHkiLCJzdW1tYXJ5IiwiZGV0YWlsIiwibGlmZSIsInJlamVjdF9yb3dfZGVsZXRpb24iLCJoYW5kbGVDbGljayIsImV2ZW50IiwiY3VycmVudFRhcmdldCIsImNvbHVtbnMiLCJPYmplY3QiLCJlbnRyaWVzIiwiZGF0YV90eXBlIiwicHJvcGVydGllcyIsImZpbHRlciIsImluZGV4Iiwia2V5IiwidmFsdWUiLCJpbmNsdWRlcyIsIm1hcCIsImhlYWRlciIsInRpdGxlIiwiYWNjZXNzb3JLZXkiLCJpZCIsIkVkaXQiLCJjZWxsIiwiY29sdW1uIiwicm93IiwidGFibGUiLCJsYWJlbCIsImNsYXNzTmFtZSIsIm9yaWdpbmFsIiwicmVwb3J0Iiwib25UZXh0Q2hhbmdlIiwiZSIsIl92YWx1ZXNDYWNoZSIsImh0bWxWYWx1ZSIsInN0eWxlIiwiaGVpZ2h0IiwibXVpVGFibGVIZWFkQ2VsbFByb3BzIiwiYWxpZ24iLCJtdWlUYWJsZUJvZHlDZWxsUHJvcHMiLCJtdWlUYWJsZUZvb3RlckNlbGxQcm9wcyIsIkNlbGwiLCJnZXRWYWx1ZSIsImNvZGUiLCJvcHRpb25MYWJlbCIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJwbGFuIiwicmVzdWx0cyIsImZpbmQiLCJuYW1lIiwib3B0aW9ucyIsImZvcm1hdCIsImFjY2Vzc29yRm4iLCJEYXRlIiwiY3JlYXRlZCIsIm1vZGlmaWVkIiwiZmlsdGVyVmFyaWFudCIsImZpbHRlckZuIiwic29ydGluZ0ZuIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwidWwiLCJ1c3IiLCJsaSIsInRlYW0iLCJzb3VyY2UiLCJsZW5ndGgiLCJ1c2VybmFtZSIsInRhcmdldCIsInNvdXJjZUhlYWRlciIsInRhcmdldEhlYWRlciIsIml0ZW1UZW1wbGF0ZSIsIml0ZW0iLCJkaXYiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwic291cmNlU3R5bGUiLCJ0YXJnZXRTdHlsZSIsImZpbHRlckJ5IiwiZmlsdGVyTWF0Y2hNb2RlIiwic291cmNlRmlsdGVyUGxhY2Vob2xkZXIiLCJ0YXJnZXRGaWx0ZXJQbGFjZWhvbGRlciIsImVycm9yIiwicm93Q291bnQiLCJlbmFibGVSb3dTZWxlY3Rpb24iLCJlbmFibGVDb2x1bW5PcmRlcmluZyIsImVuYWJsZUdsb2JhbEZpbHRlciIsImVuYWJsZUdyb3VwaW5nIiwiZW5hYmxlUm93QWN0aW9ucyIsImVuYWJsZVJvd1Bpbm5pbmciLCJlbmFibGVTdGlja3lIZWFkZXIiLCJlbmFibGVTdGlja3lGb290ZXIiLCJlbmFibGVDb2x1bW5QaW5uaW5nIiwiZW5hYmxlQ29sdW1uUmVzaXppbmciLCJlbmFibGVSb3dOdW1iZXJzIiwiZW5hYmxlRXhwYW5kQWxsIiwiZW5hYmxlRWRpdGluZyIsImVuYWJsZUV4cGFuZGluZyIsIm1hbnVhbFBhZ2luYXRpb24iLCJpbml0aWFsU3RhdGUiLCJwYWdlU2l6ZSIsInBhZ2VJbmRleCIsImNvbHVtblZpc2liaWxpdHkiLCJjcmVhdGVkX2J5IiwibW9kZmllZF9ieSIsIm1vZGlmaWVkX2J5Iiwic3RhZmYiLCJhc3Npc3RhbnRzIiwiZG9jdW1lbnQiLCJkZW5zaXR5Iiwic2hvd0dsb2JhbEZpbHRlciIsInNvcnRpbmciLCJkZXNjIiwicGFnaSIsImlzTG9hZGluZyIsImxvY2FsaXphdGlvbiIsImRpc3BsYXlDb2x1bW5EZWZPcHRpb25zIiwiZW5hYmxlSGlkaW5nIiwic2l6ZSIsImRlZmF1bHRDb2x1bW4iLCJncm93IiwiZW5hYmxlTXVsdGlTb3J0IiwibXVpVGFibGVQYXBlclByb3BzIiwiY2xhc3NlcyIsInJvb3QiLCJzeCIsImJhY2tncm91bmRDb2xvciIsImZvbnRGYW1pbHkiLCJmb250RmVhdHVyZVNldHRpbmdzIiwiY29sb3IiLCJtdWlFZGl0Um93RGlhbG9nUHJvcHMiLCJkaXNwbGF5IiwiekluZGV4IiwiZWRpdERpc3BsYXlNb2RlIiwiY3JlYXRlRGlzcGxheU1vZGUiLCJvbkVkaXRpbmdSb3dTYXZlIiwidmFsdWVzIiwicmVzdCIsInJldmFsaWRhdGUiLCJvblN1Y2Nlc3MiLCJzZXRFZGl0aW5nUm93Iiwib25FcnJvciIsInJlc3BvbnNlIiwic3RhdHVzVGV4dCIsIm9uRWRpdGluZ1Jvd0NhbmNlbCIsIm9uQ3JlYXRpbmdSb3dTYXZlIiwic2V0Q3JlYXRpbmdSb3ciLCJvbkNyZWF0aW5nUm93Q2FuY2VsIiwibXVpVGFibGVGb290ZXJQcm9wcyIsIm11aVRhYmxlQ29udGFpbmVyUHJvcHMiLCJnZXRTdGF0ZSIsImlzRnVsbFNjcmVlbiIsInJlZnMiLCJ0b3BUb29sYmFyUmVmIiwib2Zmc2V0SGVpZ2h0IiwiYm90dG9tVG9vbGJhclJlZiIsIm11aVBhZ2luYXRpb25Qcm9wcyIsIm11aVRvcFRvb2xiYXJQcm9wcyIsIm11aVRhYmxlQm9keVByb3BzIiwicmVuZGVyVG9wVG9vbGJhckN1c3RvbUFjdGlvbnMiLCJkaXJlY3Rpb24iLCJzcGFjaW5nIiwiSSIsImEiLCJpY29uIiwicm91bmRlZCIsImFyaWEtY29udHJvbHMiLCJ1bmRlZmluZWQiLCJhcmlhLWhhc3BvcHVwIiwiYXJpYS1leHBhbmRlZCIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImdldElzU29tZVJvd3NTZWxlY3RlZCIsIm11aURldGFpbFBhbmVsUHJvcHMiLCJ0aGVtZSIsInBhbGV0dGUiLCJtb2RlIiwicmVuZGVyQ3JlYXRlUm93RGlhbG9nQ29udGVudCIsImludGVybmFsRWRpdENvbXBvbmVudHMiLCJwb3NpdGlvbiIsInNwYW4iLCJ2YXJpYW50IiwidmlzaWJsZSIsIm9uSGlkZSIsImZsZXhEaXJlY3Rpb24iLCJnYXAiLCJyZW5kZXJFZGl0Um93RGlhbG9nQ29udGVudCIsImgzIiwicmVuZGVyRGV0YWlsUGFuZWwiLCJyZW5kZXJSb3dBY3Rpb25zIiwib3V0bGluZWQiLCJtZXNzYWdlIiwiYWNjZXB0Q2xhc3NOYW1lIiwiYWNjZXB0TGFiZWwiLCJyZWplY3RMYWJlbCIsImFjY2VwdCIsInJlamVjdCIsInJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx\n"));

/***/ })

});