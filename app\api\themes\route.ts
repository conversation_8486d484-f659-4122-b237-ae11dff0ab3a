import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/themes
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const validated = searchParams.get('validated')
    const domainId = searchParams.get('domainId')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' as const } },
        { code: { contains: search, mode: 'insensitive' as const } },
      ]
    }
    
    if (validated !== null && validated !== undefined) {
      where.validated = validated === 'true'
    }
    
    if (domainId) {
      where.domainId = parseInt(domainId)
    }
    
    const [themes, total] = await Promise.all([
      prisma.theme.findMany({
        where,
        skip,
        take: limit,
        include: {
          domain: true,
          process: true,
          proposingStructures: true,
          concernedStructures: true,
          risks: true,
          goals: true,
          arbitratedThemes: {
            include: {
              arbitration: {
                include: {
                  plan: true,
                },
              },
              missions: {
                select: {
                  id: true,
                  code: true,
                  type: true,
                  etat: true,
                },
              },
            },
          },
        },
        orderBy: { id: 'desc' },
      }),
      prisma.theme.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: themes,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching themes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/themes
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      validated = false,
      code,
      domainId,
      processId,
      title,
      proposedBy,
      monthStart,
      monthEnd,
      proposingStructures,
      concernedStructures,
      risks,
      goals,
    } = body
    
    const theme = await prisma.theme.create({
      data: {
        validated,
        code,
        domainId,
        processId,
        title,
        proposedBy,
        monthStart,
        monthEnd,
        proposingStructures: proposingStructures 
          ? { connect: proposingStructures.map((id: number) => ({ id })) } 
          : undefined,
        concernedStructures: concernedStructures 
          ? { connect: concernedStructures.map((id: number) => ({ id })) } 
          : undefined,
        risks: risks 
          ? { connect: risks.map((id: number) => ({ id })) } 
          : undefined,
        goals: goals 
          ? { connect: goals.map((id: number) => ({ id })) } 
          : undefined,
      },
      include: {
        domain: true,
        process: true,
        proposingStructures: true,
        concernedStructures: true,
        risks: true,
        goals: true,
      },
    })
    
    return NextResponse.json(theme, { status: 201 })
  } catch (error) {
    console.error('Error creating theme:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
