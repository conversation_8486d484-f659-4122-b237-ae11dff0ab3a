'use client';

import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import GenericTableOpportunitySheet from '../(components)/GenericTAbleOpportunitySheet';
import { getCookie } from 'cookies-next';
import { usebaseData } from '@/utilities/hooks/useBaseData';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Theme } from '@/services/schemas';
import { $Theme } from '@/services/openapi_client';

const ThemesTable = () => {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')

    const { themes} = usebaseData()
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex:0,
        pageSize: 5, //customize the default page size
    });
    if( themes.isLoading )  return (<ProgressSpinner/>)
    return   (        
        <div className="grid">
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes proposés par C.I</span>
                            <div className="text-900 font-medium text-xl">{themes?.data?.data?.results.filter((thm)=>thm.proposed_by ==='Contrôle Interne').length ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-orange-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-eye text-orange-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">{cmd_missions?.data.count ?? 0}</span>
                    <span className="text-500">since last week</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes proposés par Structures</span>
                            <div className="text-900 font-medium text-xl">{themes?.data?.data.results.filter((thm)=>thm.proposed_by ==='Structures').length  ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-cyan-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-building text-cyan-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">520 </span>
                    <span className="text-500">newly registered</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes proposés par VP</span>
                            <div className="text-900 font-medium text-xl">{themes?.data?.data.results.filter((thm)=>thm.proposed_by ==='Vice Président').length  ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-bolt text-green-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">85 </span>
                    <span className="text-500">responded</span> */}
                </div>
            </div>
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes proposés par A.I</span>
                            <div className="text-900 font-medium text-xl">{themes?.data?.data.results.filter((thm)=>thm.proposed_by ==='Audit Interne').length  ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-eye-slash text-green-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">85 </span>
                    <span className="text-500">responded</span> */}
                </div>
            </div>
            <div className="col-12">
            
                <GenericTableOpportunitySheet<Theme> data_={themes.data} isLoading={themes.isLoading} error={themes.error} data_type={$Theme} pagination={{"set":setPagination,"pagi":pagination}}></GenericTableOpportunitySheet>
            </div>
        </div>
    );
};

export default ThemesTable;
