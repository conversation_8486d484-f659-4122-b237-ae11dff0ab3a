'use client'

import React, { useEffect, useState } from 'react'
import { Card } from 'primereact/card'
import { Chart } from 'primereact/chart'
import { ProgressSpinner } from 'primereact/progressspinner'

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  totalRoles: number
  totalPermissions: number
  recentActivity: any[]
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      // Mock data for now - replace with actual API calls
      setTimeout(() => {
        setStats({
          totalUsers: 45,
          activeUsers: 38,
          totalRoles: 6,
          totalPermissions: 24,
          recentActivity: []
        })
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      setLoading(false)
    }
  }

  const userChartData = {
    labels: ['Active Users', 'Inactive Users'],
    datasets: [
      {
        data: [stats?.activeUsers || 0, (stats?.totalUsers || 0) - (stats?.activeUsers || 0)],
        backgroundColor: ['#42A5F5', '#FFA726'],
        hoverBackgroundColor: ['#64B5F6', '#FFB74D']
      }
    ]
  }

  const chartOptions = {
    plugins: {
      legend: {
        labels: {
          usePointStyle: true
        }
      }
    }
  }

  if (loading) {
    return (
      <div className="flex align-items-center justify-content-center" style={{ height: '400px' }}>
        <ProgressSpinner />
      </div>
    )
  }

  return (
    <div className="grid">
      <div className="col-12 lg:col-6 xl:col-3">
        <div className="card mb-0">
          <div className="flex justify-content-between mb-3">
            <div>
              <span className="block text-500 font-medium mb-3">Total Users</span>
              <div className="text-900 font-medium text-xl">{stats?.totalUsers}</div>
            </div>
            <div className="flex align-items-center justify-content-center bg-blue-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
              <i className="pi pi-users text-blue-500 text-xl" />
            </div>
          </div>
        </div>
      </div>

      <div className="col-12 lg:col-6 xl:col-3">
        <div className="card mb-0">
          <div className="flex justify-content-between mb-3">
            <div>
              <span className="block text-500 font-medium mb-3">Active Users</span>
              <div className="text-900 font-medium text-xl">{stats?.activeUsers}</div>
            </div>
            <div className="flex align-items-center justify-content-center bg-green-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
              <i className="pi pi-user-plus text-green-500 text-xl" />
            </div>
          </div>
        </div>
      </div>

      <div className="col-12 lg:col-6 xl:col-3">
        <div className="card mb-0">
          <div className="flex justify-content-between mb-3">
            <div>
              <span className="block text-500 font-medium mb-3">Total Roles</span>
              <div className="text-900 font-medium text-xl">{stats?.totalRoles}</div>
            </div>
            <div className="flex align-items-center justify-content-center bg-orange-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
              <i className="pi pi-shield text-orange-500 text-xl" />
            </div>
          </div>
        </div>
      </div>

      <div className="col-12 lg:col-6 xl:col-3">
        <div className="card mb-0">
          <div className="flex justify-content-between mb-3">
            <div>
              <span className="block text-500 font-medium mb-3">Permissions</span>
              <div className="text-900 font-medium text-xl">{stats?.totalPermissions}</div>
            </div>
            <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
              <i className="pi pi-key text-purple-500 text-xl" />
            </div>
          </div>
        </div>
      </div>

      <div className="col-12 xl:col-6">
        <div className="card">
          <h5>User Distribution</h5>
          <Chart type="doughnut" data={userChartData} options={chartOptions} className="w-full md:w-30rem" />
        </div>
      </div>

      <div className="col-12 xl:col-6">
        <div className="card">
          <h5>Quick Actions</h5>
          <div className="grid">
            <div className="col-12 md:col-6">
              <div className="p-3 border-1 border-300 border-round cursor-pointer hover:bg-gray-50 transition-colors transition-duration-150">
                <div className="flex align-items-center">
                  <i className="pi pi-user-plus text-blue-500 text-2xl mr-3"></i>
                  <div>
                    <div className="font-medium text-900">Add User</div>
                    <div className="text-500 text-sm">Create new user account</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-12 md:col-6">
              <div className="p-3 border-1 border-300 border-round cursor-pointer hover:bg-gray-50 transition-colors transition-duration-150">
                <div className="flex align-items-center">
                  <i className="pi pi-shield text-orange-500 text-2xl mr-3"></i>
                  <div>
                    <div className="font-medium text-900">Manage Roles</div>
                    <div className="text-500 text-sm">Configure user roles</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-12 md:col-6">
              <div className="p-3 border-1 border-300 border-round cursor-pointer hover:bg-gray-50 transition-colors transition-duration-150">
                <div className="flex align-items-center">
                  <i className="pi pi-key text-purple-500 text-2xl mr-3"></i>
                  <div>
                    <div className="font-medium text-900">Permissions</div>
                    <div className="text-500 text-sm">Manage permissions</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-12 md:col-6">
              <div className="p-3 border-1 border-300 border-round cursor-pointer hover:bg-gray-50 transition-colors transition-duration-150">
                <div className="flex align-items-center">
                  <i className="pi pi-chart-line text-green-500 text-2xl mr-3"></i>
                  <div>
                    <div className="font-medium text-900">Reports</div>
                    <div className="text-500 text-sm">View system reports</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
