import React, { ReactNode } from 'react';
import {
    Page,
    AppBreadcrumbProps,
    Breadcrumb,
    BreadcrumbItem,
    MenuProps,
    MenuModel,
    AppSubMenuProps,
    LayoutConfig,
    LayoutState,
    AppBreadcrumbState,
    Breadcrumb,
    LayoutContextProps,
    MailContextProps,
    MenuContextProps,
    ChatContextProps,
    TaskContextProps,
    AppConfigProps,
    NodeRef,
    AppTopbarRef,
    MenuModelItem,
    AppMenuItemProps,
    AppMenuItem
} from './layout';
import { Demo, LayoutType, SortOrderType, CustomEvent, ChartDataState, ChartOptionsState, AppMailSidebarItem, AppMailReplyProps, AppMailProps } from './demo';
import { PaginatedActionList, PaginatedArbitratedThemeList, PaginatedArbitrationList, PaginatedCommentReadList, PaginatedCriStructviewList, PaginatedDomainList, PaginatedGoalList, PaginatedPlanList, PaginatedProcessList, PaginatedRecommendationList, PaginatedRiskList, PaginatedThemeList, PaginatedUserList } from '@/services/schemas';

type ChildContainerProps = {
    children: ReactNode;
};
type BaseDataProps = {
    missions : AxiosResponse<PaginatedMissionSerializerReadList, any> | undefined
    users : AxiosResponse<PaginatedUserList, any> | undefined
    domains : AxiosResponse<PaginatedDomainList, any> | undefined
    processes : AxiosResponse<PaginatedProcessList, any> | undefined
    plans : AxiosResponse<PaginatedPlanList, any> | undefined
    recommendations : AxiosResponse<PaginatedRecommendationList, any> | undefined
    themes : AxiosResponse<PaginatedThemeList, any> | undefined
    arbitratedThemes : AxiosResponse<PaginatedArbitratedThemeList, any> | undefined
    structureslqs :  AxiosResponse<PaginatedCriStructviewList, any> | undefined
    risks :  AxiosResponse<PaginatedRiskList, any> | undefined
    goals :  AxiosResponse<PaginatedGoalList, any> | undefined,
    actions :  AxiosResponse<PaginatedActionList, any> | undefined,
    arbitrations :  AxiosResponse<PaginatedArbitrationList, any> | undefined,
    comments :  AxiosResponse<PaginatedCommentReadListZ, any> | undefined,
}
export type {
    Page,
    AppBreadcrumbProps,
    Breadcrumb,
    BreadcrumbItem,
    MenuProps,
    MenuModel,
    LayoutConfig,
    LayoutState,
    Breadcrumb,
    LayoutContextProps,
    MailContextProps,
    MenuContextProps,
    ChatContextProps,
    TaskContextProps,
    AppConfigProps,
    NodeRef,
    AppTopbarRef,
    AppMenuItemProps,
    ChildContainerProps,
    BaseDataProps,
    Demo,
    LayoutType,
    SortOrderType,
    CustomEvent,
    ChartDataState,
    ChartOptionsState,
    AppMailSidebarItem,
    AppMailReplyProps,
    AppMailProps,
    AppMenuItem
};
