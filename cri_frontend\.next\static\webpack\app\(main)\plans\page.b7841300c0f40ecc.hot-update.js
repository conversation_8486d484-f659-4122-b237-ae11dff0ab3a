"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/page",{

/***/ "(app-client)/./hooks/useNextApi.ts":
/*!*****************************!*\
  !*** ./hooks/useNextApi.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApiActionCreate: function() { return /* binding */ useApiActionCreate; },\n/* harmony export */   useApiActionDestroy: function() { return /* binding */ useApiActionDestroy; },\n/* harmony export */   useApiActionList: function() { return /* binding */ useApiActionList; },\n/* harmony export */   useApiActionPartialUpdate: function() { return /* binding */ useApiActionPartialUpdate; },\n/* harmony export */   useApiActionRetrieve: function() { return /* binding */ useApiActionRetrieve; },\n/* harmony export */   useApiActionUpdate: function() { return /* binding */ useApiActionUpdate; },\n/* harmony export */   useApiArbitrationCreate: function() { return /* binding */ useApiArbitrationCreate; },\n/* harmony export */   useApiArbitrationDestroy: function() { return /* binding */ useApiArbitrationDestroy; },\n/* harmony export */   useApiArbitrationList: function() { return /* binding */ useApiArbitrationList; },\n/* harmony export */   useApiArbitrationPartialUpdate: function() { return /* binding */ useApiArbitrationPartialUpdate; },\n/* harmony export */   useApiArbitrationRetrieve: function() { return /* binding */ useApiArbitrationRetrieve; },\n/* harmony export */   useApiCommentCreate: function() { return /* binding */ useApiCommentCreate; },\n/* harmony export */   useApiCommentDestroy: function() { return /* binding */ useApiCommentDestroy; },\n/* harmony export */   useApiCommentList: function() { return /* binding */ useApiCommentList; },\n/* harmony export */   useApiCommentPartialUpdate: function() { return /* binding */ useApiCommentPartialUpdate; },\n/* harmony export */   useApiCommentRetrieve: function() { return /* binding */ useApiCommentRetrieve; },\n/* harmony export */   useApiDocsCreate: function() { return /* binding */ useApiDocsCreate; },\n/* harmony export */   useApiDocsDestroy: function() { return /* binding */ useApiDocsDestroy; },\n/* harmony export */   useApiDocsUpdate: function() { return /* binding */ useApiDocsUpdate; },\n/* harmony export */   useApiDocumentsList: function() { return /* binding */ useApiDocumentsList; },\n/* harmony export */   useApiMissionCreate: function() { return /* binding */ useApiMissionCreate; },\n/* harmony export */   useApiMissionDestroy: function() { return /* binding */ useApiMissionDestroy; },\n/* harmony export */   useApiMissionDocsCreate: function() { return /* binding */ useApiMissionDocsCreate; },\n/* harmony export */   useApiMissionDocumentsList: function() { return /* binding */ useApiMissionDocumentsList; },\n/* harmony export */   useApiMissionList: function() { return /* binding */ useApiMissionList; },\n/* harmony export */   useApiMissionPartialUpdate: function() { return /* binding */ useApiMissionPartialUpdate; },\n/* harmony export */   useApiMissionRetrieve: function() { return /* binding */ useApiMissionRetrieve; },\n/* harmony export */   useApiPlanCreate: function() { return /* binding */ useApiPlanCreate; },\n/* harmony export */   useApiPlanDestroy: function() { return /* binding */ useApiPlanDestroy; },\n/* harmony export */   useApiPlanList: function() { return /* binding */ useApiPlanList; },\n/* harmony export */   useApiPlanRetrieve: function() { return /* binding */ useApiPlanRetrieve; },\n/* harmony export */   useApiPlanUpdate: function() { return /* binding */ useApiPlanUpdate; },\n/* harmony export */   useApiRecommendationCreate: function() { return /* binding */ useApiRecommendationCreate; },\n/* harmony export */   useApiRecommendationDestroy: function() { return /* binding */ useApiRecommendationDestroy; },\n/* harmony export */   useApiRecommendationList: function() { return /* binding */ useApiRecommendationList; },\n/* harmony export */   useApiRecommendationPartialUpdate: function() { return /* binding */ useApiRecommendationPartialUpdate; },\n/* harmony export */   useApiRecommendationRetrieve: function() { return /* binding */ useApiRecommendationRetrieve; },\n/* harmony export */   useApiThemeCreate: function() { return /* binding */ useApiThemeCreate; },\n/* harmony export */   useApiThemeDestroy: function() { return /* binding */ useApiThemeDestroy; },\n/* harmony export */   useApiThemeList: function() { return /* binding */ useApiThemeList; },\n/* harmony export */   useApiThemePartialUpdate: function() { return /* binding */ useApiThemePartialUpdate; },\n/* harmony export */   useApiThemeRetrieve: function() { return /* binding */ useApiThemeRetrieve; },\n/* harmony export */   useApiThemeUpdate: function() { return /* binding */ useApiThemeUpdate; },\n/* harmony export */   useApiUserCreate: function() { return /* binding */ useApiUserCreate; },\n/* harmony export */   useApiUserList: function() { return /* binding */ useApiUserList; },\n/* harmony export */   useApiUserRetrieve: function() { return /* binding */ useApiUserRetrieve; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api/nextApi */ \"(app-client)/./services/api/nextApi.ts\");\n// React hooks for Next.js API to replace Django API hooks\n\n\n// Mission hooks\nfunction useApiMissionList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"missions\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMissions(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiMissionRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"missions\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMission(id),\n        enabled: !!id\n    });\n}\nfunction useApiMissionCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createMission(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateMission(id, data);\n        },\n        onSuccess: (data, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteMission(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// Recommendation hooks\nfunction useApiRecommendationList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"recommendations\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRecommendations(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiRecommendationRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"recommendations\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRecommendation(id),\n        enabled: !!id\n    });\n}\nfunction useApiRecommendationCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createRecommendation(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRecommendationPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateRecommendation(id, data);\n        },\n        onSuccess: (data, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRecommendationDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteRecommendation(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// User hooks\nfunction useApiUserList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"users\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUsers(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiUserRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"users\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUser(id),\n        enabled: !!id\n    });\n}\nfunction useApiUserCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createUser(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"users\"\n                ]\n            });\n        }\n    });\n}\n// Plan hooks\nfunction useApiPlanList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"plans\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlans(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiPlanRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"plans\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlan(id),\n        enabled: !!id\n    });\n}\nfunction useApiPlanCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createPlan(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiPlanUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updatePlan(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiPlanDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deletePlan(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n        }\n    });\n}\n// Theme hooks\nfunction useApiThemeList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"themes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getThemes(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiThemeRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"themes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getTheme(id),\n        enabled: !!id\n    });\n}\nfunction useApiThemeCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createTheme(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemeUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemePartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemeDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteTheme(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Arbitration hooks\nfunction useApiArbitrationList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrations\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitrations(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiArbitrationRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrations\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitration(id),\n        enabled: !!id\n    });\n}\nfunction useApiArbitrationCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createArbitration(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitrationPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitration(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitrationDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteArbitration(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\n// Comment hooks\nfunction useApiCommentList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"comments\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComments(params),\n        staleTime: 2 * 60 * 1000\n    });\n}\nfunction useApiCommentRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"comments\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComment(id),\n        enabled: !!id\n    });\n}\nfunction useApiCommentCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createComment(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiCommentPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateComment(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiCommentDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteComment(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// Document hooks\nfunction useApiDocsCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].uploadDocument(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDocsCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { missionId, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].uploadMissionDocuments(missionId, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\",\n                    variables.missionId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\",\n                    variables.missionId\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDocumentsList(missionId) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"mission-documents\",\n            missionId\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMissionDocuments(missionId),\n        enabled: !!missionId\n    });\n}\nfunction useApiDocumentsList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"documents\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getDocuments(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiDocsDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteDocument(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiDocsUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateDocument(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\"\n                ]\n            });\n        }\n    });\n}\n// Action hooks\nfunction useApiActionList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"actions\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getActions(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiActionRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"actions\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAction(id),\n        enabled: !!id\n    });\n}\nfunction useApiActionCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createAction(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateAction(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateAction(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteAction(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1jbGllbnQpLy4vaG9va3MvdXNlTmV4dEFwaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDBEQUEwRDtBQUNtQjtBQUMxQjtBQUVuRCxnQkFBZ0I7QUFDVCxTQUFTSSxrQkFBa0JDLE1BSWpDO0lBQ0MsT0FBT0wsK0RBQVFBLENBQUM7UUFDZE0sVUFBVTtZQUFDO1lBQVlEO1NBQU87UUFDOUJFLFNBQVMsSUFBTUosNkRBQWNBLENBQUNLLFdBQVcsQ0FBQ0g7UUFDMUNJLFdBQVcsSUFBSSxLQUFLO0lBQ3RCO0FBQ0Y7QUFFTyxTQUFTQyxzQkFBc0JDLEVBQVU7SUFDOUMsT0FBT1gsK0RBQVFBLENBQUM7UUFDZE0sVUFBVTtZQUFDO1lBQVlLO1NBQUc7UUFDMUJKLFNBQVMsSUFBTUosNkRBQWNBLENBQUNTLFVBQVUsQ0FBQ0Q7UUFDekNFLFNBQVMsQ0FBQyxDQUFDRjtJQUNiO0FBQ0Y7QUFFTyxTQUFTRztJQUNkLE1BQU1DLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWSxDQUFDQyxPQUFjZCw2REFBY0EsQ0FBQ2UsYUFBYSxDQUFDRDtRQUN4REUsV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBVztZQUFDO1FBQ3pEO0lBQ0Y7QUFDRjtBQUVPLFNBQVNlO0lBQ2QsTUFBTU4sY0FBY2IscUVBQWNBO0lBRWxDLE9BQU9ELGtFQUFXQSxDQUFDO1FBQ2pCZSxZQUFZO2dCQUFDLEVBQUVMLEVBQUUsRUFBRU0sSUFBSSxFQUE2QjttQkFDbERkLDZEQUFjQSxDQUFDbUIsYUFBYSxDQUFDWCxJQUFJTTtRQUFJO1FBQ3ZDRSxXQUFXLENBQUNGLE1BQU1NO1lBQ2hCUixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBVztZQUFDO1lBQ3ZEUyxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztvQkFBWWlCLFVBQVVaLEVBQUU7aUJBQUM7WUFBQztRQUN2RTtJQUNGO0FBQ0Y7QUFFTyxTQUFTYTtJQUNkLE1BQU1ULGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWSxDQUFDTCxLQUFlUiw2REFBY0EsQ0FBQ3NCLGFBQWEsQ0FBQ2Q7UUFDekRRLFdBQVc7WUFDVEosWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztRQUN6RDtJQUNGO0FBQ0Y7QUFFQSx1QkFBdUI7QUFDaEIsU0FBU29CLHlCQUF5QnJCLE1BS3hDO0lBQ0MsT0FBT0wsK0RBQVFBLENBQUM7UUFDZE0sVUFBVTtZQUFDO1lBQW1CRDtTQUFPO1FBQ3JDRSxTQUFTLElBQU1KLDZEQUFjQSxDQUFDd0Isa0JBQWtCLENBQUN0QjtRQUNqREksV0FBVyxJQUFJLEtBQUs7SUFDdEI7QUFDRjtBQUVPLFNBQVNtQiw2QkFBNkJqQixFQUFVO0lBQ3JELE9BQU9YLCtEQUFRQSxDQUFDO1FBQ2RNLFVBQVU7WUFBQztZQUFtQks7U0FBRztRQUNqQ0osU0FBUyxJQUFNSiw2REFBY0EsQ0FBQzBCLGlCQUFpQixDQUFDbEI7UUFDaERFLFNBQVMsQ0FBQyxDQUFDRjtJQUNiO0FBQ0Y7QUFFTyxTQUFTbUI7SUFDZCxNQUFNZixjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVksQ0FBQ0MsT0FBY2QsNkRBQWNBLENBQUM0QixvQkFBb0IsQ0FBQ2Q7UUFDL0RFLFdBQVc7WUFDVEosWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQWtCO1lBQUM7WUFDOURTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFXO1lBQUM7UUFDekQ7SUFDRjtBQUNGO0FBRU8sU0FBUzBCO0lBQ2QsTUFBTWpCLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWTtnQkFBQyxFQUFFTCxFQUFFLEVBQUVNLElBQUksRUFBNkI7bUJBQ2xEZCw2REFBY0EsQ0FBQzhCLG9CQUFvQixDQUFDdEIsSUFBSU07UUFBSTtRQUM5Q0UsV0FBVyxDQUFDRixNQUFNTTtZQUNoQlIsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQWtCO1lBQUM7WUFDOURTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO29CQUFtQmlCLFVBQVVaLEVBQUU7aUJBQUM7WUFBQztZQUM1RUksWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztRQUN6RDtJQUNGO0FBQ0Y7QUFFTyxTQUFTNEI7SUFDZCxNQUFNbkIsY0FBY2IscUVBQWNBO0lBRWxDLE9BQU9ELGtFQUFXQSxDQUFDO1FBQ2pCZSxZQUFZLENBQUNMLEtBQWVSLDZEQUFjQSxDQUFDZ0Msb0JBQW9CLENBQUN4QjtRQUNoRVEsV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBa0I7WUFBQztZQUM5RFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztRQUN6RDtJQUNGO0FBQ0Y7QUFFQSxhQUFhO0FBQ04sU0FBUzhCLGVBQWUvQixNQUk5QjtJQUNDLE9BQU9MLCtEQUFRQSxDQUFDO1FBQ2RNLFVBQVU7WUFBQztZQUFTRDtTQUFPO1FBQzNCRSxTQUFTLElBQU1KLDZEQUFjQSxDQUFDa0MsUUFBUSxDQUFDaEM7UUFDdkNJLFdBQVcsS0FBSyxLQUFLO0lBQ3ZCO0FBQ0Y7QUFFTyxTQUFTNkIsbUJBQW1CM0IsRUFBVTtJQUMzQyxPQUFPWCwrREFBUUEsQ0FBQztRQUNkTSxVQUFVO1lBQUM7WUFBU0s7U0FBRztRQUN2QkosU0FBUyxJQUFNSiw2REFBY0EsQ0FBQ29DLE9BQU8sQ0FBQzVCO1FBQ3RDRSxTQUFTLENBQUMsQ0FBQ0Y7SUFDYjtBQUNGO0FBRU8sU0FBUzZCO0lBQ2QsTUFBTXpCLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWSxDQUFDQyxPQUFjZCw2REFBY0EsQ0FBQ3NDLFVBQVUsQ0FBQ3hCO1FBQ3JERSxXQUFXO1lBQ1RKLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFRO1lBQUM7UUFDdEQ7SUFDRjtBQUNGO0FBRUEsYUFBYTtBQUNOLFNBQVNvQyxlQUFlckMsTUFLOUI7SUFDQyxPQUFPTCwrREFBUUEsQ0FBQztRQUNkTSxVQUFVO1lBQUM7WUFBU0Q7U0FBTztRQUMzQkUsU0FBUyxJQUFNSiw2REFBY0EsQ0FBQ3dDLFFBQVEsQ0FBQ3RDO1FBQ3ZDSSxXQUFXLEtBQUssS0FBSztJQUN2QjtBQUNGO0FBRU8sU0FBU21DLG1CQUFtQmpDLEVBQVU7SUFDM0MsT0FBT1gsK0RBQVFBLENBQUM7UUFDZE0sVUFBVTtZQUFDO1lBQVNLO1NBQUc7UUFDdkJKLFNBQVMsSUFBTUosNkRBQWNBLENBQUMwQyxPQUFPLENBQUNsQztRQUN0Q0UsU0FBUyxDQUFDLENBQUNGO0lBQ2I7QUFDRjtBQUVPLFNBQVNtQztJQUNkLE1BQU0vQixjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVksQ0FBQ0MsT0FBY2QsNkRBQWNBLENBQUM0QyxVQUFVLENBQUM5QjtRQUNyREUsV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBUTtZQUFDO1FBQ3REO0lBQ0Y7QUFDRjtBQUVPLFNBQVMwQztJQUNkLE1BQU1qQyxjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVk7Z0JBQUMsRUFBRUwsRUFBRSxFQUFFTSxJQUFJLEVBQTZCO21CQUNsRGQsNkRBQWNBLENBQUM4QyxVQUFVLENBQUN0QyxJQUFJTTtRQUFJO1FBQ3BDRSxXQUFXLENBQUMrQixHQUFHM0I7WUFDYlIsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVE7WUFBQztZQUNwRFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7b0JBQVNpQixVQUFVWixFQUFFO2lCQUFDO1lBQUM7WUFDbEVJLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFlO1lBQUM7UUFDN0Q7SUFDRjtBQUNGO0FBRU8sU0FBUzZDO0lBQ2QsTUFBTXBDLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWSxDQUFDTCxLQUFlUiw2REFBY0EsQ0FBQ2lELFVBQVUsQ0FBQ3pDO1FBQ3REUSxXQUFXO1lBQ1RKLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFRO1lBQUM7WUFDcERTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFlO1lBQUM7UUFDN0Q7SUFDRjtBQUNGO0FBRUEsY0FBYztBQUNQLFNBQVMrQyxnQkFBZ0JoRCxNQU0vQjtJQUNDLE9BQU9MLCtEQUFRQSxDQUFDO1FBQ2RNLFVBQVU7WUFBQztZQUFVRDtTQUFPO1FBQzVCRSxTQUFTLElBQU1KLDZEQUFjQSxDQUFDbUQsU0FBUyxDQUFDakQ7UUFDeENJLFdBQVcsSUFBSSxLQUFLO0lBQ3RCO0FBQ0Y7QUFFTyxTQUFTOEMsb0JBQW9CNUMsRUFBVTtJQUM1QyxPQUFPWCwrREFBUUEsQ0FBQztRQUNkTSxVQUFVO1lBQUM7WUFBVUs7U0FBRztRQUN4QkosU0FBUyxJQUFNSiw2REFBY0EsQ0FBQ3FELFFBQVEsQ0FBQzdDO1FBQ3ZDRSxTQUFTLENBQUMsQ0FBQ0Y7SUFDYjtBQUNGO0FBRU8sU0FBUzhDO0lBQ2QsTUFBTTFDLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWSxDQUFDQyxPQUFjZCw2REFBY0EsQ0FBQ3VELFdBQVcsQ0FBQ3pDO1FBQ3RERSxXQUFXO1lBQ1RKLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFTO1lBQUM7UUFDdkQ7SUFDRjtBQUNGO0FBRU8sU0FBU3FEO0lBQ2QsTUFBTTVDLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWTtnQkFBQyxFQUFFTCxFQUFFLEVBQUVNLElBQUksRUFBNkI7bUJBQ2xEZCw2REFBY0EsQ0FBQ3lELFdBQVcsQ0FBQ2pELElBQUlNO1FBQUk7UUFDckNFLFdBQVcsQ0FBQytCLEdBQUczQjtZQUNiUixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBUztZQUFDO1lBQ3JEUyxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztvQkFBVWlCLFVBQVVaLEVBQUU7aUJBQUM7WUFBQztRQUNyRTtJQUNGO0FBQ0Y7QUFFTyxTQUFTa0Q7SUFDZCxNQUFNOUMsY0FBY2IscUVBQWNBO0lBRWxDLE9BQU9ELGtFQUFXQSxDQUFDO1FBQ2pCZSxZQUFZO2dCQUFDLEVBQUVMLEVBQUUsRUFBRU0sSUFBSSxFQUE2QjttQkFDbERkLDZEQUFjQSxDQUFDeUQsV0FBVyxDQUFDakQsSUFBSU07UUFBSTtRQUNyQ0UsV0FBVyxDQUFDK0IsR0FBRzNCO1lBQ2JSLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFTO1lBQUM7WUFDckRTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO29CQUFVaUIsVUFBVVosRUFBRTtpQkFBQztZQUFDO1FBQ3JFO0lBQ0Y7QUFDRjtBQUVPLFNBQVNtRDtJQUNkLE1BQU0vQyxjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVksQ0FBQ0wsS0FBZVIsNkRBQWNBLENBQUM0RCxXQUFXLENBQUNwRDtRQUN2RFEsV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBUztZQUFDO1FBQ3ZEO0lBQ0Y7QUFDRjtBQUVBLG9CQUFvQjtBQUNiLFNBQVMwRCxzQkFBc0IzRCxNQUtyQztJQUNDLE9BQU9MLCtEQUFRQSxDQUFDO1FBQ2RNLFVBQVU7WUFBQztZQUFnQkQ7U0FBTztRQUNsQ0UsU0FBUyxJQUFNSiw2REFBY0EsQ0FBQzhELGVBQWUsQ0FBQzVEO1FBQzlDSSxXQUFXLElBQUksS0FBSztJQUN0QjtBQUNGO0FBRU8sU0FBU3lELDBCQUEwQnZELEVBQVU7SUFDbEQsT0FBT1gsK0RBQVFBLENBQUM7UUFDZE0sVUFBVTtZQUFDO1lBQWdCSztTQUFHO1FBQzlCSixTQUFTLElBQU1KLDZEQUFjQSxDQUFDZ0UsY0FBYyxDQUFDeEQ7UUFDN0NFLFNBQVMsQ0FBQyxDQUFDRjtJQUNiO0FBQ0Y7QUFFTyxTQUFTeUQ7SUFDZCxNQUFNckQsY0FBY2IscUVBQWNBO0lBRWxDLE9BQU9ELGtFQUFXQSxDQUFDO1FBQ2pCZSxZQUFZLENBQUNDLE9BQWNkLDZEQUFjQSxDQUFDa0UsaUJBQWlCLENBQUNwRDtRQUM1REUsV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBZTtZQUFDO1lBQzNEUyxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBUTtZQUFDO1FBQ3REO0lBQ0Y7QUFDRjtBQUVPLFNBQVNnRTtJQUNkLE1BQU12RCxjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVk7Z0JBQUMsRUFBRUwsRUFBRSxFQUFFTSxJQUFJLEVBQTZCO21CQUNsRGQsNkRBQWNBLENBQUNvRSxpQkFBaUIsQ0FBQzVELElBQUlNO1FBQUk7UUFDM0NFLFdBQVcsQ0FBQytCLEdBQUczQjtZQUNiUixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBZTtZQUFDO1lBQzNEUyxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztvQkFBZ0JpQixVQUFVWixFQUFFO2lCQUFDO1lBQUM7WUFDekVJLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFRO1lBQUM7UUFDdEQ7SUFDRjtBQUNGO0FBRU8sU0FBU2tFO0lBQ2QsTUFBTXpELGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWSxDQUFDTCxLQUFlUiw2REFBY0EsQ0FBQ3NFLGlCQUFpQixDQUFDOUQ7UUFDN0RRLFdBQVc7WUFDVEosWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQWU7WUFBQztZQUMzRFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVE7WUFBQztRQUN0RDtJQUNGO0FBQ0Y7QUFFQSxnQkFBZ0I7QUFDVCxTQUFTb0Usa0JBQWtCckUsTUFPakM7SUFDQyxPQUFPTCwrREFBUUEsQ0FBQztRQUNkTSxVQUFVO1lBQUM7WUFBWUQ7U0FBTztRQUM5QkUsU0FBUyxJQUFNSiw2REFBY0EsQ0FBQ3dFLFdBQVcsQ0FBQ3RFO1FBQzFDSSxXQUFXLElBQUksS0FBSztJQUN0QjtBQUNGO0FBRU8sU0FBU21FLHNCQUFzQmpFLEVBQVU7SUFDOUMsT0FBT1gsK0RBQVFBLENBQUM7UUFDZE0sVUFBVTtZQUFDO1lBQVlLO1NBQUc7UUFDMUJKLFNBQVMsSUFBTUosNkRBQWNBLENBQUMwRSxVQUFVLENBQUNsRTtRQUN6Q0UsU0FBUyxDQUFDLENBQUNGO0lBQ2I7QUFDRjtBQUVPLFNBQVNtRTtJQUNkLE1BQU0vRCxjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVksQ0FBQ0MsT0FBY2QsNkRBQWNBLENBQUM0RSxhQUFhLENBQUM5RDtRQUN4REUsV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBVztZQUFDO1lBQ3ZEUyxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBa0I7WUFBQztZQUM5RFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztRQUN6RDtJQUNGO0FBQ0Y7QUFFTyxTQUFTMEU7SUFDZCxNQUFNakUsY0FBY2IscUVBQWNBO0lBRWxDLE9BQU9ELGtFQUFXQSxDQUFDO1FBQ2pCZSxZQUFZO2dCQUFDLEVBQUVMLEVBQUUsRUFBRU0sSUFBSSxFQUE2QjttQkFDbERkLDZEQUFjQSxDQUFDOEUsYUFBYSxDQUFDdEUsSUFBSU07UUFBSTtRQUN2Q0UsV0FBVyxDQUFDK0IsR0FBRzNCO1lBQ2JSLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFXO1lBQUM7WUFDdkRTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO29CQUFZaUIsVUFBVVosRUFBRTtpQkFBQztZQUFDO1lBQ3JFSSxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBa0I7WUFBQztZQUM5RFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztRQUN6RDtJQUNGO0FBQ0Y7QUFFTyxTQUFTNEU7SUFDZCxNQUFNbkUsY0FBY2IscUVBQWNBO0lBRWxDLE9BQU9ELGtFQUFXQSxDQUFDO1FBQ2pCZSxZQUFZLENBQUNMLEtBQWVSLDZEQUFjQSxDQUFDZ0YsYUFBYSxDQUFDeEU7UUFDekRRLFdBQVc7WUFDVEosWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztZQUN2RFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQWtCO1lBQUM7WUFDOURTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFXO1lBQUM7UUFDekQ7SUFDRjtBQUNGO0FBRUEsaUJBQWlCO0FBQ1YsU0FBUzhFO0lBQ2QsTUFBTXJFLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWSxDQUFDQyxPQUFtQmQsNkRBQWNBLENBQUNrRixjQUFjLENBQUNwRTtRQUM5REUsV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBVztZQUFDO1lBQ3ZEUyxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBWTtZQUFDO1FBQzFEO0lBQ0Y7QUFDRjtBQUVPLFNBQVNnRjtJQUNkLE1BQU12RSxjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVk7Z0JBQUMsRUFBRXVFLFNBQVMsRUFBRXRFLElBQUksRUFBeUM7bUJBQ3JFZCw2REFBY0EsQ0FBQ3FGLHNCQUFzQixDQUFDRCxXQUFXdEU7UUFBSTtRQUN2REUsV0FBVyxDQUFDK0IsR0FBRzNCO1lBQ2JSLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFXO1lBQUM7WUFDdkRTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO29CQUFZaUIsVUFBVWdFLFNBQVM7aUJBQUM7WUFBQztZQUM1RXhFLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFZO1lBQUM7WUFDeERTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO29CQUFxQmlCLFVBQVVnRSxTQUFTO2lCQUFDO1lBQUM7UUFDdkY7SUFDRjtBQUNGO0FBRU8sU0FBU0UsMkJBQTJCRixTQUFpQjtJQUMxRCxPQUFPdkYsK0RBQVFBLENBQUM7UUFDZE0sVUFBVTtZQUFDO1lBQXFCaUY7U0FBVTtRQUMxQ2hGLFNBQVMsSUFBTUosNkRBQWNBLENBQUN1RixtQkFBbUIsQ0FBQ0g7UUFDbEQxRSxTQUFTLENBQUMsQ0FBQzBFO0lBQ2I7QUFDRjtBQUVPLFNBQVNJLG9CQUFvQnRGLE1BQWlEO0lBQ25GLE9BQU9MLCtEQUFRQSxDQUFDO1FBQ2RNLFVBQVU7WUFBQztZQUFhRDtTQUFPO1FBQy9CRSxTQUFTLElBQU1KLDZEQUFjQSxDQUFDeUYsWUFBWSxDQUFDdkY7UUFDM0NJLFdBQVcsSUFBSSxLQUFLO0lBQ3RCO0FBQ0Y7QUFFTyxTQUFTb0Y7SUFDZCxNQUFNOUUsY0FBY2IscUVBQWNBO0lBRWxDLE9BQU9ELGtFQUFXQSxDQUFDO1FBQ2pCZSxZQUFZLENBQUNMLEtBQWVSLDZEQUFjQSxDQUFDMkYsY0FBYyxDQUFDbkY7UUFDMURRLFdBQVc7WUFDVEosWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVc7WUFBQztZQUN2RFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVk7WUFBQztZQUN4RFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQW9CO1lBQUM7UUFDbEU7SUFDRjtBQUNGO0FBRU8sU0FBU3lGO0lBQ2QsTUFBTWhGLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWTtnQkFBQyxFQUFFTCxFQUFFLEVBQUVNLElBQUksRUFBNkI7bUJBQ2xEZCw2REFBY0EsQ0FBQzZGLGNBQWMsQ0FBQ3JGLElBQUlNO1FBQUk7UUFDeENFLFdBQVcsQ0FBQytCLEdBQUczQjtZQUNiUixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBWTtZQUFDO1lBQ3hEUyxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztvQkFBYWlCLFVBQVVaLEVBQUU7aUJBQUM7WUFBQztZQUN0RUksWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQW9CO1lBQUM7UUFDbEU7SUFDRjtBQUNGO0FBRUEsZUFBZTtBQUNSLFNBQVMyRixpQkFBaUI1RixNQU1oQztJQUNDLE9BQU9MLCtEQUFRQSxDQUFDO1FBQ2RNLFVBQVU7WUFBQztZQUFXRDtTQUFPO1FBQzdCRSxTQUFTLElBQU1KLDZEQUFjQSxDQUFDK0YsVUFBVSxDQUFDN0Y7UUFDekNJLFdBQVcsSUFBSSxLQUFLO0lBQ3RCO0FBQ0Y7QUFFTyxTQUFTMEYscUJBQXFCeEYsRUFBVTtJQUM3QyxPQUFPWCwrREFBUUEsQ0FBQztRQUNkTSxVQUFVO1lBQUM7WUFBV0s7U0FBRztRQUN6QkosU0FBUyxJQUFNSiw2REFBY0EsQ0FBQ2lHLFNBQVMsQ0FBQ3pGO1FBQ3hDRSxTQUFTLENBQUMsQ0FBQ0Y7SUFDYjtBQUNGO0FBRU8sU0FBUzBGO0lBQ2QsTUFBTXRGLGNBQWNiLHFFQUFjQTtJQUVsQyxPQUFPRCxrRUFBV0EsQ0FBQztRQUNqQmUsWUFBWSxDQUFDQyxPQUFjZCw2REFBY0EsQ0FBQ21HLFlBQVksQ0FBQ3JGO1FBQ3ZERSxXQUFXO1lBQ1RKLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFVO1lBQUM7WUFDdERTLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFrQjtZQUFDO1FBQ2hFO0lBQ0Y7QUFDRjtBQUVPLFNBQVNpRztJQUNkLE1BQU14RixjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVk7Z0JBQUMsRUFBRUwsRUFBRSxFQUFFTSxJQUFJLEVBQTZCO21CQUNsRGQsNkRBQWNBLENBQUNxRyxZQUFZLENBQUM3RixJQUFJTTtRQUFJO1FBQ3RDRSxXQUFXLENBQUMrQixHQUFHM0I7WUFDYlIsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVU7WUFBQztZQUN0RFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7b0JBQVdpQixVQUFVWixFQUFFO2lCQUFDO1lBQUM7WUFDcEVJLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFrQjtZQUFDO1FBQ2hFO0lBQ0Y7QUFDRjtBQUVPLFNBQVNtRztJQUNkLE1BQU0xRixjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVk7Z0JBQUMsRUFBRUwsRUFBRSxFQUFFTSxJQUFJLEVBQTZCO21CQUNsRGQsNkRBQWNBLENBQUNxRyxZQUFZLENBQUM3RixJQUFJTTtRQUFJO1FBQ3RDRSxXQUFXLENBQUMrQixHQUFHM0I7WUFDYlIsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7aUJBQVU7WUFBQztZQUN0RFMsWUFBWUssaUJBQWlCLENBQUM7Z0JBQUVkLFVBQVU7b0JBQUM7b0JBQVdpQixVQUFVWixFQUFFO2lCQUFDO1lBQUM7WUFDcEVJLFlBQVlLLGlCQUFpQixDQUFDO2dCQUFFZCxVQUFVO29CQUFDO2lCQUFrQjtZQUFDO1FBQ2hFO0lBQ0Y7QUFDRjtBQUVPLFNBQVNvRztJQUNkLE1BQU0zRixjQUFjYixxRUFBY0E7SUFFbEMsT0FBT0Qsa0VBQVdBLENBQUM7UUFDakJlLFlBQVksQ0FBQ0wsS0FBZVIsNkRBQWNBLENBQUN3RyxZQUFZLENBQUNoRztRQUN4RFEsV0FBVztZQUNUSixZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBVTtZQUFDO1lBQ3REUyxZQUFZSyxpQkFBaUIsQ0FBQztnQkFBRWQsVUFBVTtvQkFBQztpQkFBa0I7WUFBQztRQUNoRTtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vaG9va3MvdXNlTmV4dEFwaS50cz85YjcxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFJlYWN0IGhvb2tzIGZvciBOZXh0LmpzIEFQSSB0byByZXBsYWNlIERqYW5nbyBBUEkgaG9va3NcbmltcG9ydCB7IHVzZVF1ZXJ5LCB1c2VNdXRhdGlvbiwgdXNlUXVlcnlDbGllbnQgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknXG5pbXBvcnQgbmV4dEFwaVNlcnZpY2UgZnJvbSAnQC9zZXJ2aWNlcy9hcGkvbmV4dEFwaSdcblxuLy8gTWlzc2lvbiBob29rc1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaU1pc3Npb25MaXN0KHBhcmFtcz86IHtcbiAgcGFnZT86IG51bWJlcjtcbiAgbGltaXQ/OiBudW1iZXI7XG4gIHNlYXJjaD86IHN0cmluZ1xufSkge1xuICByZXR1cm4gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ21pc3Npb25zJywgcGFyYW1zXSxcbiAgICBxdWVyeUZuOiAoKSA9PiBuZXh0QXBpU2VydmljZS5nZXRNaXNzaW9ucyhwYXJhbXMpLFxuICAgIHN0YWxlVGltZTogNSAqIDYwICogMTAwMCwgLy8gNSBtaW51dGVzXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlNaXNzaW9uUmV0cmlldmUoaWQ6IG51bWJlcikge1xuICByZXR1cm4gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ21pc3Npb25zJywgaWRdLFxuICAgIHF1ZXJ5Rm46ICgpID0+IG5leHRBcGlTZXJ2aWNlLmdldE1pc3Npb24oaWQpLFxuICAgIGVuYWJsZWQ6ICEhaWQsXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlNaXNzaW9uQ3JlYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46IChkYXRhOiBhbnkpID0+IG5leHRBcGlTZXJ2aWNlLmNyZWF0ZU1pc3Npb24oZGF0YSksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ21pc3Npb25zJ10gfSlcbiAgICB9LFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpTWlzc2lvblBhcnRpYWxVcGRhdGUoKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKHsgaWQsIGRhdGEgfTogeyBpZDogbnVtYmVyOyBkYXRhOiBhbnkgfSkgPT5cbiAgICAgIG5leHRBcGlTZXJ2aWNlLnVwZGF0ZU1pc3Npb24oaWQsIGRhdGEpLFxuICAgIG9uU3VjY2VzczogKGRhdGEsIHZhcmlhYmxlcykgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydtaXNzaW9ucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ21pc3Npb25zJywgdmFyaWFibGVzLmlkXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlNaXNzaW9uRGVzdHJveSgpIHtcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpXG5cbiAgcmV0dXJuIHVzZU11dGF0aW9uKHtcbiAgICBtdXRhdGlvbkZuOiAoaWQ6IG51bWJlcikgPT4gbmV4dEFwaVNlcnZpY2UuZGVsZXRlTWlzc2lvbihpZCksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ21pc3Npb25zJ10gfSlcbiAgICB9LFxuICB9KVxufVxuXG4vLyBSZWNvbW1lbmRhdGlvbiBob29rc1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaVJlY29tbWVuZGF0aW9uTGlzdChwYXJhbXM/OiB7XG4gIHBhZ2U/OiBudW1iZXI7XG4gIGxpbWl0PzogbnVtYmVyO1xuICBzZWFyY2g/OiBzdHJpbmc7XG4gIG1pc3Npb25JZD86IG51bWJlclxufSkge1xuICByZXR1cm4gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ3JlY29tbWVuZGF0aW9ucycsIHBhcmFtc10sXG4gICAgcXVlcnlGbjogKCkgPT4gbmV4dEFwaVNlcnZpY2UuZ2V0UmVjb21tZW5kYXRpb25zKHBhcmFtcyksXG4gICAgc3RhbGVUaW1lOiA1ICogNjAgKiAxMDAwLFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpUmVjb21tZW5kYXRpb25SZXRyaWV2ZShpZDogbnVtYmVyKSB7XG4gIHJldHVybiB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsncmVjb21tZW5kYXRpb25zJywgaWRdLFxuICAgIHF1ZXJ5Rm46ICgpID0+IG5leHRBcGlTZXJ2aWNlLmdldFJlY29tbWVuZGF0aW9uKGlkKSxcbiAgICBlbmFibGVkOiAhIWlkLFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpUmVjb21tZW5kYXRpb25DcmVhdGUoKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGRhdGE6IGFueSkgPT4gbmV4dEFwaVNlcnZpY2UuY3JlYXRlUmVjb21tZW5kYXRpb24oZGF0YSksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3JlY29tbWVuZGF0aW9ucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ21pc3Npb25zJ10gfSlcbiAgICB9LFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpUmVjb21tZW5kYXRpb25QYXJ0aWFsVXBkYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46ICh7IGlkLCBkYXRhIH06IHsgaWQ6IG51bWJlcjsgZGF0YTogYW55IH0pID0+XG4gICAgICBuZXh0QXBpU2VydmljZS51cGRhdGVSZWNvbW1lbmRhdGlvbihpZCwgZGF0YSksXG4gICAgb25TdWNjZXNzOiAoZGF0YSwgdmFyaWFibGVzKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3JlY29tbWVuZGF0aW9ucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3JlY29tbWVuZGF0aW9ucycsIHZhcmlhYmxlcy5pZF0gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbWlzc2lvbnMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlSZWNvbW1lbmRhdGlvbkRlc3Ryb3koKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGlkOiBudW1iZXIpID0+IG5leHRBcGlTZXJ2aWNlLmRlbGV0ZVJlY29tbWVuZGF0aW9uKGlkKSxcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsncmVjb21tZW5kYXRpb25zJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbWlzc2lvbnMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbi8vIFVzZXIgaG9va3NcbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlVc2VyTGlzdChwYXJhbXM/OiB7XG4gIHBhZ2U/OiBudW1iZXI7XG4gIGxpbWl0PzogbnVtYmVyO1xuICBzZWFyY2g/OiBzdHJpbmdcbn0pIHtcbiAgcmV0dXJuIHVzZVF1ZXJ5KHtcbiAgICBxdWVyeUtleTogWyd1c2VycycsIHBhcmFtc10sXG4gICAgcXVlcnlGbjogKCkgPT4gbmV4dEFwaVNlcnZpY2UuZ2V0VXNlcnMocGFyYW1zKSxcbiAgICBzdGFsZVRpbWU6IDEwICogNjAgKiAxMDAwLCAvLyAxMCBtaW51dGVzXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlVc2VyUmV0cmlldmUoaWQ6IG51bWJlcikge1xuICByZXR1cm4gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ3VzZXJzJywgaWRdLFxuICAgIHF1ZXJ5Rm46ICgpID0+IG5leHRBcGlTZXJ2aWNlLmdldFVzZXIoaWQpLFxuICAgIGVuYWJsZWQ6ICEhaWQsXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlVc2VyQ3JlYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46IChkYXRhOiBhbnkpID0+IG5leHRBcGlTZXJ2aWNlLmNyZWF0ZVVzZXIoZGF0YSksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3VzZXJzJ10gfSlcbiAgICB9LFxuICB9KVxufVxuXG4vLyBQbGFuIGhvb2tzXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpUGxhbkxpc3QocGFyYW1zPzoge1xuICBwYWdlPzogbnVtYmVyO1xuICBsaW1pdD86IG51bWJlcjtcbiAgZXhlcmNpc2U/OiBudW1iZXI7XG4gIHR5cGU/OiBzdHJpbmdcbn0pIHtcbiAgcmV0dXJuIHVzZVF1ZXJ5KHtcbiAgICBxdWVyeUtleTogWydwbGFucycsIHBhcmFtc10sXG4gICAgcXVlcnlGbjogKCkgPT4gbmV4dEFwaVNlcnZpY2UuZ2V0UGxhbnMocGFyYW1zKSxcbiAgICBzdGFsZVRpbWU6IDEwICogNjAgKiAxMDAwLFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpUGxhblJldHJpZXZlKGlkOiBudW1iZXIpIHtcbiAgcmV0dXJuIHVzZVF1ZXJ5KHtcbiAgICBxdWVyeUtleTogWydwbGFucycsIGlkXSxcbiAgICBxdWVyeUZuOiAoKSA9PiBuZXh0QXBpU2VydmljZS5nZXRQbGFuKGlkKSxcbiAgICBlbmFibGVkOiAhIWlkLFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpUGxhbkNyZWF0ZSgpIHtcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpXG5cbiAgcmV0dXJuIHVzZU11dGF0aW9uKHtcbiAgICBtdXRhdGlvbkZuOiAoZGF0YTogYW55KSA9PiBuZXh0QXBpU2VydmljZS5jcmVhdGVQbGFuKGRhdGEpLFxuICAgIG9uU3VjY2VzczogKCkgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydwbGFucyddIH0pXG4gICAgfSxcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaVBsYW5VcGRhdGUoKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKHsgaWQsIGRhdGEgfTogeyBpZDogbnVtYmVyOyBkYXRhOiBhbnkgfSkgPT5cbiAgICAgIG5leHRBcGlTZXJ2aWNlLnVwZGF0ZVBsYW4oaWQsIGRhdGEpLFxuICAgIG9uU3VjY2VzczogKF8sIHZhcmlhYmxlcykgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydwbGFucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3BsYW5zJywgdmFyaWFibGVzLmlkXSB9KVxuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydhcmJpdHJhdGlvbnMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlQbGFuRGVzdHJveSgpIHtcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpXG5cbiAgcmV0dXJuIHVzZU11dGF0aW9uKHtcbiAgICBtdXRhdGlvbkZuOiAoaWQ6IG51bWJlcikgPT4gbmV4dEFwaVNlcnZpY2UuZGVsZXRlUGxhbihpZCksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3BsYW5zJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnYXJiaXRyYXRpb25zJ10gfSlcbiAgICB9LFxuICB9KVxufVxuXG4vLyBUaGVtZSBob29rc1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaVRoZW1lTGlzdChwYXJhbXM/OiB7XG4gIHBhZ2U/OiBudW1iZXI7XG4gIGxpbWl0PzogbnVtYmVyO1xuICBzZWFyY2g/OiBzdHJpbmc7XG4gIHZhbGlkYXRlZD86IGJvb2xlYW47XG4gIGRvbWFpbklkPzogbnVtYmVyO1xufSkge1xuICByZXR1cm4gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ3RoZW1lcycsIHBhcmFtc10sXG4gICAgcXVlcnlGbjogKCkgPT4gbmV4dEFwaVNlcnZpY2UuZ2V0VGhlbWVzKHBhcmFtcyksXG4gICAgc3RhbGVUaW1lOiA1ICogNjAgKiAxMDAwLFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpVGhlbWVSZXRyaWV2ZShpZDogbnVtYmVyKSB7XG4gIHJldHVybiB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsndGhlbWVzJywgaWRdLFxuICAgIHF1ZXJ5Rm46ICgpID0+IG5leHRBcGlTZXJ2aWNlLmdldFRoZW1lKGlkKSxcbiAgICBlbmFibGVkOiAhIWlkLFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpVGhlbWVDcmVhdGUoKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGRhdGE6IGFueSkgPT4gbmV4dEFwaVNlcnZpY2UuY3JlYXRlVGhlbWUoZGF0YSksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3RoZW1lcyddIH0pXG4gICAgfSxcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaVRoZW1lVXBkYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46ICh7IGlkLCBkYXRhIH06IHsgaWQ6IG51bWJlcjsgZGF0YTogYW55IH0pID0+XG4gICAgICBuZXh0QXBpU2VydmljZS51cGRhdGVUaGVtZShpZCwgZGF0YSksXG4gICAgb25TdWNjZXNzOiAoXywgdmFyaWFibGVzKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3RoZW1lcyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3RoZW1lcycsIHZhcmlhYmxlcy5pZF0gfSlcbiAgICB9LFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpVGhlbWVQYXJ0aWFsVXBkYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46ICh7IGlkLCBkYXRhIH06IHsgaWQ6IG51bWJlcjsgZGF0YTogYW55IH0pID0+XG4gICAgICBuZXh0QXBpU2VydmljZS51cGRhdGVUaGVtZShpZCwgZGF0YSksXG4gICAgb25TdWNjZXNzOiAoXywgdmFyaWFibGVzKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3RoZW1lcyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3RoZW1lcycsIHZhcmlhYmxlcy5pZF0gfSlcbiAgICB9LFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpVGhlbWVEZXN0cm95KCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46IChpZDogbnVtYmVyKSA9PiBuZXh0QXBpU2VydmljZS5kZWxldGVUaGVtZShpZCksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3RoZW1lcyddIH0pXG4gICAgfSxcbiAgfSlcbn1cblxuLy8gQXJiaXRyYXRpb24gaG9va3NcbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlBcmJpdHJhdGlvbkxpc3QocGFyYW1zPzoge1xuICBwYWdlPzogbnVtYmVyO1xuICBsaW1pdD86IG51bWJlcjtcbiAgc2VhcmNoPzogc3RyaW5nO1xuICBwbGFuSWQ/OiBudW1iZXI7XG59KSB7XG4gIHJldHVybiB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsnYXJiaXRyYXRpb25zJywgcGFyYW1zXSxcbiAgICBxdWVyeUZuOiAoKSA9PiBuZXh0QXBpU2VydmljZS5nZXRBcmJpdHJhdGlvbnMocGFyYW1zKSxcbiAgICBzdGFsZVRpbWU6IDUgKiA2MCAqIDEwMDAsIC8vIDUgbWludXRlc1xuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpQXJiaXRyYXRpb25SZXRyaWV2ZShpZDogbnVtYmVyKSB7XG4gIHJldHVybiB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsnYXJiaXRyYXRpb25zJywgaWRdLFxuICAgIHF1ZXJ5Rm46ICgpID0+IG5leHRBcGlTZXJ2aWNlLmdldEFyYml0cmF0aW9uKGlkKSxcbiAgICBlbmFibGVkOiAhIWlkLFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpQXJiaXRyYXRpb25DcmVhdGUoKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGRhdGE6IGFueSkgPT4gbmV4dEFwaVNlcnZpY2UuY3JlYXRlQXJiaXRyYXRpb24oZGF0YSksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2FyYml0cmF0aW9ucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3BsYW5zJ10gfSlcbiAgICB9LFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpQXJiaXRyYXRpb25QYXJ0aWFsVXBkYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46ICh7IGlkLCBkYXRhIH06IHsgaWQ6IG51bWJlcjsgZGF0YTogYW55IH0pID0+XG4gICAgICBuZXh0QXBpU2VydmljZS51cGRhdGVBcmJpdHJhdGlvbihpZCwgZGF0YSksXG4gICAgb25TdWNjZXNzOiAoXywgdmFyaWFibGVzKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2FyYml0cmF0aW9ucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2FyYml0cmF0aW9ucycsIHZhcmlhYmxlcy5pZF0gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsncGxhbnMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlBcmJpdHJhdGlvbkRlc3Ryb3koKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGlkOiBudW1iZXIpID0+IG5leHRBcGlTZXJ2aWNlLmRlbGV0ZUFyYml0cmF0aW9uKGlkKSxcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnYXJiaXRyYXRpb25zJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsncGxhbnMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbi8vIENvbW1lbnQgaG9va3NcbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlDb21tZW50TGlzdChwYXJhbXM/OiB7XG4gIHBhZ2U/OiBudW1iZXI7XG4gIGxpbWl0PzogbnVtYmVyO1xuICBzZWFyY2g/OiBzdHJpbmc7XG4gIHJlY29tbWVuZGF0aW9uSWQ/OiBudW1iZXI7XG4gIG1pc3Npb25JZD86IG51bWJlcjtcbiAgdXNlcklkPzogbnVtYmVyO1xufSkge1xuICByZXR1cm4gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ2NvbW1lbnRzJywgcGFyYW1zXSxcbiAgICBxdWVyeUZuOiAoKSA9PiBuZXh0QXBpU2VydmljZS5nZXRDb21tZW50cyhwYXJhbXMpLFxuICAgIHN0YWxlVGltZTogMiAqIDYwICogMTAwMCwgLy8gMiBtaW51dGVzIChjb21tZW50cyBjaGFuZ2UgZnJlcXVlbnRseSlcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaUNvbW1lbnRSZXRyaWV2ZShpZDogbnVtYmVyKSB7XG4gIHJldHVybiB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsnY29tbWVudHMnLCBpZF0sXG4gICAgcXVlcnlGbjogKCkgPT4gbmV4dEFwaVNlcnZpY2UuZ2V0Q29tbWVudChpZCksXG4gICAgZW5hYmxlZDogISFpZCxcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaUNvbW1lbnRDcmVhdGUoKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGRhdGE6IGFueSkgPT4gbmV4dEFwaVNlcnZpY2UuY3JlYXRlQ29tbWVudChkYXRhKSxcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnY29tbWVudHMnXSB9KVxuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydyZWNvbW1lbmRhdGlvbnMnXSB9KVxuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydtaXNzaW9ucyddIH0pXG4gICAgfSxcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaUNvbW1lbnRQYXJ0aWFsVXBkYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46ICh7IGlkLCBkYXRhIH06IHsgaWQ6IG51bWJlcjsgZGF0YTogYW55IH0pID0+XG4gICAgICBuZXh0QXBpU2VydmljZS51cGRhdGVDb21tZW50KGlkLCBkYXRhKSxcbiAgICBvblN1Y2Nlc3M6IChfLCB2YXJpYWJsZXMpID0+IHtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnY29tbWVudHMnXSB9KVxuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydjb21tZW50cycsIHZhcmlhYmxlcy5pZF0gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsncmVjb21tZW5kYXRpb25zJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbWlzc2lvbnMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlDb21tZW50RGVzdHJveSgpIHtcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpXG5cbiAgcmV0dXJuIHVzZU11dGF0aW9uKHtcbiAgICBtdXRhdGlvbkZuOiAoaWQ6IG51bWJlcikgPT4gbmV4dEFwaVNlcnZpY2UuZGVsZXRlQ29tbWVudChpZCksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2NvbW1lbnRzJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsncmVjb21tZW5kYXRpb25zJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbWlzc2lvbnMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbi8vIERvY3VtZW50IGhvb2tzXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpRG9jc0NyZWF0ZSgpIHtcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpXG5cbiAgcmV0dXJuIHVzZU11dGF0aW9uKHtcbiAgICBtdXRhdGlvbkZuOiAoZGF0YTogRm9ybURhdGEpID0+IG5leHRBcGlTZXJ2aWNlLnVwbG9hZERvY3VtZW50KGRhdGEpLFxuICAgIG9uU3VjY2VzczogKCkgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydtaXNzaW9ucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2RvY3VtZW50cyddIH0pXG4gICAgfSxcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaU1pc3Npb25Eb2NzQ3JlYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46ICh7IG1pc3Npb25JZCwgZGF0YSB9OiB7IG1pc3Npb25JZDogbnVtYmVyOyBkYXRhOiBGb3JtRGF0YSB9KSA9PlxuICAgICAgbmV4dEFwaVNlcnZpY2UudXBsb2FkTWlzc2lvbkRvY3VtZW50cyhtaXNzaW9uSWQsIGRhdGEpLFxuICAgIG9uU3VjY2VzczogKF8sIHZhcmlhYmxlcykgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydtaXNzaW9ucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ21pc3Npb25zJywgdmFyaWFibGVzLm1pc3Npb25JZF0gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnZG9jdW1lbnRzJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbWlzc2lvbi1kb2N1bWVudHMnLCB2YXJpYWJsZXMubWlzc2lvbklkXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlNaXNzaW9uRG9jdW1lbnRzTGlzdChtaXNzaW9uSWQ6IG51bWJlcikge1xuICByZXR1cm4gdXNlUXVlcnkoe1xuICAgIHF1ZXJ5S2V5OiBbJ21pc3Npb24tZG9jdW1lbnRzJywgbWlzc2lvbklkXSxcbiAgICBxdWVyeUZuOiAoKSA9PiBuZXh0QXBpU2VydmljZS5nZXRNaXNzaW9uRG9jdW1lbnRzKG1pc3Npb25JZCksXG4gICAgZW5hYmxlZDogISFtaXNzaW9uSWQsXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlEb2N1bWVudHNMaXN0KHBhcmFtcz86IHsgbWlzc2lvbklkPzogbnVtYmVyOyBjb250ZXh0Pzogc3RyaW5nIH0pIHtcbiAgcmV0dXJuIHVzZVF1ZXJ5KHtcbiAgICBxdWVyeUtleTogWydkb2N1bWVudHMnLCBwYXJhbXNdLFxuICAgIHF1ZXJ5Rm46ICgpID0+IG5leHRBcGlTZXJ2aWNlLmdldERvY3VtZW50cyhwYXJhbXMpLFxuICAgIHN0YWxlVGltZTogNSAqIDYwICogMTAwMCxcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaURvY3NEZXN0cm95KCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46IChpZDogbnVtYmVyKSA9PiBuZXh0QXBpU2VydmljZS5kZWxldGVEb2N1bWVudChpZCksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ21pc3Npb25zJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnZG9jdW1lbnRzJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbWlzc2lvbi1kb2N1bWVudHMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlEb2NzVXBkYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46ICh7IGlkLCBkYXRhIH06IHsgaWQ6IG51bWJlcjsgZGF0YTogYW55IH0pID0+XG4gICAgICBuZXh0QXBpU2VydmljZS51cGRhdGVEb2N1bWVudChpZCwgZGF0YSksXG4gICAgb25TdWNjZXNzOiAoXywgdmFyaWFibGVzKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2RvY3VtZW50cyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2RvY3VtZW50cycsIHZhcmlhYmxlcy5pZF0gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbWlzc2lvbi1kb2N1bWVudHMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG5cbi8vIEFjdGlvbiBob29rc1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaUFjdGlvbkxpc3QocGFyYW1zPzoge1xuICBwYWdlPzogbnVtYmVyO1xuICBsaW1pdD86IG51bWJlcjtcbiAgc2VhcmNoPzogc3RyaW5nO1xuICByZWNvbW1lbmRhdGlvbklkPzogbnVtYmVyO1xuICBzdGF0dXM/OiBzdHJpbmc7XG59KSB7XG4gIHJldHVybiB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsnYWN0aW9ucycsIHBhcmFtc10sXG4gICAgcXVlcnlGbjogKCkgPT4gbmV4dEFwaVNlcnZpY2UuZ2V0QWN0aW9ucyhwYXJhbXMpLFxuICAgIHN0YWxlVGltZTogNSAqIDYwICogMTAwMCwgLy8gNSBtaW51dGVzXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlBY3Rpb25SZXRyaWV2ZShpZDogbnVtYmVyKSB7XG4gIHJldHVybiB1c2VRdWVyeSh7XG4gICAgcXVlcnlLZXk6IFsnYWN0aW9ucycsIGlkXSxcbiAgICBxdWVyeUZuOiAoKSA9PiBuZXh0QXBpU2VydmljZS5nZXRBY3Rpb24oaWQpLFxuICAgIGVuYWJsZWQ6ICEhaWQsXG4gIH0pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBcGlBY3Rpb25DcmVhdGUoKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGRhdGE6IGFueSkgPT4gbmV4dEFwaVNlcnZpY2UuY3JlYXRlQWN0aW9uKGRhdGEpLFxuICAgIG9uU3VjY2VzczogKCkgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydhY3Rpb25zJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsncmVjb21tZW5kYXRpb25zJ10gfSlcbiAgICB9LFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpQWN0aW9uVXBkYXRlKCkge1xuICBjb25zdCBxdWVyeUNsaWVudCA9IHVzZVF1ZXJ5Q2xpZW50KClcblxuICByZXR1cm4gdXNlTXV0YXRpb24oe1xuICAgIG11dGF0aW9uRm46ICh7IGlkLCBkYXRhIH06IHsgaWQ6IG51bWJlcjsgZGF0YTogYW55IH0pID0+XG4gICAgICBuZXh0QXBpU2VydmljZS51cGRhdGVBY3Rpb24oaWQsIGRhdGEpLFxuICAgIG9uU3VjY2VzczogKF8sIHZhcmlhYmxlcykgPT4ge1xuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydhY3Rpb25zJ10gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnYWN0aW9ucycsIHZhcmlhYmxlcy5pZF0gfSlcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsncmVjb21tZW5kYXRpb25zJ10gfSlcbiAgICB9LFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXBpQWN0aW9uUGFydGlhbFVwZGF0ZSgpIHtcbiAgY29uc3QgcXVlcnlDbGllbnQgPSB1c2VRdWVyeUNsaWVudCgpXG5cbiAgcmV0dXJuIHVzZU11dGF0aW9uKHtcbiAgICBtdXRhdGlvbkZuOiAoeyBpZCwgZGF0YSB9OiB7IGlkOiBudW1iZXI7IGRhdGE6IGFueSB9KSA9PlxuICAgICAgbmV4dEFwaVNlcnZpY2UudXBkYXRlQWN0aW9uKGlkLCBkYXRhKSxcbiAgICBvblN1Y2Nlc3M6IChfLCB2YXJpYWJsZXMpID0+IHtcbiAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnYWN0aW9ucyddIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2FjdGlvbnMnLCB2YXJpYWJsZXMuaWRdIH0pXG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ3JlY29tbWVuZGF0aW9ucyddIH0pXG4gICAgfSxcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUFwaUFjdGlvbkRlc3Ryb3koKSB7XG4gIGNvbnN0IHF1ZXJ5Q2xpZW50ID0gdXNlUXVlcnlDbGllbnQoKVxuXG4gIHJldHVybiB1c2VNdXRhdGlvbih7XG4gICAgbXV0YXRpb25GbjogKGlkOiBudW1iZXIpID0+IG5leHRBcGlTZXJ2aWNlLmRlbGV0ZUFjdGlvbihpZCksXG4gICAgb25TdWNjZXNzOiAoKSA9PiB7XG4gICAgICBxdWVyeUNsaWVudC5pbnZhbGlkYXRlUXVlcmllcyh7IHF1ZXJ5S2V5OiBbJ2FjdGlvbnMnXSB9KVxuICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydyZWNvbW1lbmRhdGlvbnMnXSB9KVxuICAgIH0sXG4gIH0pXG59XG4iXSwibmFtZXMiOlsidXNlUXVlcnkiLCJ1c2VNdXRhdGlvbiIsInVzZVF1ZXJ5Q2xpZW50IiwibmV4dEFwaVNlcnZpY2UiLCJ1c2VBcGlNaXNzaW9uTGlzdCIsInBhcmFtcyIsInF1ZXJ5S2V5IiwicXVlcnlGbiIsImdldE1pc3Npb25zIiwic3RhbGVUaW1lIiwidXNlQXBpTWlzc2lvblJldHJpZXZlIiwiaWQiLCJnZXRNaXNzaW9uIiwiZW5hYmxlZCIsInVzZUFwaU1pc3Npb25DcmVhdGUiLCJxdWVyeUNsaWVudCIsIm11dGF0aW9uRm4iLCJkYXRhIiwiY3JlYXRlTWlzc2lvbiIsIm9uU3VjY2VzcyIsImludmFsaWRhdGVRdWVyaWVzIiwidXNlQXBpTWlzc2lvblBhcnRpYWxVcGRhdGUiLCJ1cGRhdGVNaXNzaW9uIiwidmFyaWFibGVzIiwidXNlQXBpTWlzc2lvbkRlc3Ryb3kiLCJkZWxldGVNaXNzaW9uIiwidXNlQXBpUmVjb21tZW5kYXRpb25MaXN0IiwiZ2V0UmVjb21tZW5kYXRpb25zIiwidXNlQXBpUmVjb21tZW5kYXRpb25SZXRyaWV2ZSIsImdldFJlY29tbWVuZGF0aW9uIiwidXNlQXBpUmVjb21tZW5kYXRpb25DcmVhdGUiLCJjcmVhdGVSZWNvbW1lbmRhdGlvbiIsInVzZUFwaVJlY29tbWVuZGF0aW9uUGFydGlhbFVwZGF0ZSIsInVwZGF0ZVJlY29tbWVuZGF0aW9uIiwidXNlQXBpUmVjb21tZW5kYXRpb25EZXN0cm95IiwiZGVsZXRlUmVjb21tZW5kYXRpb24iLCJ1c2VBcGlVc2VyTGlzdCIsImdldFVzZXJzIiwidXNlQXBpVXNlclJldHJpZXZlIiwiZ2V0VXNlciIsInVzZUFwaVVzZXJDcmVhdGUiLCJjcmVhdGVVc2VyIiwidXNlQXBpUGxhbkxpc3QiLCJnZXRQbGFucyIsInVzZUFwaVBsYW5SZXRyaWV2ZSIsImdldFBsYW4iLCJ1c2VBcGlQbGFuQ3JlYXRlIiwiY3JlYXRlUGxhbiIsInVzZUFwaVBsYW5VcGRhdGUiLCJ1cGRhdGVQbGFuIiwiXyIsInVzZUFwaVBsYW5EZXN0cm95IiwiZGVsZXRlUGxhbiIsInVzZUFwaVRoZW1lTGlzdCIsImdldFRoZW1lcyIsInVzZUFwaVRoZW1lUmV0cmlldmUiLCJnZXRUaGVtZSIsInVzZUFwaVRoZW1lQ3JlYXRlIiwiY3JlYXRlVGhlbWUiLCJ1c2VBcGlUaGVtZVVwZGF0ZSIsInVwZGF0ZVRoZW1lIiwidXNlQXBpVGhlbWVQYXJ0aWFsVXBkYXRlIiwidXNlQXBpVGhlbWVEZXN0cm95IiwiZGVsZXRlVGhlbWUiLCJ1c2VBcGlBcmJpdHJhdGlvbkxpc3QiLCJnZXRBcmJpdHJhdGlvbnMiLCJ1c2VBcGlBcmJpdHJhdGlvblJldHJpZXZlIiwiZ2V0QXJiaXRyYXRpb24iLCJ1c2VBcGlBcmJpdHJhdGlvbkNyZWF0ZSIsImNyZWF0ZUFyYml0cmF0aW9uIiwidXNlQXBpQXJiaXRyYXRpb25QYXJ0aWFsVXBkYXRlIiwidXBkYXRlQXJiaXRyYXRpb24iLCJ1c2VBcGlBcmJpdHJhdGlvbkRlc3Ryb3kiLCJkZWxldGVBcmJpdHJhdGlvbiIsInVzZUFwaUNvbW1lbnRMaXN0IiwiZ2V0Q29tbWVudHMiLCJ1c2VBcGlDb21tZW50UmV0cmlldmUiLCJnZXRDb21tZW50IiwidXNlQXBpQ29tbWVudENyZWF0ZSIsImNyZWF0ZUNvbW1lbnQiLCJ1c2VBcGlDb21tZW50UGFydGlhbFVwZGF0ZSIsInVwZGF0ZUNvbW1lbnQiLCJ1c2VBcGlDb21tZW50RGVzdHJveSIsImRlbGV0ZUNvbW1lbnQiLCJ1c2VBcGlEb2NzQ3JlYXRlIiwidXBsb2FkRG9jdW1lbnQiLCJ1c2VBcGlNaXNzaW9uRG9jc0NyZWF0ZSIsIm1pc3Npb25JZCIsInVwbG9hZE1pc3Npb25Eb2N1bWVudHMiLCJ1c2VBcGlNaXNzaW9uRG9jdW1lbnRzTGlzdCIsImdldE1pc3Npb25Eb2N1bWVudHMiLCJ1c2VBcGlEb2N1bWVudHNMaXN0IiwiZ2V0RG9jdW1lbnRzIiwidXNlQXBpRG9jc0Rlc3Ryb3kiLCJkZWxldGVEb2N1bWVudCIsInVzZUFwaURvY3NVcGRhdGUiLCJ1cGRhdGVEb2N1bWVudCIsInVzZUFwaUFjdGlvbkxpc3QiLCJnZXRBY3Rpb25zIiwidXNlQXBpQWN0aW9uUmV0cmlldmUiLCJnZXRBY3Rpb24iLCJ1c2VBcGlBY3Rpb25DcmVhdGUiLCJjcmVhdGVBY3Rpb24iLCJ1c2VBcGlBY3Rpb25VcGRhdGUiLCJ1cGRhdGVBY3Rpb24iLCJ1c2VBcGlBY3Rpb25QYXJ0aWFsVXBkYXRlIiwidXNlQXBpQWN0aW9uRGVzdHJveSIsImRlbGV0ZUFjdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-client)/./hooks/useNextApi.ts\n"));

/***/ }),

/***/ "(app-client)/./lib/schemas.ts":
/*!************************!*\
  !*** ./lib/schemas.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Account: function() { return /* binding */ $Account; },\n/* harmony export */   $Action: function() { return /* binding */ $Action; },\n/* harmony export */   $ArbitratedTheme: function() { return /* binding */ $ArbitratedTheme; },\n/* harmony export */   $Arbitration: function() { return /* binding */ $Arbitration; },\n/* harmony export */   $Comment: function() { return /* binding */ $Comment; },\n/* harmony export */   $Constat: function() { return /* binding */ $Constat; },\n/* harmony export */   $Document: function() { return /* binding */ $Document; },\n/* harmony export */   $Domain: function() { return /* binding */ $Domain; },\n/* harmony export */   $Mission: function() { return /* binding */ $Mission; },\n/* harmony export */   $MissionDocument: function() { return /* binding */ $MissionDocument; },\n/* harmony export */   $Plan: function() { return /* binding */ $Plan; },\n/* harmony export */   $Process: function() { return /* binding */ $Process; },\n/* harmony export */   $Recommendation: function() { return /* binding */ $Recommendation; },\n/* harmony export */   $Risk: function() { return /* binding */ $Risk; },\n/* harmony export */   $Session: function() { return /* binding */ $Session; },\n/* harmony export */   $Structure: function() { return /* binding */ $Structure; },\n/* harmony export */   $StructureLQS: function() { return /* binding */ $StructureLQS; },\n/* harmony export */   $StructureLQSInterim: function() { return /* binding */ $StructureLQSInterim; },\n/* harmony export */   $Theme: function() { return /* binding */ $Theme; },\n/* harmony export */   $User: function() { return /* binding */ $User; },\n/* harmony export */   getSchema: function() { return /* binding */ getSchema; },\n/* harmony export */   schemas: function() { return /* binding */ schemas; }\n/* harmony export */ });\n// Simple schemas for table display and form generation\n// These schemas define the properties and titles for each model\nconst $Plan = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        title: {\n            title: \"Titre\"\n        },\n        exercise: {\n            title: \"Exercice\"\n        },\n        type: {\n            title: \"Type\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $Mission = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        code: {\n            title: \"Code\"\n        },\n        title: {\n            title: \"Titre\"\n        },\n        type: {\n            title: \"Type\"\n        },\n        etat: {\n            title: \"\\xc9tat\"\n        },\n        exercise: {\n            title: \"Exercice\"\n        },\n        planId: {\n            title: \"Plan\"\n        },\n        themeId: {\n            title: \"Th\\xe8me\"\n        },\n        headId: {\n            title: \"Chef de mission\"\n        },\n        supervisorId: {\n            title: \"Superviseur\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $User = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        username: {\n            title: \"Nom d'utilisateur\"\n        },\n        email: {\n            title: \"Email\"\n        },\n        firstName: {\n            title: \"Pr\\xe9nom\"\n        },\n        lastName: {\n            title: \"Nom\"\n        },\n        isActive: {\n            title: \"Actif\"\n        },\n        isStaff: {\n            title: \"Staff\"\n        },\n        isSuperuser: {\n            title: \"Superutilisateur\"\n        },\n        lastLogin: {\n            title: \"Derni\\xe8re connexion\"\n        },\n        dateJoined: {\n            title: \"Date inscription\"\n        }\n    }\n};\nconst $Recommendation = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        recommendation: {\n            title: \"Recommandation\"\n        },\n        missionId: {\n            title: \"Mission\"\n        },\n        concernedStructureId: {\n            title: \"Structure concern\\xe9e\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $Comment = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        comment: {\n            title: \"Commentaire\"\n        },\n        recommendationId: {\n            title: \"Recommandation\"\n        },\n        createdById: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        updated: {\n            title: \"Mis \\xe0 jour\"\n        }\n    }\n};\nconst $Action = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        description: {\n            title: \"Description\"\n        },\n        status: {\n            title: \"Statut\"\n        },\n        progress: {\n            title: \"Progr\\xe8s\"\n        },\n        startDate: {\n            title: \"Date de d\\xe9but\"\n        },\n        endDate: {\n            title: \"Date de fin\"\n        },\n        jobLeader: {\n            title: \"Responsable\"\n        },\n        proof: {\n            title: \"Preuve\"\n        },\n        recommendationId: {\n            title: \"Recommandation\"\n        },\n        dependencies: {\n            title: \"D\\xe9pendances\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $Arbitration = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        plan: {\n            title: \"Plan\"\n        },\n        report: {\n            title: \"Rapport\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $Theme = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        title: {\n            title: \"Titre\"\n        },\n        code: {\n            title: \"Code\"\n        },\n        validated: {\n            title: \"Valid\\xe9\"\n        },\n        proposedBy: {\n            title: \"Propos\\xe9 par\"\n        },\n        monthStart: {\n            title: \"Mois d\\xe9but\"\n        },\n        monthEnd: {\n            title: \"Mois fin\"\n        },\n        domain: {\n            title: \"Domaine\"\n        },\n        process: {\n            title: \"Processus\"\n        },\n        proposingStructures: {\n            title: \"Structures proposantes\"\n        },\n        concernedStructures: {\n            title: \"Structures concern\\xe9es\"\n        },\n        risks: {\n            title: \"Risques\"\n        },\n        goals: {\n            title: \"Objectifs\"\n        },\n        arbitratedThemes: {\n            title: \"Th\\xe8mes arbitr\\xe9s\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $ArbitratedTheme = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        arbitrationId: {\n            title: \"Arbitrage\"\n        },\n        themeId: {\n            title: \"Th\\xe8me\"\n        },\n        missionId: {\n            title: \"Mission\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        }\n    }\n};\nconst $Structure = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        name: {\n            title: \"Nom\"\n        },\n        code: {\n            title: \"Code\"\n        },\n        abbreviation: {\n            title: \"Abr\\xe9viation\"\n        },\n        type: {\n            title: \"Type\"\n        },\n        parentId: {\n            title: \"Structure parente\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        }\n    }\n};\nconst $Document = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        title: {\n            title: \"Titre\"\n        },\n        filename: {\n            title: \"Nom du fichier\"\n        },\n        filesize: {\n            title: \"Taille\"\n        },\n        mimetype: {\n            title: \"Type MIME\"\n        },\n        uploadedById: {\n            title: \"T\\xe9l\\xe9charg\\xe9 par\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        }\n    }\n};\nconst $MissionDocument = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        missionId: {\n            title: \"Mission\"\n        },\n        documentId: {\n            title: \"Document\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        }\n    }\n};\nconst $Account = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        userId: {\n            title: \"Utilisateur\"\n        },\n        provider: {\n            title: \"Fournisseur\"\n        },\n        providerId: {\n            title: \"ID Fournisseur\"\n        },\n        password: {\n            title: \"Mot de passe\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        updated: {\n            title: \"Mis \\xe0 jour\"\n        }\n    }\n};\nconst $Session = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        userId: {\n            title: \"Utilisateur\"\n        },\n        token: {\n            title: \"Token\"\n        },\n        expiresAt: {\n            title: \"Expire le\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        updated: {\n            title: \"Mis \\xe0 jour\"\n        }\n    }\n};\nconst $Constat = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        title: {\n            title: \"Titre\"\n        },\n        description: {\n            title: \"Description\"\n        },\n        content: {\n            title: \"Contenu\"\n        },\n        type: {\n            title: \"Type\"\n        },\n        parent: {\n            title: \"Parent\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $Risk = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        title: {\n            title: \"Titre\"\n        },\n        description: {\n            title: \"Description\"\n        },\n        validated: {\n            title: \"Valid\\xe9\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $Domain = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        title: {\n            title: \"Titre\"\n        },\n        description: {\n            title: \"Description\"\n        },\n        parent: {\n            title: \"Domaine parent\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $Process = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        title: {\n            title: \"Titre\"\n        },\n        description: {\n            title: \"Description\"\n        },\n        content: {\n            title: \"Contenu\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        },\n        createdBy: {\n            title: \"Cr\\xe9\\xe9 par\"\n        },\n        modifiedBy: {\n            title: \"Modifi\\xe9 par\"\n        }\n    }\n};\nconst $StructureLQS = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        name: {\n            title: \"Nom\"\n        },\n        code: {\n            title: \"Code\"\n        },\n        abbrev: {\n            title: \"Abr\\xe9viation\"\n        },\n        type: {\n            title: \"Type\"\n        },\n        correspondents: {\n            title: \"Correspondants\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        }\n    }\n};\nconst $StructureLQSInterim = {\n    properties: {\n        id: {\n            title: \"ID\"\n        },\n        name: {\n            title: \"Nom\"\n        },\n        code: {\n            title: \"Code\"\n        },\n        abbrev: {\n            title: \"Abr\\xe9viation\"\n        },\n        type: {\n            title: \"Type\"\n        },\n        created: {\n            title: \"Cr\\xe9\\xe9\"\n        },\n        modified: {\n            title: \"Modifi\\xe9\"\n        }\n    }\n};\n// Export all schemas as a collection for easy access\nconst schemas = {\n    Plan: $Plan,\n    Mission: $Mission,\n    User: $User,\n    Recommendation: $Recommendation,\n    Comment: $Comment,\n    Action: $Action,\n    Arbitration: $Arbitration,\n    Theme: $Theme,\n    ArbitratedTheme: $ArbitratedTheme,\n    Structure: $Structure,\n    Document: $Document,\n    MissionDocument: $MissionDocument,\n    Account: $Account,\n    Session: $Session,\n    Constat: $Constat,\n    Risk: $Risk,\n    Domain: $Domain,\n    Process: $Process,\n    StructureLQS: $StructureLQS,\n    StructureLQSInterim: $StructureLQSInterim\n};\n// Helper function to get schema by model name\nfunction getSchema(modelName) {\n    return schemas[modelName];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./lib/schemas.ts\n"));

/***/ }),

/***/ "(app-client)/./services/api/nextApi.ts":
/*!*********************************!*\
  !*** ./services/api/nextApi.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nextApiService: function() { return /* binding */ nextApiService; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-client)/./node_modules/next/dist/build/polyfills/process.js\");\n// Next.js API Service to replace Django API calls\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"/api\";\nclass NextApiService {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const { method = \"GET\", headers = {}, body } = options;\n        const config = {\n            method,\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...headers\n            }\n        };\n        if (body && method !== \"GET\") {\n            config.body = JSON.stringify(body);\n        }\n        const response = await fetch(\"\".concat(API_BASE_URL).concat(endpoint), config);\n        if (!response.ok) {\n            throw new Error(\"API Error: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        return response.json();\n    }\n    // Mission API methods\n    async getMissions(params) {\n        var _params, _params1, _params2;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        const query = searchParams.toString();\n        return this.request(\"/missions\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getMission(id) {\n        return this.request(\"/missions/\".concat(id));\n    }\n    async createMission(data) {\n        return this.request(\"/missions\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateMission(id, data) {\n        return this.request(\"/missions/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteMission(id) {\n        return this.request(\"/missions/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Recommendation API methods\n    async getRecommendations(params) {\n        var _params, _params1, _params2, _params3;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.missionId) searchParams.append(\"missionId\", params.missionId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/recommendations\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getRecommendation(id) {\n        return this.request(\"/recommendations/\".concat(id));\n    }\n    async createRecommendation(data) {\n        return this.request(\"/recommendations\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateRecommendation(id, data) {\n        return this.request(\"/recommendations/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteRecommendation(id) {\n        return this.request(\"/recommendations/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // User API methods\n    async getUsers(params) {\n        var _params, _params1, _params2;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        const query = searchParams.toString();\n        return this.request(\"/users\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getUser(id) {\n        return this.request(\"/users/\".concat(id));\n    }\n    async createUser(data) {\n        return this.request(\"/users\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    // Plan API methods\n    async getPlans(params) {\n        var _params, _params1, _params2, _params3;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.exercise) searchParams.append(\"exercise\", params.exercise.toString());\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.type) searchParams.append(\"type\", params.type);\n        const query = searchParams.toString();\n        return this.request(\"/plans\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getPlan(id) {\n        return this.request(\"/plans/\".concat(id));\n    }\n    async createPlan(data) {\n        return this.request(\"/plans\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updatePlan(id, data) {\n        return this.request(\"/plans/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deletePlan(id) {\n        return this.request(\"/plans/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Theme API methods\n    async getThemes(params) {\n        var _params, _params1, _params2, _params3, _params4;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if (((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.validated) !== undefined) searchParams.append(\"validated\", params.validated.toString());\n        if ((_params4 = params) === null || _params4 === void 0 ? void 0 : _params4.domainId) searchParams.append(\"domainId\", params.domainId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/themes\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getTheme(id) {\n        return this.request(\"/themes/\".concat(id));\n    }\n    async createTheme(data) {\n        return this.request(\"/themes\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateTheme(id, data) {\n        return this.request(\"/themes/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteTheme(id) {\n        return this.request(\"/themes/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Arbitration API methods\n    async getArbitrations(params) {\n        var _params, _params1, _params2, _params3;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.planId) searchParams.append(\"planId\", params.planId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/arbitrations\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getArbitration(id) {\n        return this.request(\"/arbitrations/\".concat(id));\n    }\n    async createArbitration(data) {\n        return this.request(\"/arbitrations\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateArbitration(id, data) {\n        return this.request(\"/arbitrations/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteArbitration(id) {\n        return this.request(\"/arbitrations/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Comment API methods\n    async getComments(params) {\n        var _params, _params1, _params2, _params3, _params4, _params5;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.recommendationId) searchParams.append(\"recommendationId\", params.recommendationId.toString());\n        if ((_params4 = params) === null || _params4 === void 0 ? void 0 : _params4.missionId) searchParams.append(\"missionId\", params.missionId.toString());\n        if ((_params5 = params) === null || _params5 === void 0 ? void 0 : _params5.userId) searchParams.append(\"userId\", params.userId.toString());\n        const query = searchParams.toString();\n        return this.request(\"/comments\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getComment(id) {\n        return this.request(\"/comments/\".concat(id));\n    }\n    async createComment(data) {\n        return this.request(\"/comments\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateComment(id, data) {\n        return this.request(\"/comments/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteComment(id) {\n        return this.request(\"/comments/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Document API methods\n    async uploadDocument(data) {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/documents\"), {\n            method: \"POST\",\n            credentials: \"include\",\n            body: data\n        });\n        if (!response.ok) {\n            throw new Error(\"Upload Error: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        return response.json();\n    }\n    async uploadMissionDocuments(missionId, data) {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/missions/\").concat(missionId, \"/documents\"), {\n            method: \"POST\",\n            credentials: \"include\",\n            body: data\n        });\n        if (!response.ok) {\n            throw new Error(\"Upload Error: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        return response.json();\n    }\n    async getMissionDocuments(missionId) {\n        return this.request(\"/missions/\".concat(missionId, \"/documents\"));\n    }\n    async getDocuments(params) {\n        var _params, _params1;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.missionId) searchParams.append(\"missionId\", params.missionId.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.context) searchParams.append(\"context\", params.context);\n        const query = searchParams.toString();\n        return this.request(\"/documents\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async deleteDocument(id) {\n        return this.request(\"/documents/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    async updateDocument(id, data) {\n        return this.request(\"/documents/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    // Action API methods\n    async getActions(params) {\n        var _params, _params1, _params2, _params3, _params4;\n        const searchParams = new URLSearchParams();\n        if ((_params = params) === null || _params === void 0 ? void 0 : _params.page) searchParams.append(\"page\", params.page.toString());\n        if ((_params1 = params) === null || _params1 === void 0 ? void 0 : _params1.limit) searchParams.append(\"limit\", params.limit.toString());\n        if ((_params2 = params) === null || _params2 === void 0 ? void 0 : _params2.search) searchParams.append(\"search\", params.search);\n        if ((_params3 = params) === null || _params3 === void 0 ? void 0 : _params3.recommendationId) searchParams.append(\"recommendationId\", params.recommendationId.toString());\n        if ((_params4 = params) === null || _params4 === void 0 ? void 0 : _params4.status) searchParams.append(\"status\", params.status);\n        const query = searchParams.toString();\n        return this.request(\"/actions\".concat(query ? \"?\".concat(query) : \"\"));\n    }\n    async getAction(id) {\n        return this.request(\"/actions/\".concat(id));\n    }\n    async createAction(data) {\n        return this.request(\"/actions\", {\n            method: \"POST\",\n            body: data\n        });\n    }\n    async updateAction(id, data) {\n        return this.request(\"/actions/\".concat(id), {\n            method: \"PATCH\",\n            body: data\n        });\n    }\n    async deleteAction(id) {\n        return this.request(\"/actions/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n}\nconst nextApiService = new NextApiService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (nextApiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./services/api/nextApi.ts\n"));

/***/ })

});