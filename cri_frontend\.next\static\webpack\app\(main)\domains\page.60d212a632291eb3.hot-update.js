"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/domains/page",{

/***/ "(app-client)/./app/(main)/domains/(components)/GenericTAble.tsx":
/*!**********************************************************!*\
  !*** ./app/(main)/domains/(components)/GenericTAble.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/confirmpopup */ \"(app-client)/./node_modules/primereact/confirmpopup/confirmpopup.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    _s();\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { mutate: createDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainCreate)();\n    const { mutate: updateDomain } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainUpdate)();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [parentDropDown, setParentDropDown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(data_.data_type.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"modified_by\",\n                \"created_by\"\n            ].includes(key);\n        }).map((param, index)=>{\n            let [key, value] = param;\n            if ([\n                \"report\",\n                \"content\",\n                \"note\",\n                \"order\",\n                \"comment\",\n                \"description\"\n            ].includes(key)) {\n                var _data__data_type_properties_key_title;\n                return {\n                    header: (_data__data_type_properties_key_title = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title !== void 0 ? _data__data_type_properties_key_title : key,\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        if ([\n                            \"description\",\n                            \"content\",\n                            \"report\"\n                        ].includes(key)) return null;\n                        else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: parse(cell.getValue())\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 116\n                        }, this);\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                            initialValue: row.original[key],\n                            tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                            apiKey: \"none\",\n                            init: {\n                                height: 500,\n                                menubar: true,\n                                plugins: [\n                                    \"advlist\",\n                                    \"autolink\",\n                                    \"lists\",\n                                    \"link\",\n                                    \"image\",\n                                    \"charmap\",\n                                    \"print\",\n                                    \"preview\",\n                                    \"anchor\",\n                                    \"searchreplace\",\n                                    \"visualblocks\",\n                                    \"code\",\n                                    \"fullscreen\",\n                                    \"insertdatetime\",\n                                    \"media\",\n                                    \"table\",\n                                    \"paste\",\n                                    \"code\",\n                                    \"help\",\n                                    \"wordcount\"\n                                ],\n                                toolbar: \"undo redo | formatselect | bold italic backcolor |                         alignleft aligncenter alignright alignjustify |                         bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 22\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"parent\") {\n                var _data__data_type_properties_key_title1;\n                return {\n                    header: (_data__data_type_properties_key_title1 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title1 !== void 0 ? _data__data_type_properties_key_title1 : key,\n                    accessorKey: \"parent.title\",\n                    muiTableHeadCellProps: {\n                        align: \"left\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"left\"\n                    },\n                    Edit: (param)=>{\n                        let { cell, column, row, table } = param;\n                        var _row_original_parent;\n                        setParentDropDown({\n                            \"code\": row.original.parentId,\n                            \"name\": (_row_original_parent = row.original.parent) === null || _row_original_parent === void 0 ? void 0 : _row_original_parent.title\n                        });\n                        const onChange = (event)=>{\n                            console.log(\"################AAAAAAAAAAAAAAAAAAAA####################\", event.value);\n                            setParentDropDown(event.value);\n                            row._valuesCache.parentId = event.value.code;\n                            if (row.id === \"mrt-row-create\") {\n                                table.setCreatingRow(row);\n                            } else {\n                                table.setEditingRow(row);\n                            }\n                        };\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_4__.Dropdown, {\n                            optionLabel: \"name\",\n                            value: parentDropDown,\n                            onChange: onChange,\n                            filter: true,\n                            options: data_.data_.data.results.map(function(val) {\n                                return {\n                                    \"code\": val.id,\n                                    \"name\": val.title\n                                };\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 22\n                        }, this);\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key\n                };\n            }\n            if (data_.data_type.properties[key].format === \"date-time\" || data_.data_type.properties[key].format === \"date\") {\n                var _data__data_type_properties_key_title2;\n                return {\n                    accessorFn: (row)=>new Date(key == \"created\" ? row.created : row.modified),\n                    header: (_data__data_type_properties_key_title2 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title2 !== void 0 ? _data__data_type_properties_key_title2 : key,\n                    filterVariant: \"date\",\n                    filterFn: \"lessThan\",\n                    sortingFn: \"datetime\",\n                    accessorKey: key,\n                    Cell: (param)=>{\n                        let { cell } = param;\n                        var _cell_getValue;\n                        return (_cell_getValue = cell.getValue()) === null || _cell_getValue === void 0 ? void 0 : _cell_getValue.toLocaleDateString(\"fr\");\n                    },\n                    id: key,\n                    Edit: ()=>null\n                };\n            }\n            if (key === \"proposed_by\") {\n                var _data__data_type_properties_key_title3;\n                return {\n                    header: (_data__data_type_properties_key_title3 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title3 !== void 0 ? _data__data_type_properties_key_title3 : key,\n                    accessorKey: key,\n                    editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0][\"$ref\"] ? data_.data_type.properties[key].allOf[0][\"$ref\"].enum : [],\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        SelectProps: {\n                            multiple: false\n                        }\n                    },\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"VP\" ? \"danger\" : cell.getValue() === \"STRUCT\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"VP\" ? \"Vice Pr\\xe9sident\" : cell.getValue() === \"STRUCT\" ? \"Structures\" : \"Contr\\xf4le Interne\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"etat\") {\n                var _data__data_type_properties_key_title4;\n                return {\n                    header: (_data__data_type_properties_key_title4 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title4 !== void 0 ? _data__data_type_properties_key_title4 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        // children: data_.data_type.properties[key]['$ref'].enum,\n                        SelectProps: {\n                        }\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: cell.getValue() === \"NS\" ? \"danger\" : cell.getValue() === \"EC\" ? \"success\" : \"info\",\n                            value: cell.getValue() === \"NS\" ? \"Non lanc\\xe9e\" : cell.getValue() === \"SP\" ? \"Suspendue\" : cell.getValue() === \"EC\" ? \"En cours\" : \"Cl\\xf4tur\\xe9e\"\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 38\n                        }, this);\n                    }\n                };\n            }\n            if (key === \"exercise\") {\n                var _data__data_type_properties_key_title5;\n                // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)\n                return {\n                    header: (_data__data_type_properties_key_title5 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title5 !== void 0 ? _data__data_type_properties_key_title5 : key,\n                    accessorKey: key,\n                    muiTableHeadCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableBodyCellProps: {\n                        align: \"center\"\n                    },\n                    muiTableFooterCellProps: {\n                        align: \"center\"\n                    },\n                    id: key,\n                    Cell: (param)=>/*#__PURE__*/ {\n                        let { cell, row } = param;\n                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_5__.Tag, {\n                            severity: \"success\",\n                            value: cell.getValue()\n                        }, row.original.code + row.original.created, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 15\n                        }, this);\n                    }\n                };\n            }\n            if (data_.data_type.properties[key][\"$ref\"] && data_.data_type.properties[key][\"$ref\"].enum) {\n                console.log(\"#######enum##########\", key, value);\n                var _data__data_type_properties_key_title6;\n                return {\n                    header: (_data__data_type_properties_key_title6 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title6 !== void 0 ? _data__data_type_properties_key_title6 : key,\n                    // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+\" ,\"; }, \"\"):\"\",\n                    accessorKey: key,\n                    id: key,\n                    Cell: (param)=>{\n                        let { cell, row } = param;\n                        return cell.row.original[key];\n                    },\n                    editSelectOptions: data_.data_type.properties[key][\"$ref\"].enum,\n                    muiEditTextFieldProps: {\n                        select: true,\n                        variant: \"outlined\",\n                        children: data_.data_type.properties[key][\"$ref\"].enum,\n                        SelectProps: {\n                        }\n                    }\n                };\n            } else {\n                var _data__data_type_properties_key_title7, _data__data_type_properties_key_title8;\n                if (key === \"id\") return {\n                    header: (_data__data_type_properties_key_title7 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title7 !== void 0 ? _data__data_type_properties_key_title7 : key,\n                    accessorKey: key,\n                    id: key,\n                    Edit: ()=>null\n                };\n                else return {\n                    header: (_data__data_type_properties_key_title8 = data_.data_type.properties[key].title) !== null && _data__data_type_properties_key_title8 !== void 0 ? _data__data_type_properties_key_title8 : key,\n                    accessorKey: key,\n                    id: key\n                };\n            }\n        }), []);\n    console.log(\"############## data from api \", data_);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableExpandAll: true,\n        enableEditing: true,\n        enableExpanding: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false,\n                staff: false,\n                assistants: false,\n                id: false,\n                document: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_7__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-actions\": {\n                // header: 'Edit', //change \"Actions\" to \"Edit\"\n                size: 100,\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                //elevation: 0, //change the mui box shadow\n                //customize paper styles\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, values } = param;\n            var _toast_current;\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            table.setEditingRow(null); //exit editing mode\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Sauvegarde en cours\"\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            delete values.id;\n            createDomain(values);\n            table.setCreatingRow(null); //exit creating mode\n        },\n        onCreatingRowCancel: ()=>{\n        //clear any validation errors\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                // about:\"edit modal\",\n                open: editVisible || createVisible,\n                sx: {\n                    \"& .MuiDialog-root\": {\n                        display: \"none\"\n                    },\n                    \"& .MuiDialog-container\": {\n                        display: \"none\"\n                    },\n                    zIndex: 1100\n                }\n            };\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        rounded: true,\n                        disabled: table.getIsSomeRowsSelected(),\n                        // id=\"basic-button\"\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: handleClick,\n                        icon: \"pi pi-trash\",\n                        // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        icon: \"pi pi-bell\",\n                        rounded: true,\n                        color: rowActionEnabled ? \"secondary\" : \"primary\",\n                        size: \"small\",\n                        \"aria-label\": \"edit\",\n                        onClick: ()=>setRowActionEnabled(!rowActionEnabled)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            }),\n        renderCreateRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            zIndex: \"1302 !important\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                            position: \"right\",\n                            header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"align-content-center \",\n                                        children: [\n                                            \"Cr\\xe9ation \",\n                                            data_.data_type.name,\n                                            \" \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                            variant: \"text\",\n                                            table: table,\n                                            row: row\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                ]\n                            }, void 0, true, void 0, void 0),\n                            visible: createVisible,\n                            onHide: ()=>{\n                                table.setCreatingRow(null);\n                                setCreateVisible(false);\n                            },\n                            className: \"w-full md:w-9 lg:w-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: [\n                                    internalEditComponents,\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 7\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 82\n                    }, this)\n                ]\n            }, void 0, true);\n        },\n        renderEditRowDialogContent: (param)=>/*#__PURE__*/ {\n            let { internalEditComponents, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        zIndex: \"1302 !important\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                        position: \"right\",\n                        header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"align-content-center \",\n                                    children: [\n                                        \"Editer \",\n                                        data_.data_type.name,\n                                        \" \",\n                                        row.original.code\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MRT_EditActionButtons, {\n                                        variant: \"text\",\n                                        table: table,\n                                        row: row\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        visible: editVisible,\n                        onHide: ()=>{\n                            table.setEditingRow(null);\n                            setEditVisible(false);\n                        },\n                        className: \"w-full md:w-9 lg:w-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                gap: \"1.5rem\"\n                            },\n                            children: [\n                                internalEditComponents,\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 79\n                }, this)\n            }, void 0, false);\n        },\n        renderDetailPanel: (param)=>{\n            let { row } = param;\n            return row.original.description ? parse(row.original.description) : row.original.content ? parse(row.original.content) : row.original.staff ? row.original.staff.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    display: \"grid\",\n                    margin: \"auto\",\n                    //gridTemplateColumns: '1fr 1fr',\n                    width: \"100vw\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabView, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"staff\"].title,\n                            leftIcon: \"pi pi-user mr-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.staff.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 134\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 64\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: data_.data_type.properties[\"assistants\"].title,\n                            rightIcon: \"pi pi-user ml-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: row.original.assistants.map((user, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"mailto:\" + user.email,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                user.last_name,\n                                                \" \",\n                                                user.first_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 139\n                                        }, this)\n                                    }, user.email + row.original.code, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 69\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 21\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 19\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_14__.TabPanel, {\n                            header: \"Lettre\",\n                            leftIcon: \"pi pi-file-word mr-2\",\n                            rightIcon: \"pi pi-file-pdf ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    icon: \"pi pi-check\",\n                                    rounded: true,\n                                    onClick: ()=>setVisible(true),\n                                    disabled: row.original.document === null\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_10__.Sidebar, {\n                                    header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: [\n                                            \"Lettre de mission : \",\n                                            row.original.code\n                                        ]\n                                    }, void 0, true, void 0, void 0),\n                                    visible: visible,\n                                    onHide: ()=>setVisible(false),\n                                    className: \"w-full md:w-9 lg:w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                        children: [\n                                            row.original.document !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Document, {\n                                                file: row.original.document,\n                                                onLoadSuccess: onDocumentLoadSuccess,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Page, {\n                                                    pageNumber: pageNumber\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 27\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No Document\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-column align-items-center justify-content-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Page \",\n                                                            pageNumber || (numPages ? 1 : \"--\"),\n                                                            \" of \",\n                                                            numPages || \"--\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row align-items-center justify-content-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber <= 1,\n                                                                onClick: previousPage,\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                                type: \"button\",\n                                                                disabled: pageNumber >= numPages,\n                                                                onClick: nextPage,\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 23\n                                    }, this)\n                                }, row.original.id, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 19\n                        }, this),\n                        \"          \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 650,\n                columnNumber: 15\n            }, this) : null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n        },\n        renderRowActions: (param)=>// <Box sx={{ display: 'flex', gap: '1rem' }}>\n        /*#__PURE__*/ {\n            let { cell, row, table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"p-buttonset flex p-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-pencil\",\n                        onClick: ()=>{\n                            table.setEditingRow(row);\n                            setEditVisible(true), console.log(\"editing row ...\");\n                        },\n                        rounded: true,\n                        outlined: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        size: \"small\",\n                        icon: \"pi pi-trash\",\n                        rounded: true,\n                        outlined: true,\n                        onClick: (event)=>(0,primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.confirmPopup)({\n                                target: event.currentTarget,\n                                message: \"Voulez-vous supprimer cette ligne?\",\n                                icon: \"pi pi-info-circle\",\n                                // defaultFocus: 'reject',\n                                acceptClassName: \"p-button-danger\",\n                                acceptLabel: \"Oui\",\n                                rejectLabel: \"Non\",\n                                accept,\n                                reject\n                            })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_confirmpopup__WEBPACK_IMPORTED_MODULE_15__.ConfirmPopup, {}, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 709,\n                columnNumber: 7\n            }, this);\n        }\n    });\n    // console.log(data_.isLoading)\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_6__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 737,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_16__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\domains\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 737,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"2u2a2fJ1r5Dl3tiKJxMUn3gwaXA=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_6__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/domains/(components)/GenericTAble.tsx\n"));

/***/ })

});