'use client';

import {  useApiConstatList } from '@/services/api/api/api';
import { $Constat, Constat } from '@/services/openapi_client';
import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import GenericTable from './(components)/GenericTAble';
import { getCookie } from 'cookies-next';

const ThemesTable = () => {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')

    const { data: constats, isLoading:isLoading ,error:error} = useApiConstatList({},{
        axios: { headers: { Authorization: `Token ${user?.token}` } } });
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex:0,
        pageSize: 5, //customize the default page size
    });
    if( isLoading )  return (<div></div>)
        console.log("Constats from api",constats)
    return   (        
        <div className="grid">
            <div className="col-12">
                    <GenericTable<Constat> data_={constats} isLoading={isLoading} error={error} data_type={$Constat} pagination={{"set":setPagination,"pagi":pagination}}></GenericTable>
            </div>
        </div>
    );
};

export default ThemesTable;
