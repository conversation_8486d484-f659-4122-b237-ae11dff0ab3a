from typing import Any
from django.db.models import Value as V
from django.db.models.functions import Concat
from django.contrib import admin 
from django.contrib.auth.admin import UserAdmin,GroupAdmin
from django_extensions.admin import ForeignKeyAutocompleteAdmin
from django.contrib.auth import get_user_model
# Register your models here.
from django.apps import apps
from ajax_select.admin import AjaxSelectAdmin
from .forms import *
Userium = get_user_model()
Userium.full_name = property(lambda u: u"%s %s (%s)" % (u.first_name, u.last_name,u.username))
def fullname(self):
    return self.full_name
Userium.__str__ = fullname
app = apps.get_app_config('backend')
admin.site.site_url = None
for model_name, model in app.models.items():
    # print(model._meta.verbose_name)
    if  model._meta.verbose_name  in  ["Profile utilisateur","role","permission","notification","Mission Staff","Fait","Intérim Structure LQS","Profile utilisateur","Action","Action Plan","Commentaire","Arbitrage","Plan d'actions Recommendations","Plan d'actions Annotations"] :  
        model_admin = type(model_name + "Admin", (admin.ModelAdmin,), {})

        model_admin.list_display = model.admin_list_display if hasattr(model, 'admin_list_display') else tuple([field.name for field in model._meta.fields])
        model_admin.list_filter = model.admin_list_filter if hasattr(model, 'admin_list_filter') else model_admin.list_display
        model_admin.list_display_links = model.admin_list_display_links if hasattr(model, 'admin_list_display_links') else ()
        model_admin.list_editable = model.admin_list_editable if hasattr(model, 'admin_list_editable') else ()
        model_admin.search_fields = model.admin_search_fields if hasattr(model, 'admin_search_fields') else tuple([field.name for field in model._meta.fields if field not in ['user','agent']])
        model_admin.save_as = True
        model_admin.save_as_continue = True
        admin.site.register(model, model_admin)
    # else :
    #     admin.site.unregister(model)

###############################################################################
# class TabularPlanTheme(admin.StackedInline):
#     model = PlanTheme
#     extra = 0
#     # max_num = 3
#     # min_num = 0
#     #classes = ['collapse']
class TabularInterim(admin.StackedInline):
    model = StructureLQSInterim
    extra = 0
    verbose_name ="Intérim"
    verbose_name_plural ="Intérims"
class TabularUserProfile(admin.StackedInline):
    model = UserProfile
    extra = 0
    verbose_name ="UserProfile"
    verbose_name_plural ="UserProfile"
    fk_name ="user"
UserAdmin.inlines = [TabularUserProfile]
class TabularRecommendation(admin.StackedInline):
    model = Recommendation.causes.through
    extra = 0
    verbose_name ="Recommendation associée au risque"
    verbose_name_plural ="Recommendations associées au risque"
    # max_num = 3
    # min_num = 0
    #classes = ['collapse']
###############################################################################
###############################################################################
@admin.register(ArbitratedTheme)
class ArbitratedThemeAdmin(admin.ModelAdmin):
    model = ArbitratedTheme
    save_as = True
    
    list_display = [field.name for field in model._meta.fields ]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
###############################################################################
###############################################################################
@admin.register(RiskImpact)
class RiskImpactAdmin(admin.ModelAdmin):
    model = RiskImpact
    save_as = True
    
    list_display = [field.name for field in model._meta.fields if field.name not in ["description"] ] +["imapct_description"]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    def imapct_description(self,obj):
        return h.handle("%s" % obj.description).replace('*', '').replace('\\-', '').replace('#', '')
    imapct_description.short_description ="description"
###############################################################################
###############################################################################
@admin.register(Consequence)
class ConsequeceAdmin(admin.ModelAdmin):
    model = Consequence
    save_as = True
    list_display = [field.name for field in model._meta.fields if field.name != 'content' ]+["consequence_content"]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    def consequence_content(self,obj):
        return h.handle("%s" % obj.content).replace('*', '').replace('\\-', '').replace('#', '')
    consequence_content.short_description ="contenu"
###############################################################################
###############################################################################
@admin.register(Constat)
class ConstatAdmin(admin.ModelAdmin):
    model = Constat
    save_as = True
    list_display = [field.name for field in model._meta.fields if field.name != 'content' ]+['constat_content']
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    def constat_content(self,obj):
        return h.handle("%s" % obj.content).replace('*', '').replace('\\-', '').replace('#', '')
    constat_content.short_description ="contenu"
###############################################################################
###############################################################################
@admin.register(Cause)
class CauseAdmin(admin.ModelAdmin):
    model = Cause
    save_as = True
    list_display = [field.name for field in model._meta.fields if field.name != 'content' ]+['cause_content']
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    def cause_content(self,obj):
        return h.handle("%s" % obj.content).replace('*', '').replace('\\-', '').replace('#', '')
    cause_content.short_description ="contenu"

###############################################################################
###############################################################################
@admin.register(Recommendation)
class RecommendationAdmin(admin.ModelAdmin):
    model = Recommendation
    save_as = True
    list_display = [field.name for field in model._meta.fields ]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
###############################################################################
###############################################################################
@admin.register(Mission)
class MissionAdmin(admin.ModelAdmin):
    model = Mission
    save_as = True
    list_display = [field.name for field in model._meta.fields ] +["equipe_cri"]#,"assistants_cri"]
    autocomplete_fields = ["plan"] #,"staff","assistants"]
    #list_display_links = ( 'id',)
    list_editable =["code"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    def get_queryset(self, request):
        
        return Mission.objects.active_for_user(request.user)
    def equipe_cri(self,obj):
        return mark_safe("<ul>%s</ul>"% "".join(["<li><b>%s</b></li>" % it for it in obj.staff.all().annotate(full_name=Concat('first_name', V(' '), 'last_name')).values_list("full_name",flat=True)]))
    def assistants_cri(self,obj):
        return mark_safe("<ul>%s</ul>"% "".join(["<li>%s</li>" % it for it in obj.assistants.all().annotate(full_name=Concat('first_name', V(' '), 'last_name')).values_list("full_name",flat=True)]))
    
###############################################################################
###############################################################################
# @admin.register(Exercise)
# class ExerciseAdmin(admin.ModelAdmin):
#     model = Exercise
#     save_as = True
#     list_display = [field.name for field in model._meta.fields ]
#     #list_display_links = ( 'id',)
#     #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
#     search_fields =[field.name for field in model._meta.fields ]
#     list_filter =[field.name for field in model._meta.fields ]
###############################################################################
###############################################################################
@admin.register(CriStructview)
class StructureLQSAdmin(admin.ModelAdmin):
    model = CriStructview
    # save_as = True
    inlines = [TabularInterim,]
    list_display = [field.name for field in model._meta.fields ]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
###############################################################################
###############################################################################
@admin.register(StructureLQSCorrespondents)
class StructureLQSCorrespondentsAdmin(admin.ModelAdmin):
    model = StructureLQSCorrespondents
    # save_as = True
    autocomplete_fields=['structure','correspondents']
    list_display = [field.name for field in model._meta.fields ]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
###############################################################################
###############################################################################
@admin.register(Process)
class ProcessAdmin(admin.ModelAdmin):
    model = Process
    save_as = True
    list_display = [field.name for field in model._meta.fields ]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
###############################################################################
###############################################################################
@admin.register(Domain)
class DomainAdmin(admin.ModelAdmin):
    model = Domain
    save_as = True
    list_display = [field.name for field in model._meta.fields ]+["code"]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    def code(self,obj):
        if obj.parent :
            return "%s/%s" %(obj.short_title,obj.parent.short_title )
        else :
            return obj.short_title
###############################################################################
###############################################################################
@admin.register(Risk)
class RiskAdmin(admin.ModelAdmin):
    model = Risk
    save_as = True
    list_display = [field.name for field in model._meta.fields if field.name not in ["description"]]+["description_risk"]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    # inlines = [TabularRecommendation,]
    def description_risk(self,obj):
        return h.handle(obj.description).replace('*', '').replace('\\-', '').replace('#', '')
###############################################################################
###############################################################################
@admin.register(Goal)
class GoalAdmin(admin.ModelAdmin):
    model = Goal
    save_as = True
    list_display = [field.name for field in model._meta.fields ]
    #list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
###############################################################################
###############################################################################
# @admin.register(PlanTheme)
# class PlanThemeAdmin(admin.ModelAdmin):
#     model = PlanTheme
#     save_as = True
#     list_display = [field.name for field in model._meta.fields ]
#     #list_display_links = ( 'id',)
#     #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
#     search_fields =[field.name for field in model._meta.fields ]
#     list_filter =[field.name for field in model._meta.fields ]
###############################################################################
###############################################################################
@admin.register(Plan)
class PlanAdmin(admin.ModelAdmin):
    # inlines = [TabularPlanTheme,]
    form = PlanForm
    model = Plan
    save_as = True
    list_display = [field.name for field in model._meta.fields ]
    list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    # actions = ['mark_archived',]
    # list_display = ['id','mobile_image_preview','title','tts_preview','message_template',"layout",'send_notification','quality_service','state','type','messaging_channels','desktop_alert','schedule_start','schedule_end','created_by']
    # list_display_links = ('title', 'id')
    # list_editable = ['state','type','messaging_channels','layout']
    # search_fields =['layout']
    # list_filter =['id','title','state','type','messaging_channels','desktop_alert','schedule_start','schedule_end','created_by']
    #list_select_related = ()
#     fieldsets = (
#       ('Standard info', {
#           'fields': ('title','priority','state','type','quality_service',('messaging_channels','device_type'),('user_close_timer','auto_close_timer'),),
#           'classes': ('collapse','extrapretty',),
#       }),
#       ('SMS', {
#           'fields': ('sms_content',),
#           'classes': ('collapse','extrapretty',),
#       }),
#       ('Time/Schedule info', {
#           'fields': ('time_zone','one_off','schedule_start','schedule_end', 'repeat'),
#           'classes': ('collapse','extrapretty',),
#       }),
#       ('Audio', {
#           'fields': ('volume', 'unmute','tts_language', 'text_to_speech','tts_text'),
#           'classes': ('collapse','extrapretty',),
#       }),
#       ('Body', {
#           'fields': (('layout','desktop_alert'),'body','mobile_image' ,('background_color','background_gradient','background_image'),('blink', 'blink_color')),
#           'classes': ('collapse','extrapretty',),
#       }),
#    )

#     def get_queryset(self, request):
#         qs = super().get_queryset(request)
#         qs= qs.prefetch_related("desktop_alert","quality_service","layout")
#         return qs.order_by("type")
#     # TODO: https://codepen.io/saigowthamr/pen/YjMwmV
    
#     def mobile_image_preview(self,obj):
#         return mark_safe('<img src="%s" height=50 >' % ((obj.mobile_image.url) if obj.mobile_image else  ('/static/img/no_pic.png') ))
#     mobile_image_preview.short_description ="Mobile Image"
    

    
#     def tts_preview(self,obj):
#         return mark_safe('<p><i class="volume up big icon %s" ></i><span style="display:none !important;" tts-lang="%s">%s</span></p>' % ("grey" if obj.tts_text=="" or obj.tts_text is None else "",obj.tts_language,force_escape(obj.tts_text)))
#     tts_preview.short_description ="Aperçu Audio"
    
#     def color_formatted(self,obj):
#         return(mark_safe('<p style="background:%s">%s</p>'%(obj.background_color,obj.background_color)))
#     color_formatted.short_description ="Couleur Arriere-plan"
    
#     def repeat_formatted(self,obj):
#         return pretty_cron.prettify_cron(obj.repeat)
#     repeat_formatted.short_description ="Fréquence"
    
#     def mark_archived(self,request,queryset):
#             queryset.update(mandatory=True)
#             messages.add_message(request, messages.INFO, '(%d) notifications marquées comme archivées. ' % queryset.count())
#     mark_archived.short_description ="Archiver les notifications sélectionées"
    
    # def topics(self,obj):
    #     from django.db.models import Count
    #     #<div class="floating ui teal label">22</div>
    #     notif_topics_grps =list(NotificationTopicGroup.objects.filter(notifications__in=[obj]).order_by('group_id').values_list("group_id",flat=True))
    #     notif_topics_subs =list(Group.objects.filter(id__in=notif_topics_grps).order_by('id').annotate(user_count=Count('user')).values_list('user_count',flat=True))
    #     notif_topics =list(NotificationTopicGroup.objects.filter(notifications__in=[obj]).order_by('group_id').values_list("topic_name",flat=True))
    #     if len(notif_topics) >0 :
    #         return mark_safe('<div class="ui middle aligned animated list">%s</div>' % " ".join(['<div class="item"><div class="ui orange tiny label "><b>%s</b><div class="detail">%s</div></div></div>' % (topic,notif_topics_subs[notif_topics.index(topic)]) for topic in notif_topics]))
    #     else :
    #         return mark_safe('<i class="ui tiny label red"><b>PAS DE SUJET</b></i>')
    # topics.short_description ="Sujets"
    # def send_notification(self,obj):
    #     #permissions = Permission.objects.filter(user=self.request.user)
    #     #if permissions.filter(name="Can send notification after save").exists() :
    #     #style="font-weight: bold;padding: 2px;background: %s;border-radius: 5px;color: white;"
    #     return(mark_safe('<button  class="ui tiny right labeled icon button  %s" data-url="%s"> <i class="paper plane outline icon"></i>%s</button>'%("" if  obj.layout and obj.state=='P' else "disabled", "/notification_send/%s" % obj.id if obj.layout and obj.state=='P' else '',"Send")))
    #     #else :
    #     #    return "Non-Disponible"
    # send_notification.short_description ="Action"

    # def save_model(self, request, obj, form, change):
    #     obj.created_by = request.user.username
    #     super().save_model(request, obj, form, change)

###############################################################################
###############################################################################

@admin.register(Theme)
class ThemeAdmin(admin.ModelAdmin):
    class Media:
        css = {
            'all': ()
        }
        js=("js/theme_model.js",)
    #inlines = [TabularPlanTheme,]
    form = ThemeForm
    model = Theme
    save_as = True
    list_display = [field.name for field in model._meta.fields ]+["proposed_by_struct"] 
    list_display_links = ( 'id',)
    #list_editable =[field.name for field in model._meta.fields if field.name!="id"]
    search_fields =[field.name for field in model._meta.fields ]
    list_filter =[field.name for field in model._meta.fields ]
    # actions = ['mark_archived',]
    # list_display = ['id','mobile_image_preview','title','tts_preview','message_template',"layout",'send_notification','quality_service','state','type','messaging_channels','desktop_alert','schedule_start','schedule_end','created_by']
    # list_display_links = ('title', 'id')
    # list_editable = ['state','type','messaging_channels','layout']
    # search_fields =['layout']
    # list_filter =['id','title','state','type','messaging_channels','desktop_alert','schedule_start','schedule_end','created_by']
    #list_select_related = ()
#     fieldsets = (
#       ('Standard info', {
#           'fields': ('title','priority','state','type','quality_service',('messaging_channels','device_type'),('user_close_timer','auto_close_timer'),),
#           'classes': ('collapse','extrapretty',),
#       }),
#       ('SMS', {
#           'fields': ('sms_content',),
#           'classes': ('collapse','extrapretty',),
#       }),
#       ('Time/Schedule info', {
#           'fields': ('time_zone','one_off','schedule_start','schedule_end', 'repeat'),
#           'classes': ('collapse','extrapretty',),
#       }),
#       ('Audio', {
#           'fields': ('volume', 'unmute','tts_language', 'text_to_speech','tts_text'),
#           'classes': ('collapse','extrapretty',),
#       }),
#       ('Body', {
#           'fields': (('layout','desktop_alert'),'body','mobile_image' ,('background_color','background_gradient','background_image'),('blink', 'blink_color')),
#           'classes': ('collapse','extrapretty',),
#       }),
#    )

#     def get_queryset(self, request):
#         qs = super().get_queryset(request)
#         qs= qs.prefetch_related("desktop_alert","quality_service","layout")
#         return qs.order_by("type")
#     # TODO: https://codepen.io/saigowthamr/pen/YjMwmV
    
#     def mobile_image_preview(self,obj):
#         return mark_safe('<img src="%s" height=50 >' % ((obj.mobile_image.url) if obj.mobile_image else  ('/static/img/no_pic.png') ))
#     mobile_image_preview.short_description ="Mobile Image"
    def proposed_by_struct(self,obj):
        if obj.proposed_by == "structures":
            structs = list(obj.proposing_structures.all().values_list("code_mnemonique",flat=True))
            return ", ".join(structs)
        else :
            #text = THEME_PROPOSING_ENTITY(obj.proposed_by).label
            return obj.proposed_by
    proposed_by_struct.short_description ="Proposé par"
#     def message_template(self,obj):
#         return mark_safe('<a target="_blank" href="/notification_json/%d/preview/%s/"><p class="small"><i class="eye big icon"></i><span class="large"><img src="/static/images/notification_id_%s.png" class="large-image" alt="notification_id_%s_lg" > </span></p></a>' % (obj.id,quote(dt.now().strftime("%d-%m-%Y %H:%M:%S"), safe=''),obj.id,obj.id))
#     message_template.short_description ="Aperçu"
    
#     def tts_preview(self,obj):
#         return mark_safe('<p><i class="volume up big icon %s" ></i><span style="display:none !important;" tts-lang="%s">%s</span></p>' % ("grey" if obj.tts_text=="" or obj.tts_text is None else "",obj.tts_language,force_escape(obj.tts_text)))
#     tts_preview.short_description ="Aperçu Audio"
    
#     def color_formatted(self,obj):
#         return(mark_safe('<p style="background:%s">%s</p>'%(obj.background_color,obj.background_color)))
#     color_formatted.short_description ="Couleur Arriere-plan"
    
#     def repeat_formatted(self,obj):
#         return pretty_cron.prettify_cron(obj.repeat)
#     repeat_formatted.short_description ="Fréquence"
    
#     def mark_archived(self,request,queryset):
#             queryset.update(mandatory=True)
#             messages.add_message(request, messages.INFO, '(%d) notifications marquées comme archivées. ' % queryset.count())
#     mark_archived.short_description ="Archiver les notifications sélectionées"
    
    # def topics(self,obj):
    #     from django.db.models import Count
    #     #<div class="floating ui teal label">22</div>
    #     notif_topics_grps =list(NotificationTopicGroup.objects.filter(notifications__in=[obj]).order_by('group_id').values_list("group_id",flat=True))
    #     notif_topics_subs =list(Group.objects.filter(id__in=notif_topics_grps).order_by('id').annotate(user_count=Count('user')).values_list('user_count',flat=True))
    #     notif_topics =list(NotificationTopicGroup.objects.filter(notifications__in=[obj]).order_by('group_id').values_list("topic_name",flat=True))
    #     if len(notif_topics) >0 :
    #         return mark_safe('<div class="ui middle aligned animated list">%s</div>' % " ".join(['<div class="item"><div class="ui orange tiny label "><b>%s</b><div class="detail">%s</div></div></div>' % (topic,notif_topics_subs[notif_topics.index(topic)]) for topic in notif_topics]))
    #     else :
    #         return mark_safe('<i class="ui tiny label red"><b>PAS DE SUJET</b></i>')
    # topics.short_description ="Sujets"
    # def send_notification(self,obj):
    #     #permissions = Permission.objects.filter(user=self.request.user)
    #     #if permissions.filter(name="Can send notification after save").exists() :
    #     #style="font-weight: bold;padding: 2px;background: %s;border-radius: 5px;color: white;"
    #     return(mark_safe('<button  class="ui tiny right labeled icon button  %s" data-url="%s"> <i class="paper plane outline icon"></i>%s</button>'%("" if  obj.layout and obj.state=='P' else "disabled", "/notification_send/%s" % obj.id if obj.layout and obj.state=='P' else '',"Send")))
    #     #else :
    #     #    return "Non-Disponible"
    # send_notification.short_description ="Action"

    # def save_model(self, request, obj, form, change):
    #     obj.created_by = request.user.username
    #     super().save_model(request, obj, form, change)

###############################################################################
###############################################################################