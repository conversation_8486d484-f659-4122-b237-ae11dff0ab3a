import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const planId = searchParams.get('planId')

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.report = {
        contains: search,
        mode: 'insensitive'
      }
    }
    
    if (planId) {
      where.planId = parseInt(planId)
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Fetch arbitrations with relations
    const arbitrations = await prisma.arbitration.findMany({
      where,
      include: {
        plan: {
          select: {
            id: true,
            exercise: true,
            type: true,

          }
        },
        team: {
          select: {
            id: true,
            username: true,
            email: true,
            firstName: true,
            lastName: true,
          }
        },
        arbitratedThemes: {
          include: {
            theme: {
              select: {
                id: true,
                title: true,
                validated: true,
              }
            }
          }
        }
      },
      orderBy: {
        created: 'desc'
      },
      skip,
      take: limit,
    })

    // Get total count for pagination
    const total = await prisma.arbitration.count({ where })

    return NextResponse.json({
      data: arbitrations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Arbitrations fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to create arbitrations (staff only)
    if (!session.user.isStaff) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { planId, report, teamIds } = body

    if (!planId) {
      return NextResponse.json(
        { error: 'Plan ID is required' },
        { status: 400 }
      )
    }

    // Create arbitration
    const newArbitration = await prisma.arbitration.create({
      data: {
        planId: parseInt(planId),
        report: report || null,
        createdBy: session.user.id,
        team: teamIds ? {
          connect: teamIds.map((id: string) => ({ id }))
        } : undefined,
      },
      include: {
        plan: {
          select: {
            id: true,
            exercise: true,
            type: true,
          }
        },
        team: {
          select: {
            id: true,
            username: true,
            email: true,
            firstName: true,
            lastName: true,
          }
        },
        arbitratedThemes: {
          include: {
            theme: {
              select: {
                id: true,
                title: true,
                validated: true,
              }
            
            }
          }
        }
      }
    })

    return NextResponse.json(newArbitration, { status: 201 })
  } catch (error) {
    console.error('Arbitration creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
