/** @type {import('next').NextConfig} */
const os = require('node:os');
const path = require("node:path");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const nextConfig = {
  // matcher: ['/((?!api|_next/static|_next/image|.*\\.png$).*)'],
  output: "standalone",
  experimental: {
    esmExternals: 'loose',
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  webpack: (config, { dev, isServer, webpack, nextRuntime }) => {
    config.resolve.alias.canvas = false;
    config.resolve.alias.encoding = false;



    // Handle ESM modules properly
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
    };

    config.module.rules.push({
      test: /\.node/,
      use: 'raw-loader',
    });

    config.plugins.push(
      new CopyWebpackPlugin({
        patterns: [
          {
            from: path.join(
              path.dirname(require.resolve("pdfjs-dist/package.json")),
              "cmaps"
            ),
            to: "cmaps/",
          },
        ],
      })
    );
    return config;
  },
}

module.exports = nextConfig