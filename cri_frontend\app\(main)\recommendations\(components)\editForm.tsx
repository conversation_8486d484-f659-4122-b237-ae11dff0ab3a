'use client';

import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useApiMissionCreate, useApi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useApi<PERSON>ission<PERSON>ist, useApi<PERSON><PERSON><PERSON>ist, useApiStructurelqsList, useApiThemeList, useApiUsersList } from '@/services/api/api/api';
import { ReactElement, useEffect, useMemo, useRef, useState, type ReactNode } from 'react';
import { Box, DialogActions, DialogContent, Typography } from '@mui/material';
import { MRT_EditActionButtons, MRT_RowData, MRT_TableInstance, type MRT_Row } from 'material-react-table';
import { Sidebar } from 'primereact/sidebar';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';
import { PickList, PickListChangeEvent } from 'primereact/picklist';

import { Calendar } from 'primereact/calendar';
import React from 'react';
import { But<PERSON> } from 'primereact/button';
import fr from "@/utilities/service/fr";
import { addLocale, locale } from 'primereact/api';
import { ToggleButton, ToggleButtonChangeEvent } from 'primereact/togglebutton';
import parse from 'html-react-parser';
import ActionItem from './ActionWidget';
import { DataTable, DataTableValueArray } from 'primereact/datatable';
import { Column, ColumnEditorOptions, ColumnEvent } from 'primereact/column';
import { Tag } from 'primereact/tag';
import { ProgressBar } from 'primereact/progressbar';
import sanitizeHtml from 'sanitize-html';
import { Slider } from 'primereact/slider';
import { Action, CriStructview, Recommendation } from '@/services/schemas';
import { getCookie } from 'cookies-next';
import { InputNumber, InputNumberChangeEvent } from 'primereact/inputnumber';
import { $Action, $PriorityEnum, $StatusEnum } from '@/services/openapi_client';

interface DropdownItem {
    name: string;
    code: string | number;
}

export default function RecommendationEditForm<Recommendation extends MRT_RowData> (props: {
    internalEditComponents: ReactNode[]; row: MRT_Row<Recommendation>; table: MRT_TableInstance<Recommendation>;
}) : ReactNode  {
    addLocale('fr', fr);
    locale('fr');
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    // columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} body={(data) => <><Button icon='pi pi-paperclip' onClick={attachementProofClick}></Button><InputText onChange={attachementProofChanged} type='file' hidden ref={attachementProofRef} /></>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
    const attachementProofChanged = (event: React.ChangeEventHandler<HTMLInputElement>) => {
        console.log("Plan d'actions | PREUVES", event)
    }
    function attachementProofClick(event: MouseEvent<HTMLButtonElement, MouseEvent>): void {
        console.log('click')
        attachementProofRef.current.click()
    }
    const attachementProofRef = useRef<any>(null)
    const recommandationNew = {
        "mission": 0,
        "concerned_structure": 0,
        "causes": [],
        // "actions": [],
        "recommendation": "",
        "priority": "FAIBLE",
        "validated": true,
        "status": "Réalisée",
        "accepted": true,
        "responsible": ""
    };
    const steps = ['Recommendation', 'Actions'];
    const dataTableRef = useRef<DataTable<any>>()
    const [activeStep, setActiveStep] = React.useState(0);
    const [skipped, setSkipped] = React.useState(new Set<number>());

    // const { data: recommendationsrefs, isLoading, error } = useApiRecommendationRefList();
    const { data: causes, isLoading: causes_isLoading, error: causes_error } = useApiCauseList({}, { axios: { headers: { Authorization: `Token ${user?.token}` } } });
    const { data: concerned_structures, isLoading: concerned_structures_isLoading, error: concerned_structures_error } = useApiStructurelqsList({}, { axios: { headers: { Authorization: `Token ${user?.token}` } } })
    const { data: users, isLoading: users_isLoading, error: users_error } = useApiUsersList({}, { axios: { headers: { Authorization: `Token ${user?.token}` } } })
    const { data: missions, isLoading: missions_isLoading, error: missions_error } = useApiMissionList({}, { axios: { headers: { Authorization: `Token ${user?.token}` } } })
    const [picklistSourceValueCauses, setPicklistSourceValueCauses] = useState(props.row.id === "mrt-row-create" ? causes?.data.results : causes?.data.results?.filter((val, idx) => !props.row.original.causes.map((val_) => val_.id).includes(val.id)));
    const [picklistTargetValueCauses, setPicklistTargetValueCauses] = useState(props.row.original.causes ? props.row.original.causes : []);
    const [recommandationActions, setRecommandationActions] = useState<Action[]>(props.row.original.actions || []);
    const [actionsItems, setActionsIems] = useState<ReactElement[]>([<ActionItem />, <ActionItem />]);
    const [editVisible, setEditVisible] = useState(true);
    const [itemAccepted, setItemAccepted] = useState<boolean>(props.row.original.accepted || false);
    const [itemValidated, setItemValidated] = useState<boolean>(props.row.original.validated || false);
    const [dropdownItemEtat, setDropdownItemEtat] = useState<DropdownItem | null>({ "name": props.row.original.status || '', "code": props.row.original.status || '' });
    const [dropdownItemConcernedStructure, setDropdownItemConcernedStructure] = useState<DropdownItem | null>({ "name": props.row.original.concerned_structure.code_mnemonique  || props.row.original.concerned_structure.libell_stru || props.row.original.concerned_structure.code_stru, "code": `${props.row.original.concerned_structure.id}` });
    const [dropdownItemRecommendation, setDropdownItemRecommendation] = useState(props.row.id === "mrt-row-create" ? null : props.row.original.recommendation);
    const [dropdownItemResponsible, setDropdownItemResponsible] = useState(props.row.id === "mrt-row-create" ? null : props.row.original.responsible);
    const [dropdownItemMission, setDropdownItemMission] = useState<DropdownItem | null>(props.row.id === "mrt-row-create" ? null : { "name": props.row.original.mission || '', "code": props.row.original.mission });
    const [numRecommdantaionMission, setNumRecommdantaionMission] = useState(props.row.original.numrecommandation ?? -1);
    const [dropdownItemPriority, setDropdownItemPriority] = useState<DropdownItem | null>({ "name": props.row.original.priority || '', "code": props.row.original.priority || '' });

    useEffect(() => {
        setPicklistTargetValueCauses(props.row.original.causes)
        setPicklistSourceValueCauses(props.row.id === "mrt-row-create" ? causes?.data.results : causes?.data.results?.filter((val, idx) => !props.row.original.causes.map((val_) => val_.id).includes(val.id)));
        // setDropdownItemTheme({ "name": mission_theme?.theme.title || "", "code": mission_theme?.theme.code || "" });
        // setActions(props.row.original.action_plan?.actions || [])
        props.row._valuesCache = { ...props.row._valuesCache, causes: props.row.original.causes || [] }

    }, [causes]);

    useEffect(() => {
        console.log("useEffect", recommandationActions)
        props.row._valuesCache = { ...props.row._valuesCache, actions: recommandationActions }
        props.row._valuesCache._changedValues = {
            ...props.row._valuesCache._changedValues, actions: recommandationActions.map((action: Action) => {
                console.log(action)
                return {
                    "id": action.id,
                    "job_leader": action.job_leader,
                    "description": action.description,
                    "start_date": action.start_date,
                    "end_date": action.end_date,
                    "validated": action.validated,
                    "status": action.status,
                    "progress": action.progress,
                    "accepted": action.accepted,
                }
            }
            )
        }
    }, [recommandationActions]);

    useEffect(() => {
        // props.row._valuesCache = { ...props.row._valuesCache, ...recommandationNew }
    }, []);
    // useEffect(() => {
    //     console.log("######################editform############################", props.row._valuesCache)

    // }, [props.row._valuesCache]);

    const handleNumRecommendationChange = (e: InputNumberChangeEvent) => {
        console.log("handleNumRecommendationChange", e.value)

        setNumRecommdantaionMission(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ numrecommandation: e.value } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, numrecommandation: e.value }

    }
    const handleMissionChange = (e: DropdownChangeEvent) => {
        console.log("handleMissionChange", e.value)
        console.log(missions?.data.results.filter((mission) => mission.code === e.value.name))
        setDropdownItemMission(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ mission: missions?.data.results.find((mission) => mission.code === e.value.name)?.id } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, mission: missions?.data.results.find((mission) => mission.code === e.value.name)?.id }

    }
    const handleAcceptationChange = (e: ToggleButtonChangeEvent) => {
        console.log("handleAcceptationChange", e.value)
        setItemAccepted(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ accepted: e.value } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, accepted: e.value }

    }
    const handleValidationChange = (e: ToggleButtonChangeEvent) => {
        console.log("handleValidationChange", e.value)
        setItemValidated(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ validated: e.value } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, validated: e.value }

    }
    const handleEtatChange = (e: DropdownChangeEvent) => {
        console.log("handleEtatChange", e.value)

        setDropdownItemEtat(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ status: e.value.name } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, status: e.value.name }

    }
    const handlePriorityChange = (e: DropdownChangeEvent) => {
        console.log("handlePriorityChange", e.value)

        setDropdownItemPriority(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ priority: e.value.name } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, priority: e.value.name }

    }
    const handleConcernedStructureChange = (e: DropdownChangeEvent) => {
        console.log("handleConcernedStructureChange", e.value)

        setDropdownItemConcernedStructure(e.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ concerned_structure: e.value.code } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, concerned_structure: e.value.code }

    }
    const handleRecommendationRefChange = (e) => {
        console.log("handleRecommendationRefChange", e)
        setDropdownItemRecommendation(e.target.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ recommendation: e.target.value } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, recommendation: e.target.value }

    }
    const handleResponsibleChange = (e) => {
        console.log("handleRecommendationRefChange", e)
        setDropdownItemResponsible(e.target.value)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ responsible: e.target.value } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, responsible: e.target.value }
    }
    const handleCauses = (e: PickListChangeEvent) => {
        console.info('handleCauses')
        setPicklistSourceValueCauses(e.source);
        console.log('source Causes', e.source)
        setPicklistTargetValueCauses(e.target);
        console.log('target Causes', e.target)
        props.row._valuesCache = { ...props.row._valuesCache, ...{ causes: e.target.map((cause) => { return cause.id }) } }
        props.row._valuesCache._changedValues = { ...props.row._valuesCache._changedValues, causes: e.target.map((cause) => { return cause.id }) }
    }
    // const cellEditor = (options) => {
    //     if (options.field === 'price') return priceEditor(options);
    //     else return textEditor(options);
    // };
    const insertAction = () => {
        const newaction = {
            // "id": recommandationActions.length,
            "job_leader": {
                "username": "",
                "first_name": "job",
                "last_name": "leader",
                "email": ""
            },
            "description": "Définir une nouvelle tâche",
            "start_date": new Date().toISOString().split('T')[0],
            "end_date": new Date().toISOString().split('T')[0],
            "validated": false,
            "status": "En cours",
            "progress": 0,
            "accepted": false,
            "dependencies": [],
            "proof": null
        }
        const recommandationActions_ = [...recommandationActions]
        recommandationActions_.push(newaction)
        setRecommandationActions(recommandationActions_)


    }
    const textEditor = (options: ColumnEditorOptions) => {
        console.log("##################################", options)
        return <InputText type="text" value={options.value} onChange={(e) => options.editorCallback(e.target.value)} onKeyDown={(e) => e.stopPropagation()} />;
    };
    const dateEditor = (options: ColumnEditorOptions) => {
        return <Calendar value={options.value} onChange={(e) => options.editorCallback(e.value)} />;
    };
    const statusEditor = (options: ColumnEditorOptions) => {
        return <Dropdown value={options.value} onChange={(e) => options.editorCallback(e.value)} options={['En cours', 'Réalisée', 'Non Réalisée']} />;
    };
    const jobLeaderEditor = (options: ColumnEditorOptions) => {
        return <Dropdown onAbort={(e) => console.log("aborting", e)} filter showFilterClear optionLabel='name' value={{ name: `${options.value?.last_name} ${options.value?.first_name}`, code: `${options.value?.id}` }} onChange={(e) => { console.log("aborting", e); options.editorCallback(e.value) }} options={users?.data.results.map((user) => { return { name: `${user.last_name} ${user.first_name}`, code: `${user.id}` } })} />;
    };
    const progressEditor = (options: ColumnEditorOptions) => {
        return <><InputText value={options.value} onChange={(e) => options.editorCallback(e.target.value)} /><Slider value={options.value} onChange={(e) => options.editorCallback(e.value)} /></>;
    };
    const boolEditor = (options: ColumnEditorOptions) => {
        return <ToggleButton checked={options.value} onChange={(e) => options.editorCallback(e.value)} />;
    };
    const onCellEditComplete = (e: ColumnEvent) => {
        let { rowData, newValue, field, originalEvent: event } = e;
        if (field === 'description') {
            rowData[field] = newValue;
        }
        else if (field === 'job_leader') {
            if (newValue !== undefined) {
                if (newValue.id)
                    rowData.job_leader = users?.data.results.find((user) => user.id === parseInt(newValue?.id));
                if (newValue.code) rowData.job_leader = users?.data.results.find((user) => user.id === parseInt(newValue?.code));
            }
        }
        else if (['start_date', 'end_date'].includes(field) && newValue !== undefined) {
            rowData[field] = newValue instanceof Date ? (newValue as Date)?.toISOString().split('T')[0] : new Date(newValue).toISOString().split('T')[0];
        }
        else if (['validated', 'accepted'].includes(field)) {
            rowData[field] = newValue;
        }
        else if (['progress'].includes(field)) {
            rowData[field] = newValue;
        }
        else if (['status'].includes(field)) {
            rowData[field] = newValue;
        }
        else {
            rowData[field] = newValue;
        }
        console.log("settings actions", dataTableRef.current?.props.value!)
        const recommandationActions_ = [...dataTableRef.current?.props.value!]

        setRecommandationActions(recommandationActions_)

    };
    const generateColumns = () => {
        let columns = [];
        for (const [key, value] of Object.entries($Action.properties).filter(([key, value], index) => !['validated', 'accepted', 'created_by', 'dependencies', 'modified_by', 'created', 'modified', 'id'].includes(key))) {
            if (key === 'description') {
                columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} editor={(options) => textEditor(options)} body={(data) => parse(sanitizeHtml(data.description))} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (key === 'job_leader') {
                columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} editor={(options) => jobLeaderEditor(options)} body={(data) => `${data.job_leader?.last_name} ${data.job_leader?.first_name}`} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (['start_date', 'end_date'].includes(key)) {
                columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} editor={(options) => dateEditor(options)} body={(data) => new Date(data[key]).toLocaleDateString()} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (['validated', 'accepted'].includes(key)) {
                columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} editor={(options) => boolEditor(options)} body={(data) => data[key] ? <i className="pi pi-check-circle" style={{ color: 'green' }}></i> : <i className="pi pi-times-circle" style={{ color: 'red' }}></i>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (['progress'].includes(key)) {
                columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} editor={(options) => progressEditor(options)} body={(data) => <ProgressBar value={data[key]}></ProgressBar>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (['status'].includes(key)) {
                columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} editor={(options) => statusEditor(options)} body={(data) => <Tag value={data[key]}></Tag>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }
            else if (['proof'].includes(key)) {
                columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} body={(data) => <><Button icon='pi pi-paperclip' onClick={attachementProofClick}></Button><InputText onChange={attachementProofChanged} type='file' hidden ref={attachementProofRef} /></>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
            }

        }
        columns.push(<Column header={'Action'} sortableDisabled={true} field={"action"} body={(options) => <Button icon='pi pi-trash' onClick={(e) => { console.log(options); setRecommandationActions(dataTableRef.current?.props.value?.filter((act) => act.id != options.id)); }} />} sortable style={{ width: '35%' }} />)
        return columns;
    }

    ///////////////////////////////////////////////////////////////////////////////    
    const isStepOptional = (step: number) => {
        return step === 1;
    };

    const isStepSkipped = (step: number) => {
        return skipped.has(step);
    };

    console.log(dataTableRef.current?.props.value)
    console.log(recommandationActions)
    return (
        <>
            <div style={{ zIndex: '1302 !important' }}>
                <Sidebar position='right' header={
                    <div className='flex flex-row w-full flex-wrap justify-content-between'>
                        <Typography variant="h4" className='align-content-center '>{props.row.id === 'mrt-row-create' ? `Nouvelle recommandation` : props.row._valuesCache.actions_add ? `Saisie des actions pour la recommandation N° ${props.row.original.id}` : `Editer Recommandation N° ${props.row.original.id}`}</Typography>
                        <DialogActions>
                            <MRT_EditActionButtons variant="text" table={props.table} row={props.row} />
                        </DialogActions>
                    </div>} visible={editVisible} onHide={() => { props.table.setEditingRow(null); setEditVisible(false) }} className="w-full md:w-9 lg:w-8">
                    <DialogContent
                        sx={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}
                    >
                        <div className="col-12">
                            <React.Fragment>
                                {!props.row._valuesCache.actions_add && (
                                    <div className="card">
                                        <div className="p-fluid formgrid grid">

                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="numrecommandation">N° Recommandation</label>
                                                <InputNumber className={props.row._valuesCache.error?.data?.["numrecommandation"] ? 'p-invalid' : ''} id="numrecommandation" value={numRecommdantaionMission} onChange={(e) => handleNumRecommendationChange(e)} placeholder="Saisir le numéro de la recommandation" />
                                                {props.row._valuesCache.error?.data?.["numrecommandation"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["numrecommandation"][0]}</small>}
                                                <small>Automatiquement incrémentable</small>
                                            </div>
                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="mission">Mission</label>
                                                <Dropdown filter className={props.row._valuesCache.error?.data?.["mission"] ? 'p-invalid' : ''} id="mission" value={dropdownItemMission} onChange={(e) => handleMissionChange(e)} options={missions?.data.results.map(function (val) { return { "name": val.code, "code": val.code } })} optionLabel="name" placeholder="Choisir une mission"></Dropdown>
                                                {props.row._valuesCache.error?.data?.["mission"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["mission"][0]}</small>}
                                            </div>
                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="concerned_structure">Structure Concernée</label>
                                                <Dropdown className={props.row._valuesCache.error?.data?.["concerned_structure"] ? 'p-invalid' : ''} filter id="concerned_structure" value={dropdownItemConcernedStructure} onChange={(e) => handleConcernedStructureChange(e)} options={concerned_structures?.data.results.map(function (val: CriStructview) { return { "name": val.code_mnemonique || val.libell_stru || val.code_stru, "code": `${val.id}` } })} optionLabel="name" placeholder="Choisir une structure"></Dropdown>
                                                {props.row._valuesCache.error?.data?.["concerned_structure"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["concerned_structure"][0]}</small>}
                                            </div>
                                            <div className="field col-6">
                                                <label htmlFor="responsible">Responsable</label>
                                                <InputText className={props.row._valuesCache.error?.data?.["responsible"] ? 'p-invalid' : ''} id="responsible" value={dropdownItemResponsible} onChange={(e) => handleResponsibleChange(e)} placeholder="Saisir le responsable"></InputText>
                                                {props.row._valuesCache.error?.data?.["responsible"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["responsible"][0]}</small>}

                                            </div>
                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="status">Statut</label>
                                                <Dropdown className={props.row._valuesCache.error?.data?.["status"] ? 'p-invalid' : ''} filter id="status" value={dropdownItemEtat} onChange={(e) => handleEtatChange(e)} options={$StatusEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir un statut"></Dropdown>
                                                {props.row._valuesCache.error?.data?.["status"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["status"][0]}</small>}
                                            </div>
                                            <div className="field col-12 md:col-6">
                                                <label htmlFor="priority">Priorité</label>
                                                <Dropdown className={props.row._valuesCache.error?.data?.["priority"] ? 'p-invalid' : ''} filter id="priority" value={dropdownItemPriority} onChange={(e) => handlePriorityChange(e)} options={$PriorityEnum.enum.map(function (val) { return { "name": val, "code": val } })} optionLabel="name" placeholder="Choisir une priorité"></Dropdown>
                                                {props.row._valuesCache.error?.data?.["priority"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["priority"][0]}</small>}
                                            </div>
                                            <div className="field col-12">
                                                <label htmlFor="recommendation">Recommendation</label>
                                                <InputTextarea className={props.row._valuesCache.error?.data?.["recommendation"] ? 'p-invalid' : ''} id="recommendation" value={dropdownItemRecommendation} onChange={(e) => handleRecommendationRefChange(e)} placeholder="Saisir la recommendation"></InputTextarea>
                                                {props.row._valuesCache.error?.data?.["recommendation"] && <small className='p-error'>{props.row._valuesCache.error?.data?.["recommendation"][0]}</small>}
                                            </div>

                                            <div className="field col-12">
                                                <label htmlFor="picklist_causes">Causes</label>
                                                <PickList
                                                    id='picklist_causes'
                                                    source={picklistSourceValueCauses}
                                                    target={picklistTargetValueCauses}
                                                    sourceHeader="Disponibles"
                                                    targetHeader="Séléctionnés"
                                                    itemTemplate={(item) => <div>{item.content}</div>}
                                                    onChange={(e) => {
                                                        handleCauses(e)
                                                    }}
                                                    sourceStyle={{ height: '200px' }}
                                                    targetStyle={{ height: '200px' }}
                                                    filter filterBy='content'
                                                    filterMatchMode='contains'
                                                    sourceFilterPlaceholder="Recherche" targetFilterPlaceholder="Recherche"
                                                >
                                                </PickList>

                                            </div>

                                        </div>
                                    </div>
                                )}
                                {props.row._valuesCache.actions_add && (
                                    <div className="card">
                                        <Button label='Ajouter une action' icon='pi pi-plus-circle' onClick={() => insertAction()}></Button>
                                        <DataTable<Action[]> ref={dataTableRef} editMode="cell" style={{ width: '100%' }} value={recommandationActions} rows={5} paginator responsiveLayout="scroll">
                                            {generateColumns()}
                                        </DataTable>
                                        {/* {actionsItems?.map((val,index)=><React.Fragment key={index}> {val}</React.Fragment>)} */}
                                    </div>
                                )}

                            </React.Fragment>
                        </div>
                    </DialogContent>
                </Sidebar>
            </div >
        </>);
};



