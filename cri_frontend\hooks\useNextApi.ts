// React hooks for Next.js API to replace Django API hooks
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import nextApiService from '@/services/api/nextApi'

// Mission hooks
export function useApiMissionList(params?: {
  page?: number;
  limit?: number;
  search?: string
}) {
  return useQuery({
    queryKey: ['missions', params],
    queryFn: () => nextApiService.getMissions(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useApiMissionRetrieve(id: number) {
  return useQuery({
    queryKey: ['missions', id],
    queryFn: () => nextApiService.getMission(id),
    enabled: !!id,
  })
}

export function useApiMissionCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => nextApiService.createMission(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['missions'] })
    },
  })
}

export function useApiMissionPartialUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updateMission(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['missions'] })
      queryClient.invalidateQueries({ queryKey: ['missions', variables.id] })
    },
  })
}

export function useApiMissionDestroy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => nextApiService.deleteMission(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['missions'] })
    },
  })
}

// Recommendation hooks
export function useApiRecommendationList(params?: {
  page?: number;
  limit?: number;
  search?: string;
  missionId?: number
}) {
  return useQuery({
    queryKey: ['recommendations', params],
    queryFn: () => nextApiService.getRecommendations(params),
    staleTime: 5 * 60 * 1000,
  })
}

export function useApiRecommendationRetrieve(id: number) {
  return useQuery({
    queryKey: ['recommendations', id],
    queryFn: () => nextApiService.getRecommendation(id),
    enabled: !!id,
  })
}

export function useApiRecommendationCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => nextApiService.createRecommendation(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
      queryClient.invalidateQueries({ queryKey: ['missions'] })
    },
  })
}

export function useApiRecommendationPartialUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updateRecommendation(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
      queryClient.invalidateQueries({ queryKey: ['recommendations', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['missions'] })
    },
  })
}

export function useApiRecommendationDestroy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => nextApiService.deleteRecommendation(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
      queryClient.invalidateQueries({ queryKey: ['missions'] })
    },
  })
}

// User hooks
export function useApiUserList(params?: {
  page?: number;
  limit?: number;
  search?: string
}) {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => nextApiService.getUsers(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useApiUserRetrieve(id: number) {
  return useQuery({
    queryKey: ['users', id],
    queryFn: () => nextApiService.getUser(id),
    enabled: !!id,
  })
}

export function useApiUserCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => nextApiService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}

// Plan hooks
export function useApiPlanList(params?: {
  page?: number;
  limit?: number;
  exercise?: number;
  type?: string
}) {
  return useQuery({
    queryKey: ['plans', params],
    queryFn: () => nextApiService.getPlans(params),
    staleTime: 10 * 60 * 1000,
  })
}

export function useApiPlanRetrieve(id: number) {
  return useQuery({
    queryKey: ['plans', id],
    queryFn: () => nextApiService.getPlan(id),
    enabled: !!id,
  })
}

export function useApiPlanCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => nextApiService.createPlan(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] })
    },
  })
}

export function useApiPlanUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updatePlan(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['plans'] })
      queryClient.invalidateQueries({ queryKey: ['plans', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['arbitrations'] })
    },
  })
}

export function useApiPlanDestroy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => nextApiService.deletePlan(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plans'] })
      queryClient.invalidateQueries({ queryKey: ['arbitrations'] })
    },
  })
}

// Theme hooks
export function useApiThemeList(params?: {
  page?: number;
  limit?: number;
  search?: string;
  validated?: boolean;
  domainId?: number;
}) {
  return useQuery({
    queryKey: ['themes', params],
    queryFn: () => nextApiService.getThemes(params),
    staleTime: 5 * 60 * 1000,
  })
}

export function useApiThemeRetrieve(id: number) {
  return useQuery({
    queryKey: ['themes', id],
    queryFn: () => nextApiService.getTheme(id),
    enabled: !!id,
  })
}

export function useApiThemeCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => nextApiService.createTheme(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['themes'] })
    },
  })
}

export function useApiThemePartialUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updateTheme(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['themes'] })
      queryClient.invalidateQueries({ queryKey: ['themes', variables.id] })
    },
  })
}

// Arbitration hooks
export function useApiArbitrationList(params?: {
  page?: number;
  limit?: number;
  search?: string;
  planId?: number;
}) {
  return useQuery({
    queryKey: ['arbitrations', params],
    queryFn: () => nextApiService.getArbitrations(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useApiArbitrationRetrieve(id: number) {
  return useQuery({
    queryKey: ['arbitrations', id],
    queryFn: () => nextApiService.getArbitration(id),
    enabled: !!id,
  })
}

export function useApiArbitrationCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => nextApiService.createArbitration(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['arbitrations'] })
      queryClient.invalidateQueries({ queryKey: ['plans'] })
    },
  })
}

export function useApiArbitrationPartialUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updateArbitration(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['arbitrations'] })
      queryClient.invalidateQueries({ queryKey: ['arbitrations', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['plans'] })
    },
  })
}

export function useApiArbitrationDestroy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => nextApiService.deleteArbitration(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['arbitrations'] })
      queryClient.invalidateQueries({ queryKey: ['plans'] })
    },
  })
}

// Comment hooks
export function useApiCommentList(params?: {
  page?: number;
  limit?: number;
  search?: string;
  recommendationId?: number;
  missionId?: number;
  userId?: number;
}) {
  return useQuery({
    queryKey: ['comments', params],
    queryFn: () => nextApiService.getComments(params),
    staleTime: 2 * 60 * 1000, // 2 minutes (comments change frequently)
  })
}

export function useApiCommentRetrieve(id: number) {
  return useQuery({
    queryKey: ['comments', id],
    queryFn: () => nextApiService.getComment(id),
    enabled: !!id,
  })
}

export function useApiCommentCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => nextApiService.createComment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['comments'] })
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
      queryClient.invalidateQueries({ queryKey: ['missions'] })
    },
  })
}

export function useApiCommentPartialUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updateComment(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['comments'] })
      queryClient.invalidateQueries({ queryKey: ['comments', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
      queryClient.invalidateQueries({ queryKey: ['missions'] })
    },
  })
}

export function useApiCommentDestroy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => nextApiService.deleteComment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['comments'] })
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
      queryClient.invalidateQueries({ queryKey: ['missions'] })
    },
  })
}

// Document hooks
export function useApiDocsCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: FormData) => nextApiService.uploadDocument(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['missions'] })
      queryClient.invalidateQueries({ queryKey: ['documents'] })
    },
  })
}

export function useApiMissionDocsCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ missionId, data }: { missionId: number; data: FormData }) =>
      nextApiService.uploadMissionDocuments(missionId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['missions'] })
      queryClient.invalidateQueries({ queryKey: ['missions', variables.missionId] })
      queryClient.invalidateQueries({ queryKey: ['documents'] })
      queryClient.invalidateQueries({ queryKey: ['mission-documents', variables.missionId] })
    },
  })
}

export function useApiMissionDocumentsList(missionId: number) {
  return useQuery({
    queryKey: ['mission-documents', missionId],
    queryFn: () => nextApiService.getMissionDocuments(missionId),
    enabled: !!missionId,
  })
}

export function useApiDocumentsList(params?: { missionId?: number; context?: string }) {
  return useQuery({
    queryKey: ['documents', params],
    queryFn: () => nextApiService.getDocuments(params),
    staleTime: 5 * 60 * 1000,
  })
}

export function useApiDocsDestroy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => nextApiService.deleteDocument(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['missions'] })
      queryClient.invalidateQueries({ queryKey: ['documents'] })
      queryClient.invalidateQueries({ queryKey: ['mission-documents'] })
    },
  })
}

export function useApiDocsUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updateDocument(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['documents'] })
      queryClient.invalidateQueries({ queryKey: ['documents', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['mission-documents'] })
    },
  })
}

// Action hooks
export function useApiActionList(params?: {
  page?: number;
  limit?: number;
  search?: string;
  recommendationId?: number;
  status?: string;
}) {
  return useQuery({
    queryKey: ['actions', params],
    queryFn: () => nextApiService.getActions(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useApiActionRetrieve(id: number) {
  return useQuery({
    queryKey: ['actions', id],
    queryFn: () => nextApiService.getAction(id),
    enabled: !!id,
  })
}

export function useApiActionCreate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => nextApiService.createAction(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['actions'] })
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
    },
  })
}

export function useApiActionUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updateAction(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['actions'] })
      queryClient.invalidateQueries({ queryKey: ['actions', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
    },
  })
}

export function useApiActionPartialUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      nextApiService.updateAction(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['actions'] })
      queryClient.invalidateQueries({ queryKey: ['actions', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
    },
  })
}

export function useApiActionDestroy() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => nextApiService.deleteAction(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['actions'] })
      queryClient.invalidateQueries({ queryKey: ['recommendations'] })
    },
  })
}
