"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/page",{

/***/ "(app-client)/./app/(main)/page.tsx":
/*!*****************************!*\
  !*** ./app/(main)/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_chart__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/chart */ \"(app-client)/./node_modules/primereact/chart/chart.esm.js\");\n/* harmony import */ var primereact_column__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/column */ \"(app-client)/./node_modules/primereact/column/column.esm.js\");\n/* harmony import */ var primereact_datatable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/datatable */ \"(app-client)/./node_modules/primereact/datatable/datatable.esm.js\");\n/* harmony import */ var primereact_menu__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/menu */ \"(app-client)/./node_modules/primereact/menu/menu.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../layout/context/layoutcontext */ \"(app-client)/./layout/context/layoutcontext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-client)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utilities_service_ProductService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utilities/service/ProductService */ \"(app-client)/./utilities/service/ProductService.tsx\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* eslint-disable @next/next/no-img-element */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst lineData = {\n    labels: [\n        \"January\",\n        \"February\",\n        \"March\",\n        \"April\",\n        \"May\",\n        \"June\",\n        \"July\"\n    ],\n    datasets: [\n        {\n            label: \"First Dataset\",\n            data: [\n                65,\n                59,\n                80,\n                81,\n                56,\n                55,\n                40\n            ],\n            fill: false,\n            backgroundColor: \"#2f4860\",\n            borderColor: \"#2f4860\",\n            tension: 0.4\n        },\n        {\n            label: \"Second Dataset\",\n            data: [\n                28,\n                48,\n                40,\n                19,\n                86,\n                27,\n                90\n            ],\n            fill: false,\n            backgroundColor: \"#00bb7e\",\n            borderColor: \"#00bb7e\",\n            tension: 0.4\n        }\n    ]\n};\nconst Dashboard = ()=>{\n    var _recommendations_data_data, _recommendations_data, _recommendations, _actions_data_data, _actions_data, _actions, _recommendations_data1, _recommendations_data2, _recommendations_data3, _recommendations1;\n    _s();\n    // Fetch data using specific hooks\n    const { data: recommendations, isLoading: recommendationsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiRecommendationList)({\n        limit: 10\n    });\n    const { data: plans, isLoading: plansLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiPlanList)({\n        limit: 5\n    });\n    const { data: missions, isLoading: missionsLoading } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiMissionList)({\n        limit: 5\n    });\n    console.log(\"[Dashboard]\", recommendations);\n    const menu1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const menu2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [lineOptions, setLineOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { layoutConfig } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_layout_context_layoutcontext__WEBPACK_IMPORTED_MODULE_2__.LayoutContext);\n    const applyLightTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#495057\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#495057\"\n                    },\n                    grid: {\n                        color: \"#ebedef\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    const applyDarkTheme = ()=>{\n        const lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: \"#ebedef\"\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: \"#ebedef\"\n                    },\n                    grid: {\n                        color: \"rgba(160, 167, 181, .3)\"\n                    }\n                }\n            }\n        };\n        setLineOptions(lineOptions);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        _utilities_service_ProductService__WEBPACK_IMPORTED_MODULE_4__.ProductService.getProductsSmall().then((data)=>setProducts(data));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (layoutConfig.colorScheme === \"light\") {\n            applyLightTheme();\n        } else {\n            applyDarkTheme();\n        }\n    }, [\n        layoutConfig.colorScheme\n    ]);\n    const formatCurrency = (value)=>{\n        var _value;\n        return (_value = value) === null || _value === void 0 ? void 0 : _value.toLocaleString(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Missions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: Array.isArray(missions) ? missions.length : 0\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-blue-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-briefcase text-blue-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: (_recommendations = recommendations) === null || _recommendations === void 0 ? void 0 : (_recommendations_data = _recommendations.data) === null || _recommendations_data === void 0 ? void 0 : (_recommendations_data_data = _recommendations_data.data) === null || _recommendations_data_data === void 0 ? void 0 : _recommendations_data_data.count\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-orange-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-thumbs-up-fill text-orange-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: (_actions = actions) === null || _actions === void 0 ? void 0 : (_actions_data = _actions.data) === null || _actions_data === void 0 ? void 0 : (_actions_data_data = _actions_data.data) === null || _actions_data_data === void 0 ? void 0 : _actions_data_data.count\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-cyan-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-cog text-cyan-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 lg:col-6 xl:col-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card mb-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-content-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"block text-500 font-medium mb-3\",\n                                        children: \"Comments\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 font-medium text-xl\",\n                                        children: \"152 Unread\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-center bg-purple-100 border-round\",\n                                style: {\n                                    width: \"2.5rem\",\n                                    height: \"2.5rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"pi pi-comment text-purple-500 text-xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Commentaires\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 17\n                            }, undefined),\n                            ((_recommendations_data1 = recommendations.data) === null || _recommendations_data1 === void 0 ? void 0 : _recommendations_data1.isLoading) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_6__.ProgressSpinner, {\n                                style: {\n                                    width: \"50px\",\n                                    height: \"50px\"\n                                },\n                                strokeWidth: \"8\",\n                                fill: \"var(--surface-ground)\",\n                                animationDuration: \".5s\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 53\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                                stripedRows: true,\n                                value: (_recommendations_data2 = recommendations.data) === null || _recommendations_data2 === void 0 ? void 0 : _recommendations_data2.flatMap((rec)=>rec.comments),\n                                rows: 10,\n                                scrollable: true,\n                                paginator: true,\n                                resizableColumns: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"left\",\n                                        field: \"recommendation\",\n                                        body: (data)=>\"\".concat(recommendations.data.data.results.find((rec)=>rec.id === data.recommendation).mission, \" * \").concat(data.recommendation),\n                                        header: \"Recommendation\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"20%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"left\",\n                                        field: \"comment\",\n                                        body: (data)=>data.comment,\n                                        header: \"Commentaire\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"70%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"center\",\n                                        field: \"created\",\n                                        body: (data)=>new Date(data.created).toLocaleString(\"fr\"),\n                                        header: \"Date\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"12%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        align: \"center\",\n                                        field: \"created_by\",\n                                        body: (data)=>{\n                                            var _data_created_by, _data_created_by1;\n                                            return data.created_by ? \"\".concat((_data_created_by = data.created_by) === null || _data_created_by === void 0 ? void 0 : _data_created_by.last_name, \" \").concat((_data_created_by1 = data.created_by) === null || _data_created_by1 === void 0 ? void 0 : _data_created_by1.first_name) : \"\";\n                                        },\n                                        header: \"Utilisateur\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"12%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_8__.Column, {\n                                        rowEditor: true,\n                                        header: \"Action\",\n                                        sortableDisabled: true,\n                                        field: \"action\",\n                                        sortable: true,\n                                        style: {\n                                            width: \"6%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-content-between align-items-center mb-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Plans d'actions\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu1_current;\n                                                    return (_menu1_current = menu1.current) === null || _menu1_current === void 0 ? void 0 : _menu1_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_10__.Menu, {\n                                                ref: menu1,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-none p-0 m-0\",\n                                children: (_recommendations1 = recommendations) === null || _recommendations1 === void 0 ? void 0 : (_recommendations_data3 = _recommendations1.data) === null || _recommendations_data3 === void 0 ? void 0 : _recommendations_data3.map((rec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-900 font-medium mr-2 mb-1 md:mb-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/recommendations/\" + rec.id,\n                                                                children: [\n                                                                    \"Recommandation n\\xb0 \",\n                                                                    rec.numrecommandation\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 90\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 text-600\",\n                                                            children: rec.mission\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 md:mt-0 flex align-items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\",\n                                                            style: {\n                                                                height: \"8px\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-orange-500 h-full\",\n                                                                style: {\n                                                                    width: \"\".concat(rec.actions.reduce((accumulator, act)=>accumulator += act.progress || 0, 0), \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 37\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-500 ml-3 font-medium\",\n                                                            children: [\n                                                                \"%\",\n                                                                (rec.actions.reduce((accumulator, act)=>accumulator += act.progress || 0, 0) / rec.actions.length || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-12 xl:col-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                children: \"Sales Overview\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chart__WEBPACK_IMPORTED_MODULE_11__.Chart, {\n                                type: \"line\",\n                                data: lineData,\n                                options: lineOptions\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex align-items-center justify-content-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                type: \"button\",\n                                                icon: \"pi pi-ellipsis-v\",\n                                                rounded: true,\n                                                text: true,\n                                                className: \"p-button-plain\",\n                                                onClick: (event)=>{\n                                                    var _menu2_current;\n                                                    return (_menu2_current = menu2.current) === null || _menu2_current === void 0 ? void 0 : _menu2_current.toggle(event);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_menu__WEBPACK_IMPORTED_MODULE_10__.Menu, {\n                                                ref: menu2,\n                                                popup: true,\n                                                model: [\n                                                    {\n                                                        label: \"Add New\",\n                                                        icon: \"pi pi-fw pi-plus\"\n                                                    },\n                                                    {\n                                                        label: \"Remove\",\n                                                        icon: \"pi pi-fw pi-minus\"\n                                                    }\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"TODAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 mx-0 mt-0 mb-4 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Richard Jones\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a blue t-shirt for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"79$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-orange-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-download text-xl text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-700 line-height-3\",\n                                                children: [\n                                                    \"Your request for withdrawal of \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-500 font-medium\",\n                                                        children: \"2500$\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 60\n                                                    }, undefined),\n                                                    \" has been initiated.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-600 font-medium mb-3\",\n                                children: \"YESTERDAY\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"p-0 m-0 list-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-dollar text-xl text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Keyser Wick\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: [\n                                                            \" \",\n                                                            \"has purchased a black jacket for \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-500\",\n                                                                children: \"59$\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex align-items-center py-2 border-bottom-1 surface-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3rem h-3rem flex align-items-center justify-content-center bg-pink-100 border-circle mr-3 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"pi pi-question text-xl text-pink-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-900 line-height-3\",\n                                                children: [\n                                                    \"Jane Davis\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-700\",\n                                                        children: \" has posted a new questions about your product.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-5 shadow-2 flex flex-column md:flex-row md:align-items-center justify-content-between mb-3\",\n                        style: {\n                            borderRadius: \"1rem\",\n                            background: \"linear-gradient(0deg, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.5)), linear-gradient(92.54deg, #1C80CF 47.88%, #FFFFFF 100.01%)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100 font-medium text-xl mt-2 mb-3\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white font-medium text-5xl\",\n                                        children: \"TEST\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 mr-auto md:mt-0 md:mr-0\",\n                                children: \"TEST\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\",\n        lineNumber: 137,\n        columnNumber: 13\n    }, undefined);\n};\n_s(Dashboard, \"dIK6/XOP/w75Cc1eGiU9nkJpz0A=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiRecommendationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiMissionList\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/page.tsx\n"));

/***/ })

});