FROM python:3.12-alpine

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN pip config set global.index http://orndevrepos.corp.sonatrach.dz/repository/python/pypi
RUN pip config set global.index-url http://orndevrepos.corp.sonatrach.dz/repository/python/simple
RUN pip config set global.trusted-host orndevrepos.corp.sonatrach.dz

# Allows docker to cache installed dependencies between builds
COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Mounts the application code to the image
COPY . /app

EXPOSE 3001

RUN python manage.py migrate

CMD ["gunicorn", "--config", "gunicorn_config.py", "app.wsgi:application"]