'use client'
import { Box, Chip, Stack } from '@mui/material';
import { Editor } from '@tinymce/tinymce-react';
import parse from 'html-react-parser';
import {
  MRT_RowData,
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef
} from 'material-react-table';
import { MRT_Localization_FR } from 'material-react-table/locales/fr';
import { useRouter } from 'next/navigation';
import { Button } from 'primereact/button';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { Dialog } from 'primereact/dialog';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useEffect, useMemo, useRef, useState } from 'react';

import RecommendationEditForm from './editForm';
import RecommendationDetails from '../followup/[recommendation_id]/page';
import { getCookie } from 'cookies-next';
import { options } from 'sanitize-html';
import { DataTable, DataTableRowClickEvent } from 'primereact/datatable';
import { Action, CriStructview, Recommendation, CommentRead, ActionWriteRequest, RecommendationWriteRequest } from '@/services/schemas';
import { Column } from 'primereact/column';
import { ProgressBar } from 'primereact/progressbar';
import dynamic from 'next/dynamic';
import { OverlayPanel } from 'primereact/overlaypanel';
import { InputTextarea } from 'primereact/inputtextarea';
import CommentActionDialog from './CommentActionDialog';
import { Badge } from 'primereact/badge';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircleCheck, faCircleXmark, faRectangleXmark, faSquareCheck } from '@fortawesome/free-regular-svg-icons'
import React from 'react';
import { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';
import { ToggleButton, ToggleButtonChangeEvent } from 'primereact/togglebutton';
import { getPrioritySeverity, getStatusSeverity } from '@/utilities/functions/utils';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Can } from '@/app/Can';
import { useApiActionPartialUpdate, useApiCommentCreate, useApiRecommendationCreate, useApiRecommendationDestroy, useApiRecommendationPartialUpdate } from '@/hooks/useNextApi';
import { $Action } from '@/lib/schemas';

export default function GenericTableRecommendationFolluwUp<T extends MRT_RowData>(data_: { isLoading: any; data_: any, error: any, data_type: any | undefined, pagination: any }) {
  // const ReactJson = useMemo(() => dynamic(() => import('react-json-view'), { ssr: false }), []);

  const user = JSON.parse(getCookie('user')?.toString() || '{}')
  const toast = useRef<any>(null);

  const { push } = useRouter();
  const planActionDataTableRef = useRef<DataTable<Action[]>>(null);
  const overlayProofPanelRef = useRef<any>(null);
  const overlayValidationAcceptationPanelRef = useRef<any>(null);
  const [overlayValidationAcceptationTitle, setOverlayValidationAcceptationTitle] = useState('');
  const [recommendation_id, setRecommendationId] = useState(0);
  const [recommendation$, setRecommendation$] = useState<Recommendation | null>(null);
  const [action_comment, setActionComment] = useState('');
  const [action_comments, setActionComments] = useState([]);
  const [actionId, setActionId] = useState(0)
  const [visible, setVisible] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [rowActionEnabled, setRowActionEnabled] = useState(false);
  const [detailsDialogVisible, setDetailsDialogVisible] = useState(false);
  const [actionCommentDialogVisible, setActionCommentDialogVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [clickEvent, setClickEvent] = useState<ToggleButtonChangeEvent | null>(null);
  const open = Boolean(anchorEl);
  // Hooks
  const { data: comment_create_data, isPending: comment_create_isMutating, error: comment_create_error, mutate: trigger_comment_create } = useApiCommentCreate()
  const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = useApiRecommendationCreate()
  const { data: data_modify, error: error_modify, isPending: isMutating_modify, mutate: trigger_modify } = useApiRecommendationPartialUpdate()
  const { data: data_delete, error: error_delete, isPending: isMutating_delete, mutate: trigger_delete } = useApiRecommendationDestroy()
  const { data: data_action_update, error: error_action_update, isPending: isMutating_action_update, mutate: trigger_action_update } = useApiActionPartialUpdate()

  // Functions
  function onPaginationChange(state: any) {
    console.log("PAGINATION", data_.pagination);
    data_.pagination.set(state)
  };

  function updateActionAcceptation(e: ToggleButtonChangeEvent, actionId: number) {
    setActionId(actionId);
    // setClickEvent(e);
    if (!e.value) {
      setOverlayValidationAcceptationTitle(`Acceptation : ${actionId}`)
      overlayValidationAcceptationPanelRef.current.toggle(e.originalEvent);
    }
    else { trigger_action_update({ id: actionId, data: { accepted: true } }) }
  }
  function updateActionValidation(e: ToggleButtonChangeEvent, actionId: number) {
    setActionId(actionId);
    if (!e.value) {
      setOverlayValidationAcceptationTitle(`Validation : ${actionId}`)
      overlayValidationAcceptationPanelRef.current.toggle(e.originalEvent);
    }
    else {
      trigger_action_update({ id: actionId, data: { validated: true } })
    }
  }
  function attachementViewProofClick(event: MouseEvent<HTMLButtonElement, MouseEvent>): void {
    overlayProofPanelRef.current.toggle(event);
  }
  function addCommentClick(event: MouseEvent<HTMLButtonElement, MouseEvent>, action_id: number, recommendation_id_: number): void {
    setActionId(action_id)
    console.log(action_id, recommendation_id_)
    console.log(data_.data_.data.results)
    console.log(data_.data_.data.results.filter((recommendation: Recommendation) => recommendation.id === recommendation_id_))
    setActionComments(data_.data_.data.results.find((recommendation: Recommendation) => recommendation.id === recommendation_id_)?.actions.find((action: Action) => action.id === action_id)?.comments.sort((a, b) => new Date(b.created).getTime() - new Date(a.created).getTime()))
    if (!actionCommentDialogVisible) setActionCommentDialogVisible(true)
    // overlayProofPanelRef.current.toggle(event);
  }

  const accept = () => {
    trigger_delete(
      { id: actionId },
      {

        onSuccess: () => toast.current?.show({ severity: 'info', summary: 'Suppression', detail: 'Enregistrement supprimé' }),
        onError: (error) => toast.current?.show({ severity: 'info', summary: 'Suppression', detail: `${error.code}` }),
      }
    )
  };

  const reject = () => {
    toast.current.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
  };
  function onDocumentLoadSuccess({ numPages }) {
    setNumPages(numPages);
    setPageNumber(1);
  }
  function changePage(offset: number) {
    setPageNumber(prevPageNumber => prevPageNumber + offset);
  }
  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  useEffect(() => {
    console.log("recommendation_id", recommendation_id);
  }, [recommendation_id, actionId]);
  useEffect(() => {
    // updateActionAcceptation(clickEvent,actionId)
  }, [actionId]);

  const columns = useMemo<MRT_ColumnDef<T>[]>(
    () =>
      Object.entries(data_.data_type.properties).filter(([key, value], index) => !['modified_by', 'created_by', 'causes', 'comments', 'constats', 'actions'].includes(key)).map(([key, value], index) => {
        if (['report', 'content', 'note', 'order', 'comment', 'description'].includes(key)) {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Cell: ({ cell }) => { if (["description", "content", "report"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },

            Edit: ({ cell, column, row, table }) => {
              return <Editor
                onChange={(e) => { row._valuesCache.content = e.target.getContent() }}
                initialValue={row.original[key]}
                tinymceScriptSrc="http://localhost:3000/tinymce/tinymce.min.js"
                apiKey='none'
                init={{
                  height: 500,
                  menubar: true,
                  plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'print', 'preview', 'anchor',
                    'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'paste', 'code', 'help', 'wordcount'
                  ],
                  toolbar:
                    'undo redo | formatselect | bold italic backcolor | \
                        alignleft aligncenter alignright alignjustify | \
                        bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen'
                  ,


                }}
              />
                ;
            },
          }
        }
        if (data_.data_type.properties[key].format === 'date-time' || data_.data_type.properties[key].format === 'date') {

          return {
            accessorFn: (row) => new Date(row[key]),
            header: data_.data_type.properties[key].title ?? key,
            filterVariant: 'date',
            filterFn: 'lessThan',
            sortingFn: 'datetime',
            accessorKey: key,
            Cell: ({ cell }) => cell.getValue<Date>()?.toLocaleDateString('fr'),
            id: key,
            Edit: () => null,
          }
        }
        if (key === "concerned_structure") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorFn: (row) => row[key].code_mnemonique,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Chip style={{ fontSize: 12, fontWeight: "bold", fontFamily: "monospace", color: 'var(--text-color)', background: 'transparent', border: ' 2px dotted green', borderRadius: 50 }} label={cell.getValue<string>() || row.original.concerned_structure.code_stru}></Chip>

          }
        }
        if (key === "validated") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Tag severity={cell.getValue<boolean>() ? 'success' : 'danger'} icon={cell.getValue<boolean>() ? <FontAwesomeIcon size='2x' icon={faSquareCheck} /> : <FontAwesomeIcon size='2x' icon={faRectangleXmark} />} value=''></Tag>

          }
        }
        if (key === "accepted") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Tag severity={cell.getValue<boolean>() ? 'success' : 'danger'} icon={cell.getValue<boolean>() ? <FontAwesomeIcon size='2x' icon={faCircleCheck} /> : <FontAwesomeIcon size='2x' icon={faCircleXmark} />} value=''></Tag>

          }
        }
        if (key === "priority") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableFooterCellProps: {
              align: 'center',
            },
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Tag className='w-9rem text-md' severity={getPrioritySeverity(cell.getValue<string>())} value={cell.getValue<string>()}></Tag>

          }
        }
        if (key === "recommendation") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <div className='white-space-normal'>{cell.getValue<string>()}</div>

          }
        }
        if (key === "proposed_by") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0]['$ref'] ? data_.data_type.properties[key].allOf[0]['$ref'].enum : []
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Tag
              key={row.original.code + row.original.created}
              severity={cell.getValue<String>() === "VP" ?
                "danger" : cell.getValue<String>() === "STRUCT" ? "success" : "info"
              }
              value={cell.getValue<String>() === "VP" ?
                "Vice Président" : cell.getValue<String>() === "STRUCT" ? "Structures" : "Contrôle Interne"}
            >

            </Tag>
          }
        }
        if (key === "status") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,
            muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',
              // children: data_.data_type.properties[key]['$ref'].enum,
              SelectProps: {
                // multiple: true,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            id: key,
            Cell: ({ cell, row }) => cell.getValue<string>() ? <Tag
              key={row.original.code + row.original.created}
              severity={getStatusSeverity(cell.getValue<string>())}
              value={cell.getValue<string>().toUpperCase()}
              className='w-9rem text-md'
            >
            </Tag> : 'N/D'
          }
        }
        if (key === "type") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },

            editSelectOptions: data_.data_type.properties[key]['$ref'].enum
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                key={row.original.code + row.original.created}
                severity={
                  cell.getValue<String>() === "Commandée" ?
                    "danger" : cell.getValue<String>() === "Planifiée" ? "warning" : cell.getValue<String>() === 'AI' ? "info" : 'success'
                }
                value={cell.getValue<String>()}
              >

              </Tag>
          }

        }
        if (key === "exercise") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },

            editSelectOptions: data_.data_type.properties[key]['$ref'].enum
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                key={row.original.code + row.original.created}
                severity={'success'}
                value={cell.getValue<String>()}
              />
          }

        }
        if (key === "mission") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                className='w-full'
                style={{ fontSize: 12, fontFamily: "monospace", color: 'var(--text-color)', background: 'transparent', border: ' 2px solid orange' }}
                severity={
                  'success'
                }
                value={cell.getValue<string>()}
              />
          }

        }
        if (key === "theme") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: 'theme.theme.title',
            muiTableHeadCellProps: {
              align: 'left',
            },
            muiTableBodyCellProps: {
              align: 'left',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            // Cell: ({ cell, row }) =>parse(cell.getValue<ArbitratedTheme>().theme.title)

          }
        }
        if (key === "numrecommandation") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Badge style={{ background: 'transparent', border: '2px solid var(--primary-500)', fontSize: 12, fontWeight: "bold", fontFamily: "monospace", color: 'var(--text-color)', }} size={'large'} value={`${cell.getValue<string>()}`} severity="warning"></Badge>
          }
        }
        if (data_.data_type.properties[key]['$ref'] && data_.data_type.properties[key]['$ref'].enum) {
          return {
            header: data_.data_type.properties[key].title ?? key,
            // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+" ,"; }, ""):"",
            accessorKey: key,
            id: key,
            Cell: ({ cell, row }) => cell.row.original[key],
            editSelectOptions: data_.data_type.properties[key]['$ref'].enum,
            muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',
              children: data_.data_type.properties[key]['$ref'].enum,
              SelectProps: {
                // multiple: true,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
          }
        }
        else {
          if (key === "id") return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Edit: () => null,
          }; else
            return {
              header: data_.data_type.properties[key].title ?? key,
              accessorKey: key,
              id: key,
              // Edit: () => null,
            };

        }
      })
    ,
    [],
  );
  // const actionBodyTemplate = (rowData: any, options: ColumnBodyOptions) => {
  //     return (
  //         <React.Fragment>
  //             {!options.rowEditor?.editing && <Button icon="pi pi-pencil" rounded outlined severity="info" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.element?.props.onClick(e) }} />}
  //             {options.rowEditor?.editing && <Button icon="pi pi-save" rounded outlined severity="success" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.onSaveClick(e) }} />}
  //             {options.rowEditor?.editing && <Button icon="pi pi-times" rounded outlined severity="info" onClick={(e) => { setCommentID(rowData.id); options.rowEditor?.onCancelClick(e) }} />}
  //             {!options.rowEditor?.editing && <Button icon="pi pi-trash" rounded outlined severity="danger" onClick={(e) => { setCommentID(rowData.id); setCommentID(rowData.id); confirm1(e, rowData.id) }} />}
  //         </React.Fragment>
  //     );
  // }
  const generateRecommandationActionsColumns = () => {
    let columns = [];
    for (const [key, value] of Object.entries($Action.properties).filter(([key, value], index) => !['created_by', 'dependencies', 'modified_by', 'created', 'modified', 'id'].includes(key))) {

      if (key === 'description') {
        columns.push(<Column field={key} body={(data) => data.description} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
      }
      else if (key === 'job_leader') {
        columns.push(<Column field={key} body={(data) => `${data.job_leader?.last_name} ${data.job_leader?.first_name}`} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
      }
      else if (['start_date', 'end_date'].includes(key)) {
        columns.push(<Column field={key} body={(data) => new Date(data[key]).toLocaleDateString()} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
      }
      // else if (['validated', 'accepted'].includes(key)) {
      //     columns.push(<Column field={key}  body={(data) => data[key] ? <i className="pi pi-check-circle" style={{ color: 'green' }}></i> : <i className="pi pi-times-circle" style={{ color: 'red' }}></i>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
      // }
      else if (['progress'].includes(key)) {
        columns.push(<Column field={key} body={(data) => <ProgressBar value={data[key]}></ProgressBar>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)
      }
      else if (['status'].includes(key)) {
        columns.push(<Column field={key} body={(data) => <Tag className='w-7rem' value={data[key].toUpperCase()}></Tag>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '45%' }} />)
      }
      else if (['accepted'].includes(key)) {
        columns.push(<Column field={key} body={(data) => <ToggleButton checked={data.accepted} onLabel="Oui" offLabel="Non" onIcon="pi pi-check" offIcon="pi pi-times" onChange={(e) => { updateActionAcceptation(e, data.id) }} />} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '45%' }} />)
      }
      else if (['validated'].includes(key)) {
        columns.push(<Column field={key} body={(data) => <ToggleButton checked={data.validated} onLabel="Oui" offLabel="Non" onIcon="pi pi-check" offIcon="pi pi-times" onChange={(e) => { updateActionValidation(e, data.id) }} />} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '45%' }} />)
      }
      else if (['proof'].includes(key)) {
        columns.push(<Column field={key} body={(data) => <Button severity='warning' icon='pi pi-paperclip' onClick={attachementViewProofClick}></Button>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '45%' }} />)
      }


    }
    columns.push(<Column align={'center'} field={'comment'} body={(data) => <Button rounded outlined severity='info' icon='pi pi-comments' onClick={(e) => { console.log("addCommentClick", data); addCommentClick(e, data.id, data.recommendation) }}><Badge value={data.comments.length ?? 0} severity="danger"></Badge></Button>} header={'Commentaires'} sortable style={{ width: '25%' }} />)


    return columns;
  }
  console.log('#######################',data_)
  const table = useMaterialReactTable<T>({
    columns,
    data: data_.error ? [] : data_.data_ ? data_.data_ : [data_.data_], //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    rowCount: data_.error ? 0 : data_.data_? data_.data_.length : 1,
    enableRowSelection: true, //enable some features
    enableColumnOrdering: true, //enable a feature for all columns
    enableGlobalFilter: true, //turn off a feature
    enableGrouping: true,
    enableRowActions: true,
    enableRowPinning: true,
    enableStickyHeader: true,
    enableStickyFooter: true,
    enableColumnPinning: true,
    enableColumnResizing: true,
    enableRowNumbers: true,
    enableExpandAll: true,
    enableEditing: true,
    enableExpanding: true,
    manualPagination: true,
    initialState: {
      pagination: { pageSize: 5, pageIndex: 1 },
      columnVisibility: { created_by: false, created: false, modfied_by: false, modified: false, modified_by: false, staff: false, assistants: false, id: false, document: false },
      density: 'compact',
      showGlobalFilter: true,
      sorting: [{ id: 'id', desc: false }],
    },
    state: {
      pagination: data_.pagination.pagi,
      isLoading: data_.isLoading, //cell skeletons and loading overlay
      showProgressBars: data_.isLoading, //progress bars while refetching
      isSaving: isMutating_create, //progress bars and save button spinners
    },
    localization: MRT_Localization_FR,
    onPaginationChange: onPaginationChange,
    displayColumnDefOptions: {
      'mrt-row-pin': {
        enableHiding: true,
      },
      'mrt-row-expand': {
        enableHiding: true,
      },
      'mrt-row-actions': {
        // header: 'Edit', //change "Actions" to "Edit"
        size: 160,
        enableHiding: true,
        grow: true
        //use a text button instead of a icon button
        // Cell: ({ row, table }) => (
        //   <Button onClick={() => table.setEditingRow(row)}>Edit Customer</Button>
        // ),
      },
      'mrt-row-numbers': {
        enableHiding: true, //now row numbers are hidable too
      },
    },
    defaultColumn: {
      grow: true,
      enableMultiSort: true,
    },
    muiTablePaperProps: ({ table }) => ({
      //elevation: 0, //change the mui box shadow
      //customize paper styles
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      classes: { root: 'p-datatable-gridlines text-900 font-medium text-xl' },

      sx: {
        height: `calc(100vh - 9rem)`,
        // height: `calc(100vh - 200px)`,
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        "& .MuiTablePagination-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
        "& .MuiSvgIcon-root": {
          color: "var(--surface-900) !important",
        },
        "& .MuiInputBase-input": {
          color: "var(--surface-900) !important",
        },
        "& .MuiTableSortLabel-root": {
          color: "var(--surface-900) !important",
        },
        "& .MuiBox-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
      },
    }),
    editDisplayMode: 'modal',
    createDisplayMode: 'modal',
    onEditingRowSave: ({ table, row, values }) => {
      //validate data
      //save data to api
      const { created, modified, id, mission, recommendation, concerned_structure, causes, actions_add, ...rest } = values;

      // values._changedValues.actions= values._changedValues.actions.filter(action => action.id === undefined)
      values._changedValues.actions.forEach((action : any) => action.job_leader = action.job_leader.id)
      console.log("onEditingRowSave", values, rest, values._changedValues)
      trigger_modify(
        { id: id, data: values._changedValues },
        {
          onSuccess: () => toast.current?.show({ severity: 'info', summary: 'Modification', detail: 'Enregistrement modifié' }),
          onError: (error) => toast.current?.show({ severity: 'info', summary: 'Modification', detail: `${error.message}` }),
        }
      )
      table.setEditingRow(null); //exit editing mode
    },
    onEditingRowCancel: () => {
      //clear any validation errors
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });

    },
    onCreatingRowSave: ({ table, values, row }) => {
      //validate data
      //save data to api
      console.log("onCreatingRowSave", values)
      const { created, modified, id, ...rest } = values;
      values.actions = values.actions.filter((action:any) => action.id === null || action.id === undefined).forEach((action:any) => action.job_leader = action.job_leader.id)
      console.log("onCreatingRowSave", values)

      trigger_create(
        { data: values as RecommendationWriteRequest},
        {

          onSuccess: () => {
            toast.current?.show({ severity: 'info', summary: 'Création', detail: 'Enregistrement créé' });
            table.setCreatingRow(null);
          },
          onError: (err) => {
            toast.current?.show({ severity: 'error', summary: 'Création', detail: `${err.response?.statusText}` });
            console.log("onCreatingRowSave", err.response); row._valuesCache = { error: err.response, ...row._valuesCache };
            return
          },
        }
      )
    },
    onCreatingRowCancel: () => {
      //clear any validation errors
    },
    muiEditRowDialogProps: ({ row, table }) => ({
      //optionally customize the dialog
      // about:"edit modal",
      open: editVisible || createVisible,
      sx: {
        '& .MuiDialog-root': {
          // display: 'none'

        },
        '& .MuiDialog-container': {
          // display: 'none'
          "& .MuiPaper-root": {
            maxWidth: '50vw'
          }

        },
        zIndex: 1100

      }
    }),
    muiTableFooterProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        "& .MuiTableFooter-root": {
          backgroundColor: "var(--surface-card) !important",
        },
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableContainerProps: ({ table }) => ({
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        height: table.getState().isFullScreen ? `calc(100vh)` : `calc(100vh - 9rem - ${table.refs.topToolbarRef.current?.offsetHeight}px - ${table.refs.bottomToolbarRef.current?.offsetHeight}px)`

      },
    }),
    muiPaginationProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {

        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableHeadCellProps: {
      sx: {
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTopToolbarProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important"
      },

    },
    muiTableBodyProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        //stripe the rows, make odd rows a darker color
        '& tr:nth-of-type(odd) > td': {
          backgroundColor: 'var(--surface-card)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
        '& tr:nth-of-type(even) > td': {
          backgroundColor: 'var(--surface-border)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
      },
    },
    renderTopToolbarCustomActions: ({ table }) => (
      <Stack direction={"row"} spacing={1}>
        <Can I='view' a='recommendation'>
          <Button
            icon="pi pi-plus"
            rounded
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={(event) => {
              table.setCreatingRow(true); setCreateVisible(true), console.log("creating row ...");
            }}

            size="small"
          />
        </Can>
        <Can I='view' a='recommendation'>
          <Button
            rounded
            disabled={table.getIsSomeRowsSelected()}
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
            icon="pi pi-trash"
            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}
            size="small"
          />
        </Can>
        {isMutating_action_update && <ProgressSpinner style={{ width: '32px', height: '32px' }} strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />}
      </Stack>
    ),
    muiDetailPanelProps: () => ({
      sx: (theme) => ({
        backgroundColor:
          theme.palette.mode === 'dark'
            ? 'rgba(255,210,244,0.1)'
            : 'rgba(0,0,0,0.1)',

      }),
    }),
    renderCreateRowDialogContent: RecommendationEditForm,
    renderEditRowDialogContent: RecommendationEditForm,
    renderDetailPanel: ({ row }) =>
      <Box
        sx={{
          display: 'grid',
          margin: 'auto',
          //gridTemplateColumns: '1fr 1fr',
          width: '100vw',
        }}
      >
        <TabView >
          <TabPanel header={<div className='flex align-items-center justify-content-center gap-2'>
            <Button tooltip={"Ajouter/Editer les actions"} size='small' icon="pi pi-plus" onClick={() => { row._valuesCache.actions_add = true; setRecommendationId(row.original.id); table.setEditingRow(row); setEditVisible(true), console.log("editing row ..."); }} rounded outlined />
            <span>Plan d'actions</span></div>} rightIcon="pi pi-thumbs-up ml-2">
            <DataTable<Action[]> ref={planActionDataTableRef} onRowClick={(event: DataTableRowClickEvent) => setActionId(event.data.id)} tableStyle={{ maxWidth: '70vw' }} value={row.original.actions} rows={5} paginator resizableColumns responsiveLayout="scroll">
              {generateRecommandationActionsColumns()}
            </DataTable>
            <div>{actionId}</div>
            {/* Dialog for proof  */}
            <OverlayPanel
              showCloseIcon
              closeOnEscape
              dismissable={false}
              className='grid w-3'
              pt={{
                content: { className: 'w-full h-15rem	' },
                closeButton: {
                  style: {
                    left: '-1rem',
                    right: '0'
                  }
                }
              }}
              ref={overlayProofPanelRef}>
              <div className='grid w-full'>
                <div className='col-12 lg:col-12 xl:col-12 cursor-pointer'>
                  <InputTextarea rows={5} className='w-full' placeholder='Commentaire' onChange={(e) => { setActionComment(e.target.value) }} />
                </div>
                <div className='col-12 lg:col-12 xl:col-12 cursor-pointer'>
                  <Button className='w-full' icon='pi pi-save' onClick={(e) => {
                    trigger_comment_create({ recommendation: null, action: actionId, comment: action_comment, type: '' }, { revalidate: true, populateCache: true })
                    overlayProofPanelRef.current.toggle(e);
                  }} />
                </div>
              </div>
            </OverlayPanel>
            {/* ---------------------------------  */}
            {/* Dialog for Acceptation/Validation  */}
            <OverlayPanel
              showCloseIcon
              closeOnEscape
              dismissable={false}
              className='grid w-3'
              pt={{
                content: { className: 'w-full h-18rem	' },
                closeButton: {
                  style: {
                    left: '-1rem',
                    right: '0'
                  }
                }
              }}
              ref={overlayValidationAcceptationPanelRef} >
              <div className='grid w-full'>
                <div className='col-12 lg:col-12 xl:col-12 cursor-pointer'>
                  {overlayValidationAcceptationTitle}
                </div>
                <div className='col-12 lg:col-12 xl:col-12 cursor-pointer'>
                  <InputTextarea rows={5} className='w-full' placeholder='Commentaire' onChange={(e) => { setActionComment(e.target.value) }} />
                </div>
                <div className='col-12 lg:col-12 xl:col-12 cursor-pointer'>
                  <Button className='w-full' icon='pi pi-save' onClick={(e) => {
                    trigger_comment_create({
                      data: {
                        recommendation: null,
                        action: actionId,
                        comment: action_comment,
                        type: overlayValidationAcceptationTitle.includes('Validation') ? 'Validation' : 'Acceptation',
                        value: overlayValidationAcceptationTitle.includes('Validation') ? 'Non Validé' : 'Non Accepté'
                      }
                    });
                    trigger_action_update(overlayValidationAcceptationTitle.includes('Validation') ? { id: actionId, data: { validated: false } } : { id: actionId, data: { accepted: false } });
                    overlayValidationAcceptationPanelRef.current.toggle(e);
                  }} />
                </div>
              </div>
            </OverlayPanel>
            {/* ---------------------------------  */}
            <CommentActionDialog
              visible={actionCommentDialogVisible}
              setVisible={setActionCommentDialogVisible}
              action_comments={action_comments} action_id={actionId} />
          </TabPanel>
        </TabView>
      </Box>,
    // Les buttons action de la ligne (row)
    renderRowActions: ({ cell, row, table }) => (
      <span className="p-buttonset flex p-1">
        <Can I='view' a='recommendation'><Button tooltip={"voir détails recommandation"} size='small' icon="pi pi-eye" onClick={() => { setRecommendationId(row.original.id); setRecommendation$(row.original); setDetailsDialogVisible(true); }} rounded outlined /></Can>
        <Can I='view' a='recommendation'><Button tooltip={"Modifier recommandation"} size='small' icon="pi pi-pencil" onClick={() => { row._valuesCache.actions_add = false; setRecommendationId(row.original.id); table.setEditingRow(row); setEditVisible(true), console.log("editing row ..."); }} rounded outlined /></Can>
        <Can I='view' a='recommendation'><Button tooltip={"Supprimer recommandation"} size='small' icon="pi pi-trash" rounded outlined
          onClick={(event) => {
            setRecommendationId(row.original.id); confirmPopup({
              target: event.currentTarget,
              message: 'Voulez-vous supprimer cette ligne?',
              icon: 'pi pi-info-circle',
              // defaultFocus: 'reject',
              acceptClassName: 'p-button-danger',
              acceptLabel: 'Oui',
              rejectLabel: 'Non',
              accept,
              reject
            })
          }}
        /></Can>
        <ConfirmPopup />
      </span>
    ),
  });

  return (
    <>
      <MaterialReactTable table={table} />
      <Toast ref={toast} />
      {recommendation$ && <RecommendationDetails params={{ recommendation: recommendation$, detailsDialogVisible, setDetailsDialogVisible }} />}
    </>
  );
}

