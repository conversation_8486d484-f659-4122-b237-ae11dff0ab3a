import { MissionSerializerRead, User } from "@/services/schemas";
import { MRT_ColumnDef, MRT_Row } from "material-react-table";
import  jsPDF  from 'jspdf'; //or use your library of choice here
import autoTable from 'jspdf-autotable';
export const getMissionEtatSeverity = (etat:string) => {
    switch (etat) {
        case 'Non Lancée':
            return 'info';

        case 'Suspendue':
            return 'warning';

        case 'En cours':
            return 'success';
        
        case 'Clôturée':
            return 'danger';

        default:
            return null;
    }

}

export const getMissionTypeSeverity = (type:string) => {
    switch (type) {
        case 'Commandée':
            return 'success';

        case 'Planifiée':
            return 'warning';

        case 'Avis & Conseils':
            return 'danger';

        default:
            return null;
    }

}

export const getRecommendationPrioritySeverity = (type:string) => {
    switch (type) {
        case 'FAIBLE':
            return 'success';

        case 'NORMALE':
            return 'warning';

        case 'ELEVEE':
            return 'danger';

        default:
            return null;
    }

}

export const handleExportRows = (rows: MRT_Row<MissionSerializerRead>[],columns:MRT_ColumnDef<MissionSerializerRead>[]) => {
    const doc = new jsPDF();
    const tableData = rows.map((row) => Object.values(row.original));
    const tableHeaders = columns.map((c) => c.header);

    autoTable(doc, {
      head: [tableHeaders],
      body: tableData,
    });

    doc.save('mrt-pdf-example.pdf');
  };

export const getUserFullname = (user: User) => user ? `${user.first_name} ${user.last_name}` :''

export  const getPrioritySeverity = (value: string) => {
    switch (value) {
      case 'FAIBLE':
        return 'success'
      case 'NORMALE':
        return 'warning'
      case 'ELEVEE':
        return 'danger'
      default:
        return undefined
    }
  }
  export  const getStatusSeverity = (value: string) => {
    switch (value) {
      case 'Réalisée':
        return 'success'
      case 'En cours':
        return 'warning'
      case 'Non Réalisée':
        return 'danger'
      default:
        return undefined
    }
  }