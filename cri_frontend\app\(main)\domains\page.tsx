'use client';

import {  useApiDomainList, useApiRiskList } from '@/services/api/api/api';
import { $Domain, $Risk, Domain, Risk } from '@/services/openapi_client';
import { MRT_PaginationState } from 'material-react-table';
import { useState } from 'react';
import GenericTable from './(components)/GenericTAble';
import { getCookie } from 'cookies-next';

const ThemesTable = () => {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex:0,
        pageSize: 30, //customize the default page size
    });
    const { data: domains, isLoading:isLoading ,error:error} = useApiDomainList({ page: pagination.pageIndex + 1 ,page_size:pagination.pageSize},{
        axios: { headers: { Authorization: `Token ${user?.token}` } }, swr: {
          revalidate: true,
          populateCache: true,
        }
      });

    
    if( isLoading )  return (<div></div>)
    return   (        
        <div className="grid">
            <div className="col-12">
                <GenericTable<Domain> data_={domains} isLoading={isLoading} error={error} data_type={$Domain} pagination={{"set":setPagination,"pagi":pagination}}></GenericTable>                    
            </div>
        </div>
    );
};

export default ThemesTable;
