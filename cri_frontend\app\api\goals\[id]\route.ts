import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/goals/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid goal ID' }, { status: 400 })
    }

    const goal = await prisma.goal.findUnique({
      where: { id },
      include: {
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
            domain: {
              select: {
                id: true,
                title: true,
              }
            },
            process: {
              select: {
                id: true,
                title: true,
              }
            }
          }
        }
      },
    })

    if (!goal) {
      return NextResponse.json({ error: 'Goal not found' }, { status: 404 })
    }

    return NextResponse.json({ data: goal })
  } catch (error) {
    console.error('Error fetching goal:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/goals/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can update goals
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid goal ID' }, { status: 400 })
    }

    const body = await request.json()
    const { title, description } = body

    // Check if goal exists
    const existingGoal = await prisma.goal.findUnique({
      where: { id },
    })

    if (!existingGoal) {
      return NextResponse.json({ error: 'Goal not found' }, { status: 404 })
    }

    // Update goal
    const goal = await prisma.goal.update({
      where: { id },
      data: {
        ...(title !== undefined && { title }),
        ...(description !== undefined && { description }),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
          }
        }
      },
    })

    return NextResponse.json({ data: goal })
  } catch (error) {
    console.error('Error updating goal:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/goals/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can delete goals
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid goal ID' }, { status: 400 })
    }

    // Check if goal exists
    const existingGoal = await prisma.goal.findUnique({
      where: { id },
      include: {
        themes: true,
      },
    })

    if (!existingGoal) {
      return NextResponse.json({ error: 'Goal not found' }, { status: 404 })
    }

    // Check if goal has associated themes
    if (existingGoal.themes.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete goal with associated themes' },
        { status: 400 }
      )
    }

    // Delete goal
    await prisma.goal.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Goal deleted successfully' })
  } catch (error) {
    console.error('Error deleting goal:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
