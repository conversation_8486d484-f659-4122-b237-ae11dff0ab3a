'use client'
import { MissionDocument, Plan, Recommendation, User } from '@/services/schemas';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import GenericDataTable from '@/utilities/components/GenericDataTable';
import { Button } from 'primereact/button';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
// import { Document, Page, pdfjs } from 'react-pdf';
import { Editor } from '@tinymce/tinymce-react';
import parse from 'html-react-parser';
// import 'react-pdf/dist/Page/AnnotationLayer.css';
// import 'react-pdf/dist/Page/TextLayer.css';
import { LocalizationMap, Viewer, Worker } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import fr_FR from '@react-pdf-viewer/locales/lib/fr_FR.json';
import { getCookie } from 'cookies-next';
import { Chip } from 'primereact/chip';
import { Column, ColumnBodyOptions } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { OverlayPanel } from 'primereact/overlaypanel';
import MissionDetails from '../[mission_id]/page';
import MissionEditForm from './editForm';

import { Badge } from 'primereact/badge';
import CommentRecommendationDialog from './CommentRecommendationDialog';
import { getMissionEtatSeverity, getMissionTypeSeverity, handleExportRows } from '@/utilities/functions/utils';
import { LayoutContext } from '@/layout/context/layoutcontext';
import { useLoadingSpinner } from '@/utilities/hooks/useSpinner';
import { useToast } from '@/utilities/hooks/useToast';
import { FileUpload, FileUploadHandlerEvent, FileUploadHeaderTemplateOptions, FileUploadSelectEvent, ItemTemplateOptions } from 'primereact/fileupload';
import { Image } from 'primereact/image';
import { ProgressSpinner } from 'primereact/progressspinner';
import { classNames } from 'primereact/utils';
import Link from 'next/link';
import { Can } from '@/app/Can';
import ability from '@/app/ability';
import { useQueryClient } from '@tanstack/react-query';
import { useApiCommentCreate, useApiDocsCreate, useApiDocsDestroy, useApiMissionCreate, useApiMissionPartialUpdate, useApiPlanList, useApiRecommendationPartialUpdate } from '@/hooks/useNextApi';
import { MissionEtat, Mission, CriStructview } from '@prisma/client';
import { MRT_ColumnDef, MRT_RowData } from 'material-react-table';
import { Stack } from '@mui/material';


//TODO make loading here

//If using TypeScript, define the shape of your data (optional, but recommended)
// interface GenericTableProps {
//   data_: any;
//   isLoading?: boolean;
// }

export default function GenericTable<Mission extends MRT_RowData>(data_: { isLoading: any; data_: any, error: any, data_type: any | undefined, pagination: any, mutate: any }) {
  const user = JSON.parse(getCookie('user')?.toString() || '{}')
  const defaultLayoutPluginInstance = defaultLayoutPlugin();
  const { layoutConfig } = useContext(LayoutContext);
  const { setLoadingSpinner } = useLoadingSpinner()
  const toastRef = useToast()
  const { data: plans } = useApiPlanList()

  //////////////////////FileUpload Els & fucntions///////////////////////////////////
  const { data: doc_create_data, mutate: trigger } = useApiDocsCreate()

  const fileUploadRef = useRef<FileUpload>(null);

  const itemTemplateFiles = (product, index) => {
    return (
      <div className="col-12" key={product.id}>
        <div className={classNames('flex flex-column xl:flex-row xl:align-items-start p-4 gap-4', { 'border-top-1 surface-border': index !== 0 })}>
          <img className="w-9 sm:w-16rem xl:w-10rem shadow-2 block xl:block mx-auto border-round" src={`https://primefaces.org/cdn/primereact/images/product/${product.image}`} alt={product.name} />
          <div className="flex flex-column sm:flex-row justify-content-between align-items-center xl:align-items-start flex-1 gap-4">
            <div className="flex flex-column align-items-center sm:align-items-start gap-3">
              <div className="text-2xl font-bold text-900">{product.name}</div>
              <div className="flex align-items-center gap-3">
                <span className="flex align-items-center gap-2">
                  <i className="pi pi-tag"></i>
                  <span className="font-semibold">{product.category}</span>
                </span>
                <Tag value={product.inventoryStatus}></Tag>
              </div>
            </div>
            <div className="flex sm:flex-column align-items-center sm:align-items-end gap-3 sm:gap-2">
              <span className="text-2xl font-semibold">${product.price}</span>
              <Button icon="pi pi-shopping-cart" className="p-button-rounded" disabled={product.inventoryStatus === 'OUTOFSTOCK'}></Button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const customUploader = (event: FileUploadHandlerEvent) => {
    console.log("Files UPLOAD", event.files)
    trigger({ mission: mission_id, document: event.files })
  };

  const onTemplateRemove = (file, callback) => {
    callback();
  };
  const headerTemplate = (options: FileUploadHeaderTemplateOptions) => {
    const { className, chooseButton, uploadButton, cancelButton } = options;
    console.log("++++++++----------+++++++++++++", cancelButton)
    return (
      <div className={className} style={{ backgroundColor: 'transparent', display: 'flex', alignItems: 'center' }}>
        <Button style={{ width: '38px' }} rounded severity='success' icon='pi pi-fw pi-images' onClick={chooseButton.props.onClick}></Button>
        <Button style={{ width: '38px' }} rounded severity='danger' icon='pi pi-fw pi-times' onClick={cancelButton.props.onClick}></Button>
        {/* <div className="flex align-items-center gap-3 ml-auto">
                    <span>{1200} / 1 MB</span>
                    <ProgressBar value={50} showValue={false} style={{ width: '10rem', height: '12px' }}></ProgressBar>
                </div> */}
      </div>
    );
  };

  const itemTemplate = (file: object & File, props: ItemTemplateOptions) => {
    return (
      <div className="flex align-items-center flex-wrap">
        <div className="flex align-items-center gap-4" style={{ width: '40%' }}>
          <Link target="_blank" href={file.objectURL} ><img alt={file.name} role="presentation" src={file.type.includes('image') ? file.objectURL : '/images/pdf.webp'} width={50} /></Link>
          <span className="flex flex-column text-left ml-3">
            {file.name}
            <small>{new Date().toLocaleDateString()}</small>
          </span>
        </div>
        <Tag value={props.formatSize} severity={fileUploadRef.current?.getUploadedFiles().includes(file) ? "success" : "warning"} className="px-3 py-2" />
        {fileUploadRef.current?.state?.uploading && <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />}
        <Button type="button" icon="pi pi-times" className="p-button-outlined p-button-rounded p-button-danger ml-auto" onClick={() => onTemplateRemove(file, props.onRemove)} />
      </div>
    );
  };
  ///////////////////////////////////////////////////////////////////////////
  const queryClient = useQueryClient();

  const toast = useRef<any>(null);
  const overlayPanelRef = useRef<any>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [recommedantionCommentDialogVisible, setRecommedantionCommentDialogVisible] = useState(false);
  const [mission_id, setMissionId] = useState(0);
  const [recomm_id, setRecommId] = useState(0);
  const [comment, setComment] = useState({});
  const [triggerData, setTriggerData] = useState(null);
  const [selectedRecommendation, setSelectedRecommendation] = useState(null);
  const [recomm_comment, setRecommComment] = useState('');
  const [mission_doc, setMissionDoc] = useState<MissionDocument | null>(null);
  const [visible, setVisible] = useState(false);
  const [detailsDialogVisible, setDetailsDialogVisible] = useState(false);
  const [missionDocsDialogVisible, setMissionDocsDialogVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [rowActionEnabled, setRowActionEnabled] = useState(false);

  // FETCH HOOKS
  // const { data: plans, isLoading: plan_isLoading, error: plan_error } = useApiPlanList({}, { axios: { headers: { Authorization: `Token ${user?.token}` } } })
  const { data: rec_update__data, isPending: rec_update_isMutating, error: rec_update_error, mutate: trigger_recommandation_update } = useApiRecommendationPartialUpdate()
  const { data, isPending, error, mutate: trigger_mission_create } = useApiMissionCreate()
  const { data: comments_data, isPending: comments_isMutating, error: comments_error, mutate: trigger_comments_create } = useApiCommentCreate()
  const { data: data_up, isPending: isMutating_up, error: error_up, mutate: trigger_mission_update } = useApiMissionPartialUpdate()
  const { data: docs_data, isPending: docs_isMutating, error: docs_error, mutate: trigger_docs_create } = useApiDocsCreate()
  const { data: docs_data_delete, isPending: docs_isMutating_delete, error: docs_error_delete, mutate: trigger_docs_delete } = useApiDocsDestroy()

  /////////////////////////////////////////////////////////////////////////////

  // useEffect(() => { if (triggerData) { trigger_recommandation_update(triggerData); data_.mutate() } }, [recomm_id, triggerData, trigger_recommandation_update])
  // useEffect(() => {
  //   fileUploadRef.current?.setUploadedFiles(data_.data_?.data?.results?.find(mission => mission.id === mission_id).mission_docs?.map((doc: MissionDocument) => {
  //     return {
  //       objectURL: doc.document,
  //       id: doc.id,
  //       name: doc.document!,
  //       type: doc.type,
  //       size: doc.size,
  //       lastModified: doc.modified,
  //       webkitRelativePath: ''
  //     } as unknown as File
  //   }))
  //   // fileUploadRef.current?.setFiles(data_.data_?.data?.results?.find(mission => mission.id === mission_id).mission_docs?.map((doc: MissionDocument) => {
  //   //   return {
  //   //     objectURL: doc.document,
  //   //     id: doc.id,
  //   //     name: doc.document!,
  //   //     type:  doc.type,
  //   //     size : doc.size
  //   //   }
  //   // }))
  // }, [missionDocsDialogVisible, mission_doc, mission_id])
  /////////////////////////////////////////////////////////////////////////////
  function onPaginationChange(state: any) {
    //console.log(data_.pagination);
    data_.pagination.set(state)
  };

  const accept = () => {
    toastRef.current.show({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 });
  };

  const reject = () => {
    toast.current.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
  };

  function onDocumentLoadSuccess({ numPages }: { numPages: number | null }) {
    setNumPages(numPages);
    setPageNumber(1);
  }
  function changePage(offset: number) {
    setPageNumber(prevPageNumber => prevPageNumber + offset);
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const columns = useMemo<MRT_ColumnDef<Mission>[]>(
    () =>
      Object.entries(data_.data_type.properties).filter(([key, value], index) => !['modified_by', 'mission_docs', 'created_by', 'recommendations', 'head', 'supervisor', 'assistants', 'staff'].includes(key)).map(([key, value], index) => {
        if (['report', 'content', 'note', 'order', 'comment', 'description'].includes(key)) {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Cell: ({ cell }) => { if (["description", "content", "report"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },
            Edit: ({ cell, column, row, table }) => {
              return <Editor
                initialValue={row.original[key]}
                tinymceScriptSrc="http://localhost:3000/tinymce/tinymce.min.js"
                apiKey='none'
                init={{
                  height: 500,
                  menubar: true,
                  plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'print', 'preview', 'anchor',
                    'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'paste', 'code', 'help', 'wordcount'
                  ],
                  toolbar:
                    'undo redo | formatselect | bold italic backcolor | \
                        alignleft aligncenter alignright alignjustify | \
                        bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen'
                }}
              />
                ;
            },
          }
        }
        if (data_.data_type.properties[key].format === 'date-time' || data_.data_type.properties[key].format === 'date') {

          return {
            accessorFn: (row) => new Date(row[key]),
            header: data_.data_type.properties[key].title ?? key,
            filterVariant: 'date',
            filterFn: 'lessThan',
            sortingFn: 'datetime',
            accessorKey: key,
            Cell: ({ cell }) => new Date(cell.getValue<Date>()).toLocaleDateString('fr'),
            id: key,
            Edit: () => null,
          }
        }
        if (key === 'head') {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Cell: ({ cell, row }) => <a href={`mailto:${cell.getValue<User>()?.email}?subject=Mission : ${row.original.code}`}>{cell.getValue<User>()?.last_name || 'N/D'} {cell.getValue<User>()?.first_name || 'N/D'}</a>
          };
        }
        if (key === 'supervisor') {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Cell: ({ cell, row }) => <a href={`mailto:${cell.getValue<User>()?.email}?subject=Mission : ${row.original.code}`}>{cell.getValue<User>()?.last_name || 'N/D'} {cell.getValue<User>()?.first_name || 'N/D'}</a>
          };
        }
        if (key === "controled_structures") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'left',
            },
            muiTableBodyCellProps: {
              align: 'left',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Stack direction={'row'} spacing={1}>
                {cell.getValue<CriStructview[]>().map((val) => <Tag
                  style={{ fontSize: 12, fontFamily: "monospace", color: 'var(--text-color)', background: 'transparent', border: ' 2px dotted green', borderRadius: 50 }}
                  severity={
                    'success'
                  }
                  value={val.code_mnemonique ?? 'N/D'}
                />)

                  // <Chip style={{ backgroundColor: 'green', color: 'white' }} label={val.code_mnemonique ?? 'N/D'}></Chip>)
                }
              </Stack>

          }
        }
        if (key === "proposed_by") {

          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            editSelectOptions: data_.data_type.properties[key].allOf && data_.data_type.properties[key].allOf[0]['$ref'] ? data_.data_type.properties[key].allOf[0]['$ref'].enum : []
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Tag
              key={row.original.code + row.original.created}
              severity={cell.getValue<string>() === "VP" ?
                "danger" : cell.getValue<string>() === "STRUCT" ? "success" : "info"
              }
              value={cell.getValue<string>() === "VP" ?
                "Vice Président" : cell.getValue<string>() === "STRUCT" ? "Structures" : "Contrôle Interne"}
            >

            </Tag>
          }
        }
        if (key === "etat") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,
            muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',
              // children: data_.data_type.properties[key]['$ref'].enum,
              SelectProps: {
                // multiple: true,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            id: key,
            Cell: ({ cell, row }) => <Tag
              className='w-7rem'
              key={row.original.code + row.original.created}
              severity={getMissionEtatSeverity(cell.getValue<string>())}
              value={cell.getValue<string>()}
            >
            </Tag>
          }
        }
        if (key === 'plan') {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            // Edit: () => null,
            Cell: ({ cell, row }) => cell.getValue<string>() ? <Tag className='w-9rem' severity={'info'} value={cell.getValue<string>()} /> : '/'
          };
        }
        if (key === "type") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },

            editSelectOptions: data_.data_type.properties[key]['$ref'].enum
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }

              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                className='w-7rem'
                key={row.original.code + row.original.created}
                severity={getMissionTypeSeverity(cell.getValue<string>())}
                value={cell.getValue<string>()}
              >

              </Tag>
          }

        }
        if (key === "exercise") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            }
            //editSelectOptions: data_.data_type.properties[key]['$ref'].enum,
            , muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',

              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => row.original.exercise ?
              <Tag
                key={row.original.code + row.original.created}
                severity={
                  'success'
                }
                value={cell.getValue<string>()}
              >
              </Tag> : '/'
          }

        }
        if (key === "code") {
          // console.log(data_.data_type.properties[key].allOf[0]['$ref'].enum)
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'center',
            },
            muiTableBodyCellProps: {
              align: 'center',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) =>
              <Tag
                className='w-full'
                style={{ fontSize: 12, fontFamily: "monospace", color: 'var(--text-color)', background: 'transparent', border: ' 2px solid orange' }}
                severity={
                  'success'
                }
                value={cell.getValue<string>()}
              />
          }
        }
        if (key === "theme") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: 'theme.theme.title',
            muiTableHeadCellProps: {
              align: 'left',
            },
            muiTableBodyCellProps: {
              align: 'left',
            },
            // editSelectOptions: data_.data_type.properties[key]['$ref'].enum ,
            muiEditTextFieldProps: {
              // select: true,
              variant: 'outlined',
              SelectProps: {
                multiple: false,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <div className='white-space-normal'>{cell.getValue<string>()}</div>
          }
        }
        if (key === "assistants") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'left',
            },
            muiTableBodyCellProps: {
              align: 'left',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Stack direction={'column'} spacing={2} alignContent={'start'} alignItems={'start'}>
              {row.original.assistants.map((user: User) => <a href={`mailto:${user.email}`}>* {user.last_name} {user.first_name}</a>)}
            </Stack>
          }
        }
        if (key === "staff") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            muiTableHeadCellProps: {
              align: 'left',
            },
            muiTableBodyCellProps: {
              align: 'left',
            },
            muiTableFooterCellProps: {
              align: 'center',
            },
            id: key,
            Cell: ({ cell, row }) => <Stack direction={'column'} spacing={2} alignContent={'start'} alignItems={'start'}>
              {row.original.staff.map((user: User) => <a href={`mailto:${user.email}`}>* {user.last_name} {user.first_name}</a>)}
            </Stack>
          }
        }
        if (data_.data_type.properties[key]['$ref'] && data_.data_type.properties[key]['$ref'].enum) {
          return {
            header: data_.data_type.properties[key].title ?? key,
            // accessorFn: (originalRow) =>originalRow[key].length >0 ? originalRow[key].reduce(function (acc, obj) { return acc + obj.username+" ,"; }, ""):"",
            accessorKey: key,
            id: key,
            Cell: ({ cell, row }) => cell.row.original[key],
            editSelectOptions: data_.data_type.properties[key]['$ref'].enum,
            muiEditTextFieldProps: {
              select: true,
              variant: 'outlined',
              children: data_.data_type.properties[key]['$ref'].enum,
              SelectProps: {
                // multiple: true,
                // value: formState.userRoles,
                // onChange: handleFieldChange
              }
              // error: !!validationErrors?.state,
              // helperText: validationErrors?.state,
            },
          }
        }
        else {
          if (key === "id") return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            Edit: () => null,
          }; else
            return {
              header: data_.data_type.properties[key].title ?? key,
              accessorKey: key,
              id: key,
              // Edit: () => null,
            };

        }
      }),
    [],
  );
  const table = useMaterialReactTable<MissionSerializerRead>({
    columns,
    data: data_.data_?.data.results || [], //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    rowCount: data_.data_?.data.count || 0,
    enableRowSelection: true, //enable some features
    enableColumnOrdering: true, //enable a feature for all columns
    enableGlobalFilter: true, //turn off a feature
    enableGrouping: true,
    // enableRowActions: true,
    // enableRowPinning: true,
    enableStickyHeader: true,
    enableStickyFooter: true,
    enableColumnPinning: true,
    enableColumnResizing: true,
    enableRowNumbers: true,
    enableExpandAll: true,
    enableEditing: true,
    enableExpanding: true,
    manualPagination: true,
    localization: MRT_Localization_FR,
    initialState: {
      columnOrder: [
        'exercise',
        'plan',
        'code',
        'type',
        'controled_structures',
        'theme'
      ],
      pagination: { pageSize: 5, pageIndex: 1 },
      columnVisibility: { created_by: false, created: false, modfied_by: false, modified: false, modified_by: false, id: false, },
      density: 'compact',
      showGlobalFilter: true,
      sorting: [{ id: 'id', desc: false }],
    },
    state: {
      pagination: data_.pagination.pagi,
      isLoading: data_.isLoading, //cell skeletons and loading overlay
      showProgressBars: data_.isLoading, //progress bars while refetching
      showSkeletons: data_.isLoading
      // isSaving: isSavingTodos, //progress bars and save button spinners
    },
    onPaginationChange: onPaginationChange,
    displayColumnDefOptions: {
      'mrt-row-pin': {
        enableHiding: true,
      },
      'mrt-row-expand': {
        enableHiding: true,
      },
      'mrt-row-actions': {
        // header: 'Edit', //change "Actions" to "Edit"
        size: 160,
        enableHiding: true,
        grow: true


        //use a text button instead of a icon button
        // Cell: ({ row, table }) => (
        //   <Button onClick={() => table.setEditingRow(row)}>Edit Customer</Button>
        // ),
      },
      'mrt-row-numbers': {
        enableHiding: true, //now row numbers are hidable too
      },
    },
    defaultColumn: {
      grow: true,
      enableMultiSort: true,
    },
    muiTablePaperProps: ({ table }) => ({
      //elevation: 0, //change the mui box shadow
      //customize paper styles
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      classes: { root: 'p-datatable-gridlines text-900 font-medium text-xl' },

      sx: {
        height: `calc(100vh - 9rem)`,
        // height: `calc(100vh - 200px)`,
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        "& .MuiTablePagination-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
        "& .MuiSvgIcon-root": {
          color: "var(--surface-900) !important",
        },
        "& .MuiInputBase-input": {
          color: "var(--surface-900) !important",
        },
        "& .MuiTableSortLabel-root": {
          color: "var(--surface-900) !important",
        },
        "& .MuiBox-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
      },
    }),
    editDisplayMode: 'modal',
    createDisplayMode: 'modal',
    onEditingRowSave: async ({ table, row, values }) => {
      console.log("onEditingRowSave", values)
      setLoadingSpinner(true)
      // let new_values = {
      //   "id": values['id'],
      //   "head": values['head']?.id ?? row.original.head.id,
      //   "supervisor": values['supervisor']?.id ?? row.original.supervisor.id,
      //   "plan": values['plan']?.id || plans?.data.data.results.find((plan: Plan) => plan.code === values['plan'])?.id || null,
      //   "theme": values['theme']?.id ?? row.original.theme.id,
      //   "controled_structures": values['controled_structures']?.map((val: CriStructview) => val.id),
      //   "assistants": values['assistants']?.map((val: User) => val.id) ?? row.original.assistants.map((val: User) => val.id),
      //   "staff": values['staff']?.map((val: User) => val.id) ?? row.original.staff.map((val: User) => val.id),
      //   "exercise": values['exercise'] ?? row.original.exercise,
      //   "type": values['type'] ?? row.original.type,
      //   "code": values['code'] ?? row.original.code,
      //   "etat": values['etat'] ?? row.original.etat,
      //   "start_date": new Date(values['start_date']).toISOString().split('T')[0] ?? new Date(row.original.start_date).toISOString().split('T')[0],
      //   "end_date": new Date(values['end_date']).toISOString().split('T')[0] ?? new Date(row.original.end_date).toISOString().split('T')[0],
      // }
      console.log("onEditingRowSave___", row._valuesCache)

      const result = await trigger_mission_update({ id: row.original.id, data: { ...values } }, {

        onSuccess: (data, vars, context) => {
          setLoadingSpinner(false)
          //  TODO refetch after mutation
          queryClient.invalidateQueries({ queryKey: ['http://10.39.107.98:3001/api/mission/'] });
          table.setEditingRow(null); //exit creating mode
          toast.current!.show({ severity: 'success', summary: 'Info', detail: `Mission ${values.code} mise à ajour` });
        },
        onError: (error) => {
          setLoadingSpinner(false)
          toast.current!.show({ severity: 'error', summary: 'Info', detail: `${error.response?.statusText}` });
          //console.log("onEditingRowSave", error.response);
          row._valuesCache = { error: error.response, ...row._valuesCache };
          return;
        }
      })
    },
    onEditingRowCancel: () => {
      //clear any validation errors
      toast.current!.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });

    },
    onCreatingRowSave: async ({ table, row, values }) => {
      console.log("onCreatingRowSave", values)
      trigger_mission_create({
        data: {
          ...values,
        }
      }, {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['http://10.39.107.98:3001/api/mission/'] });
          table.setCreatingRow(null); //exit creating mode
          toast.current!.show({ severity: 'success', summary: 'Info', detail: `Mission ${values.code} créée` });
        },
        onError(error) {
          toast.current!.show({ severity: 'error', summary: 'Info', detail: `${error.response?.statusText}` });
          //console.log("onCreatingRowSave", error.response);
          row._valuesCache = { error: error.response, ...row._valuesCache };
          return;
        },
      })

    },
    onCreatingRowCancel: () => {
      //clear any validation errors
      toast.current!.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });

    },
    muiEditRowDialogProps: ({ row, table }) => ({
      //optionally customize the dialog
      // about:"edit modal",
      open: editVisible,//|| createVisible,
      sx: {
        '& .MuiDialog-root': {
          display: 'none'
        },
        '& .MuiDialog-container': {
          display: 'none'
        },
        zIndex: 1100

      }
    }),
    muiTableFooterProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        "& .MuiTableFooter-root": {
          backgroundColor: "var(--surface-card) !important",
        },
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableContainerProps: ({ table }) => ({
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        height: table.getState().isFullScreen ? `calc(100vh)` : `calc(100vh - 9rem - ${table.refs.topToolbarRef.current?.offsetHeight}px - ${table.refs.bottomToolbarRef.current?.offsetHeight}px)`

      },
    }),
    muiPaginationProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {

        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableHeadCellProps: {
      sx: {
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTopToolbarProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important"
      },

    },
    muiTableBodyProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        //stripe the rows, make odd rows a darker color
        '& tr:nth-of-type(odd) > td': {
          backgroundColor: 'var(--surface-card)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
        '& tr:nth-of-type(even) > td': {
          backgroundColor: 'var(--surface-border)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
      },
    },
    muiDetailPanelProps: () => ({
      sx: (theme) => ({
        backgroundColor:
          theme.palette.mode === 'dark'
            ? 'rgba(255,210,244,0.1)'
            : 'rgba(0,0,0,0.1)',
      }),
    }),
    renderTopToolbarCustomActions: ({ table }) => (
      <Stack direction={"row"} spacing={1}>
        {/* <Can I='add' a='mission'> */}
          <Button
            icon="pi pi-plus"
            rounded
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={(event) => {
              table.setCreatingRow(true);
              //setCreateVisible(true);
              setEditVisible(true);
              console.log("creating row ...");
            }}
            size="small"
          >

          </Button>
        {/* </Can> */}
        <Can I='delete' a='mission'>
          <Button
            rounded
            disabled={table.getIsSomeRowsSelected()}
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
            icon="pi pi-trash"
            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}
            size="small"
          >
          </Button>
        </Can>
        <Can I='print' a='mission'>
          <Button icon="pi pi-file-pdf" rounded size="small"
            aria-label="edit"
            onClick={() => {
              const rows = table.getRowModel().rows
              // rows.map(row =>{
              //   if(row.)
              // })
              handleExportRows(rows, columns)
            }
            }>
          </Button>
        </Can>
      </Stack>
    ),
    renderCreateRowDialogContent: MissionEditForm,
    renderEditRowDialogContent: MissionEditForm,
    renderDetailPanel: ({ row }) => (
      <Box
        sx={{
          display: 'grid',
          margin: 'auto',
          //gridTemplateColumns: '1fr 1fr',
          width: '100vw',
        }}
      >
        <TabView>
          <TabPanel header={<Stack alignItems={'center'} spacing={2} direction={'row'}>
            <Can I='add' a='recommendation'><Button className='h-1rem' tooltip={"Ajouter/Editer les actions"} size='small' icon="pi pi-plus" onClick={() => { row._valuesCache.recommendations_add = true; setSelectedRecommendation(row.original.id); table.setEditingRow(row); setEditVisible(true), console.log("editing row ..."); }} /></Can>
            <div>{"Recommendations"}</div>
            <Badge style={{ top: -'4px' }} value={`${row.original.recommendations.length}`}></Badge>
          </Stack>
          } leftIcon={<i style={{ fontSize: '1.5rem' }} className="pi pi-thumbs-up  p-overlay-badge mr-2"></i>}>
            <DataTable
              dataKey="id"
              selectionMode={'single'}
              metaKeySelection={false}
              selection={selectedRecommendation}
              onRowSelect={(e) => { setSelectedRecommendation(e.data); }}
              // onSelectionChange={(e) => {console.log(e.value) ;setSelectedRecommendation(e.value);}}
              resizableColumns
              value={row.original.recommendations}
              size='small'
              stripedRows
              sortMode="multiple"
              rows={10}
              showGridlines
              paginator
              scrollable
              removableSort
              tableStyle={{ maxWidth: '70vw' }}
              emptyMessage={'Pas de recommendations.'}>
              <Column field="numrecommandation" filter header="N°" align={'center'} sortable />
              <Column field="recommendation" header="Description" sortable style={{ whiteSpace: 'normal', width: '35%' }} body={(data) => parse(data.recommendation)} />

              <Column field="constats" align={'center'} header="Constats" sortable style={{ width: '15%' }} body={(data) => data.constats?.map((val) => val.id).join(",")} />
              <Column field="concerned_structure.code_mnemonique" filter align={'center'} header="Structure Concerné" sortable body={(data) => <Tag className='w-9rem'>{data.concerned_structure.code_mnemonique || '/'}</Tag>} />
              <Column field="priority" align={'center'} filter header="Priorité" sortable style={{ width: '15%' }} />
              <Column field="responsible" className='font-bold' align={'center'} filter header="Responsable" sortable style={{ width: '15%' }} />
              <Column
                header="Action"
                align={'center'}
                style={{ width: '10%' }}
                body={(data) => (
                  <Stack direction={'column'} spacing={1}>
                    <Stack direction={'row'} spacing={1}>
                      <Button
                        disabled={data.accepted === true}
                        tooltip="Retenue"
                        placeholder="Mouse"
                        rounded
                        outlined
                        severity='success'
                        icon="pi pi-check"
                        onClick={() => {
                          trigger_recommandation_update({ id: data.id, data: { accepted: true } })
                        }}
                      />
                      <Button
                        disabled={data.accepted === null}
                        tooltip="Non Concerné"
                        placeholder="Mouse"
                        rounded
                        outlined
                        severity='warning'
                        icon="pi pi-question-circle"
                        onClick={(e) => {
                          // setRecommId(data.id)
                          setTriggerData({ accepted: null })
                          // setComment({
                          //   recommendation: data.id,
                          //   ...comment,
                          //   type: 'Non Concerné'
                          // })
                          overlayPanelRef.current.toggle(e);
                        }}
                      />
                      <Button
                        disabled={data.accepted === false}
                        tooltip="Non Retenue"
                        placeholder="Mouse"
                        rounded
                        outlined
                        severity={'danger'}
                        icon="pi pi-times"
                        onClick={(e) => {
                          setRecommId(data.id);
                          setTriggerData({ accepted: false, })
                          setComment({
                            recommendation: data.id,
                            ...comment,
                            type: 'Non Retenue'
                          })
                          overlayPanelRef.current.toggle(e);
                          // trigger_recommandation_update({ accepted: false }, { revalidate: true, populateCache: true })
                        }} />

                      <OverlayPanel showCloseIcon closeOnEscape dismissable={false} className='grid w-3' pt={{
                        content: { className: 'w-full h-18rem	' },
                        closeButton: {
                          style: {
                            left: '-1rem',
                            right: '0'
                          }
                        }
                      }} ref={overlayPanelRef} >
                        <div className='grid w-full'>
                          <div className='col-12 lg:col-12 xl:col-12 cursor-pointer'>
                            <label className='font-bold mb-2 pb-2'>Recommandation : {triggerData?.accepted === false ? 'Non Retenue' : 'Non Concerné'}</label>
                            <InputTextarea rows={5} className='w-full' placeholder='Commentaire'
                              onChange={(e) => setComment({ ...comment, comment: e.target.value })}
                            />
                          </div>
                          <div className='col-12 lg:col-12 xl:col-12 cursor-pointer'>
                            <label htmlFor='files' className='font-bold mb-2 pb-2'>Attachement</label>
                            <input id='files' type='file' multiple />
                          </div>
                          <div className='col-12 lg:col-12 xl:col-12 cursor-pointer'>
                            <Button className='w-full' icon='pi pi-save' onClick={(e) => {
                              trigger_recommandation_update({ id: data.id, data: { accepted: triggerData?.accepted } })
                              trigger_comments_create({
                                data: {
                                  ...comment, type: 'Non Retenue', recommendation: data.id,

                                }
                              })
                              overlayPanelRef.current.toggle(e);
                            }} />
                          </div>
                        </div>
                      </OverlayPanel>
                    </Stack>
                    <div>
                      <Chip className={`w-full ${data.accepted ? 'bg-green-300' : data.accepted === false ? 'bg-red-300' : 'bg-yellow-300'} text-lg border-1 border-dashed font-bold`}
                        label={data.accepted ? 'Retenue' : data.accepted === false ? 'Non retenue' : 'Non concerné'}
                        icon={data.accepted ? 'pi pi-check text-green-600 text-xl' : data.accepted === false ? 'pi pi-times text-red-600 text-xl' : 'pi pi-question-circle text-orange-600 text-xl'}>
                      </Chip>
                    </div>
                  </Stack>
                )}
              />
              <Column
                header="Commentaires"
                align={'center'}
                style={{ width: '10%' }}
                body={(data) => (
                  <Button rounded outlined severity='info' icon="pi pi-comments" onClick={(e) => {
                    setRecommId(data.id);
                    setRecommedantionCommentDialogVisible(true)
                  }}><Badge value={row.original.recommendations.find((rec: Recommendation) => rec.id === data.id)?.comments?.length ?? 0} severity="danger"></Badge></Button>
                )}
              />
            </DataTable>
            <span>{recomm_id}</span>
            <CommentRecommendationDialog
              visible={recommedantionCommentDialogVisible}
              setVisible={setRecommedantionCommentDialogVisible}
              recommendation_comments={row.original.recommendations.find((rec: Recommendation) => rec.id === recomm_id)?.comments ?? []} recommendation_id={recomm_id} />
          </TabPanel>
          <TabPanel header={<Stack alignItems={'center'} spacing={2} direction={'row'}><div>{data_.data_type.properties["staff"].title}</div> <Badge style={{ top: -'4px' }} value={`${row.original.staff.length + 2}`}></Badge></Stack>} leftIcon={<i style={{ fontSize: '1.5rem' }} className="pi pi-user  p-overlay-badge mr-2"></i>}>
            <div className='flex flex-column gap-1'>
              <span><b>Chef de mission</b> : {row.original.head?.first_name || 'N/D'} {row.original.head?.last_name}</span>
              <span><b>Superviseur</b> : {row.original.supervisor?.first_name || 'N/D'} {row.original.supervisor?.last_name}</span>
              <span><b>Equipe</b> :</span>
              {row.original.staff.length > 0 ? <ul>{row.original.staff.map((user, idx) => <a key={user.email + row.original.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul> : <span>Aucun membre de mission</span>}
            </div>
          </TabPanel>
          <TabPanel header={<Stack alignItems={'center'} spacing={2} direction={'row'}><div>{"Assistants"}</div> <Badge style={{ top: -'4px' }} value={`${row.original.assistants.length}`}></Badge></Stack>} leftIcon={<i style={{ fontSize: '1.5rem' }} className="pi pi-user  p-overlay-badge mr-2"></i>}>
            {row.original.assistants.length > 0 ? <ul>{row.original.assistants.map((user, idx) => <a key={user.email + row.original.code} href={"mailto:" + user.email}><li>{user.last_name} {user.first_name}</li></a>)}</ul> : <span>Aucun assistant</span>}
          </TabPanel>
          <TabPanel header={<Stack alignItems={'center'} spacing={3} direction={'row'}><div>{"Documents"}</div> <Badge style={{ top: -'4px' }} value={`${row.original.mission_docs.filter((doc: MissionDocument) => doc.context === 'MISSION').length}`}></Badge></Stack>} leftIcon="pi pi-file-word mr-2" rightIcon="pi pi-file-pdf ml-2" disabled={row.original.mission_docs.length === 0}>
            <Stack direction={'column'} spacing={1}>
              {row.original.mission_docs?.filter((doc: MissionDocument) => doc.context === 'MISSION').map((doc: MissionDocument) => (
                <Stack direction={'row'} spacing={2} alignItems={'center'} alignContent={'center'}>
                  <Button icon="pi pi-eye" rounded onClick={() => { setMissionDoc(doc); setVisible(true) }} />
                  <div>{doc.document.replace('http://10.39.107.98:3001/media/missions/', '')}</div>
                  <Can I='delete' a='missiondocument'>
                    <Button size='small'
                      icon="pi pi-trash"
                      severity='danger'
                      rounded
                      onClick={() => { setMissionDoc(doc); trigger_docs_delete({id:doc.id}) }}>

                    </Button>
                  </Can>
                </Stack>
              )
              )}
            </Stack>
            <Sidebar key={row.original.id}
              header={<h3>💼 Mission : {row.original.code} | 📄 {mission_doc?.document.replace('http://10.39.107.98:3001/media/missions/', '')}</h3>}
              visible={visible} onHide={() => setVisible(false)}
              className="w-full md:w-9 lg:w-7">
              <div className="h-full w-full flex flex-column justify-items-center align-content-center	align-items-center justify-content-center gap-1 pt-3">
                {(mission_doc?.document.includes("jpg") || mission_doc?.document.includes("webp")) ? <Image downloadable imageStyle={{ height: 'calc(100vh - 300px)', width: '50vw' }} src={mission_doc?.document}></Image> :
                  <Worker workerUrl="/pdfjs/pdf.worker.js">
                    <div style={{ height: 'calc(100vh - 100px)', width: 'calc(50vw - 100px)' }}>
                      <Viewer
                        localization={fr_FR as unknown as LocalizationMap}
                        fileUrl={mission_doc?.document}
                        theme={layoutConfig.colorScheme}
                        plugins={[
                          defaultLayoutPluginInstance,
                        ]}
                      />
                    </div>
                  </Worker>}
              </div>
            </Sidebar>
          </TabPanel>
        </TabView>
      </Box >
    ),
    renderRowActions: ({ cell, row, table }) => (
      <span className="p-buttonset flex p-1">
        <Button size='small' icon="pi pi-eye" onClick={() => { setMissionId(row.original.id); setDetailsDialogVisible(true) }} rounded outlined />
        {
          ability.can('change', 'mission') &&
          <Button size='small' icon="pi pi-pencil" onClick={() => { table.setEditingRow(row); setMissionId(row.original.id); setEditVisible(true), console.log("editing row ..."); }} rounded outlined />
        }
        {
          ability.can('add', 'missiondocument') &&
          <Button size='small' icon="pi pi-file-plus" onClick={function () {
            setMissionId(row.original.id); setMissionDocsDialogVisible(true), console.log("adding docs to mission ...");
          }} rounded outlined />
        }
        <Can I='delete_mission' a='mission'>
          <Button size='small' icon="pi pi-trash" rounded outlined
            onClick={(event) => confirmPopup({
              target: event.currentTarget,
              message: 'Voulez-vous supprimer cette ligne?',
              icon: 'pi pi-info-circle',
              // defaultFocus: 'reject',
              acceptClassName: 'p-button-danger',
              acceptLabel: 'Oui',
              rejectLabel: 'Non',
              accept,
              reject
            })}
          />
          <ConfirmPopup />
        </Can>
      </span>
      // </Box>
    ),
  });

  // Return the GenericDataTable component with the data
  return <GenericDataTable data_={data_} />;
}
