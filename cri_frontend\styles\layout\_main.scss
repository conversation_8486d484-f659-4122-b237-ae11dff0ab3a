* {
    box-sizing: border-box;
}

html {
    height: 100%;
}

body {
    font-family: var(--font-family);
    color: var(--text-color);
    background-color: var(--surface-ground);
    margin: 0;
    padding: 0;
    min-height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

.layout-wrapper {
    min-height: 100vh;
}

.p-sidebar .p-sidebar-header {
    padding-top: 1.0rem !important;
    padding-bottom: 1.0rem !important;
    padding-right: 4.0rem !important;
    padding-left: 4.0rem !important;
}

.field label {
    font-weight: bold;
}

.p-sidebar-custom-header {
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.02), 0px 0px 2px rgba(0, 0, 0, 0.05), 0px 1px 4px rgba(0, 0, 0, 0.08) !important;
}