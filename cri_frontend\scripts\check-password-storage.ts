import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkPasswordStorage() {
  console.log('🔍 Checking password storage in BetterAuth...\n')
  
  try {
    // Check users table
    console.log('👤 Users in database:')
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        isStaff: true,
        isSuperuser: true,
        createdAt: true,
      }
    })
    
    if (users.length === 0) {
      console.log('   No users found')
    } else {
      users.forEach(user => {
        console.log(`   - ${user.email} (${user.username}) - Staff: ${user.isStaff}, Super: ${user.isSuperuser}`)
      })
    }
    
    console.log('\n🔐 Accounts (where passwords are stored):')
    const accounts = await prisma.account.findMany({
      select: {
        id: true,
        userId: true,
        provider: true,
        type: true,
        password: true,
        createdAt: true,
        user: {
          select: {
            email: true,
            username: true,
          }
        }
      }
    })
    
    if (accounts.length === 0) {
      console.log('   No accounts found')
      console.log('   ❌ This means no passwords have been set yet!')
    } else {
      accounts.forEach(account => {
        const hasPassword = account.password ? '✅ Has password' : '❌ No password'
        console.log(`   - ${account.user.email} (${account.provider}) - ${hasPassword}`)
      })
    }
    
    console.log('\n🔑 Sessions:')
    const sessions = await prisma.session.findMany({
      select: {
        id: true,
        userId: true,
        expires: true,
        user: {
          select: {
            email: true,
          }
        }
      }
    })
    
    if (sessions.length === 0) {
      console.log('   No active sessions')
    } else {
      sessions.forEach(session => {
        const isExpired = session.expires < new Date() ? '(expired)' : '(active)'
        console.log(`   - ${session.user.email} ${isExpired}`)
      })
    }
    
    console.log('\n📊 Summary:')
    console.log(`   Users: ${users.length}`)
    console.log(`   Accounts: ${accounts.length}`)
    console.log(`   Sessions: ${sessions.length}`)
    console.log(`   Accounts with passwords: ${accounts.filter(a => a.password).length}`)
    
    if (accounts.filter(a => a.password).length === 0) {
      console.log('\n💡 To create an admin with password:')
      console.log('   1. Visit: http://localhost:3001/create-admin.html')
      console.log('   2. Or run: npm run admin:reset && visit the URL above')
    }
    
  } catch (error) {
    console.error('❌ Error checking password storage:', error)
  }
}

checkPasswordStorage()
  .catch((e) => {
    console.error('❌ Error:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
