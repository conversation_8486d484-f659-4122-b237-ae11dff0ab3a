/**
 * Enums matching the Prisma schema definitions
 * These enums provide TypeScript types and runtime values for dropdowns and forms
 */

// Plan Type Enum
export enum PlanType {
  AUDIT_INTERN = 'AUDIT_INTERN',
  CTRL_INTERN = 'CTRL_INTERN',
  HORS_PLAN = 'HORS_PLAN',
}

export const PlanTypeLabels = {
  [PlanType.AUDIT_INTERN]: 'Audit Interne',
  [PlanType.CTRL_INTERN]: 'Contrôle Interne',
  [PlanType.HORS_PLAN]: 'Hors Plan',
}

export const PlanTypeOptions = Object.values(PlanType).map(value => ({
  value,
  label: PlanTypeLabels[value],
  name: PlanTypeLabels[value],
  code: value,
}))

// Mission Type Enum
export enum MissionType {
  COMMANDED = 'COMMANDED',
  PLANIFIED = 'PLANIFIED',
  AVIS_CONSEIL = 'AVIS_CONSEIL',
}

export const MissionTypeLabels = {
  [MissionType.COMMANDED]: 'Commandée',
  [MissionType.PLANIFIED]: 'Planifiée',
  [MissionType.AVIS_CONSEIL]: 'Avis & Conseils',
}

export const MissionTypeOptions = Object.values(MissionType).map(value => ({
  value,
  label: MissionTypeLabels[value],
  name: MissionTypeLabels[value],
  code: value,
}))

// Mission Etat Enum
export enum MissionEtat {
  NotStarted = 'NotStarted',
  Suspended = 'Suspended',
  InProgress = 'InProgress',
  Closed = 'Closed',
}

export const MissionEtatLabels = {
  [MissionEtat.NotStarted]: 'Non Lancée',
  [MissionEtat.Suspended]: 'Suspendue',
  [MissionEtat.InProgress]: 'En cours',
  [MissionEtat.Closed]: 'Clôturée',
}

export const MissionEtatOptions = Object.values(MissionEtat).map(value => ({
  value,
  label: MissionEtatLabels[value],
  name: MissionEtatLabels[value],
  code: value,
}))

// Theme Proposing Entity Enum (This is the one used in editForm.tsx)
export enum ThemeProposingEntity {
  VP = 'VP',
  CI = 'CI',
  AI = 'AI',
  STRUCT = 'STRUCT',
}

export const ThemeProposingEntityLabels = {
  [ThemeProposingEntity.VP]: 'Vice Président',
  [ThemeProposingEntity.CI]: 'Contrôle Interne',
  [ThemeProposingEntity.AI]: 'Audit Interne',
  [ThemeProposingEntity.STRUCT]: 'Structures',
}

export const ThemeProposingEntityOptions = Object.values(ThemeProposingEntity).map(value => ({
  value,
  label: ThemeProposingEntityLabels[value],
  name: ThemeProposingEntityLabels[value],
  code: value,
}))

// Alias for backward compatibility with existing code
export const ProposedByEnum = ThemeProposingEntity
export const ProposedByEnumLabels = ThemeProposingEntityLabels
export const ProposedByEnumOptions = ThemeProposingEntityOptions

// Create enum-like object with .enum property for compatibility
export const $ProposedByEnum = {
  enum: Object.values(ThemeProposingEntity),
  labels: ThemeProposingEntityLabels,
  options: ThemeProposingEntityOptions,
}

// Recommendation Priority Enum
export enum RecommendationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
}

export const RecommendationPriorityLabels = {
  [RecommendationPriority.LOW]: 'FAIBLE',
  [RecommendationPriority.NORMAL]: 'NORMALE',
  [RecommendationPriority.HIGH]: 'ELEVEE',
}

export const RecommendationPriorityOptions = Object.values(RecommendationPriority).map(value => ({
  value,
  label: RecommendationPriorityLabels[value],
  name: RecommendationPriorityLabels[value],
  code: value,
}))

// Recommendation Etat Enum
export enum RecommendationEtat {
  Accomplished = 'Accomplished',
  NotAccomplished = 'NotAccomplished',
  InProgress = 'InProgress',
}

export const RecommendationEtatLabels = {
  [RecommendationEtat.Accomplished]: 'Réalisée',
  [RecommendationEtat.NotAccomplished]: 'Non Réalisée',
  [RecommendationEtat.InProgress]: 'En cours',
}

export const RecommendationEtatOptions = Object.values(RecommendationEtat).map(value => ({
  value,
  label: RecommendationEtatLabels[value],
  name: RecommendationEtatLabels[value],
  code: value,
}))

// Action Etat Enum
export enum ActionEtat {
  Accomplished = 'Accomplished',
  NotAccomplished = 'NotAccomplished',
  InProgress = 'InProgress',
}

export const ActionEtatLabels = {
  [ActionEtat.Accomplished]: 'Réalisée',
  [ActionEtat.NotAccomplished]: 'Non Réalisée',
  [ActionEtat.InProgress]: 'En cours',
}

export const ActionEtatOptions = Object.values(ActionEtat).map(value => ({
  value,
  label: ActionEtatLabels[value],
  name: ActionEtatLabels[value],
  code: value,
}))

// Document Type Enum
export enum DocumentType {
  MISSION = 'MISSION',
  ACTION = 'ACTION',
  RECOMMENDATION = 'RECOMMENDATION',
}

export const DocumentTypeLabels = {
  [DocumentType.MISSION]: 'MISSION',
  [DocumentType.ACTION]: 'ACTION',
  [DocumentType.RECOMMENDATION]: 'RECOMMENDATION',
}

export const DocumentTypeOptions = Object.values(DocumentType).map(value => ({
  value,
  label: DocumentTypeLabels[value],
  name: DocumentTypeLabels[value],
  code: value,
}))

// Validation Enum
export enum ValidationEnum {
  YES = 'YES',
  NO = 'NO',
  AC = 'AC',
  NA = 'NA',
}

export const ValidationEnumLabels = {
  [ValidationEnum.YES]: 'Validé',
  [ValidationEnum.NO]: 'Non Validé',
  [ValidationEnum.AC]: 'Accepté',
  [ValidationEnum.NA]: 'Non Accepté',
}

export const ValidationEnumOptions = Object.values(ValidationEnum).map(value => ({
  value,
  label: ValidationEnumLabels[value],
  name: ValidationEnumLabels[value],
  code: value,
}))

// Recommendation Action Type Enum
export enum RecommendationActionType {
  accepted = 'accepted',
  not_accepted = 'not_accepted',
  not_concerned = 'not_concerned',
}

export const RecommendationActionTypeLabels = {
  [RecommendationActionType.accepted]: 'Retenue',
  [RecommendationActionType.not_accepted]: 'Non Retenue',
  [RecommendationActionType.not_concerned]: 'Non Concerné',
}

export const RecommendationActionTypeOptions = Object.values(RecommendationActionType).map(value => ({
  value,
  label: RecommendationActionTypeLabels[value],
  name: RecommendationActionTypeLabels[value],
  code: value,
}))

// Export all enums for easy access
export const enums = {
  PlanType,
  MissionType,
  MissionEtat,
  ThemeProposingEntity,
  ProposedByEnum,
  RecommendationPriority,
  RecommendationEtat,
  ActionEtat,
  DocumentType,
  ValidationEnum,
  RecommendationActionType,
}

// Export all labels for easy access
export const enumLabels = {
  PlanType: PlanTypeLabels,
  MissionType: MissionTypeLabels,
  MissionEtat: MissionEtatLabels,
  ThemeProposingEntity: ThemeProposingEntityLabels,
  ProposedByEnum: ProposedByEnumLabels,
  RecommendationPriority: RecommendationPriorityLabels,
  RecommendationEtat: RecommendationEtatLabels,
  ActionEtat: ActionEtatLabels,
  DocumentType: DocumentTypeLabels,
  ValidationEnum: ValidationEnumLabels,
  RecommendationActionType: RecommendationActionTypeLabels,
}

// Export all options for easy access
export const enumOptions = {
  PlanType: PlanTypeOptions,
  MissionType: MissionTypeOptions,
  MissionEtat: MissionEtatOptions,
  ThemeProposingEntity: ThemeProposingEntityOptions,
  ProposedByEnum: ProposedByEnumOptions,
  RecommendationPriority: RecommendationPriorityOptions,
  RecommendationEtat: RecommendationEtatOptions,
  ActionEtat: ActionEtatOptions,
  DocumentType: DocumentTypeOptions,
  ValidationEnum: ValidationEnumOptions,
  RecommendationActionType: RecommendationActionTypeOptions,
}
