'use client'
import { MissionDocument, Plan, Recommendation, User } from '@/services/schemas';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { Button } from 'primereact/button';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useContext, useRef, useState } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import parse from 'html-react-parser';
import {
  useApiCommentCreate,
  useApiDocsCreate,
  useApiDocsDestroy,
  useApiMissionCreate,
  useApiMissionPartialUpdate,
  useApiRecommendationPartialUpdate,
  useApiMissionDocsCreate
} from '@/hooks/useNextApi';
import { LocalizationMap, Viewer, Worker } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import fr_FR from '@react-pdf-viewer/locales/lib/fr_FR.json';
import { getCookie } from 'cookies-next';
import { Chip } from 'primereact/chip';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { OverlayPanel } from 'primereact/overlaypanel';
import MissionDetails from '../[mission_id]/page';
import MissionEditForm from './editForm';
import { Badge } from 'primereact/badge';
import CommentRecommendationDialog from './CommentRecommendationDialog';
import { getMissionEtatSeverity, getMissionTypeSeverity, handleExportRows } from '@/utilities/functions/utils';
// Remove this import as we'll define the type locally
import { LayoutContext } from '@/layout/context/layoutcontext';
import { useLoadingSpinner } from '@/utilities/hooks/useSpinner';
import { useToast } from '@/utilities/hooks/useToast';
import { usebaseData } from '@/utilities/hooks/useBaseData';
import { FileUpload, FileUploadHandlerEvent, FileUploadHeaderTemplateOptions, FileUploadSelectEvent, ItemTemplateOptions } from 'primereact/fileupload';
import { Image } from 'primereact/image';
import { ProgressSpinner } from 'primereact/progressspinner';
import { classNames } from 'primereact/utils';
import Link from 'next/link';
import { Can } from '@/app/Can';
import ability from '@/app/ability';
import { useQueryClient } from '@tanstack/react-query';
import { InputText } from 'primereact/inputtext';
import { FilterMatchMode, FilterOperator } from 'primereact/api';

export default function GenericTable<T>(data_: { isLoading: any; data_: any, error: any, data_type: any | undefined, pagination: any, mutate: any }) {
  const user = JSON.parse(getCookie('user')?.toString() || '{}')
  const defaultLayoutPluginInstance = defaultLayoutPlugin();
  const { layoutConfig } = useContext(LayoutContext);
  const { setLoadingSpinner } = useLoadingSpinner()
  const toastRef = useToast()
  const { plans } = usebaseData()
  const queryClient = useQueryClient();
  const toast = useRef<Toast>(null);

  // State
  const [filters, setFilters] = useState({
    global: { value: '', matchMode: FilterMatchMode.CONTAINS },
    code: { value: '', matchMode: FilterMatchMode.STARTS_WITH },
    type: { value: '', matchMode: FilterMatchMode.EQUALS },
    etat: { value: '', matchMode: FilterMatchMode.EQUALS },
    exercise: { value: '', matchMode: FilterMatchMode.EQUALS },
  });
  const [globalFilterValue, setGlobalFilterValue] = useState('');
  const [selectedMission, setSelectedMission] = useState(null);
  const [mission_id, setMissionId] = useState(0);
  const [recomm_id, setRecommId] = useState(0);
  const [recommedantionCommentDialogVisible, setRecommedantionCommentDialogVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [detailsDialogVisible, setDetailsDialogVisible] = useState(false);
  const [missionDocsDialogVisible, setMissionDocsDialogVisible] = useState(false);
  const [mission_doc, setMissionDoc] = useState<MissionDocument | null>(null);
  const [comment, setComment] = useState({});
  const [triggerData, setTriggerData] = useState(null);
  const [selectedRecommendation, setSelectedRecommendation] = useState(null);

  // Refs
  const fileUploadRef = useRef<FileUpload>(null);
  const overlayPanelRef = useRef<OverlayPanel>(null);

  // API hooks - Updated for Next.js API
  const { mutate: trigger_mission_docs_create } = useApiMissionDocsCreate()

  // Functions
  const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setGlobalFilterValue(value);

    let _filters = { ...filters };
    _filters['global'].value = value;
    setFilters(_filters);
  };

  const clearFilter = () => {
    setFilters({
      global: { value: '', matchMode: FilterMatchMode.CONTAINS },
      code: { value: '', matchMode: FilterMatchMode.STARTS_WITH },
      type: { value: '', matchMode: FilterMatchMode.EQUALS },
      etat: { value: '', matchMode: FilterMatchMode.EQUALS },
      exercise: { value: '', matchMode: FilterMatchMode.EQUALS },
    });
    setGlobalFilterValue('');
  };

  const customUploader = (event: FileUploadHandlerEvent) => {
    // Create FormData for file upload
    const formData = new FormData()
    formData.append('context', 'MISSION')
    formData.append('description', 'Mission document')
    event.files.forEach((file, index) => {
      formData.append(`document_${index}`, file)
    })
    trigger_mission_docs_create({ missionId: mission_id, data: formData })
  };

  const onTemplateRemove = (file: any, callback: () => void) => {
    callback();
  };

  const headerTemplate = (options: FileUploadHeaderTemplateOptions) => {
    const { className, chooseButton, uploadButton, cancelButton } = options;
    return (
      <div className={className} style={{ backgroundColor: 'transparent', display: 'flex', alignItems: 'center' }}>
        <Button style={{ width: '38px' }} rounded severity='success' icon='pi pi-fw pi-images' onClick={chooseButton.props.onClick}></Button>
        <Button style={{ width: '38px' }} rounded severity='danger' icon='pi pi-fw pi-times' onClick={cancelButton.props.onClick}></Button>
      </div>
    );
  };

  const itemTemplate = (file: object & File, props: ItemTemplateOptions) => {
    return (
      <div className="flex align-items-center flex-wrap">
        <div className="flex align-items-center gap-4" style={{ width: '40%' }}>
          <Link target="_blank" href={file.objectURL} ><img alt={file.name} role="presentation" src={file.type.includes('image') ? file.objectURL : '/images/pdf.webp'} width={50} /></Link>
          <span className="flex flex-column text-left ml-3">
            {file.name}
            <small>{new Date().toLocaleDateString()}</small>
          </span>
        </div>
        <Tag value={props.formatSize} severity={fileUploadRef.current?.getUploadedFiles().includes(file) ? "success" : "warning"} className="px-3 py-2" />
        {fileUploadRef.current?.state?.uploading && <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />}
        <Button type="button" icon="pi pi-times" className="p-button-outlined p-button-rounded p-button-danger ml-auto" onClick={() => onTemplateRemove(file, props.onRemove)} />
      </div>
    );
  };

  const accept = () => {
    toastRef.current.show({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 });
  };

  const reject = () => {
    toast.current.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
  };

  const renderHeader = () => {
    return (
      <div className="flex justify-content-between">
        <Button type="button" icon="pi pi-filter-slash" label="Clear" outlined onClick={clearFilter} />
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText value={globalFilterValue} onChange={onGlobalFilterChange} placeholder="Keyword Search" />
        </span>
      </div>
    );
  };

  const header = renderHeader();

  const actionBodyTemplate = (rowData) => {
    return (
      <div className="flex gap-2 justify-content-center">
        <Button icon="pi pi-eye" rounded outlined severity="info" onClick={() => { setMissionId(rowData.id); setDetailsDialogVisible(true) }} />
        <Can I="edit" a="mission">
          <Button icon="pi pi-pencil" rounded outlined severity="success" onClick={() => {
            trigger_mission_update({ id: rowData.id, data: { ...rowData } })
          }} />
        </Can>
        <Can I="delete" a="mission">
          <Button icon="pi pi-trash" rounded outlined severity="danger" onClick={(event) => confirmPopup({
            target: event.currentTarget,
            message: 'Voulez-vous supprimer cette ligne?',
            icon: 'pi pi-info-circle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Oui',
            rejectLabel: 'Non',
            accept,
            reject
          })} />
        </Can>
        <Button icon="pi pi-file-plus" rounded outlined onClick={() => { setMissionId(rowData.id); setMissionDocsDialogVisible(true) }} />
      </div>
    );
  };

  const typeBodyTemplate = (rowData) => {
    return <Tag severity={getMissionTypeSeverity(rowData.type)} value={rowData.type} />;
  };

  const etatBodyTemplate = (rowData) => {
    return <Tag severity={getMissionEtatSeverity(rowData.etat)} value={rowData.etat} />;
  };

  const exerciseBodyTemplate = (rowData) => {
    return rowData.exercise ? <Tag severity="success" value={rowData.exercise} /> : '/';
  };

  const planBodyTemplate = (rowData) => {
    return rowData.plan ? <Tag severity="info" value={rowData.plan} /> : '/';
  };

  const codeBodyTemplate = (rowData) => {
    return <Tag style={{ fontSize: 12, fontFamily: "monospace", color: 'var(--text-color)', background: 'transparent', border: ' 2px solid orange' }} value={rowData.code} />;
  };

  const structuresBodyTemplate = (rowData) => {
    return (
      <div className="flex flex-wrap gap-1">
        {rowData.controled_structures.map((val) => (
          <Tag
            key={val.id}
            style={{ fontSize: 12, fontFamily: "monospace", color: 'var(--text-color)', background: 'transparent', border: ' 2px dotted green', borderRadius: 50 }}
            severity="success"
            value={val.code_mnemonique || 'N/D'}
          />
        ))}
      </div>
    );
  };

  // Render the DataTable
  return (
    <>
      <Toast ref={toast} />
      <ConfirmPopup />

      <DataTable
        value={data_.data_?.data.results || []}
        paginator
        rows={10}
        rowsPerPageOptions={[5, 10, 25, 50]}
        tableStyle={{ minWidth: '50rem' }}
        selectionMode="single"
        selection={selectedMission}
        onSelectionChange={(e) => setSelectedMission(e.value)}
        dataKey="id"
        filters={filters}
        filterDisplay="menu"
        loading={data_.isLoading}
        responsiveLayout="scroll"
        globalFilterFields={['code', 'type', 'etat', 'exercise', 'plan', 'theme.theme.title']}
        header={header}
        emptyMessage="No missions found."
        resizableColumns
        columnResizeMode="fit"
        showGridlines
        stripedRows
        size="small"
        scrollable
        scrollHeight="400px"
      >
        <Column field="code" header="Code" body={codeBodyTemplate} sortable filter filterPlaceholder="Search by code" />
        <Column field="type" header="Type" body={typeBodyTemplate} sortable filter filterPlaceholder="Search by type" />
        <Column field="etat" header="État" body={etatBodyTemplate} sortable filter filterPlaceholder="Search by état" />
        <Column field="exercise" header="Exercice" body={exerciseBodyTemplate} sortable filter filterPlaceholder="Search by exercise" />
        <Column field="plan" header="Plan" body={planBodyTemplate} sortable filter filterPlaceholder="Search by plan" />
        <Column field="controled_structures" header="Structures" body={structuresBodyTemplate} />
        <Column field="theme.theme.title" header="Thème" sortable filter filterPlaceholder="Search by theme" />
        <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '12rem' }} />
      </DataTable>

      <Dialog maximizable dismissableMask header={`Mission ID : ${mission_id}`} visible={detailsDialogVisible} style={{ width: '80vw' }} onHide={() => { if (!detailsDialogVisible) return; setDetailsDialogVisible(false); }}>
        <MissionDetails props={{ mission_id: mission_id, mission: data_.data_?.data.results.find((mission_: MissionSerializerRead) => mission_.id === mission_id) }} />
      </Dialog>

      <Dialog maximizable
        dismissableMask
        header={`Ajouter/Editer documents mission : ${data_.data_?.data.results.find((mission_: MissionSerializerRead) => mission_.id === mission_id)?.code ?? 'N/D'}`}
        visible={missionDocsDialogVisible} style={{ width: '60vw' }}
        onHide={() => { if (!missionDocsDialogVisible) return; setMissionDocsDialogVisible(false); }}
        onShow={() => {
          fileUploadRef.current?.setUploadedFiles(data_.data_?.data?.results?.find(mission => mission.id === mission_id).mission_docs?.map((doc: MissionDocument) => {
            return {
              objectURL: doc.document,
              id: doc.id,
              name: doc.name,
              type: doc.type,
              size: doc.size,
              lastModified: new Date(doc.modified).getMilliseconds(),
              webkitRelativePath: ''
            } as unknown as File
          }))
        }}
      >
        <div className="field col-12">
          <div className="flex flex-column">
            <label htmlFor="mission_docs">Documents</label>
            <FileUpload id="mission_docs"
              ref={fileUploadRef}
              name="docs[]"
              mode='advanced'
              multiple
              accept="image/*,application/pdf"
              maxFileSize={1000000000}
              emptyTemplate={<p className="m-0">Drag and drop files to here to upload.</p>}
              itemTemplate={itemTemplate}
              uploadHandler={customUploader}
              customUpload={true}
              cancelOptions={{ style: { display: 'none' } }}
            />
          </div>
        </div>
      </Dialog>
    </>
  );
}
