self.__RSC_MANIFEST="{\n  \"ssrModuleMapping\": {\n    \"(app-client)/./node_modules/next/dist/client/components/error-boundary.js\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./node_modules/next/dist/client/components/error-boundary.js\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./node_modules/next/dist/client/components/app-router.js\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./node_modules/next/dist/client/components/app-router.js\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./node_modules/next/dist/client/components/layout-router.js\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./node_modules/next/dist/client/components/layout-router.js\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./node_modules/next/dist/client/components/render-from-template-context.js\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./node_modules/next/dist/client/components/render-from-template-context.js\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./app/layout.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./app/layout.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./layout/layout.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./layout/layout.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./app/login/page.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./app/login/page.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./app/(main)/page.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./app/(main)/page.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./app/(main)/missions/page.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./app/(main)/missions/page.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./app/(main)/recommendations/followup/page.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./app/(main)/recommendations/followup/page.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./app/(main)/recommendations/[recommendation_id]/page.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./app/(main)/recommendations/[recommendation_id]/page.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./app/(main)/plans/page.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./app/(main)/plans/page.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    },\n    \"(app-client)/./app/(main)/plans/arbitrations/page.tsx\": {\n      \"*\": {\n        \"id\": \"(sc_client)/./app/(main)/plans/arbitrations/page.tsx\",\n        \"name\": \"*\",\n        \"chunks\": [],\n        \"async\": false\n      }\n    }\n  },\n  \"edgeSSRModuleMapping\": {},\n  \"clientModules\": {\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\error-boundary.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/error-boundary.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\error-boundary.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/error-boundary.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\app-router.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/app-router.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\app-router.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/app-router.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\layout-router.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/layout-router.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\layout-router.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/layout-router.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\render-from-template-context.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/render-from-template-context.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\render-from-template-context.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/render-from-template-context.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\static-generation-searchparams-bailout-provider.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\node_modules\\\\next\\\\dist\\\\esm\\\\client\\\\components\\\\static-generation-searchparams-bailout-provider.js\": {\n      \"id\": \"(app-client)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app-client-internals:static/chunks/app-client-internals.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout.tsx\": {\n      \"id\": \"(app-client)/./app/layout.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/layout:static/chunks/app/layout.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\layout\\\\layout.tsx\": {\n      \"id\": \"(app-client)/./layout/layout.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/(main)/layout:static/chunks/app/(main)/layout.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\login\\\\page.tsx\": {\n      \"id\": \"(app-client)/./app/login/page.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/login/page:static/chunks/app/login/page.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page.tsx\": {\n      \"id\": \"(app-client)/./app/(main)/page.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/(main)/page:static/chunks/app/(main)/page.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\missions\\\\page.tsx\": {\n      \"id\": \"(app-client)/./app/(main)/missions/page.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/(main)/missions/page:static/chunks/app/(main)/missions/page.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\followup\\\\page.tsx\": {\n      \"id\": \"(app-client)/./app/(main)/recommendations/followup/page.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/(main)/recommendations/followup/page:static/chunks/app/(main)/recommendations/followup/page.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\": {\n      \"id\": \"(app-client)/./app/(main)/recommendations/[recommendation_id]/page.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/(main)/recommendations/[recommendation_id]/page:static/chunks/app/(main)/recommendations/[recommendation_id]/page.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\page.tsx\": {\n      \"id\": \"(app-client)/./app/(main)/plans/page.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/(main)/plans/page:static/chunks/app/(main)/plans/page.js\"\n      ],\n      \"async\": false\n    },\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\arbitrations\\\\page.tsx\": {\n      \"id\": \"(app-client)/./app/(main)/plans/arbitrations/page.tsx\",\n      \"name\": \"*\",\n      \"chunks\": [\n        \"app/(main)/plans/arbitrations/page:static/chunks/app/(main)/plans/arbitrations/page.js\"\n      ],\n      \"async\": false\n    }\n  },\n  \"entryCSSFiles\": {\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\layout\": [\n      \"static/css/app/layout.css\"\n    ],\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\layout\": [],\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\login\\\\page\": [],\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\page\": [],\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\missions\\\\page\": [\n      \"static/css/app/(main)/missions/page.css\"\n    ],\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\followup\\\\page\": [],\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page\": [],\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\page\": [],\n    \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\arbitrations\\\\page\": []\n  }\n}"