from rest_framework.pagination import PageNumberPagination,LimitOffsetPagination

class StandardResultsSetPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 1000

class Unpaginated(LimitOffsetPagination):
    count =9999999
    limit = 9999999
    offset = 0
    def paginate_queryset(self, queryset, request, view=None):
        self.count = 9999999
        self.limit = 9999999
        self.offset = 0
        self.request = request
        self.display_page_controls = False

        return list(queryset)
    
class MultiplePaginationMixin:
    def get_pagination_class(self):
        return self.pagination_class

    @property
    def paginator(self):
        pagination_class = self.get_pagination_class()
        if pagination_class is None:
            return None
        return pagination_class()