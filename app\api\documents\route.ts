import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

// POST /api/documents - Upload documents
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const missionId = formData.get('mission') as string
    const context = formData.get('context') as string || 'MISSION'
    const description = formData.get('description') as string || ''
    
    if (!missionId) {
      return NextResponse.json(
        { error: 'Mission ID is required' },
        { status: 400 }
      )
    }

    // Verify mission exists
    const mission = await prisma.mission.findUnique({
      where: { id: parseInt(missionId) }
    })

    if (!mission) {
      return NextResponse.json(
        { error: 'Mission not found' },
        { status: 404 }
      )
    }

    const uploadedDocuments = []

    // Process each file
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('document') && value instanceof File) {
        const file = value as File
        
        if (file.size === 0) continue

        // Create upload directory if it doesn't exist
        const uploadDir = join(process.cwd(), 'public', 'uploads', 'missions', missionId)
        if (!existsSync(uploadDir)) {
          await mkdir(uploadDir, { recursive: true })
        }

        // Generate unique filename
        const timestamp = Date.now()
        const fileExtension = file.name.split('.').pop()
        const fileName = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`
        const filePath = join(uploadDir, fileName)
        const relativePath = `/uploads/missions/${missionId}/${fileName}`

        // Save file to disk
        const bytes = await file.arrayBuffer()
        const buffer = Buffer.from(bytes)
        await writeFile(filePath, buffer)

        // Save document record to database
        const document = await prisma.missionDocument.create({
          data: {
            document: relativePath,
            size: BigInt(file.size),
            name: file.name,
            type: file.type,
            missionId: parseInt(missionId),
            context: context as any, // DocumentType enum
            description,
          }
        })

        uploadedDocuments.push({
          ...document,
          size: document.size.toString(), // Convert BigInt to string for JSON
        })
      }
    }

    return NextResponse.json({
      message: 'Documents uploaded successfully',
      documents: uploadedDocuments,
    }, { status: 201 })

  } catch (error) {
    console.error('Error uploading documents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/documents - Get documents with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const missionId = searchParams.get('missionId')
    const context = searchParams.get('context')
    
    const where: any = {}
    
    if (missionId) {
      where.missionId = parseInt(missionId)
    }
    
    if (context) {
      where.context = context
    }
    
    const documents = await prisma.missionDocument.findMany({
      where,
      include: {
        mission: {
          select: {
            id: true,
            code: true,
            type: true,
          }
        }
      },
      orderBy: { created: 'desc' }
    })
    
    // Convert BigInt to string for JSON serialization
    const serializedDocuments = documents.map(doc => ({
      ...doc,
      size: doc.size?.toString() || '0',
    }))
    
    return NextResponse.json({
      data: {
        results: serializedDocuments,
        count: serializedDocuments.length,
      }
    })
  } catch (error) {
    console.error('Error fetching documents:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
