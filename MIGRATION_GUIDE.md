# Django to Next.js App Router + Prisma Migration Guide

## Overview
This guide outlines the migration from Django backend to Next.js App Router API with Prisma ORM.

## What's Been Created

### 1. Prisma Schema (`prisma/schema.prisma`)
- Complete database schema based on Django models
- All relationships and constraints preserved
- Enums for choice fields
- Proper field mappings and indexes

### 2. Next.js API Routes (`app/api/`)
- **Missions API**: `/api/missions` and `/api/missions/[id]`
- **Recommendations API**: `/api/recommendations` and `/api/recommendations/[id]`
- **Users API**: `/api/users`
- **Plans API**: `/api/plans`
- **Themes API**: `/api/themes`

### 3. API Service Layer (`services/api/nextApi.ts`)
- Centralized API service to replace Django API calls
- Consistent error handling
- Authentication token management

### 4. React Hooks (`hooks/useNextApi.ts`)
- Drop-in replacements for Django API hooks
- Uses React Query for caching and state management
- Same interface as existing hooks

### 5. Database Client (`lib/prisma.ts`)
- Prisma client instance with connection pooling
- Development/production environment handling

## Migration Steps

### Step 1: Database Setup
1. Update `.env` file with your PostgreSQL connection string:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/cri_database?schema=public"
   ```

2. Run Prisma migrations:
   ```bash
   npx prisma db push
   ```

3. Generate Prisma client:
   ```bash
   npx prisma generate
   ```

### Step 2: Update Component Imports
Replace Django API imports with Next.js API imports:

**Before:**
```typescript
import { useApiMissionCreate, useApiMissionPartialUpdate } from '@/services/api/api/api';
```

**After:**
```typescript
import { useApiMissionCreate, useApiMissionPartialUpdate } from '@/hooks/useNextApi';
```

### Step 3: Update API Calls
The hook interfaces remain the same, so most components should work without changes:

```typescript
// This remains the same
const { data, isPending, error, mutate: trigger_mission_create } = useApiMissionCreate({
  axios: { headers: { Authorization: `Token ${user?.token}` } }
})
```

### Step 4: Data Structure Changes
Some minor adjustments may be needed for data structures:

1. **BigInt fields**: Some IDs are now BigInt (like `concernedStructureId`)
2. **Date fields**: Ensure proper date formatting
3. **Enum values**: Check enum mappings in schema

## API Endpoints Mapping

| Django Endpoint | Next.js Endpoint | Methods |
|----------------|------------------|---------|
| `/api/mission/` | `/api/missions` | GET, POST |
| `/api/mission/{id}/` | `/api/missions/{id}` | GET, PATCH, DELETE |
| `/api/recommendation/` | `/api/recommendations` | GET, POST |
| `/api/recommendation/{id}/` | `/api/recommendations/{id}` | GET, PATCH, DELETE |
| `/api/users/` | `/api/users` | GET, POST |
| `/api/plan/` | `/api/plans` | GET, POST |
| `/api/theme/` | `/api/themes` | GET, POST |

## Key Features Preserved

### 1. Pagination
```typescript
// Same interface as Django
const missions = await nextApiService.getMissions({
  page: 1,
  limit: 10,
  search: 'query'
})
```

### 2. Filtering and Search
```typescript
// Search across multiple fields
const recommendations = await nextApiService.getRecommendations({
  search: 'audit',
  missionId: 123
})
```

### 3. Relationships
All Django relationships are preserved:
- Foreign keys
- Many-to-many relationships
- Reverse relationships through includes

### 4. Authentication
Token-based authentication is maintained:
```typescript
// Automatically includes auth token from cookies
const response = await nextApiService.getMissions()
```

## Testing the Migration

### 1. Start the Development Server
```bash
npm run dev
```

### 2. Test API Endpoints
Use tools like Postman or curl to test endpoints:
```bash
curl http://localhost:3000/api/missions
```

### 3. Check Database Connection
```bash
npx prisma studio
```

## Rollback Plan
If issues arise, you can:
1. Keep Django backend running on different port
2. Switch imports back to Django API
3. Update environment variables to point to Django

## Performance Benefits
- **Faster API responses**: No network overhead between frontend and backend
- **Better caching**: React Query with optimistic updates
- **Type safety**: Full TypeScript support with Prisma
- **Reduced complexity**: Single codebase for frontend and API

## Next Steps
1. Migrate remaining API endpoints (comments, documents, etc.)
2. Add authentication middleware
3. Implement file upload handling
4. Add API rate limiting
5. Set up database migrations workflow

## Troubleshooting

### Common Issues
1. **Prisma Client not found**: Run `npx prisma generate`
2. **Database connection errors**: Check DATABASE_URL in .env
3. **Type errors**: Ensure Prisma client is up to date
4. **Missing relationships**: Check include statements in API calls

### Debug Mode
Enable Prisma query logging:
```typescript
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})
```
