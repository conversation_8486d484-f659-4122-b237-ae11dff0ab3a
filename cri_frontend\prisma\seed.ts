import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create permissions
  const permissions = [
    // Mission permissions
    { action: 'create', subject: 'Mission' },
    { action: 'read', subject: 'Mission' },
    { action: 'update', subject: 'Mission' },
    { action: 'delete', subject: 'Mission' },
    { action: 'manage', subject: 'Mission' },

    // Recommendation permissions
    { action: 'create', subject: 'Recommendation' },
    { action: 'read', subject: 'Recommendation' },
    { action: 'update', subject: 'Recommendation' },
    { action: 'delete', subject: 'Recommendation' },
    { action: 'manage', subject: 'Recommendation' },

    // Plan permissions
    { action: 'create', subject: 'Plan' },
    { action: 'read', subject: 'Plan' },
    { action: 'update', subject: 'Plan' },
    { action: 'delete', subject: 'Plan' },
    { action: 'manage', subject: 'Plan' },

    // Theme permissions
    { action: 'create', subject: 'Theme' },
    { action: 'read', subject: 'Theme' },
    { action: 'update', subject: 'Theme' },
    { action: 'delete', subject: 'Theme' },
    { action: 'manage', subject: 'Theme' },

    // User permissions
    { action: 'create', subject: 'User' },
    { action: 'read', subject: 'User' },
    { action: 'update', subject: 'User' },
    { action: 'delete', subject: 'User' },
    { action: 'manage', subject: 'User' },

    // Document permissions
    { action: 'create', subject: 'MissionDocument' },
    { action: 'read', subject: 'MissionDocument' },
    { action: 'update', subject: 'MissionDocument' },
    { action: 'delete', subject: 'MissionDocument' },
    { action: 'add', subject: 'MissionDocument' },

    // Comment permissions
    { action: 'create', subject: 'Comment' },
    { action: 'read', subject: 'Comment' },
    { action: 'update', subject: 'Comment' },
    { action: 'delete', subject: 'Comment' },

    // Action permissions
    { action: 'create', subject: 'Action' },
    { action: 'read', subject: 'Action' },
    { action: 'update', subject: 'Action' },
    { action: 'delete', subject: 'Action' },

    // Domain and Process permissions
    { action: 'create', subject: 'Domain' },
    { action: 'read', subject: 'Domain' },
    { action: 'update', subject: 'Domain' },
    { action: 'delete', subject: 'Domain' },

    { action: 'create', subject: 'Process' },
    { action: 'read', subject: 'Process' },
    { action: 'update', subject: 'Process' },
    { action: 'delete', subject: 'Process' },

    // Arbitration permissions
    { action: 'create', subject: 'Arbitration' },
    { action: 'read', subject: 'Arbitration' },
    { action: 'update', subject: 'Arbitration' },
    { action: 'delete', subject: 'Arbitration' },

    // Legacy permissions for existing components
    { action: 'change', subject: 'Mission' },
    { action: 'edit', subject: 'Mission' },
    { action: 'change', subject: 'Plan' },
    { action: 'edit', subject: 'Plan' },
    { action: 'change', subject: 'Recommendation' },
    { action: 'edit', subject: 'Recommendation' },
  ]

  console.log('Creating permissions...')
  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: {
        action_subject: {
          action: permission.action,
          subject: permission.subject,
        },
      },
      update: {},
      create: permission,
    })
  }

  // Create roles
  const roles = [
    {
      name: 'Super Admin',
      description: 'Full system access',
    },
    {
      name: 'Admin',
      description: 'Administrative access to most features',
    },
    {
      name: 'Mission Manager',
      description: 'Can manage missions and related entities',
    },
    {
      name: 'Auditor',
      description: 'Can create and manage audit missions',
    },
    {
      name: 'Viewer',
      description: 'Read-only access to most content',
    },
    {
      name: 'Staff',
      description: 'Basic staff permissions',
    },
  ]

  console.log('Creating roles...')
  const createdRoles = []
  for (const role of roles) {
    const createdRole = await prisma.role.upsert({
      where: { name: role.name },
      update: {},
      create: role,
    })
    createdRoles.push(createdRole)
  }

  // Assign permissions to roles
  console.log('Assigning permissions to roles...')

  // Super Admin gets all permissions
  const superAdminRole = createdRoles.find(r => r.name === 'Super Admin')
  if (superAdminRole) {
    const allPermissions = await prisma.permission.findMany()
    for (const permission of allPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: superAdminRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: superAdminRole.id,
          permissionId: permission.id,
        },
      })
    }
  }

  // Admin gets most permissions except user management
  const adminRole = createdRoles.find(r => r.name === 'Admin')
  if (adminRole) {
    const adminPermissions = await prisma.permission.findMany({
      where: {
        NOT: {
          AND: [
            { subject: 'User' },
            { action: { in: ['create', 'delete'] } },
          ],
        },
      },
    })
    for (const permission of adminPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: adminRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: adminRole.id,
          permissionId: permission.id,
        },
      })
    }
  }

  // Mission Manager gets mission-related permissions
  const missionManagerRole = createdRoles.find(r => r.name === 'Mission Manager')
  if (missionManagerRole) {
    const missionPermissions = await prisma.permission.findMany({
      where: {
        subject: { in: ['Mission', 'Recommendation', 'Action', 'Comment', 'MissionDocument'] },
      },
    })
    for (const permission of missionPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: missionManagerRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: missionManagerRole.id,
          permissionId: permission.id,
        },
      })
    }
  }

  // Viewer gets read permissions
  const viewerRole = createdRoles.find(r => r.name === 'Viewer')
  if (viewerRole) {
    const readPermissions = await prisma.permission.findMany({
      where: { action: 'read' },
    })
    for (const permission of readPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: viewerRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: viewerRole.id,
          permissionId: permission.id,
        },
      })
    }
  }

  // Create admin user
  console.log('Creating admin user...')
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'admin-user-id',
      email: '<EMAIL>',
      username: 'admin',
      firstName: 'Admin',
      lastName: 'User',
      name: 'Admin User',
      isActive: true,
      isStaff: true,
      isSuperuser: true,
      emailVerified: new Date(),
      dateJoined: new Date(),
    },
  })

  // Assign Super Admin role to admin user
  if (superAdminRole) {
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: adminUser.id,
          roleId: superAdminRole.id,
        },
      },
      update: {},
      create: {
        userId: adminUser.id,
        roleId: superAdminRole.id,
      },
    })
  }

  console.log('✅ Database seeded successfully!')
  console.log('🔑 Admin user created:')
  console.log('   Email: <EMAIL>')
  console.log('   Username: admin')
  console.log('   Role: Super Admin')
  console.log('   Note: Use BetterAuth to set password on first login')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
