'use client';


import { useApiArbitrationList } from '@/hooks/useNextApi';

import { useState } from 'react';
import { MRT_PaginationState } from 'material-react-table';
import GenericTable from '../(components)/GenericTAbleArbitration';
import { Arbitration } from '@prisma/client';
import { $Arbitration } from '@/lib/schemas';

const TableDemo = () => {
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex: 0,
        pageSize: 5, //customize the default page size
    });
    const [selected, setSelected] = useState([]);

    const { data: arbitrations, isLoading, error } = useApiArbitrationList({
        page: pagination.pageIndex + 1
    });
    // const { data: date_create, error: error_create, isMutating: isMutating_create, trigger: trigger_create } = useApiMissionCreate();
    // const { data, error: error_, isMutating, trigger } = useApiMissionDestroy(1);


    if (isLoading) return (<div>Loading ...</div>)
    return (
        <div className="grid">

            <div className="col-12">
                {/* <div className="card"> */}
                {/* <h5>Missions</h5> */}
                <GenericTable<Arbitration>
                    data_={arbitrations} isLoading={isLoading}
                    error={error} data_type={$Arbitration}
                    pagination={{ "set": setPagination, "pagi": pagination }}>
                </GenericTable>
                {/* </div> */}
            </div>
        </div>
    );
};

export default TableDemo;
