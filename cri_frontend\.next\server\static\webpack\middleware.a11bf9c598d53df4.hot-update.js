"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./app/ability.ts":
/*!************************!*\
  !*** ./app/ability.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAbilityForUser: () => (/* binding */ createAbilityForUser),\n/* harmony export */   createDefaultAbility: () => (/* binding */ createDefaultAbility),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defineAbilitiesFor: () => (/* binding */ defineAbilitiesFor)\n/* harmony export */ });\n/* harmony import */ var _casl_ability__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @casl/ability */ \"(middleware)/./node_modules/@casl/ability/dist/es6m/index.mjs\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(middleware)/./lib/prisma.ts\");\n\n\n// Cache for user permissions to avoid repeated database queries\nconst permissionCache = new Map();\n// Define abilities based on user roles and permissions from database\nasync function defineAbilitiesFor(user) {\n    const { can, cannot, build } = new _casl_ability__WEBPACK_IMPORTED_MODULE_1__.AbilityBuilder(_casl_ability__WEBPACK_IMPORTED_MODULE_1__.createMongoAbility);\n    if (!user) {\n        // Unauthenticated users - minimal permissions\n        can(\"read\", [\n            \"Mission\",\n            \"Recommendation\",\n            \"Plan\",\n            \"Theme\"\n        ]);\n        return build();\n    }\n    // Superuser has all permissions\n    if (user.isSuperuser) {\n        can(\"manage\", \"all\");\n        return build();\n    }\n    // Check cache first\n    const cacheKey = `${user.id}_${user.updatedAt || user.dateJoined}`;\n    let userPermissions = permissionCache.get(cacheKey);\n    if (!userPermissions) {\n        try {\n            // Fetch user roles and permissions from database\n            userPermissions = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.findMany({\n                where: {\n                    userId: user.id\n                },\n                include: {\n                    role: {\n                        include: {\n                            rolePermissions: {\n                                include: {\n                                    permission: true\n                                }\n                            }\n                        }\n                    }\n                }\n            });\n            // Cache the permissions\n            permissionCache.set(cacheKey, userPermissions);\n        } catch (error) {\n            console.error(\"Error fetching user permissions:\", error);\n            userPermissions = [];\n        }\n    }\n    // Apply permissions from database\n    for (const userRole of userPermissions){\n        for (const rolePermission of userRole.role.rolePermissions){\n            const permission = rolePermission.permission;\n            // Apply the permission\n            if (permission.conditions) {\n                can(permission.action, permission.subject, permission.conditions);\n            } else {\n                can(permission.action, permission.subject);\n            }\n            // Apply field-level permissions if specified\n            if (permission.fields && permission.fields.length > 0) {\n                can(permission.action, permission.subject, permission.fields);\n            }\n        }\n    }\n    // Default permissions for authenticated users\n    can(\"read\", [\n        \"Mission\",\n        \"Recommendation\",\n        \"Plan\",\n        \"Theme\",\n        \"Domain\",\n        \"Process\"\n    ]);\n    can(\"read\", \"User\", {\n        id: user.id\n    }); // Users can read their own profile\n    can(\"update\", \"User\", {\n        id: user.id\n    }); // Users can update their own profile\n    // Staff members get additional permissions\n    if (user.isStaff) {\n        can(\"create\", [\n            \"Mission\",\n            \"Recommendation\",\n            \"Comment\"\n        ]);\n        can(\"update\", [\n            \"Mission\",\n            \"Recommendation\"\n        ], {\n            createdBy: user.id\n        });\n        can(\"delete\", [\n            \"Comment\"\n        ], {\n            createdBy: user.id\n        });\n        can(\"change\", [\n            \"Mission\",\n            \"Plan\",\n            \"Recommendation\"\n        ]);\n        can(\"add\", [\n            \"MissionDocument\"\n        ]);\n    }\n    return build();\n}\n// Create a default ability (for unauthenticated users)\nconst createDefaultAbility = ()=>{\n    const { can, build } = new _casl_ability__WEBPACK_IMPORTED_MODULE_1__.AbilityBuilder(_casl_ability__WEBPACK_IMPORTED_MODULE_1__.createMongoAbility);\n    can(\"read\", [\n        \"Mission\",\n        \"Recommendation\",\n        \"Plan\",\n        \"Theme\"\n    ]);\n    return build();\n};\n// Synchronous ability factory for immediate use (fallback)\nfunction createAbilityForUser(user) {\n    const { can, build } = new _casl_ability__WEBPACK_IMPORTED_MODULE_1__.AbilityBuilder(_casl_ability__WEBPACK_IMPORTED_MODULE_1__.createMongoAbility);\n    if (!user) {\n        can(\"read\", [\n            \"Mission\",\n            \"Recommendation\",\n            \"Plan\",\n            \"Theme\"\n        ]);\n        return build();\n    }\n    if (user.isSuperuser) {\n        can(\"manage\", \"all\");\n        return build();\n    }\n    // Basic permissions for authenticated users\n    can(\"read\", [\n        \"Mission\",\n        \"Recommendation\",\n        \"Plan\",\n        \"Theme\",\n        \"Domain\",\n        \"Process\"\n    ]);\n    can(\"read\", \"User\", {\n        id: user.id\n    });\n    can(\"update\", \"User\", {\n        id: user.id\n    });\n    if (user.isStaff) {\n        can(\"create\", [\n            \"Mission\",\n            \"Recommendation\",\n            \"Comment\"\n        ]);\n        can(\"update\", [\n            \"Mission\",\n            \"Recommendation\"\n        ], {\n            createdBy: user.id\n        });\n        can(\"delete\", [\n            \"Comment\"\n        ], {\n            createdBy: user.id\n        });\n        can(\"change\", [\n            \"Mission\",\n            \"Plan\",\n            \"Recommendation\"\n        ]);\n        can(\"add\", [\n            \"MissionDocument\"\n        ]);\n        can(\"edit\", [\n            \"Mission\",\n            \"Plan\",\n            \"Recommendation\"\n        ]);\n    }\n    return build();\n}\n// Default ability for initial load\nconst ability = createDefaultAbility();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ability);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./app/ability.ts\n");

/***/ })

});