import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/themes/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid theme ID' }, { status: 400 })
    }

    const theme = await prisma.theme.findUnique({
      where: { id },
      include: {
        domain: true,
        process: true,
        proposingStructures: true,
        concernedStructures: true,
        risks: true,
        goals: true,
        arbitratedThemes: {
          include: {
            arbitration: {
              include: {
                plan: true,
              },
            },
            missions: {
              select: {
                id: true,
                code: true,
                type: true,
                etat: true,
              },
            },
          },
        },
      },
    })

    if (!theme) {
      return NextResponse.json({ error: 'Theme not found' }, { status: 404 })
    }

    return NextResponse.json({ data: theme })
  } catch (error) {
    console.error('Error fetching theme:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/themes/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can update themes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid theme ID' }, { status: 400 })
    }

    const body = await request.json()
    const {
      validated,
      code,
      domainId,
      processId,
      title,
      proposedBy,
      monthStart,
      monthEnd,
      proposingStructures,
      concernedStructures,
      risks,
      goals,
    } = body

    // Check if theme exists
    const existingTheme = await prisma.theme.findUnique({
      where: { id },
    })

    if (!existingTheme) {
      return NextResponse.json({ error: 'Theme not found' }, { status: 404 })
    }

    // Update theme
    const theme = await prisma.theme.update({
      where: { id },
      data: {
        ...(validated !== undefined && { validated }),
        ...(code !== undefined && { code }),
        ...(domainId !== undefined && { domainId }),
        ...(processId !== undefined && { processId }),
        ...(title !== undefined && { title }),
        ...(proposedBy !== undefined && { proposedBy }),
        ...(monthStart !== undefined && { monthStart }),
        ...(monthEnd !== undefined && { monthEnd }),
        modifiedBy: session.user.id.toString(),
        // Handle many-to-many relationships
        ...(proposingStructures && {
          proposingStructures: {
            set: proposingStructures.map((id: number) => ({ id }))
          }
        }),
        ...(concernedStructures && {
          concernedStructures: {
            set: concernedStructures.map((id: number) => ({ id }))
          }
        }),
        ...(risks && {
          risks: {
            set: risks.map((id: number) => ({ id }))
          }
        }),
        ...(goals && {
          goals: {
            set: goals.map((id: number) => ({ id }))
          }
        }),
      },
      include: {
        domain: true,
        process: true,
        proposingStructures: true,
        concernedStructures: true,
        risks: true,
        goals: true,
      },
    })

    return NextResponse.json({ data: theme })
  } catch (error) {
    console.error('Error updating theme:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/themes/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can delete themes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid theme ID' }, { status: 400 })
    }

    // Check if theme exists
    const existingTheme = await prisma.theme.findUnique({
      where: { id },
      include: {
        arbitratedThemes: true,
      },
    })

    if (!existingTheme) {
      return NextResponse.json({ error: 'Theme not found' }, { status: 404 })
    }

    // Check if theme has related arbitrations
    if (existingTheme.arbitratedThemes.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete theme with existing arbitrations' },
        { status: 400 }
      )
    }

    // Delete theme
    await prisma.theme.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Theme deleted successfully' })
  } catch (error) {
    console.error('Error deleting theme:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
