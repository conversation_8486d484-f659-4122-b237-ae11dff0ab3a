from drf_rw_serializers.viewsets import GenericViewSet as RWGenericViewSet

from drf_spectacular.openapi import AutoSchema


class CustomAutoSchema(AutoSchema):
    """ Utilize custom drf_rw_serializers methods for directional serializers """

    def get_request_serializer(self):
        if isinstance(self.view, RWGenericViewSet):
            return self.view.get_write_serializer()
        return self._get_serializer()

    def get_response_serializers(self):
        if isinstance(self.view, RWGenericViewSet):
            return self.view.get_read_serializer()
        return self._get_serializer()