import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/processes
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const parentId = searchParams.get('parentId')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' as const } },
        { shortTitle: { contains: search, mode: 'insensitive' as const } },
      ]
    }
    
    if (parentId) {
      where.parentId = parseInt(parentId)
    }
    
    const [processes, total] = await Promise.all([
      prisma.process.findMany({
        where,
        skip,
        take: limit,
        include: {
          parent: {
            select: {
              id: true,
              title: true,
              shortTitle: true,
            }
          },
          children: {
            select: {
              id: true,
              title: true,
              shortTitle: true,
            }
          },
          themes: {
            select: {
              id: true,
              title: true,
              validated: true,
            }
          }
        },
        orderBy: { id: 'desc' },
      }),
      prisma.process.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: processes,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching processes:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/processes
export async function POST(request: NextRequest) {
  try {
    // Check authentication - only staff can create processes
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const body = await request.json()
    
    const {
      title,
      shortTitle,
      parentId,
    } = body
    
    // Validate required fields
    if (!title || !shortTitle) {
      return NextResponse.json(
        { error: 'title and shortTitle are required' },
        { status: 400 }
      )
    }

    // Check if parent exists (if provided)
    if (parentId) {
      const parent = await prisma.process.findUnique({
        where: { id: parentId }
      })
      
      if (!parent) {
        return NextResponse.json(
          { error: 'Parent process not found' },
          { status: 404 }
        )
      }
    }

    const process = await prisma.process.create({
      data: {
        title,
        shortTitle,
        parentId,
        createdBy: session.user.id.toString(),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        parent: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        children: {
          select: {
            id: true,
            title: true,
            shortTitle: true,
          }
        },
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
          }
        }
      },
    })

    return NextResponse.json({ data: process }, { status: 201 })
  } catch (error) {
    console.error('Error creating process:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
