{"ssrModuleMapping": {"(app-client)/./node_modules/next/dist/client/components/error-boundary.js": {"*": {"id": "(sc_client)/./node_modules/next/dist/client/components/error-boundary.js", "name": "*", "chunks": [], "async": false}}, "(app-client)/./node_modules/next/dist/client/components/app-router.js": {"*": {"id": "(sc_client)/./node_modules/next/dist/client/components/app-router.js", "name": "*", "chunks": [], "async": false}}, "(app-client)/./node_modules/next/dist/client/components/layout-router.js": {"*": {"id": "(sc_client)/./node_modules/next/dist/client/components/layout-router.js", "name": "*", "chunks": [], "async": false}}, "(app-client)/./node_modules/next/dist/client/components/render-from-template-context.js": {"*": {"id": "(sc_client)/./node_modules/next/dist/client/components/render-from-template-context.js", "name": "*", "chunks": [], "async": false}}, "(app-client)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js": {"*": {"id": "(sc_client)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js", "name": "*", "chunks": [], "async": false}}, "(app-client)/./app/layout.tsx": {"*": {"id": "(sc_client)/./app/layout.tsx", "name": "*", "chunks": [], "async": false}}, "(app-client)/./app/login/page.tsx": {"*": {"id": "(sc_client)/./app/login/page.tsx", "name": "*", "chunks": [], "async": false}}, "(app-client)/./layout/layout.tsx": {"*": {"id": "(sc_client)/./layout/layout.tsx", "name": "*", "chunks": [], "async": false}}, "(app-client)/./app/(main)/page.tsx": {"*": {"id": "(sc_client)/./app/(main)/page.tsx", "name": "*", "chunks": [], "async": false}}, "(app-client)/./app/(main)/missions/page.tsx": {"*": {"id": "(sc_client)/./app/(main)/missions/page.tsx", "name": "*", "chunks": [], "async": false}}, "(app-client)/./app/(main)/plans/page.tsx": {"*": {"id": "(sc_client)/./app/(main)/plans/page.tsx", "name": "*", "chunks": [], "async": false}}}, "edgeSSRModuleMapping": {}, "clientModules": {"E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js": {"id": "(app-client)/./node_modules/next/dist/client/components/error-boundary.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js": {"id": "(app-client)/./node_modules/next/dist/client/components/error-boundary.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\client\\components\\app-router.js": {"id": "(app-client)/./node_modules/next/dist/client/components/app-router.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js": {"id": "(app-client)/./node_modules/next/dist/client/components/app-router.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js": {"id": "(app-client)/./node_modules/next/dist/client/components/layout-router.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js": {"id": "(app-client)/./node_modules/next/dist/client/components/layout-router.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js": {"id": "(app-client)/./node_modules/next/dist/client/components/render-from-template-context.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js": {"id": "(app-client)/./node_modules/next/dist/client/components/render-from-template-context.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js": {"id": "(app-client)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js": {"id": "(app-client)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js", "name": "*", "chunks": ["app-client-internals:static/chunks/app-client-internals.js"], "async": false}, "E:\\graci_home\\cri_frontend\\app\\layout.tsx": {"id": "(app-client)/./app/layout.tsx", "name": "*", "chunks": ["app/layout:static/chunks/app/layout.js"], "async": false}, "E:\\graci_home\\cri_frontend\\app\\login\\page.tsx": {"id": "(app-client)/./app/login/page.tsx", "name": "*", "chunks": ["app/login/page:static/chunks/app/login/page.js"], "async": false}, "E:\\graci_home\\cri_frontend\\layout\\layout.tsx": {"id": "(app-client)/./layout/layout.tsx", "name": "*", "chunks": ["app/(main)/layout:static/chunks/app/(main)/layout.js"], "async": false}, "E:\\graci_home\\cri_frontend\\app\\(main)\\page.tsx": {"id": "(app-client)/./app/(main)/page.tsx", "name": "*", "chunks": ["app/(main)/page:static/chunks/app/(main)/page.js"], "async": false}, "E:\\graci_home\\cri_frontend\\app\\(main)\\missions\\page.tsx": {"id": "(app-client)/./app/(main)/missions/page.tsx", "name": "*", "chunks": ["app/(main)/missions/page:static/chunks/app/(main)/missions/page.js"], "async": false}, "E:\\graci_home\\cri_frontend\\app\\(main)\\plans\\page.tsx": {"id": "(app-client)/./app/(main)/plans/page.tsx", "name": "*", "chunks": ["app/(main)/plans/page:static/chunks/app/(main)/plans/page.js"], "async": false}}, "entryCSSFiles": {"E:\\graci_home\\cri_frontend\\app\\layout": ["static/css/app/layout.css"], "E:\\graci_home\\cri_frontend\\app\\login\\page": [], "E:\\graci_home\\cri_frontend\\app\\(main)\\layout": [], "E:\\graci_home\\cri_frontend\\app\\(main)\\page": [], "E:\\graci_home\\cri_frontend\\app\\(main)\\missions\\page": ["static/css/app/(main)/missions/page.css"], "E:\\graci_home\\cri_frontend\\app\\(main)\\plans\\page": []}}