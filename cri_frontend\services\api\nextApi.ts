// Next.js API Service to replace Django API calls
import { getCookie } from 'cookies-next'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api'

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  body?: any
}

class NextApiService {
  private async request<T>(endpoint: string, options: ApiOptions = {}): Promise<T> {
    const { method = 'GET', headers = {}, body } = options

    const config: RequestInit = {
      method,
      credentials: 'include', // Include cookies for authentication
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    }

    if (body && method !== 'GET') {
      config.body = JSON.stringify(body)
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, config)

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  // Mission API methods
  async getMissions(params?: { page?: number; limit?: number; search?: string }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)

    const query = searchParams.toString()
    return this.request(`/missions${query ? `?${query}` : ''}`)
  }

  async getMission(id: number) {
    return this.request(`/missions/${id}`)
  }

  async createMission(data: any) {
    return this.request('/missions', {
      method: 'POST',
      body: data,
    })
  }

  async updateMission(id: number, data: any) {
    return this.request(`/missions/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteMission(id: number) {
    return this.request(`/missions/${id}`, {
      method: 'DELETE',
    })
  }

  // Recommendation API methods
  async getRecommendations(params?: {
    page?: number;
    limit?: number;
    search?: string;
    missionId?: number
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.missionId) searchParams.append('missionId', params.missionId.toString())

    const query = searchParams.toString()
    return this.request(`/recommendations${query ? `?${query}` : ''}`)
  }

  async getRecommendation(id: number) {
    return this.request(`/recommendations/${id}`)
  }

  async createRecommendation(data: any) {
    return this.request('/recommendations', {
      method: 'POST',
      body: data,
    })
  }

  async updateRecommendation(id: number, data: any) {
    return this.request(`/recommendations/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteRecommendation(id: number) {
    return this.request(`/recommendations/${id}`, {
      method: 'DELETE',
    })
  }

  // User API methods
  async getUsers(params?: { page?: number; limit?: number; search?: string }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)

    const query = searchParams.toString()
    return this.request(`/users${query ? `?${query}` : ''}`)
  }

  async getUser(id: number) {
    return this.request(`/users/${id}`)
  }

  async createUser(data: any) {
    return this.request('/users', {
      method: 'POST',
      body: data,
    })
  }

  // Plan API methods
  async getPlans(params?: {
    page?: number;
    limit?: number;
    exercise?: number;
    type?: string
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.exercise) searchParams.append('exercise', params.exercise.toString())
    if (params?.type) searchParams.append('type', params.type)

    const query = searchParams.toString()
    return this.request(`/plans${query ? `?${query}` : ''}`)
  }

  async getPlan(id: number) {
    return this.request(`/plans/${id}`)
  }

  async createPlan(data: any) {
    return this.request('/plans', {
      method: 'POST',
      body: data,
    })
  }

  async updatePlan(id: number, data: any) {
    return this.request(`/plans/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deletePlan(id: number) {
    return this.request(`/plans/${id}`, {
      method: 'DELETE',
    })
  }

  // Theme API methods
  async getThemes(params?: {
    page?: number;
    limit?: number;
    search?: string;
    validated?: boolean;
    domainId?: number;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.validated !== undefined) searchParams.append('validated', params.validated.toString())
    if (params?.domainId) searchParams.append('domainId', params.domainId.toString())

    const query = searchParams.toString()
    return this.request(`/themes${query ? `?${query}` : ''}`)
  }

  async getTheme(id: number) {
    return this.request(`/themes/${id}`)
  }

  async createTheme(data: any) {
    return this.request('/themes', {
      method: 'POST',
      body: data,
    })
  }

  async updateTheme(id: number, data: any) {
    return this.request(`/themes/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteTheme(id: number) {
    return this.request(`/themes/${id}`, {
      method: 'DELETE',
    })
  }

  // Arbitration API methods
  async getArbitrations(params?: {
    page?: number;
    limit?: number;
    search?: string;
    planId?: number;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.planId) searchParams.append('planId', params.planId.toString())

    const query = searchParams.toString()
    return this.request(`/arbitrations${query ? `?${query}` : ''}`)
  }

  async getArbitration(id: number) {
    return this.request(`/arbitrations/${id}`)
  }

  async createArbitration(data: any) {
    return this.request('/arbitrations', {
      method: 'POST',
      body: data,
    })
  }

  async updateArbitration(id: number, data: any) {
    return this.request(`/arbitrations/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteArbitration(id: number) {
    return this.request(`/arbitrations/${id}`, {
      method: 'DELETE',
    })
  }

  // Comment API methods
  async getComments(params?: {
    page?: number;
    limit?: number;
    search?: string;
    recommendationId?: number;
    missionId?: number;
    userId?: number;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.recommendationId) searchParams.append('recommendationId', params.recommendationId.toString())
    if (params?.missionId) searchParams.append('missionId', params.missionId.toString())
    if (params?.userId) searchParams.append('userId', params.userId.toString())

    const query = searchParams.toString()
    return this.request(`/comments${query ? `?${query}` : ''}`)
  }

  async getComment(id: number) {
    return this.request(`/comments/${id}`)
  }

  async createComment(data: any) {
    return this.request('/comments', {
      method: 'POST',
      body: data,
    })
  }

  async updateComment(id: number, data: any) {
    return this.request(`/comments/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteComment(id: number) {
    return this.request(`/comments/${id}`, {
      method: 'DELETE',
    })
  }

  // Document API methods
  async uploadDocument(data: FormData) {
    const response = await fetch(`${API_BASE_URL}/documents`, {
      method: 'POST',
      credentials: 'include',
      body: data,
    })

    if (!response.ok) {
      throw new Error(`Upload Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  async uploadMissionDocuments(missionId: number, data: FormData) {
    const response = await fetch(`${API_BASE_URL}/missions/${missionId}/documents`, {
      method: 'POST',
      credentials: 'include',
      body: data,
    })

    if (!response.ok) {
      throw new Error(`Upload Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  async getMissionDocuments(missionId: number) {
    return this.request(`/missions/${missionId}/documents`)
  }

  async getDocuments(params?: { missionId?: number; context?: string }) {
    const searchParams = new URLSearchParams()
    if (params?.missionId) searchParams.append('missionId', params.missionId.toString())
    if (params?.context) searchParams.append('context', params.context)

    const query = searchParams.toString()
    return this.request(`/documents${query ? `?${query}` : ''}`)
  }

  async deleteDocument(id: number) {
    return this.request(`/documents/${id}`, {
      method: 'DELETE',
    })
  }

  async updateDocument(id: number, data: any) {
    return this.request(`/documents/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  // Action API methods
  async getActions(params?: {
    page?: number;
    limit?: number;
    search?: string;
    recommendationId?: number;
    status?: string;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.recommendationId) searchParams.append('recommendationId', params.recommendationId.toString())
    if (params?.status) searchParams.append('status', params.status)

    const query = searchParams.toString()
    return this.request(`/actions${query ? `?${query}` : ''}`)
  }

  async getAction(id: number) {
    return this.request(`/actions/${id}`)
  }

  async createAction(data: any) {
    return this.request('/actions', {
      method: 'POST',
      body: data,
    })
  }

  async updateAction(id: number, data: any) {
    return this.request(`/actions/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteAction(id: number) {
    return this.request(`/actions/${id}`, {
      method: 'DELETE',
    })
  }

  // ArbitratedTheme API methods
  async getArbitratedThemes(params?: {
    page?: number;
    limit?: number;
    search?: string;
    arbitrationId?: number;
    themeId?: number;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.arbitrationId) searchParams.append('arbitrationId', params.arbitrationId.toString())
    if (params?.themeId) searchParams.append('themeId', params.themeId.toString())

    const query = searchParams.toString()
    return this.request(`/arbitrated-themes${query ? `?${query}` : ''}`)
  }

  async getArbitratedTheme(id: number) {
    return this.request(`/arbitrated-themes/${id}`)
  }

  async createArbitratedTheme(data: any) {
    return this.request('/arbitrated-themes', {
      method: 'POST',
      body: data,
    })
  }

  async updateArbitratedTheme(id: number, data: any) {
    return this.request(`/arbitrated-themes/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteArbitratedTheme(id: number) {
    return this.request(`/arbitrated-themes/${id}`, {
      method: 'DELETE',
    })
  }

  // Domain API methods
  async getDomains(params?: {
    page?: number;
    limit?: number;
    search?: string;
    parentId?: number;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.parentId) searchParams.append('parentId', params.parentId.toString())

    const query = searchParams.toString()
    return this.request(`/domains${query ? `?${query}` : ''}`)
  }

  async getDomain(id: number) {
    return this.request(`/domains/${id}`)
  }

  async createDomain(data: any) {
    return this.request('/domains', {
      method: 'POST',
      body: data,
    })
  }

  async updateDomain(id: number, data: any) {
    return this.request(`/domains/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteDomain(id: number) {
    return this.request(`/domains/${id}`, {
      method: 'DELETE',
    })
  }

  // Process API methods
  async getProcesses(params?: {
    page?: number;
    limit?: number;
    search?: string;
    parentId?: number;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.parentId) searchParams.append('parentId', params.parentId.toString())

    const query = searchParams.toString()
    return this.request(`/processes${query ? `?${query}` : ''}`)
  }

  async getProcess(id: number) {
    return this.request(`/processes/${id}`)
  }

  async createProcess(data: any) {
    return this.request('/processes', {
      method: 'POST',
      body: data,
    })
  }

  async updateProcess(id: number, data: any) {
    return this.request(`/processes/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteProcess(id: number) {
    return this.request(`/processes/${id}`, {
      method: 'DELETE',
    })
  }

  // Risk API methods
  async getRisks(params?: {
    page?: number;
    limit?: number;
    search?: string;
    validated?: boolean;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.validated !== undefined) searchParams.append('validated', params.validated.toString())

    const query = searchParams.toString()
    return this.request(`/risks${query ? `?${query}` : ''}`)
  }

  async getRisk(id: number) {
    return this.request(`/risks/${id}`)
  }

  async createRisk(data: any) {
    return this.request('/risks', {
      method: 'POST',
      body: data,
    })
  }

  async updateRisk(id: number, data: any) {
    return this.request(`/risks/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteRisk(id: number) {
    return this.request(`/risks/${id}`, {
      method: 'DELETE',
    })
  }

  // Goal API methods
  async getGoals(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)

    const query = searchParams.toString()
    return this.request(`/goals${query ? `?${query}` : ''}`)
  }

  async getGoal(id: number) {
    return this.request(`/goals/${id}`)
  }

  async createGoal(data: any) {
    return this.request('/goals', {
      method: 'POST',
      body: data,
    })
  }

  async updateGoal(id: number, data: any) {
    return this.request(`/goals/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteGoal(id: number) {
    return this.request(`/goals/${id}`, {
      method: 'DELETE',
    })
  }

  // Structure API methods
  async getStructures(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)

    const query = searchParams.toString()
    return this.request(`/structures${query ? `?${query}` : ''}`)
  }

  async getStructure(id: number) {
    return this.request(`/structures/${id}`)
  }
}

export const nextApiService = new NextApiService()
export default nextApiService
