"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/recommendations/[recommendation_id]/page",{

/***/ "(app-client)/./app/(main)/recommendations/[recommendation_id]/page.tsx":
/*!*****************************************************************!*\
  !*** ./app/(main)/recommendations/[recommendation_id]/page.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utilities_components_BlockViewer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utilities/components/BlockViewer */ \"(app-client)/./utilities/components/BlockViewer.tsx\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_chip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/chip */ \"(app-client)/./node_modules/primereact/chip/chip.esm.js\");\n/* harmony import */ var primereact_column__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/column */ \"(app-client)/./node_modules/primereact/column/column.esm.js\");\n/* harmony import */ var primereact_datatable__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/datatable */ \"(app-client)/./node_modules/primereact/datatable/datatable.esm.js\");\n/* harmony import */ var primereact_tabview__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/tabview */ \"(app-client)/./node_modules/primereact/tabview/tabview.esm.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-client)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var _lib_schemas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/schemas */ \"(app-client)/./lib/schemas.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst RecommendationRefDetails = (param)=>{\n    let { params } = param;\n    var _recommendation, _recommendation1, _recommendation2, _recommendation3, _rec_, _recommendation4, _recommendation5, _recommendation6;\n    _s();\n    let rec_;\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const { recommendation } = params;\n    const { data: recommendations } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiRecommendationList)({\n        limit: 100\n    });\n    console.log(\"search params\", searchParams);\n    if (!recommendation) {\n        rec_ = Array.isArray(recommendations) ? recommendations.find((rec)=>rec.id === searchParams.recommandation_id) : null;\n    } else {\n        rec_ = recommendation;\n    }\n    // Simplified actions display - remove complex column generation for now\n    const displayActions = ()=>{\n        let columns = [];\n        for (const [key, value] of Object.entries(_lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"created_by\",\n                \"dependencies\",\n                \"modified_by\",\n                \"created\",\n                \"modified\",\n                \"id\"\n            ].includes(key);\n        })){\n            if (key === \"description\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                    field: key,\n                    body: (data)=>data.description,\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 30\n                }, undefined));\n            } else if (key === \"job_leader\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                    field: key,\n                    body: (data)=>{\n                        var _data_job_leader, _data_job_leader1;\n                        return \"\".concat((_data_job_leader = data.job_leader) === null || _data_job_leader === void 0 ? void 0 : _data_job_leader.last_name, \" \").concat((_data_job_leader1 = data.job_leader) === null || _data_job_leader1 === void 0 ? void 0 : _data_job_leader1.first_name);\n                    },\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 30\n                }, undefined));\n            } else if ([\n                \"start_date\",\n                \"end_date\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                    field: key,\n                    body: (data)=>new Date(data[key]).toLocaleDateString(),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 30\n                }, undefined));\n            } else if ([\n                \"progress\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProgressBar, {\n                            value: data[key]\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 30\n                }, undefined));\n            } else if ([\n                \"status\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tag, {\n                            value: data[key]\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 30\n                }, undefined));\n            } else if ([\n                \"proof\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                    field: key,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            severity: \"warning\",\n                            icon: \"pi pi-paperclip\",\n                            onClick: attachementViewProofClick\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_5__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"45%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 30\n                }, undefined));\n            }\n        }\n        var _data_comments_length;\n        columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n            align: \"center\",\n            field: \"comment\",\n            body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                    rounded: true,\n                    outlined: true,\n                    severity: \"info\",\n                    icon: \"pi pi-comments\",\n                    onClick: (e)=>{\n                        console.log(data);\n                        addCommentClick(e, data.id, data.recommendation);\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                        value: (_data_comments_length = data.comments.length) !== null && _data_comments_length !== void 0 ? _data_comments_length : 0,\n                        severity: \"danger\"\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0),\n            header: \"Commentaires\",\n            sortable: true,\n            style: {\n                width: \"25%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n            lineNumber: 64,\n            columnNumber: 22\n        }, undefined));\n        return columns;\n    };\n    var _recommendation_comments, _recommendation_actions;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utilities_components_BlockViewer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                header: \"Mission \".concat((_recommendation = recommendation) === null || _recommendation === void 0 ? void 0 : _recommendation.id),\n                containerClassName: \"surface-0 px-4 py-4 md:px-6 lg:px-8\",\n                status: ((_recommendation1 = recommendation) === null || _recommendation1 === void 0 ? void 0 : _recommendation1.accepted) ? \"Accept\\xe9e\" : \"Non-Accept\\xe9e\",\n                priority: (_recommendation2 = recommendation) === null || _recommendation2 === void 0 ? void 0 : _recommendation2.priority,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"surface-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-none p-0 m-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex align-items-center py-3 px-2 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-500 w-6 md:w-2 font-medium\",\n                                        children: ((_recommendation3 = recommendation) === null || _recommendation3 === void 0 ? void 0 : _recommendation3.recommendation) ? \"Plan\" : \"Exercice\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 w-full md:w-8 md:flex-order-0 flex-order-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-500 w-6 md:w-2 font-medium\",\n                                        children: \"Th\\xe9matique\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 w-full md:w-8 md:flex-order-0 flex-order-1\",\n                                        children: (_rec_ = rec_) === null || _rec_ === void 0 ? void 0 : _rec_.responsible\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-500 w-6 md:w-2 font-medium\",\n                                        children: \"Structures concern\\xe9es\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-900 w-full md:w-8 md:flex-order-0 flex-order-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            direction: \"row\",\n                                            spacing: 1,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                                label: (_recommendation4 = recommendation) === null || _recommendation4 === void 0 ? void 0 : _recommendation4.concerned_structure.code_mnemonique\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 74\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex align-items-center py-3 px-2 border-top-1 border-300 flex-wrap w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_10__.TabView, {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_10__.TabPanel, {\n                                            header: \"Commtaires\",\n                                            rightIcon: \"pi pi-thumbs-up ml-2\",\n                                            className: \"align-content-center align-items-center justify-content-center \",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_11__.DataTable, {\n                                                resizableColumns: true,\n                                                value: (_recommendation_comments = (_recommendation5 = recommendation) === null || _recommendation5 === void 0 ? void 0 : _recommendation5.comments) !== null && _recommendation_comments !== void 0 ? _recommendation_comments : [],\n                                                size: \"small\",\n                                                stripedRows: true,\n                                                rows: 5,\n                                                paginator: true,\n                                                emptyMessage: \"Pas de commentaires.\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                                                        field: \"id\",\n                                                        header: \"N\\xb0\",\n                                                        sortable: true\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                                                        field: \"created_by\",\n                                                        header: \"Constats\",\n                                                        sortable: true,\n                                                        style: {\n                                                            width: \"35%\"\n                                                        },\n                                                        body: (data)=>{\n                                                            var _data_constats;\n                                                            return (_data_constats = data.constats) === null || _data_constats === void 0 ? void 0 : _data_constats.map((constat)=>constat.id).join(\",\");\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                                                        field: \"created_\",\n                                                        header: \"Structure Concern\\xe9\",\n                                                        sortable: true,\n                                                        body: (data)=>data.concerned_structure.code_mnemonique\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                                                        field: \"comment\",\n                                                        header: \"Priorit\\xe9\",\n                                                        sortable: true,\n                                                        style: {\n                                                            width: \"35%\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_6__.Column, {\n                                                        header: \"Voir\",\n                                                        style: {\n                                                            width: \"15%\"\n                                                        },\n                                                        body: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                    icon: \"pi pi-eye\",\n                                                                    text: true\n                                                                }, void 0, false, void 0, void 0)\n                                                            }, void 0, false)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tabview__WEBPACK_IMPORTED_MODULE_10__.TabPanel, {\n                                            header: \"Actions\",\n                                            leftIcon: \"pi pi-file-word mr-2\",\n                                            rightIcon: \"pi pi-file-pdf ml-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_11__.DataTable, {\n                                                tableStyle: {\n                                                    maxWidth: \"70vw\"\n                                                },\n                                                value: (_recommendation_actions = (_recommendation6 = recommendation) === null || _recommendation6 === void 0 ? void 0 : _recommendation6.actions) !== null && _recommendation_actions !== void 0 ? _recommendation_actions : [],\n                                                rows: 5,\n                                                paginator: true,\n                                                resizableColumns: true,\n                                                responsiveLayout: \"scroll\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 33\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n            lineNumber: 72,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\[recommendation_id]\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 9\n    }, undefined);\n};\n_s(RecommendationRefDetails, \"apGH5WERTBvi07ohsvJNZrMHJ4k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_4__.useApiRecommendationList\n    ];\n});\n_c = RecommendationRefDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RecommendationRefDetails);\nvar _c;\n$RefreshReg$(_c, \"RecommendationRefDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/recommendations/[recommendation_id]/page.tsx\n"));

/***/ })

});