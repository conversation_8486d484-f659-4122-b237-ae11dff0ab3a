# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-13 20:40+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"
#: .\backend\models.py:20
msgid "Modéré"
msgstr ""

#: .\backend\models.py:21
msgid "Critique"
msgstr ""

#: .\backend\models.py:22
msgid "Faible"
msgstr ""

#: .\backend\models.py:23
msgid "Elevé"
msgstr ""

#: .\backend\models.py:46
msgid "Vice Président"
msgstr ""

#: .\backend\models.py:47
msgid "Contrôle Interne"
msgstr ""

#: .\backend\models.py:48
msgid "Structures"
msgstr ""

#: .\backend\models.py:104
msgid "Thème"
msgstr ""

#: .\backend\models.py:105
msgid "Vivier de Thèmes"
msgstr ""

#: .\backend\models.py:119
msgid "Fiche d'pportunité"
msgstr ""

#: .\backend\models.py:120
msgid "Fiches d'opportunité"
msgstr ""

#: .\backend\models.py:123 .\backend\models.py:133
msgid "Intitulé"
msgstr ""

#: .\backend\models.py:124 .\backend\models.py:134
msgid "Abbréviation"
msgstr ""

#: .\backend\models.py:125
msgid "Domaine parent"
msgstr ""

#: .\backend\models.py:130
msgid "Domaine"
msgstr ""

#: .\backend\models.py:131
msgid "Domaines"
msgstr ""

#: .\backend\models.py:135
msgid "Processus parent"
msgstr ""
