"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx":
/*!*********************************************************!*\
  !*** ./app/(main)/themes/(components)/GenericTAble.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_chip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/chip */ \"(app-client)/./node_modules/primereact/chip/chip.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const [theme_id, setThemeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: arbitrations, isLoading, error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList)();\n    const { data: themes, isLoading: isLoading_themes, error: error_themes } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList)();\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate)();\n    const { data: data_update, error: error_update, isPending: isMutating_update, mutate: trigger_update } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate)();\n    const getSeverity = (str)=>{\n        switch(str){\n            case \"Vice Pr\\xe9sident\":\n                return \"success\";\n            case \"Contr\\xf4le Interne\":\n                return \"warning\";\n            case \"Audit Interne\":\n                return \"warning\";\n            case \"Structures\":\n                return \"danger\";\n            default:\n                return null;\n        }\n    };\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                header: \"ID\",\n                accessorKey: \"id\",\n                size: 70,\n                Edit: ()=>null\n            },\n            {\n                header: \"Aribtrage\",\n                accessorKey: \"arbitration\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"arbitration\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-11rem text-sm\",\n                        severity: row.original.arbitration.plan.code.includes(\"Audit\") ? \"danger\" : \"info\",\n                        value: row.original.arbitration.plan.code\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_arbitration_plan, _row__valuesCache_arbitration, _row__valuesCache_arbitration1, _arbitrations_data, _arbitrations;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _arbitrations, _arbitrations1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                arbitration: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : _arbitrations.data.results.find((arbi)=>arbi.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                arbitration: (_arbitrations1 = arbitrations) === null || _arbitrations1 === void 0 ? void 0 : _arbitrations1.data.results.find((arbi)=>arbi.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un\",\n                        value: {\n                            name: ((_row__valuesCache_arbitration = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration === void 0 ? void 0 : (_row__valuesCache_arbitration_plan = _row__valuesCache_arbitration.plan) === null || _row__valuesCache_arbitration_plan === void 0 ? void 0 : _row__valuesCache_arbitration_plan.code) || null,\n                            code: ((_row__valuesCache_arbitration1 = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration1 === void 0 ? void 0 : _row__valuesCache_arbitration1.id) || null\n                        },\n                        options: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : (_arbitrations_data = _arbitrations.data) === null || _arbitrations_data === void 0 ? void 0 : _arbitrations_data.results.map((arbi)=>{\n                            return {\n                                code: arbi.id,\n                                name: arbi.plan.code\n                            };\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Propos\\xe9 par\",\n                accessorKey: \"theme\",\n                id: \"theme_proposed_by\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                Edit: ()=>null,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-9rem text-sm\",\n                        severity: getSeverity(cell.getValue().proposedBy),\n                        value: cell.getValue().proposedBy\n                    }, row.original.code + row.original.created, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 36\n                    }, this);\n                }\n            },\n            {\n                header: \"Structures proposantes\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"proposing_structures\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    console.log(cell.getValue().proposing_structures);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: cell.getValue().proposing_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                style: {\n                                    backgroundColor: \"green\",\n                                    color: \"white\"\n                                },\n                                label: val.code_mnemonique\n                            }, \"thm\".concat(row.original.theme.id, \"_ps\").concat(idx), false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 78\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 20\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Structures concern\\xe9es\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"concerned_structures\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [\n                            \" \",\n                            cell.getValue().concerned_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                    style: {\n                                        backgroundColor: \"green\",\n                                        color: \"white\"\n                                    },\n                                    label: val.code_mnemonique\n                                }, \"thm\".concat(row.original.theme.id, \"_cs\").concat(idx), false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 137\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Domaine\",\n                accessorKey: \"domain\",\n                id: \"domain\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.domain.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Processus\",\n                accessorKey: \"process\",\n                id: \"process\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.process.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Intitul\\xe9\",\n                accessorKey: \"theme\",\n                id: \"title\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.theme.title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_theme, _row__valuesCache_theme1, _themes;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _themes, _themes1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                theme: (_themes = themes) === null || _themes === void 0 ? void 0 : _themes.data.results.find((thm)=>thm.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                theme: (_themes1 = themes) === null || _themes1 === void 0 ? void 0 : _themes1.data.results.find((thm)=>thm.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un\",\n                        value: {\n                            name: ((_row__valuesCache_theme = row._valuesCache.theme) === null || _row__valuesCache_theme === void 0 ? void 0 : _row__valuesCache_theme.title) || null,\n                            code: ((_row__valuesCache_theme1 = row._valuesCache.theme) === null || _row__valuesCache_theme1 === void 0 ? void 0 : _row__valuesCache_theme1.id) || null\n                        },\n                        options: (_themes = themes) === null || _themes === void 0 ? void 0 : _themes.data.results.map((thm)=>{\n                            return {\n                                code: thm.id,\n                                name: thm.title\n                            };\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Remarque\",\n                accessorKey: \"note\",\n                id: \"note\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.note\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>/*#__PURE__*/ {\n                    let { row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                        id: \"note\",\n                        initialValue: row.original.note,\n                        tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                        apiKey: \"none\",\n                        init: {\n                            height: 500,\n                            menubar: true,\n                            plugins: [\n                                \"advlist\",\n                                \"autolink\",\n                                \"lists\",\n                                \"link\",\n                                \"image\",\n                                \"charmap\",\n                                \"print\",\n                                \"preview\",\n                                \"anchor\",\n                                \"searchreplace\",\n                                \"visualblocks\",\n                                \"code\",\n                                \"fullscreen\",\n                                \"insertdatetime\",\n                                \"media\",\n                                \"table\",\n                                \"paste\",\n                                \"code\",\n                                \"help\",\n                                \"wordcount\"\n                            ],\n                            toolbar: \"undo redo | formatselect | bold italic backcolor |                                       alignleft aligncenter alignright alignjustify |                                       bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                        },\n                        onChange: (e)=>{\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                note: e.target.getContent()\n                            };\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 30\n                    }, this);\n                }\n            }\n        ], [\n        arbitrations,\n        themes\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableEditing: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            setThemeId(row.original.id);\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let update_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_update(update_values, {\n                revalidate: true,\n                populateCache: true,\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"Th\\xe8me modifi\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.response);\n                    row._valuesCache = {\n                        error: err.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, row, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let insert_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_create(insert_values, {\n                revalidate: true,\n                populateCache: true,\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.response);\n                    row._valuesCache = {\n                        error: err.response,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: (param)=>{\n            let { table } = param;\n            //clear any validation errors\n            table.setCreatingRow(null);\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                //about:\"edit modal\",\n                // open: editVisible || createVisible,\n                maxWidth: \"md\"\n            };\n        // sx: {\n        //   //  '& .MuiDialog-root': {\n        //   //    width :'70vw'\n        //   //  },\n        //   // '& .MuiDialog-container': {\n        //   //   width :'70vw'\n        //   // },\n        //   zIndex: 1100,\n        // }\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_4__.Can, {\n                    I: \"add\",\n                    a: \"ArbitratedTheme\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            })\n    });\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n        lineNumber: 603,\n        columnNumber: 26\n    }, this);\n    console.log(arbitrations);\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_10__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 607,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_13__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 607,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"bDGdo1+71fnDMhQHJ0dTBFzwcyk=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx\n"));

/***/ })

});