import React from "react";
import { ViewMode } from "gantt-task-react";
import { Button } from "primereact/button";
import { ButtonGroup } from 'primereact/buttongroup';
import { InputSwitch } from "primereact/inputswitch";

type ViewSwitcherProps = {
  isChecked: boolean;
  onViewListChange: (isChecked: boolean) => void;
  onViewModeChange: (viewMode: ViewMode) => void;
};
export const ViewSwitcher: React.FC<ViewSwitcherProps> = ({
  onViewModeChange,
  onViewListChange,
  isChecked,
}) => {
  return (
    <div className="flex flex-wrap justify-content-center gap-3">
      {/* <ButtonGroup> */}

      <Button
        className="Button"
        onClick={() => onViewModeChange(ViewMode.Hour)}
      >
        Heure
      </Button>
      <Button
        className="Button"
        onClick={() => onViewModeChange(ViewMode.QuarterDay)}
      >
        1/4 Jour
      </Button>
      <Button
        className="Button"
        onClick={() => onViewModeChange(ViewMode.HalfDay)}
      >
        1/2 Jour
      </Button>
      <Button className="Button" onClick={() => onViewModeChange(ViewMode.Day)}>
        Jour
      </Button>
      <Button
        className="Button"
        onClick={() => onViewModeChange(ViewMode.Week)}
      >
        Semaine
      </Button>
      <Button
        className="Button"
        onClick={() => onViewModeChange(ViewMode.Month)}
      >
        Mois
      </Button>
      <Button
        className="Button"
        onClick={() => onViewModeChange(ViewMode.Year)}
      >
        Année
      </Button>
      
     
      
      {/* </ButtonGroup> */}
      <InputSwitch
      
            checked={isChecked}
            onChange={() => onViewListChange(!isChecked)}
          />
      
      
      
    </div>
  );
};
