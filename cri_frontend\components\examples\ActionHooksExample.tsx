/**
 * Example component demonstrating how to use Action hooks
 * This shows different ways to use the Action API hooks
 */

import React, { useState } from 'react';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { ProgressBar } from 'primereact/progressbar';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
import { 
  useApiActionList, 
  useApiActionCreate, 
  useApiActionUpdate, 
  useApiActionPartialUpdate,
  useApiActionDestroy,
  useApiRecommendationList,
  useApiUserList 
} from '@/hooks/useNextApi';

export default function ActionHooksExample() {
  const [selectedRecommendationId, setSelectedRecommendationId] = useState<number | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedAction, setSelectedAction] = useState<any>(null);
  const [formData, setFormData] = useState({
    description: '',
    status: 'PENDING',
    progress: 0,
    startDate: null,
    endDate: null,
    jobLeaderId: null,
    recommendationId: null,
  });

  const toast = useRef<Toast>(null);

  // Fetch data
  const { data: actions, isLoading: actionsLoading, refetch: refetchActions } = useApiActionList({
    recommendationId: selectedRecommendationId || undefined,
    limit: 50
  });

  const { data: recommendations } = useApiRecommendationList({ limit: 100 });
  const { data: users } = useApiUserList({ limit: 100 });

  // Mutations
  const createAction = useApiActionCreate();
  const updateAction = useApiActionUpdate();
  const partialUpdateAction = useApiActionPartialUpdate();
  const deleteAction = useApiActionDestroy();

  // Status options
  const statusOptions = [
    { label: 'En attente', value: 'PENDING' },
    { label: 'En cours', value: 'IN_PROGRESS' },
    { label: 'Terminé', value: 'COMPLETED' },
    { label: 'Annulé', value: 'CANCELLED' }
  ];

  const handleCreate = async () => {
    try {
      await createAction.mutateAsync({
        ...formData,
        recommendationId: selectedRecommendationId,
      });
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Action créée avec succès' 
      });
      
      setShowCreateDialog(false);
      resetForm();
      refetchActions();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la création' 
      });
    }
  };

  const handleUpdate = async () => {
    try {
      await updateAction.mutateAsync({
        id: selectedAction.id,
        data: formData,
      });
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Action mise à jour avec succès' 
      });
      
      setShowEditDialog(false);
      resetForm();
      refetchActions();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la mise à jour' 
      });
    }
  };

  const handlePartialUpdate = async (actionId: number, updates: any) => {
    try {
      await partialUpdateAction.mutateAsync({
        id: actionId,
        data: updates,
      });
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Action mise à jour' 
      });
      
      refetchActions();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la mise à jour' 
      });
    }
  };

  const handleDelete = async (actionId: number) => {
    try {
      await deleteAction.mutateAsync(actionId);
      
      toast.current?.show({ 
        severity: 'success', 
        summary: 'Succès', 
        detail: 'Action supprimée avec succès' 
      });
      
      refetchActions();
    } catch (error) {
      toast.current?.show({ 
        severity: 'error', 
        summary: 'Erreur', 
        detail: 'Erreur lors de la suppression' 
      });
    }
  };

  const resetForm = () => {
    setFormData({
      description: '',
      status: 'PENDING',
      progress: 0,
      startDate: null,
      endDate: null,
      jobLeaderId: null,
      recommendationId: null,
    });
  };

  const openCreateDialog = () => {
    resetForm();
    setShowCreateDialog(true);
  };

  const openEditDialog = (action: any) => {
    setSelectedAction(action);
    setFormData({
      description: action.description || '',
      status: action.status || 'PENDING',
      progress: action.progress || 0,
      startDate: action.startDate ? new Date(action.startDate) : null,
      endDate: action.endDate ? new Date(action.endDate) : null,
      jobLeaderId: action.jobLeaderId || null,
      recommendationId: action.recommendationId || null,
    });
    setShowEditDialog(true);
  };

  // Column templates
  const statusBodyTemplate = (rowData: any) => {
    const severity = rowData.status === 'COMPLETED' ? 'success' : 
                    rowData.status === 'IN_PROGRESS' ? 'warning' : 
                    rowData.status === 'CANCELLED' ? 'danger' : 'info';
    
    return <Tag value={rowData.status} severity={severity} />;
  };

  const progressBodyTemplate = (rowData: any) => {
    return <ProgressBar value={rowData.progress || 0} />;
  };

  const actionsBodyTemplate = (rowData: any) => {
    return (
      <div className="flex gap-2">
        <Button 
          icon="pi pi-pencil" 
          size="small" 
          onClick={() => openEditDialog(rowData)}
          tooltip="Modifier"
        />
        <Button 
          icon="pi pi-check" 
          size="small" 
          severity="success"
          onClick={() => handlePartialUpdate(rowData.id, { status: 'COMPLETED', progress: 100 })}
          tooltip="Marquer comme terminé"
        />
        <Button 
          icon="pi pi-trash" 
          size="small" 
          severity="danger"
          onClick={() => handleDelete(rowData.id)}
          tooltip="Supprimer"
        />
      </div>
    );
  };

  return (
    <div className="action-hooks-example">
      <Toast ref={toast} />
      
      <div className="card">
        <h4>Exemple d'utilisation des hooks Action</h4>
        
        {/* Recommendation filter */}
        <div className="field">
          <label htmlFor="recommendation-filter">Filtrer par recommandation:</label>
          <Dropdown
            id="recommendation-filter"
            value={selectedRecommendationId}
            options={Array.isArray(recommendations) ? recommendations.map((rec: any) => ({
              label: `${rec.id} - ${rec.recommendation?.substring(0, 50)}...`,
              value: rec.id
            })) : []}
            onChange={(e) => setSelectedRecommendationId(e.value)}
            placeholder="Sélectionner une recommandation"
            showClear
            className="w-full"
          />
        </div>

        {/* Actions table */}
        <div className="flex justify-content-between align-items-center mb-3">
          <h5>Actions</h5>
          <Button 
            label="Nouvelle Action" 
            icon="pi pi-plus" 
            onClick={openCreateDialog}
            disabled={!selectedRecommendationId}
          />
        </div>

        <DataTable 
          value={Array.isArray(actions) ? actions : []} 
          loading={actionsLoading}
          paginator 
          rows={10}
          emptyMessage="Aucune action trouvée"
        >
          <Column field="id" header="ID" />
          <Column field="description" header="Description" style={{ maxWidth: '300px' }} />
          <Column field="status" header="Statut" body={statusBodyTemplate} />
          <Column field="progress" header="Progrès" body={progressBodyTemplate} />
          <Column field="startDate" header="Date début" body={(data) => 
            data.startDate ? new Date(data.startDate).toLocaleDateString('fr') : '-'
          } />
          <Column field="endDate" header="Date fin" body={(data) => 
            data.endDate ? new Date(data.endDate).toLocaleDateString('fr') : '-'
          } />
          <Column header="Actions" body={actionsBodyTemplate} />
        </DataTable>
      </div>

      {/* Create Dialog */}
      <Dialog 
        header="Créer une Action" 
        visible={showCreateDialog} 
        onHide={() => setShowCreateDialog(false)}
        style={{ width: '500px' }}
      >
        <div className="field">
          <label htmlFor="description">Description *</label>
          <InputTextarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="status">Statut</label>
          <Dropdown
            id="status"
            value={formData.status}
            options={statusOptions}
            onChange={(e) => setFormData({ ...formData, status: e.value })}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="jobLeader">Responsable</label>
          <Dropdown
            id="jobLeader"
            value={formData.jobLeaderId}
            options={Array.isArray(users) ? users.map((user: any) => ({
              label: `${user.firstName} ${user.lastName}`,
              value: user.id
            })) : []}
            onChange={(e) => setFormData({ ...formData, jobLeaderId: e.value })}
            placeholder="Sélectionner un responsable"
            showClear
            className="w-full"
          />
        </div>

        <div className="flex justify-content-end gap-2 mt-4">
          <Button 
            label="Annuler" 
            severity="secondary" 
            onClick={() => setShowCreateDialog(false)} 
          />
          <Button 
            label="Créer" 
            onClick={handleCreate}
            loading={createAction.isPending}
            disabled={!formData.description}
          />
        </div>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog 
        header="Modifier l'Action" 
        visible={showEditDialog} 
        onHide={() => setShowEditDialog(false)}
        style={{ width: '500px' }}
      >
        <div className="field">
          <label htmlFor="edit-description">Description *</label>
          <InputTextarea
            id="edit-description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="edit-status">Statut</label>
          <Dropdown
            id="edit-status"
            value={formData.status}
            options={statusOptions}
            onChange={(e) => setFormData({ ...formData, status: e.value })}
            className="w-full"
          />
        </div>

        <div className="field">
          <label htmlFor="edit-progress">Progrès (%)</label>
          <InputText
            id="edit-progress"
            type="number"
            min="0"
            max="100"
            value={formData.progress.toString()}
            onChange={(e) => setFormData({ ...formData, progress: parseInt(e.target.value) || 0 })}
            className="w-full"
          />
        </div>

        <div className="flex justify-content-end gap-2 mt-4">
          <Button 
            label="Annuler" 
            severity="secondary" 
            onClick={() => setShowEditDialog(false)} 
          />
          <Button 
            label="Mettre à jour" 
            onClick={handleUpdate}
            loading={updateAction.isPending}
            disabled={!formData.description}
          />
        </div>
      </Dialog>
    </div>
  );
}
