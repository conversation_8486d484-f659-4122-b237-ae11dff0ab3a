"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/opportunity_sheet/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/editForm.tsx":
/*!*****************************************************!*\
  !*** ./app/(main)/themes/(components)/editForm.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_inputtext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/inputtext */ \"(app-client)/./node_modules/primereact/inputtext/inputtext.esm.js\");\n/* harmony import */ var primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/inputtextarea */ \"(app-client)/./node_modules/primereact/inputtextarea/inputtextarea.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var _mui_material_Stepper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Stepper */ \"(app-client)/./node_modules/@mui/material/Stepper/Stepper.js\");\n/* harmony import */ var _mui_material_Step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/material/Step */ \"(app-client)/./node_modules/@mui/material/Step/Step.js\");\n/* harmony import */ var _mui_material_StepLabel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/StepLabel */ \"(app-client)/./node_modules/@mui/material/StepLabel/StepLabel.js\");\n/* harmony import */ var primereact_calendar__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! primereact/calendar */ \"(app-client)/./node_modules/primereact/calendar/calendar.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_progressspinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! primereact/progressspinner */ \"(app-client)/./node_modules/primereact/progressspinner/progressspinner.esm.js\");\n/* harmony import */ var primereact_togglebutton__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! primereact/togglebutton */ \"(app-client)/./node_modules/primereact/togglebutton/togglebutton.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var primereact_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/card */ \"(app-client)/./node_modules/primereact/card/card.esm.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var _lib_enums__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/enums */ \"(app-client)/./lib/enums.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// import { useApiDomainList, useApiGoalList, useApiMissionCreate, useApiMissionDestroy, useApiMissionList, useApiPlanList, useApiProcessList, useApiRiskList, useApiStructurelqsList, useApiThemeList, useApiUsersList } from '@/services/api/api/api';\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ThemeEditForm = (props)=>{\n    var _getCookie, _structures_lqs_data, _structures_lqs, _risks_data, _risks, _goals_data, _goals, _structures_lqs_data1, _structures_lqs1, _structures_lqs_error, _props_row_original, _props_row__valuesCache_error_data, _props_row__valuesCache_error, _props_row__valuesCache_error_data1, _props_row__valuesCache_error1, _props_row__valuesCache_error_data2, _props_row__valuesCache_error2, _props_row__valuesCache_error_data3, _props_row__valuesCache_error3, _props_row__valuesCache_error_data4, _props_row__valuesCache_error4, _props_row__valuesCache_error_data5, _props_row__valuesCache_error5, _props_row__valuesCache_error_data6, _props_row__valuesCache_error6, _props_row__valuesCache_error_data7, _props_row__valuesCache_error7, _props_row__valuesCache_error_data8, _props_row__valuesCache_error8, _props_row__valuesCache_error_data9, _props_row__valuesCache_error9, _props_row__valuesCache_error_data10, _props_row__valuesCache_error10, _props_row__valuesCache_error_data11, _props_row__valuesCache_error11, _props_row__valuesCache_error_data12, _props_row__valuesCache_error12, _props_row__valuesCache_error_data13, _props_row__valuesCache_error13, _props_row__valuesCache_error_data14, _props_row__valuesCache_error14, _domains, _props_row__valuesCache_error_data15, _props_row__valuesCache_error15, _props_row__valuesCache_error_data16, _props_row__valuesCache_error16, _props_row__valuesCache_error_data17, _props_row__valuesCache_error17, _processes, _props_row__valuesCache_error_data18, _props_row__valuesCache_error18, _props_row__valuesCache_error_data19, _props_row__valuesCache_error19, _props_row__valuesCache_error_data20, _props_row__valuesCache_error20, _props_row__valuesCache_error_data21, _props_row__valuesCache_error21, _props_row__valuesCache_error_data22, _props_row__valuesCache_error22, _props_row__valuesCache_error_data23, _props_row__valuesCache_error23, _props_row__valuesCache_error_data24, _props_row__valuesCache_error24, _props_row__valuesCache_error_data25, _props_row__valuesCache_error25, _props_row__valuesCache_error_data26, _props_row__valuesCache_error26, _props_row__valuesCache_error_data27, _props_row__valuesCache_error27, _props_row__valuesCache_error_data28, _props_row__valuesCache_error28, _props_row__valuesCache_error_data29, _props_row__valuesCache_error29, _props_row__valuesCache_error_data30, _props_row__valuesCache_error30, _props_row__valuesCache_error_data31, _props_row__valuesCache_error31, _props_row__valuesCache_error_data32, _props_row__valuesCache_error32, _props_row__valuesCache_error_data33, _props_row__valuesCache_error33, _props_row__valuesCache_error_data34, _props_row__valuesCache_error34;\n    _s();\n    ///////////////////////////////////////////////////////////////////////////////\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_2__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    // Fetch data using specific hooks\n    const { data: users, isLoading, error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiUserList)({\n        limit: 100\n    });\n    const { data: plans, isLoading: plan_isLoading, error: plan_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiPlanList)({\n        limit: 100\n    });\n    const { data: themes, isLoading: themes_isLoading, error: themes_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiThemeList)({\n        limit: 100\n    });\n    const { data: risks, isLoading: risks_isLoading, error: risks_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiRiskList)({\n        limit: 100\n    });\n    const { data: goals, isLoading: goals_isLoading, error: goals_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiGoalList)({\n        limit: 100\n    });\n    const { data: domains, isLoading: domains_isLoading, error: domains_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainList)({\n        limit: 100\n    });\n    const { data: processes, isLoading: processes_isLoading, error: processes_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiProcessList)({\n        limit: 100\n    });\n    const { data: structures_lqs, isLoading: structures_lqs_isLoading, error: structures_lqs_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiStructurelqsList)({\n        limit: 100\n    });\n    ///////////////////////////Stepper functions///////////////////////////////////\n    const [activeStep, setActiveStep] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const [skipped, setSkipped] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    const isStepOptional = (step)=>{\n        return step === 1;\n    };\n    const isStepSkipped = (step)=>{\n        return skipped.has(step);\n    };\n    const handleNext = ()=>{\n        let newSkipped = skipped;\n        if (isStepSkipped(activeStep)) {\n            newSkipped = new Set(newSkipped.values());\n            newSkipped.delete(activeStep);\n        }\n        setActiveStep((prevActiveStep)=>prevActiveStep + 1);\n        setSkipped(newSkipped);\n    };\n    const handleBack = ()=>{\n        setActiveStep((prevActiveStep)=>prevActiveStep - 1);\n    };\n    const handleSkip = ()=>{\n        if (!isStepOptional(activeStep)) {\n            // You probably want to guard against something like this,\n            // it should never occur unless someone's actively trying to break something.\n            throw new Error(\"You can't skip a step that isn't optional.\");\n        }\n        setActiveStep((prevActiveStep)=>prevActiveStep + 1);\n        setSkipped((prevSkipped)=>{\n            const newSkipped = new Set(prevSkipped.values());\n            newSkipped.add(activeStep);\n            return newSkipped;\n        });\n    };\n    const handleReset = ()=>{\n        setActiveStep(0);\n    };\n    ///////////////////////////Stepper functions///////////////////////////////////\n    ///////////////////////////////////////////////////////////////////////////////\n    const steps = [\n        \"Th\\xe8me\",\n        \"Risques\",\n        \"Objectifs\"\n    ]; //'Structures Proposantes', 'Structures conernées',\n    ///////////////////////////////////////////////////////////////////////////////\n    const [theme_data, setThemeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"domain\": props.row.id === \"mrt-row-create\" ? null : props.row.original.domain.id,\n        \"process\": props.row.id === \"mrt-row-create\" ? null : props.row.original.process.id,\n        \"risks\": props.row.id === \"mrt-row-create\" ? [] : props.row.original.risks.map((risk)=>risk.id),\n        \"goals\": props.row.id === \"mrt-row-create\" ? [] : props.row.original.goals.map((goal)=>goal.id),\n        \"proposing_structures\": props.row.id === \"mrt-row-create\" ? [] : props.row.original.proposing_structures.map((struct)=>struct.id),\n        \"concerned_structures\": props.row.id === \"mrt-row-create\" ? [] : props.row.original.concerned_structures.map((struct)=>struct.id),\n        \"validated\": props.row.id === \"mrt-row-create\" ? false : props.row.original.validated,\n        \"code\": props.row.id === \"mrt-row-create\" ? \"\" : props.row.original.code,\n        \"title\": props.row.id === \"mrt-row-create\" ? \"\" : props.row.original.title,\n        \"proposed_by\": props.row.id === \"mrt-row-create\" ? null : props.row.original.proposed_by,\n        \"month_start\": props.row.id === \"mrt-row-create\" ? null : props.row.original.month_start,\n        \"month_end\": props.row.id === \"mrt-row-create\" ? null : props.row.original.month_end,\n        \"id\": props.row.id === \"mrt-row-create\" ? null : props.row.original.id\n    });\n    const handleTheme = (field, event)=>{\n        const theme_new = {\n            ...theme_data,\n            ...{\n                [field]: event\n            }\n        };\n        props.row._valuesCache = theme_new;\n        console.log(theme_new);\n        setThemeData(theme_new);\n    };\n    // const { data: users,            isLoading, error                                                  } = useApiUsersList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }});\n    // const { data: plans,            isLoading: plan_isLoading, error: plan_error                          } = useApiPlanList()\n    // const { data: risks,            isLoading: risks_isLoading, error: risks_error                   } = useApiRiskList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    // const { data: themes,           isLoading: themes_isLoading, error: themes_error                  } = useApiThemeList()\n    // const { data: goals,            isLoading: goals_isLoading, error: goals_error                       } = useApiGoalList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    // const { data: domains,          isLoading: domains_isLoading, error: domains_error               } = useApiDomainList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    // const { data: processes,        isLoading: processes_isLoading, error: processes_error               } = useApiProcessList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    // const { data: structures_lqs,   isLoading: structures_lqs_isLoading, error: structures_lqs_error } = useApiStructurelqsList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }})\n    const [editDialogVisible, setEditDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [picklistSourceValueProposingStructures, setPicklistSourceValueProposingStructures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_structures_lqs = structures_lqs) === null || _structures_lqs === void 0 ? void 0 : (_structures_lqs_data = _structures_lqs.data) === null || _structures_lqs_data === void 0 ? void 0 : _structures_lqs_data.results);\n    const [picklistTargetValueProposingStructures, setPicklistTargetValueProposingStructures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? [] : props.row.original.proposing_structures);\n    const [picklistSourceValueRisks, setPicklistSourceValueRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_risks = risks) === null || _risks === void 0 ? void 0 : (_risks_data = _risks.data) === null || _risks_data === void 0 ? void 0 : _risks_data.results);\n    const [picklistTargetValueRisks, setPicklistTargetValueRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? [] : props.row.original.risks);\n    const [picklistSourceValueGoals, setPicklistSourceValueGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_goals = goals) === null || _goals === void 0 ? void 0 : (_goals_data = _goals.data) === null || _goals_data === void 0 ? void 0 : _goals_data.results);\n    const [picklistTargetValueGoals, setPicklistTargetValueGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? [] : props.row.original.goals);\n    const [picklistSourceValueConcernedStructures, setPicklistSourceValueConcernedStructures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_structures_lqs1 = structures_lqs) === null || _structures_lqs1 === void 0 ? void 0 : (_structures_lqs_data1 = _structures_lqs1.data) === null || _structures_lqs_data1 === void 0 ? void 0 : _structures_lqs_data1.results);\n    const [picklistTargetValueConcernedStructures, setPicklistTargetValueConcernedStructures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? [] : props.row.original.proposing_structures);\n    const [dropdownItemDomain, setDropdownItemDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : {\n        \"name\": props.row.original.domain.title,\n        \"code\": props.row.original.domain.id\n    });\n    const [dropdownItemProcess, setDropdownItemProcess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : {\n        \"name\": props.row.original.process.title,\n        \"code\": props.row.original.process.id\n    });\n    const [dropdownItemProposedBy, setDropdownItemProposedBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : {\n        \"name\": props.row.original.proposed_by,\n        \"code\": props.row.original.proposed_by\n    });\n    const [theme_validated, setThemeValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? false : props.row.original.validated);\n    const [theme_code, setThemeCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : props.row.original.code);\n    const [theme_title, setThemeTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? \"\" : props.row.original.title);\n    const [theme_end_date, setThemeEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? new Date() : new Date(props.row.original.month_end));\n    const [theme_start_date, setThemeStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? new Date() : new Date(props.row.original.month_start));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _structures_lqs, _structures_lqs1, _structures_lqs2, _structures_lqs3, _risks, _risks1, _goals, _goals1;\n        setPicklistSourceValueConcernedStructures(props.row.id === \"mrt-row-create\" ? (_structures_lqs = structures_lqs) === null || _structures_lqs === void 0 ? void 0 : _structures_lqs.data.results : (_structures_lqs1 = structures_lqs) === null || _structures_lqs1 === void 0 ? void 0 : _structures_lqs1.data.results.filter((struct)=>!props.row.original.concerned_structures.map((struct_)=>struct_.id).includes(struct.id)));\n        setPicklistTargetValueConcernedStructures(props.row.id === \"mrt-row-create\" ? [] : props.row.original.concerned_structures);\n        setPicklistSourceValueProposingStructures(props.row.id === \"mrt-row-create\" ? (_structures_lqs2 = structures_lqs) === null || _structures_lqs2 === void 0 ? void 0 : _structures_lqs2.data.results : (_structures_lqs3 = structures_lqs) === null || _structures_lqs3 === void 0 ? void 0 : _structures_lqs3.data.results.filter((struct)=>!props.row.original.proposing_structures.map((struct_)=>struct_.id).includes(struct.id)));\n        setPicklistTargetValueProposingStructures(props.row.id === \"mrt-row-create\" ? [] : props.row.original.proposing_structures);\n        setPicklistSourceValueRisks(props.row.id === \"mrt-row-create\" ? (_risks = risks) === null || _risks === void 0 ? void 0 : _risks.data.data.results : (_risks1 = risks) === null || _risks1 === void 0 ? void 0 : _risks1.data.results.filter((risk)=>!props.row.original.risks.map((risk_)=>risk_.id).includes(risk.id)));\n        setPicklistTargetValueRisks(props.row.id === \"mrt-row-create\" ? [] : props.row.original.risks);\n        setPicklistSourceValueGoals(props.row.id === \"mrt-row-create\" ? (_goals = goals) === null || _goals === void 0 ? void 0 : _goals.data.data.results : (_goals1 = goals) === null || _goals1 === void 0 ? void 0 : _goals1.data.results.filter((goal)=>!props.row.original.goals.map((goal_)=>goal_.id).includes(goal.id)));\n        setPicklistTargetValueGoals(props.row.id === \"mrt-row-create\" ? [] : props.row.original.goals);\n    }, [\n        structures_lqs\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // setDropdownItemDomain(props.row.id === 'mrt-row-create' ? null : { \"name\": props.row.original.domain.title, \"code\": props.row.original.domain.id })\n        props.row._valuesCache = {\n            ...theme_data\n        };\n    }, []);\n    if (plan_isLoading && structures_lqs_isLoading && domains_isLoading && processes_isLoading && risks_isLoading && goals_isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressspinner__WEBPACK_IMPORTED_MODULE_5__.ProgressSpinner, {\n        style: {\n            width: \"50px\",\n            height: \"50px\"\n        },\n        strokeWidth: \"8\",\n        fill: \"var(--surface-ground)\",\n        animationDuration: \".5s\"\n    }, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n        lineNumber: 182,\n        columnNumber: 143\n    }, undefined);\n    if (structures_lqs_error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: (_structures_lqs_error = structures_lqs_error) === null || _structures_lqs_error === void 0 ? void 0 : _structures_lqs_error.message\n    }, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n        lineNumber: 183,\n        columnNumber: 39\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                zIndex: \"1302 !important\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_6__.Sidebar, {\n                position: \"right\",\n                header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"align-content-center \",\n                            children: [\n                                props.row.id === \"mrt-row-create\" ? \"Nouveau th\\xe8me\" : \"Editer th\\xe9me :\",\n                                \" \",\n                                (_props_row_original = props.row.original) === null || _props_row_original === void 0 ? void 0 : _props_row_original.code\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        ((_props_row__valuesCache_error = props.row._valuesCache.error) === null || _props_row__valuesCache_error === void 0 ? void 0 : (_props_row__valuesCache_error_data = _props_row__valuesCache_error.data) === null || _props_row__valuesCache_error_data === void 0 ? void 0 : _props_row__valuesCache_error_data[\"non_field_errors\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                            className: \"p-error\",\n                            children: (_props_row__valuesCache_error1 = props.row._valuesCache.error) === null || _props_row__valuesCache_error1 === void 0 ? void 0 : (_props_row__valuesCache_error_data1 = _props_row__valuesCache_error1.data) === null || _props_row__valuesCache_error_data1 === void 0 ? void 0 : _props_row__valuesCache_error_data1[\"non_field_errors\"][0]\n                        }, void 0, false, void 0, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_8__.MRT_EditActionButtons, {\n                                variant: \"text\",\n                                table: props.table,\n                                row: props.row\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    ]\n                }, void 0, true, void 0, void 0),\n                visible: editDialogVisible,\n                onHide: ()=>{\n                //  props.table.setEditingRow(null); setEditDialogVisible(false)\n                },\n                className: \"w-full md:w-9 lg:w-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: \"0.7rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Stepper__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                activeStep: activeStep,\n                                sx: {\n                                    paddingY: \"0.7rem\"\n                                },\n                                children: steps.map((label, index)=>{\n                                    const stepProps = {};\n                                    const labelProps = {};\n                                    if (isStepOptional(index)) {\n                                        labelProps.optional = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            variant: \"caption\",\n                                            children: \"Optional\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 41\n                                        }, undefined);\n                                    }\n                                    if (isStepSkipped(index)) {\n                                        stepProps.completed = false;\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Step__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        ...stepProps,\n                                        sx: {\n                                            \"& .MuiStepLabel-root .Mui-completed\": {\n                                                color: \"secondary.dark\"\n                                            },\n                                            \"& .MuiStepLabel-label.Mui-completed.MuiStepLabel-alternativeLabel\": {\n                                                color: \"white\"\n                                            },\n                                            \"& .MuiStepLabel-root .Mui-active\": {\n                                                color: \"var(--primary-color)\"\n                                            },\n                                            \"& .MuiStepLabel-label.Mui-active.MuiStepLabel-alternativeLabel\": {\n                                                color: \"white\"\n                                            },\n                                            \"& .MuiStepLabel-root .Mui-active .MuiStepIcon-text\": {\n                                                fill: \"white\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_StepLabel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            ...labelProps,\n                                            children: label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, label, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 37\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 25\n                            }, undefined),\n                            activeStep === steps.length ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        sx: {\n                                            mt: 2,\n                                            mb: 1\n                                        },\n                                        children: \"All steps completed - you're finished\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            flexDirection: \"row\",\n                                            pt: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                sx: {\n                                                    flex: \"1 1 auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                onClick: handleReset,\n                                                children: \"Reset\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 29\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                children: [\n                                    activeStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_card__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-fluid formgrid grid\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"title\",\n                                                            children: \"Th\\xe9matique\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_17__.InputTextarea, {\n                                                            className: ((_props_row__valuesCache_error2 = props.row._valuesCache.error) === null || _props_row__valuesCache_error2 === void 0 ? void 0 : (_props_row__valuesCache_error_data2 = _props_row__valuesCache_error2.data) === null || _props_row__valuesCache_error_data2 === void 0 ? void 0 : _props_row__valuesCache_error_data2[\"title\"]) ? \"p-invalid\" : \"\",\n                                                            id: \"title\",\n                                                            defaultValue: theme_title,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"title\", e.target.value);\n                                                                setThemeTitle(e.target.value);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error3 = props.row._valuesCache.error) === null || _props_row__valuesCache_error3 === void 0 ? void 0 : (_props_row__valuesCache_error_data3 = _props_row__valuesCache_error3.data) === null || _props_row__valuesCache_error_data3 === void 0 ? void 0 : _props_row__valuesCache_error_data3[\"title\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error4 = props.row._valuesCache.error) === null || _props_row__valuesCache_error4 === void 0 ? void 0 : (_props_row__valuesCache_error_data4 = _props_row__valuesCache_error4.data) === null || _props_row__valuesCache_error_data4 === void 0 ? void 0 : _props_row__valuesCache_error_data4[\"title\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 99\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"code\",\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_18__.InputText, {\n                                                            className: ((_props_row__valuesCache_error5 = props.row._valuesCache.error) === null || _props_row__valuesCache_error5 === void 0 ? void 0 : (_props_row__valuesCache_error_data5 = _props_row__valuesCache_error5.data) === null || _props_row__valuesCache_error_data5 === void 0 ? void 0 : _props_row__valuesCache_error_data5[\"code\"]) ? \"p-invalid\" : \"\",\n                                                            id: \"code\",\n                                                            type: \"text\",\n                                                            defaultValue: theme_code,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"code\", e.target.value);\n                                                                setThemeCode(e.target.value);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error6 = props.row._valuesCache.error) === null || _props_row__valuesCache_error6 === void 0 ? void 0 : (_props_row__valuesCache_error_data6 = _props_row__valuesCache_error6.data) === null || _props_row__valuesCache_error_data6 === void 0 ? void 0 : _props_row__valuesCache_error_data6[\"code\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error7 = props.row._valuesCache.error) === null || _props_row__valuesCache_error7 === void 0 ? void 0 : (_props_row__valuesCache_error_data7 = _props_row__valuesCache_error7.data) === null || _props_row__valuesCache_error_data7 === void 0 ? void 0 : _props_row__valuesCache_error_data7[\"code\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 98\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"proposed_by\",\n                                                            children: \"Propos\\xe9 par\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_19__.Dropdown, {\n                                                            className: ((_props_row__valuesCache_error8 = props.row._valuesCache.error) === null || _props_row__valuesCache_error8 === void 0 ? void 0 : (_props_row__valuesCache_error_data8 = _props_row__valuesCache_error8.data) === null || _props_row__valuesCache_error_data8 === void 0 ? void 0 : _props_row__valuesCache_error_data8[\"proposed_by\"]) ? \"p-invalid\" : \"\",\n                                                            filter: true,\n                                                            id: \"proposed_by\",\n                                                            value: dropdownItemProposedBy,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"proposed_by\", e.value.name);\n                                                                setDropdownItemProposedBy(e.value);\n                                                            },\n                                                            options: _lib_enums__WEBPACK_IMPORTED_MODULE_4__.$ProposedByEnum.enum.map(function(val) {\n                                                                return {\n                                                                    \"name\": val,\n                                                                    \"code\": val\n                                                                };\n                                                            }),\n                                                            optionLabel: \"name\",\n                                                            placeholder: \"Choisir un\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error9 = props.row._valuesCache.error) === null || _props_row__valuesCache_error9 === void 0 ? void 0 : (_props_row__valuesCache_error_data9 = _props_row__valuesCache_error9.data) === null || _props_row__valuesCache_error_data9 === void 0 ? void 0 : _props_row__valuesCache_error_data9[\"proposedBy\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error10 = props.row._valuesCache.error) === null || _props_row__valuesCache_error10 === void 0 ? void 0 : (_props_row__valuesCache_error_data10 = _props_row__valuesCache_error10.data) === null || _props_row__valuesCache_error_data10 === void 0 ? void 0 : _props_row__valuesCache_error_data10[\"proposedBy\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 104\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"validated\",\n                                                            children: \"Valid\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_togglebutton__WEBPACK_IMPORTED_MODULE_20__.ToggleButton, {\n                                                            className: ((_props_row__valuesCache_error11 = props.row._valuesCache.error) === null || _props_row__valuesCache_error11 === void 0 ? void 0 : (_props_row__valuesCache_error_data11 = _props_row__valuesCache_error11.data) === null || _props_row__valuesCache_error_data11 === void 0 ? void 0 : _props_row__valuesCache_error_data11[\"validated\"]) ? \"p-invalid\" : \"\",\n                                                            onLabel: \"Oui\",\n                                                            offLabel: \"Non\",\n                                                            color: \"green\",\n                                                            id: \"validated\",\n                                                            checked: theme_validated,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"validated\", e.value);\n                                                                setThemeValidated(e.value);\n                                                            },\n                                                            placeholder: \"Choisir un\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error12 = props.row._valuesCache.error) === null || _props_row__valuesCache_error12 === void 0 ? void 0 : (_props_row__valuesCache_error_data12 = _props_row__valuesCache_error12.data) === null || _props_row__valuesCache_error_data12 === void 0 ? void 0 : _props_row__valuesCache_error_data12[\"validated\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error13 = props.row._valuesCache.error) === null || _props_row__valuesCache_error13 === void 0 ? void 0 : (_props_row__valuesCache_error_data13 = _props_row__valuesCache_error13.data) === null || _props_row__valuesCache_error_data13 === void 0 ? void 0 : _props_row__valuesCache_error_data13[\"validated\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 103\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"domain\",\n                                                            children: \"Domaine\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_19__.Dropdown, {\n                                                            className: ((_props_row__valuesCache_error14 = props.row._valuesCache.error) === null || _props_row__valuesCache_error14 === void 0 ? void 0 : (_props_row__valuesCache_error_data14 = _props_row__valuesCache_error14.data) === null || _props_row__valuesCache_error_data14 === void 0 ? void 0 : _props_row__valuesCache_error_data14[\"domain\"]) ? \"p-invalid\" : \"\",\n                                                            filter: true,\n                                                            id: \"domain\",\n                                                            value: dropdownItemDomain,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"domain\", e.value.code);\n                                                                setDropdownItemDomain(e.value);\n                                                            },\n                                                            options: (_domains = domains) === null || _domains === void 0 ? void 0 : _domains.data.data.results.map(function(val) {\n                                                                return {\n                                                                    \"name\": val.title,\n                                                                    \"code\": val.id\n                                                                };\n                                                            }),\n                                                            optionLabel: \"name\",\n                                                            placeholder: \"Choisir un\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error15 = props.row._valuesCache.error) === null || _props_row__valuesCache_error15 === void 0 ? void 0 : (_props_row__valuesCache_error_data15 = _props_row__valuesCache_error15.data) === null || _props_row__valuesCache_error_data15 === void 0 ? void 0 : _props_row__valuesCache_error_data15[\"domain\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error16 = props.row._valuesCache.error) === null || _props_row__valuesCache_error16 === void 0 ? void 0 : (_props_row__valuesCache_error_data16 = _props_row__valuesCache_error16.data) === null || _props_row__valuesCache_error_data16 === void 0 ? void 0 : _props_row__valuesCache_error_data16[\"domain\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 100\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"process\",\n                                                            children: \"Processus\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_19__.Dropdown, {\n                                                            className: ((_props_row__valuesCache_error17 = props.row._valuesCache.error) === null || _props_row__valuesCache_error17 === void 0 ? void 0 : (_props_row__valuesCache_error_data17 = _props_row__valuesCache_error17.data) === null || _props_row__valuesCache_error_data17 === void 0 ? void 0 : _props_row__valuesCache_error_data17[\"process\"]) ? \"p-invalid\" : \"\",\n                                                            filter: true,\n                                                            id: \"process\",\n                                                            value: dropdownItemProcess,\n                                                            onChange: (e)=>{\n                                                                handleTheme(\"process\", e.value.code);\n                                                                setDropdownItemProcess(e.value);\n                                                            },\n                                                            options: (_processes = processes) === null || _processes === void 0 ? void 0 : _processes.data.data.results.map(function(val) {\n                                                                return {\n                                                                    \"name\": val.title,\n                                                                    \"code\": val.id\n                                                                };\n                                                            }),\n                                                            optionLabel: \"name\",\n                                                            placeholder: \"Choisir un\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error18 = props.row._valuesCache.error) === null || _props_row__valuesCache_error18 === void 0 ? void 0 : (_props_row__valuesCache_error_data18 = _props_row__valuesCache_error18.data) === null || _props_row__valuesCache_error_data18 === void 0 ? void 0 : _props_row__valuesCache_error_data18[\"process\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error19 = props.row._valuesCache.error) === null || _props_row__valuesCache_error19 === void 0 ? void 0 : (_props_row__valuesCache_error_data19 = _props_row__valuesCache_error19.data) === null || _props_row__valuesCache_error_data19 === void 0 ? void 0 : _props_row__valuesCache_error_data19[\"process\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 101\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"start_date\",\n                                                            children: \"Date D\\xe9but\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_calendar__WEBPACK_IMPORTED_MODULE_21__.Calendar, {\n                                                            className: ((_props_row__valuesCache_error20 = props.row._valuesCache.error) === null || _props_row__valuesCache_error20 === void 0 ? void 0 : (_props_row__valuesCache_error_data20 = _props_row__valuesCache_error20.data) === null || _props_row__valuesCache_error_data20 === void 0 ? void 0 : _props_row__valuesCache_error_data20[\"month_start\"]) ? \"p-invalid\" : \"\",\n                                                            id: \"month_start\",\n                                                            value: new Date(theme_start_date),\n                                                            onChange: (e)=>{\n                                                                var _e_value;\n                                                                handleTheme(\"month_start\", (_e_value = e.value) === null || _e_value === void 0 ? void 0 : _e_value.toISOString().split(\"T\")[0]);\n                                                                setThemeStartDate(e.value);\n                                                            },\n                                                            locale: \"fr\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error21 = props.row._valuesCache.error) === null || _props_row__valuesCache_error21 === void 0 ? void 0 : (_props_row__valuesCache_error_data21 = _props_row__valuesCache_error21.data) === null || _props_row__valuesCache_error_data21 === void 0 ? void 0 : _props_row__valuesCache_error_data21[\"month_start\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error22 = props.row._valuesCache.error) === null || _props_row__valuesCache_error22 === void 0 ? void 0 : (_props_row__valuesCache_error_data22 = _props_row__valuesCache_error22.data) === null || _props_row__valuesCache_error_data22 === void 0 ? void 0 : _props_row__valuesCache_error_data22[\"month_start\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 105\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-12 md:col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"end_date\",\n                                                            children: \"Date Fin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_calendar__WEBPACK_IMPORTED_MODULE_21__.Calendar, {\n                                                            className: ((_props_row__valuesCache_error23 = props.row._valuesCache.error) === null || _props_row__valuesCache_error23 === void 0 ? void 0 : (_props_row__valuesCache_error_data23 = _props_row__valuesCache_error23.data) === null || _props_row__valuesCache_error_data23 === void 0 ? void 0 : _props_row__valuesCache_error_data23[\"month_end\"]) ? \"p-invalid\" : \"\",\n                                                            id: \"month_end\",\n                                                            value: new Date(theme_end_date),\n                                                            onChange: (e)=>{\n                                                                var _e_value;\n                                                                handleTheme(\"month_end\", (_e_value = e.value) === null || _e_value === void 0 ? void 0 : _e_value.toISOString().split(\"T\")[0]);\n                                                                setThemeEndDate(e.value);\n                                                            },\n                                                            locale: \"fr\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        ((_props_row__valuesCache_error24 = props.row._valuesCache.error) === null || _props_row__valuesCache_error24 === void 0 ? void 0 : (_props_row__valuesCache_error_data24 = _props_row__valuesCache_error24.data) === null || _props_row__valuesCache_error_data24 === void 0 ? void 0 : _props_row__valuesCache_error_data24[\"month_end\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                            className: \"p-error\",\n                                                            children: (_props_row__valuesCache_error25 = props.row._valuesCache.error) === null || _props_row__valuesCache_error25 === void 0 ? void 0 : (_props_row__valuesCache_error_data25 = _props_row__valuesCache_error25.data) === null || _props_row__valuesCache_error_data25 === void 0 ? void 0 : _props_row__valuesCache_error_data25[\"month_end\"][0]\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 103\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"picklist_concerned_structrures\",\n                                                            children: \"Structures Concern\\xe9es\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"card\",\n                                                            style: {\n                                                                borderColor: ((_props_row__valuesCache_error26 = props.row._valuesCache.error) === null || _props_row__valuesCache_error26 === void 0 ? void 0 : (_props_row__valuesCache_error_data26 = _props_row__valuesCache_error26.data) === null || _props_row__valuesCache_error_data26 === void 0 ? void 0 : _props_row__valuesCache_error_data26[\"concerned_structrures\"]) ? \"#e24c4c\" : \"\"\n                                                            },\n                                                            children: [\n                                                                ((_props_row__valuesCache_error27 = props.row._valuesCache.error) === null || _props_row__valuesCache_error27 === void 0 ? void 0 : (_props_row__valuesCache_error_data27 = _props_row__valuesCache_error27.data) === null || _props_row__valuesCache_error_data27 === void 0 ? void 0 : _props_row__valuesCache_error_data27[\"concerned_structrures\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    className: \"p-error\",\n                                                                    children: (_props_row__valuesCache_error28 = props.row._valuesCache.error) === null || _props_row__valuesCache_error28 === void 0 ? void 0 : (_props_row__valuesCache_error_data28 = _props_row__valuesCache_error28.data) === null || _props_row__valuesCache_error_data28 === void 0 ? void 0 : _props_row__valuesCache_error_data28[\"concerned_structrures\"][0]\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 119\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_22__.PickList, {\n                                                                    id: \"picklist_concerned_structrures\",\n                                                                    source: picklistSourceValueConcernedStructures,\n                                                                    target: picklistTargetValueConcernedStructures,\n                                                                    sourceHeader: \"De\",\n                                                                    targetHeader: \"A\",\n                                                                    itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm\",\n                                                                            children: [\n                                                                                item.libell_stru,\n                                                                                \" | \",\n                                                                                item.code_mnemonique\n                                                                            ]\n                                                                        }, void 0, true, void 0, void 0),\n                                                                    onChange: (e)=>{\n                                                                        setPicklistSourceValueConcernedStructures(e.source);\n                                                                        setPicklistTargetValueConcernedStructures(e.target);\n                                                                        handleTheme(\"concerned_structures\", e.target.map((struct)=>struct.id));\n                                                                    },\n                                                                    sourceStyle: {\n                                                                        height: \"200px\"\n                                                                    },\n                                                                    targetStyle: {\n                                                                        height: \"200px\"\n                                                                    },\n                                                                    filter: true,\n                                                                    filterBy: \"libell_stru,code_mnemonique\",\n                                                                    filterMatchMode: \"contains\",\n                                                                    sourceFilterPlaceholder: \"Recherche\",\n                                                                    targetFilterPlaceholder: \"Recherche\",\n                                                                    className: ((_props_row__valuesCache_error29 = props.row._valuesCache.error) === null || _props_row__valuesCache_error29 === void 0 ? void 0 : (_props_row__valuesCache_error_data29 = _props_row__valuesCache_error29.data) === null || _props_row__valuesCache_error_data29 === void 0 ? void 0 : _props_row__valuesCache_error_data29[\"concerned_structrures\"]) ? \"p-invalid\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 53\n                                                                }, undefined),\n                                                                ((_props_row__valuesCache_error30 = props.row._valuesCache.error) === null || _props_row__valuesCache_error30 === void 0 ? void 0 : (_props_row__valuesCache_error_data30 = _props_row__valuesCache_error30.data) === null || _props_row__valuesCache_error_data30 === void 0 ? void 0 : _props_row__valuesCache_error_data30[\"concerned_structrures\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    className: \"p-error\",\n                                                                    children: (_props_row__valuesCache_error31 = props.row._valuesCache.error) === null || _props_row__valuesCache_error31 === void 0 ? void 0 : (_props_row__valuesCache_error_data31 = _props_row__valuesCache_error31.data) === null || _props_row__valuesCache_error_data31 === void 0 ? void 0 : _props_row__valuesCache_error_data31[\"concerned_structrures\"][0]\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 119\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"field col-6 text-center \",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"picklist_proposing_structures\",\n                                                            children: \"Structures Proposantes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"card\",\n                                                            style: {\n                                                                borderColor: ((_props_row__valuesCache_error32 = props.row._valuesCache.error) === null || _props_row__valuesCache_error32 === void 0 ? void 0 : (_props_row__valuesCache_error_data32 = _props_row__valuesCache_error32.data) === null || _props_row__valuesCache_error_data32 === void 0 ? void 0 : _props_row__valuesCache_error_data32[\"proposing_structures\"]) ? \"#e24c4c\" : \"\"\n                                                            },\n                                                            children: [\n                                                                ((_props_row__valuesCache_error33 = props.row._valuesCache.error) === null || _props_row__valuesCache_error33 === void 0 ? void 0 : (_props_row__valuesCache_error_data33 = _props_row__valuesCache_error33.data) === null || _props_row__valuesCache_error_data33 === void 0 ? void 0 : _props_row__valuesCache_error_data33[\"proposing_structures\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                    className: \"p-error w-full text-sm \",\n                                                                    children: (_props_row__valuesCache_error34 = props.row._valuesCache.error) === null || _props_row__valuesCache_error34 === void 0 ? void 0 : (_props_row__valuesCache_error_data34 = _props_row__valuesCache_error34.data) === null || _props_row__valuesCache_error_data34 === void 0 ? void 0 : _props_row__valuesCache_error_data34[\"proposing_structures\"][0]\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 118\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_22__.PickList, {\n                                                                    id: \"picklist_proposing_structures\",\n                                                                    source: picklistSourceValueProposingStructures,\n                                                                    target: picklistTargetValueProposingStructures,\n                                                                    sourceHeader: \"De\",\n                                                                    targetHeader: \"A\",\n                                                                    itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm\",\n                                                                            children: [\n                                                                                item.code_mnemonique,\n                                                                                \" | \",\n                                                                                item.libell_stru\n                                                                            ]\n                                                                        }, void 0, true, void 0, void 0),\n                                                                    onChange: (e)=>{\n                                                                        setPicklistSourceValueProposingStructures(e.source);\n                                                                        setPicklistTargetValueProposingStructures(e.target);\n                                                                        handleTheme(\"proposing_structures\", e.target.map((struct)=>struct.id));\n                                                                    },\n                                                                    sourceStyle: {\n                                                                        height: \"200px\"\n                                                                    },\n                                                                    targetStyle: {\n                                                                        height: \"200px\"\n                                                                    },\n                                                                    filter: true,\n                                                                    filterBy: \"libell_stru,code_mnemonique\",\n                                                                    filterMatchMode: \"contains\",\n                                                                    sourceFilterPlaceholder: \"Recherche\",\n                                                                    targetFilterPlaceholder: \"Recherche\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 53\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    activeStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"field col-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"picklist_risks\",\n                                                    children: \"Risques\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_22__.PickList, {\n                                                        id: \"picklist_risks\",\n                                                        source: picklistSourceValueRisks,\n                                                        target: picklistTargetValueRisks,\n                                                        sourceHeader: \"Disponibles\",\n                                                        targetHeader: \"S\\xe9l\\xe9ctionn\\xe9s\",\n                                                        itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: item.description\n                                                            }, void 0, false, void 0, void 0),\n                                                        onChange: (e)=>{\n                                                            setPicklistSourceValueRisks(e.source);\n                                                            setPicklistTargetValueRisks(e.target);\n                                                            handleTheme(\"risks\", e.target.map((risk)=>risk.id));\n                                                        },\n                                                        sourceStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        targetStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        filter: true,\n                                                        filterBy: \"content\",\n                                                        filterMatchMode: \"contains\",\n                                                        sourceFilterPlaceholder: \"Recherche\",\n                                                        targetFilterPlaceholder: \"Recherche\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    activeStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"field col-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"picklist_goals\",\n                                                    children: \"Objectifs\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"card\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_22__.PickList, {\n                                                        id: \"picklist_goals\",\n                                                        source: picklistSourceValueGoals,\n                                                        target: picklistTargetValueGoals,\n                                                        sourceHeader: \"Disponibles\",\n                                                        targetHeader: \"S\\xe9l\\xe9ctionn\\xe9s\",\n                                                        itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: item.description\n                                                            }, void 0, false, void 0, void 0),\n                                                        onChange: (e)=>{\n                                                            setPicklistSourceValueGoals(e.source);\n                                                            setPicklistTargetValueGoals(e.target);\n                                                            handleTheme(\"goals\", e.target.map((goal)=>goal.id));\n                                                        },\n                                                        sourceStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        targetStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        filter: true,\n                                                        filterBy: \"content\",\n                                                        filterMatchMode: \"contains\",\n                                                        sourceFilterPlaceholder: \"Recherche\",\n                                                        targetFilterPlaceholder: \"Recherche\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            flexDirection: \"row\",\n                                            pt: 2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                color: \"inherit\",\n                                                disabled: activeStep === 0,\n                                                onClick: handleBack,\n                                                sx: {\n                                                    mr: 1\n                                                },\n                                                children: \"Back\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                sx: {\n                                                    flex: \"1 1 auto\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            isStepOptional(activeStep) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                color: \"inherit\",\n                                                onClick: handleSkip,\n                                                children: \"Skip\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                                                onClick: handleNext,\n                                                children: activeStep === steps.length - 1 ? \"Finish\" : \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n                lineNumber: 188,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 187,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s(ThemeEditForm, \"sav7yWpWzY6iQvOg2taVuP2fUus=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiPlanList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiThemeList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiRiskList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiGoalList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiDomainList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiProcessList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_3__.useApiStructurelqsList\n    ];\n});\n_c = ThemeEditForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ThemeEditForm);\nvar _c;\n$RefreshReg$(_c, \"ThemeEditForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/editForm.tsx\n"));

/***/ })

});