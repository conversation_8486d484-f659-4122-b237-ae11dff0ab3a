/**
 * Example component demonstrating how to use centralized schemas
 * This shows different ways to use the schemas from /lib/schemas.ts
 */

import React, { useState } from 'react';
import { MRT_PaginationState } from 'material-react-table';
import { 
  $Plan, 
  $Mission, 
  $User, 
  $Arbitration, 
  getSchema, 
  schemas 
} from '@/lib/schemas';
import { 
  useApiPlanList, 
  useApiMissionList, 
  useApiUserList,
  useApiArbitrationList 
} from '@/hooks/useNextApi';
import GenericTable from '@/utilities/components/GenericTAble';

// Example 1: Basic usage with direct schema import
export function PlansTableExample() {
  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const { data: plans, isLoading, error } = useApiPlanList({
    page: pagination.pageIndex + 1
  });

  return (
    <div className="card">
      <h5>Plans Table (Using $Plan schema)</h5>
      <GenericTable
        data_={plans}
        isLoading={isLoading}
        error={error}
        data_type={$Plan}  // Direct schema usage
        pagination={{ set: setPagination, pagi: pagination }}
      />
    </div>
  );
}

// Example 2: Dynamic schema selection
export function DynamicTableExample() {
  const [selectedModel, setSelectedModel] = useState<string>('Plan');
  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // Get schema dynamically
  const schema = getSchema(selectedModel);

  // Dynamic data fetching based on selected model
  const { data: plans } = useApiPlanList({ page: pagination.pageIndex + 1 });
  const { data: missions } = useApiMissionList({ page: pagination.pageIndex + 1 });
  const { data: arbitrations } = useApiArbitrationList({ page: pagination.pageIndex + 1 });

  const getData = () => {
    switch (selectedModel) {
      case 'Plan': return plans;
      case 'Mission': return missions;
      case 'Arbitration': return arbitrations;
      default: return null;
    }
  };

  return (
    <div className="card">
      <h5>Dynamic Table (Schema selected at runtime)</h5>
      
      <div className="field">
        <label htmlFor="model-select">Select Model:</label>
        <select 
          id="model-select"
          value={selectedModel} 
          onChange={(e) => setSelectedModel(e.target.value)}
          className="p-inputtext"
        >
          {Object.keys(schemas).map(modelName => (
            <option key={modelName} value={modelName}>
              {modelName}
            </option>
          ))}
        </select>
      </div>

      {schema && (
        <GenericTable
          data_={getData()}
          isLoading={false}
          error={null}
          data_type={schema}  // Dynamic schema usage
          pagination={{ set: setPagination, pagi: pagination }}
        />
      )}
    </div>
  );
}

// Example 3: Schema field inspection
export function SchemaInspectorExample() {
  const [selectedSchema, setSelectedSchema] = useState<string>('Plan');
  
  const schema = getSchema(selectedSchema);

  return (
    <div className="card">
      <h5>Schema Inspector</h5>
      
      <div className="field">
        <label htmlFor="schema-select">Select Schema:</label>
        <select 
          id="schema-select"
          value={selectedSchema} 
          onChange={(e) => setSelectedSchema(e.target.value)}
          className="p-inputtext"
        >
          {Object.keys(schemas).map(modelName => (
            <option key={modelName} value={modelName}>
              {modelName}
            </option>
          ))}
        </select>
      </div>

      {schema && (
        <div className="mt-3">
          <h6>Fields in {selectedSchema} schema:</h6>
          <div className="grid">
            {Object.entries(schema.properties).map(([fieldName, fieldConfig]) => (
              <div key={fieldName} className="col-12 md:col-6 lg:col-4">
                <div className="surface-card p-3 border-round">
                  <div className="font-bold text-primary">{fieldName}</div>
                  <div className="text-sm text-600">{fieldConfig.title}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Example 4: Multiple tables with different schemas
export function MultiTableExample() {
  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: 5,
  });

  const { data: plans, isLoading: plansLoading } = useApiPlanList({
    page: pagination.pageIndex + 1
  });
  
  const { data: missions, isLoading: missionsLoading } = useApiMissionList({
    page: pagination.pageIndex + 1
  });

  return (
    <div className="grid">
      <div className="col-12 lg:col-6">
        <div className="card">
          <h5>Plans</h5>
          <GenericTable
            data_={plans}
            isLoading={plansLoading}
            error={null}
            data_type={$Plan}
            pagination={{ set: setPagination, pagi: pagination }}
          />
        </div>
      </div>
      
      <div className="col-12 lg:col-6">
        <div className="card">
          <h5>Missions</h5>
          <GenericTable
            data_={missions}
            isLoading={missionsLoading}
            error={null}
            data_type={$Mission}
            pagination={{ set: setPagination, pagi: pagination }}
          />
        </div>
      </div>
    </div>
  );
}

// Main example component combining all examples
export default function SchemaExamples() {
  const [activeExample, setActiveExample] = useState<string>('basic');

  const examples = {
    basic: <PlansTableExample />,
    dynamic: <DynamicTableExample />,
    inspector: <SchemaInspectorExample />,
    multi: <MultiTableExample />,
  };

  return (
    <div className="schema-examples">
      <div className="card">
        <h4>Schema Usage Examples</h4>
        <p>These examples demonstrate different ways to use the centralized schemas from <code>/lib/schemas.ts</code></p>
        
        <div className="field">
          <label htmlFor="example-select">Select Example:</label>
          <select 
            id="example-select"
            value={activeExample} 
            onChange={(e) => setActiveExample(e.target.value)}
            className="p-inputtext"
          >
            <option value="basic">Basic Usage</option>
            <option value="dynamic">Dynamic Schema Selection</option>
            <option value="inspector">Schema Inspector</option>
            <option value="multi">Multiple Tables</option>
          </select>
        </div>
      </div>

      {examples[activeExample as keyof typeof examples]}
    </div>
  );
}
