import NextAuth from "next-auth"
import Credentials from "next-auth/providers/credentials"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { prisma } from "./prisma"
import bcrypt from "bcryptjs"

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials,req) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Find user by email
          const user = await prisma.user.findUnique({
            where: { email: credentials.email as string }
          })

          if (!user) {
            return null
          }

          // Find account with password
          const account = await prisma.account.findFirst({
            where: {
              userId: user.id,
              providerId: "credential"
            }
          })

          if (!account?.password) {
            return null
          }

          // Verify password
          const isValidPassword = await bcrypt.compare(credentials.password, account.password)

          if (!isValidPassword) {
            return null
          }

          // Update last login
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLogin: new Date() }
          })

          return {
            id: user.id,
            email: user.email,
            name: user.username,
            username: user.username,
            isStaff: user.isStaff,
            isSuperuser: user.isSuperuser,
            isActive: user.isActive,
          }
        } catch (error) {
          console.error('Auth error:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt" as const,
    maxAge: 60 * 60 * 24 * 7, // 7 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.username = user.username
        token.isStaff = user.isStaff
        token.isSuperuser = user.isSuperuser
        token.isActive = user.isActive
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.username = token.username as string
        session.user.isStaff = token.isStaff as boolean
        session.user.isSuperuser = token.isSuperuser as boolean
        session.user.isActive = token.isActive as boolean
      }
      return session
    }
  },
  pages: {
    signIn: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET,
})
console.log('✅ NextAuth configured successfully')

export type Session = {
  user: {
    id: string
    email: string
    name: string
    username: string
    isStaff: boolean
    isSuperuser: boolean
    isActive: boolean
  }
}
