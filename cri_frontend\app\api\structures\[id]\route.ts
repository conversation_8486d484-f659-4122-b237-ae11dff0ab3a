import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/structures/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid structure ID' }, { status: 400 })
    }

    const structure = await prisma.criStructview.findUnique({
      where: { id: BigInt(id) },
      include: {
        structureCorrespondents: {
          include: {
            correspondents: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
                email: true,
              }
            }
          }
        },
        structureInterim: {
          include: {
            interim: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
                email: true,
              }
            }
          }
        },
        themeProposing: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
            domain: {
              select: {
                id: true,
                title: true,
              }
            },
            process: {
              select: {
                id: true,
                title: true,
              }
            }
          }
        },
        themeConcerned: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
            domain: {
              select: {
                id: true,
                title: true,
              }
            },
            process: {
              select: {
                id: true,
                title: true,
              }
            }
          }
        },
        recommendations: {
          select: {
            id: true,
            title: true,
            priority: true,
            etat: true,
          }
        }
      },
    })

    if (!structure) {
      return NextResponse.json({ error: 'Structure not found' }, { status: 404 })
    }

    // Convert BigInt to number for JSON serialization
    const structureData = {
      ...structure,
      id: Number(structure.id),
    }

    return NextResponse.json({ data: structureData })
  } catch (error) {
    console.error('Error fetching structure:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
