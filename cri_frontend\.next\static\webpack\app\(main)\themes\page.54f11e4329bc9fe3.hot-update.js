"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/themes/page",{

/***/ "(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx":
/*!*********************************************************!*\
  !*** ./app/(main)/themes/(components)/GenericTAble.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GenericTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! material-react-table/locales/fr */ \"(app-client)/./node_modules/material-react-table/locales/fr/index.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/toast */ \"(app-client)/./node_modules/primereact/toast/toast.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tinymce/tinymce-react */ \"(app-client)/./node_modules/@tinymce/tinymce-react/lib/es2015/main/ts/index.js\");\n/* harmony import */ var primereact_chip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/chip */ \"(app-client)/./node_modules/primereact/chip/chip.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_Can__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/Can */ \"(app-client)/./app/Can.ts\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction GenericTable(data_) {\n    var _getCookie;\n    _s();\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    const [theme_id, setThemeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rowTobe, setRowTobe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createVisible, setCreateVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rowActionEnabled, setRowActionEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const open = Boolean(anchorEl);\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const toast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { data: arbitrations, isLoading, error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList)();\n    const { data: themes, isLoading: isLoading_themes, error: error_themes } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList)();\n    const { data: data_create, error: error_create, isPending: isMutating_create, mutate: trigger_create } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate)();\n    const { data: data_update, error: error_update, isPending: isMutating_update, mutate: trigger_update } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate)();\n    const getSeverity = (str)=>{\n        switch(str){\n            case \"Vice Pr\\xe9sident\":\n                return \"success\";\n            case \"Contr\\xf4le Interne\":\n                return \"warning\";\n            case \"Audit Interne\":\n                return \"warning\";\n            case \"Structures\":\n                return \"danger\";\n            default:\n                return null;\n        }\n    };\n    function onPaginationChange(state) {\n        console.log(data_.pagination);\n        data_.pagination.set(state);\n    }\n    const accept = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"info\",\n            summary: \"Confirmed\",\n            detail: \"You have accepted\",\n            life: 3000\n        });\n    };\n    const reject = ()=>{\n        var _toast_current;\n        (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n            severity: \"warn\",\n            summary: \"Rejected\",\n            detail: \"You have rejected\",\n            life: 3000\n        });\n    };\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setPageNumber(1);\n    }\n    function changePage(offset) {\n        setPageNumber((prevPageNumber)=>prevPageNumber + offset);\n    }\n    function previousPage() {\n        changePage(-1);\n    }\n    function nextPage() {\n        changePage(1);\n    }\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                header: \"ID\",\n                accessorKey: \"id\",\n                size: 70,\n                Edit: ()=>null\n            },\n            {\n                header: \"Aribtrage\",\n                accessorKey: \"arbitration\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"arbitration\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-11rem text-sm\",\n                        severity: row.original.arbitration.plan.code.includes(\"Audit\") ? \"danger\" : \"info\",\n                        value: row.original.arbitration.plan.code\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_arbitration, _row__valuesCache_arbitration1, _arbitrations_data_data_results, _arbitrations_data_data, _arbitrations_data, _arbitrations;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _arbitrations_data_data_results, _arbitrations_data_data, _arbitrations_data, _arbitrations, _arbitrations_data_data_results1, _arbitrations_data_data1, _arbitrations_data1, _arbitrations1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                arbitration: (_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : (_arbitrations_data = _arbitrations.data) === null || _arbitrations_data === void 0 ? void 0 : (_arbitrations_data_data = _arbitrations_data.data) === null || _arbitrations_data_data === void 0 ? void 0 : (_arbitrations_data_data_results = _arbitrations_data_data.results) === null || _arbitrations_data_data_results === void 0 ? void 0 : _arbitrations_data_data_results.find((arbi)=>arbi.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                arbitration: (_arbitrations1 = arbitrations) === null || _arbitrations1 === void 0 ? void 0 : (_arbitrations_data1 = _arbitrations1.data) === null || _arbitrations_data1 === void 0 ? void 0 : (_arbitrations_data_data1 = _arbitrations_data1.data) === null || _arbitrations_data_data1 === void 0 ? void 0 : (_arbitrations_data_data_results1 = _arbitrations_data_data1.results) === null || _arbitrations_data_data_results1 === void 0 ? void 0 : _arbitrations_data_data_results1.find((arbi)=>arbi.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir arbitrage\",\n                        value: {\n                            name: ((_row__valuesCache_arbitration = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration === void 0 ? void 0 : _row__valuesCache_arbitration.id) || null,\n                            code: ((_row__valuesCache_arbitration1 = row._valuesCache.arbitration) === null || _row__valuesCache_arbitration1 === void 0 ? void 0 : _row__valuesCache_arbitration1.id) || null\n                        },\n                        options: ((_arbitrations = arbitrations) === null || _arbitrations === void 0 ? void 0 : (_arbitrations_data = _arbitrations.data) === null || _arbitrations_data === void 0 ? void 0 : (_arbitrations_data_data = _arbitrations_data.data) === null || _arbitrations_data_data === void 0 ? void 0 : (_arbitrations_data_data_results = _arbitrations_data_data.results) === null || _arbitrations_data_data_results === void 0 ? void 0 : _arbitrations_data_data_results.map((arbi)=>{\n                            return {\n                                code: arbi.id,\n                                name: arbi.plan.type\n                            };\n                        })) || []\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Propos\\xe9 par\",\n                accessorKey: \"theme\",\n                id: \"theme_proposed_by\",\n                muiTableHeadCellProps: {\n                    align: \"center\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"center\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                Edit: ()=>null,\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_6__.Tag, {\n                        className: \"w-9rem text-sm\",\n                        severity: getSeverity(cell.getValue().proposedBy),\n                        value: cell.getValue().proposedBy\n                    }, row.original.code + row.original.created, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 36\n                    }, this);\n                }\n            },\n            {\n                header: \"Structures proposantes\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"proposing_structures\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    var _cell_getValue_proposingStructures;\n                    console.log(cell.getValue().proposingStructures);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: ((_cell_getValue_proposingStructures = cell.getValue().proposingStructures) === null || _cell_getValue_proposingStructures === void 0 ? void 0 : _cell_getValue_proposingStructures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                style: {\n                                    backgroundColor: \"green\",\n                                    color: \"white\"\n                                },\n                                label: val.codeMnemonique || val.code_mnemonique\n                            }, \"thm\".concat(row.original.theme.id, \"_ps\").concat(idx), false, {\n                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 91\n                            }, this))) || []\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 20\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Structures concern\\xe9es\",\n                accessorKey: \"theme\",\n                muiTableHeadCellProps: {\n                    align: \"left\"\n                },\n                muiTableBodyCellProps: {\n                    align: \"left\"\n                },\n                muiTableFooterCellProps: {\n                    align: \"center\"\n                },\n                id: \"concerned_structures\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [\n                            \" \",\n                            cell.getValue().concerned_structures.map((val, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_chip__WEBPACK_IMPORTED_MODULE_9__.Chip, {\n                                    style: {\n                                        backgroundColor: \"green\",\n                                        color: \"white\"\n                                    },\n                                    label: val.code_mnemonique\n                                }, \"thm\".concat(row.original.theme.id, \"_cs\").concat(idx), false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 137\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Domaine\",\n                accessorKey: \"domain\",\n                id: \"domain\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.domain.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Processus\",\n                accessorKey: \"process\",\n                id: \"process\",\n                Cell: (param)=>{\n                    let { cell, row } = param;\n                    return row.original.theme.process.title;\n                },\n                Edit: ()=>null\n            },\n            {\n                header: \"Intitul\\xe9\",\n                accessorKey: \"theme\",\n                id: \"title\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.theme.title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>{\n                    let { row } = param;\n                    var _row__valuesCache_theme, _row__valuesCache_theme1, _themes_data_data_results, _themes_data_data, _themes_data, _themes;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_7__.Dropdown, {\n                        filter: true,\n                        onChange: (e)=>{\n                            var _themes_data_data_results, _themes_data_data, _themes_data, _themes, _themes_data_data_results1, _themes_data_data1, _themes_data1, _themes1;\n                            console.log(e);\n                            setRowTobe({\n                                ...rowTobe,\n                                theme: (_themes = themes) === null || _themes === void 0 ? void 0 : (_themes_data = _themes.data) === null || _themes_data === void 0 ? void 0 : (_themes_data_data = _themes_data.data) === null || _themes_data_data === void 0 ? void 0 : (_themes_data_data_results = _themes_data_data.results) === null || _themes_data_data_results === void 0 ? void 0 : _themes_data_data_results.find((thm)=>thm.id === e.value.code)\n                            });\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                theme: (_themes1 = themes) === null || _themes1 === void 0 ? void 0 : (_themes_data1 = _themes1.data) === null || _themes_data1 === void 0 ? void 0 : (_themes_data_data1 = _themes_data1.data) === null || _themes_data_data1 === void 0 ? void 0 : (_themes_data_data_results1 = _themes_data_data1.results) === null || _themes_data_data_results1 === void 0 ? void 0 : _themes_data_data_results1.find((thm)=>thm.id === e.value.code)\n                            };\n                        },\n                        optionLabel: \"name\",\n                        placeholder: \"Choisir un th\\xe9me\",\n                        value: {\n                            name: ((_row__valuesCache_theme = row._valuesCache.theme) === null || _row__valuesCache_theme === void 0 ? void 0 : _row__valuesCache_theme.title) || null,\n                            code: ((_row__valuesCache_theme1 = row._valuesCache.theme) === null || _row__valuesCache_theme1 === void 0 ? void 0 : _row__valuesCache_theme1.id) || null\n                        },\n                        options: ((_themes = themes) === null || _themes === void 0 ? void 0 : (_themes_data = _themes.data) === null || _themes_data === void 0 ? void 0 : (_themes_data_data = _themes_data.data) === null || _themes_data_data === void 0 ? void 0 : (_themes_data_data_results = _themes_data_data.results) === null || _themes_data_data_results === void 0 ? void 0 : _themes_data_data_results.map((thm)=>{\n                            return {\n                                code: thm.id,\n                                name: thm.title\n                            };\n                        })) || []\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 30\n                    }, this);\n                }\n            },\n            {\n                header: \"Remarque\",\n                accessorKey: \"note\",\n                id: \"note\",\n                Cell: (param)=>/*#__PURE__*/ {\n                    let { cell, row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"white-space-normal\",\n                        children: row.original.note\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 36\n                    }, this);\n                },\n                Edit: (param)=>/*#__PURE__*/ {\n                    let { row } = param;\n                    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tinymce_tinymce_react__WEBPACK_IMPORTED_MODULE_2__.Editor, {\n                        id: \"note\",\n                        initialValue: row.original.note,\n                        tinymceScriptSrc: \"http://localhost:3000/tinymce/tinymce.min.js\",\n                        apiKey: \"none\",\n                        init: {\n                            height: 500,\n                            menubar: true,\n                            plugins: [\n                                \"advlist\",\n                                \"autolink\",\n                                \"lists\",\n                                \"link\",\n                                \"image\",\n                                \"charmap\",\n                                \"print\",\n                                \"preview\",\n                                \"anchor\",\n                                \"searchreplace\",\n                                \"visualblocks\",\n                                \"code\",\n                                \"fullscreen\",\n                                \"insertdatetime\",\n                                \"media\",\n                                \"table\",\n                                \"paste\",\n                                \"code\",\n                                \"help\",\n                                \"wordcount\"\n                            ],\n                            toolbar: \"undo redo | formatselect | bold italic backcolor |                                       alignleft aligncenter alignright alignjustify |                                       bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen\"\n                        },\n                        onChange: (e)=>{\n                            row._valuesCache = {\n                                ...row._valuesCache,\n                                note: e.target.getContent()\n                            };\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 30\n                    }, this);\n                }\n            }\n        ], [\n        isLoading,\n        isLoading_themes\n    ]);\n    const table = (0,material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable)({\n        columns,\n        data: data_.error ? [] : data_.data_.data.results ? data_.data_.data.results : [\n            data_.data_.data\n        ],\n        rowCount: data_.error ? 0 : data_.data_.data.results ? data_.data_.data.count : 1,\n        enableRowSelection: true,\n        enableColumnOrdering: true,\n        enableGlobalFilter: true,\n        enableGrouping: true,\n        enableRowActions: true,\n        enableRowPinning: true,\n        enableStickyHeader: true,\n        enableStickyFooter: true,\n        enableColumnPinning: true,\n        enableColumnResizing: true,\n        enableRowNumbers: true,\n        enableEditing: true,\n        manualPagination: true,\n        initialState: {\n            pagination: {\n                pageSize: 5,\n                pageIndex: 1\n            },\n            columnVisibility: {\n                created_by: false,\n                created: false,\n                modfied_by: false,\n                modified: false,\n                modified_by: false\n            },\n            density: \"compact\",\n            showGlobalFilter: true,\n            sorting: [\n                {\n                    id: \"id\",\n                    desc: false\n                }\n            ]\n        },\n        state: {\n            pagination: data_.pagination.pagi,\n            isLoading: data_.isLoading\n        },\n        localization: material_react_table_locales_fr__WEBPACK_IMPORTED_MODULE_11__.MRT_Localization_FR,\n        onPaginationChange: onPaginationChange,\n        displayColumnDefOptions: {\n            \"mrt-row-pin\": {\n                enableHiding: true\n            },\n            \"mrt-row-expand\": {\n                enableHiding: true\n            },\n            \"mrt-row-numbers\": {\n                enableHiding: true\n            }\n        },\n        defaultColumn: {\n            grow: true,\n            enableMultiSort: true\n        },\n        muiTablePaperProps: (param)=>{\n            let { table } = param;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                classes: {\n                    root: \"p-datatable-gridlines text-900 font-medium text-xl\"\n                },\n                sx: {\n                    height: \"calc(100vh - 9rem)\",\n                    // height: `calc(100vh - 200px)`,\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    \"& .MuiTablePagination-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    },\n                    \"& .MuiBox-root \": {\n                        backgroundColor: \"var(--surface-card) !important\",\n                        fontFamily: \"var(--font-family)\",\n                        fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                        color: \"var(--surface-900) !important\"\n                    }\n                }\n            };\n        },\n        editDisplayMode: \"modal\",\n        createDisplayMode: \"modal\",\n        onEditingRowSave: (param)=>{\n            let { table, row, values } = param;\n            setThemeId(row.original.id);\n            //validate data\n            //save data to api\n            console.log(\"onEditingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let update_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_update({\n                id: rest.id,\n                data: update_values\n            }, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Modification\",\n                        detail: \"Th\\xe8me modifi\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.message);\n                    row._valuesCache = {\n                        error: err.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onEditingRowCancel: ()=>{\n            var //clear any validation errors\n            _toast_current;\n            (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                severity: \"info\",\n                summary: \"Info\",\n                detail: \"Annulation\"\n            });\n        },\n        onCreatingRowSave: (param)=>{\n            let { table, row, values } = param;\n            //validate data\n            //save data to api\n            console.log(\"onCreatingRowSave\", values);\n            const { theme, note, arbitration, ...rest } = values;\n            let insert_values = {\n                theme: theme.id,\n                note: note,\n                arbitration: arbitration.id\n            };\n            trigger_create(insert_values, {\n                onSuccess: ()=>{\n                    var _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"info\",\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"Enregistrement cr\\xe9\\xe9\"\n                    });\n                    table.setCreatingRow(null);\n                },\n                onError: (err)=>{\n                    var _err_response, _err_response1, _toast_current;\n                    (_toast_current = toast.current) === null || _toast_current === void 0 ? void 0 : _toast_current.show({\n                        severity: \"error\",\n                        life: 10000,\n                        summary: \"Cr\\xe9ation\",\n                        detail: \"\".concat(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data.message) || ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.data.non_field_errors))\n                    });\n                    console.log(\"onCreatingRowSave\", err.message);\n                    row._valuesCache = {\n                        error: err.message,\n                        ...row._valuesCache\n                    };\n                    return;\n                }\n            });\n        },\n        onCreatingRowCancel: (param)=>{\n            let { table } = param;\n            //clear any validation errors\n            table.setCreatingRow(null);\n        },\n        muiEditRowDialogProps: (param)=>{\n            let { row, table } = param;\n            return {\n                //optionally customize the dialog\n                //about:\"edit modal\",\n                // open: editVisible || createVisible,\n                maxWidth: \"md\"\n            };\n        // sx: {\n        //   //  '& .MuiDialog-root': {\n        //   //    width :'70vw'\n        //   //  },\n        //   // '& .MuiDialog-container': {\n        //   //   width :'70vw'\n        //   // },\n        //   zIndex: 1100,\n        // }\n        },\n        muiTableFooterProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                \"& .MuiTableFooter-root\": {\n                    backgroundColor: \"var(--surface-card) !important\"\n                },\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableContainerProps: (param)=>{\n            let { table } = param;\n            var _table_refs_topToolbarRef_current, _table_refs_bottomToolbarRef_current;\n            return {\n                className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n                sx: {\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                    // borderRadius: '0',\n                    // border: '1px dashed #e0e0e0',\n                    backgroundColor: \"var(--surface-card)\",\n                    height: table.getState().isFullScreen ? \"calc(100vh)\" : \"calc(100vh - 9rem - \".concat((_table_refs_topToolbarRef_current = table.refs.topToolbarRef.current) === null || _table_refs_topToolbarRef_current === void 0 ? void 0 : _table_refs_topToolbarRef_current.offsetHeight, \"px - \").concat((_table_refs_bottomToolbarRef_current = table.refs.bottomToolbarRef.current) === null || _table_refs_bottomToolbarRef_current === void 0 ? void 0 : _table_refs_bottomToolbarRef_current.offsetHeight, \"px)\")\n                }\n            };\n        },\n        muiPaginationProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card) !important\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTableHeadCellProps: {\n            sx: {\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\",\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n            }\n        },\n        muiTopToolbarProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                // borderRadius: '0',\n                // border: '1px dashed #e0e0e0',\n                backgroundColor: \"var(--surface-card)\",\n                color: \"var(--surface-900) !important\"\n            }\n        },\n        muiTableBodyProps: {\n            className: \"p-datatable-gridlines text-900 font-medium text-xl\",\n            sx: {\n                fontFamily: \"var(--font-family)\",\n                fontFeatureSettings: \"var(--font-feature-settings, normal)\",\n                //stripe the rows, make odd rows a darker color\n                \"& tr:nth-of-type(odd) > td\": {\n                    backgroundColor: \"var(--surface-card)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                },\n                \"& tr:nth-of-type(even) > td\": {\n                    backgroundColor: \"var(--surface-border)\",\n                    color: \"var(--surface-900) !important\",\n                    fontFamily: \"var(--font-family)\",\n                    fontFeatureSettings: \"var(--font-feature-settings, normal)\"\n                }\n            }\n        },\n        renderTopToolbarCustomActions: (param)=>/*#__PURE__*/ {\n            let { table } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                direction: \"row\",\n                spacing: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Can__WEBPACK_IMPORTED_MODULE_4__.Can, {\n                    I: \"add\",\n                    a: \"ArbitratedTheme\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                        icon: \"pi pi-plus\",\n                        rounded: true,\n                        \"aria-controls\": open ? \"basic-menu\" : undefined,\n                        \"aria-haspopup\": \"true\",\n                        \"aria-expanded\": open ? \"true\" : undefined,\n                        onClick: (event)=>{\n                            table.setCreatingRow(true);\n                            setCreateVisible(true), console.log(\"creating row ...\");\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, this);\n        },\n        muiDetailPanelProps: ()=>({\n                sx: (theme)=>({\n                        backgroundColor: theme.palette.mode === \"dark\" ? \"rgba(255,210,244,0.1)\" : \"rgba(0,0,0,0.1)\"\n                    })\n            })\n    });\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n        lineNumber: 601,\n        columnNumber: 26\n    }, this);\n    console.log(\"-----------------------------------------\", arbitrations);\n    //note: you can also pass table options as props directly to <MaterialReactTable /> instead of using useMaterialReactTable\n    //but the useMaterialReactTable hook will be the most recommended way to define table options\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_10__.MaterialReactTable, {\n                table: table\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 605,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_toast__WEBPACK_IMPORTED_MODULE_13__.Toast, {\n                ref: toast\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\themes\\\\(components)\\\\GenericTAble.tsx\",\n                lineNumber: 605,\n                columnNumber: 48\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GenericTable, \"bDGdo1+71fnDMhQHJ0dTBFzwcyk=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitrationList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiThemeList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeCreate,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_5__.useApiArbitratedThemeUpdate,\n        material_react_table__WEBPACK_IMPORTED_MODULE_10__.useMaterialReactTable\n    ];\n});\n_c = GenericTable;\nvar _c;\n$RefreshReg$(_c, \"GenericTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/themes/(components)/GenericTAble.tsx\n"));

/***/ })

});