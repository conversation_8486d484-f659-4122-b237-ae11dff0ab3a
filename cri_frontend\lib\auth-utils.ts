import { NextRequest } from 'next/server'
import { getSession } from './auth-custom'
import { createAbilityForUser, defineAbilitiesFor } from '@/app/ability'

export interface AuthenticatedUser {
  id: string
  email: string
  username: string
  firstName?: string
  lastName?: string
  name?: string
  isActive: boolean
  isStaff: boolean
  isSuperuser: boolean
  dateJoined: Date
  lastLogin?: Date
}

export interface AuthResult {
  user: AuthenticatedUser | null
  error?: string
}

/**
 * Get the authenticated user from the request
 */
export async function getAuthenticatedUser(request: NextRequest): Promise<AuthResult> {
  try {
    const session = await getSession(request)

    if (!session || !session.user) {
      return { user: null, error: 'No session found' }
    }

    const user = session.user as AuthenticatedUser

    if (!user.isActive) {
      return { user: null, error: 'Account is disabled' }
    }

    return { user }
  } catch (error) {
    console.error('Error getting authenticated user:', error)
    return { user: null, error: 'Authentication error' }
  }
}

/**
 * Check if user has permission to perform an action on a subject
 */
export async function checkPermission(
  user: AuthenticatedUser,
  action: string,
  subject: string,
  resource?: any
): Promise<boolean> {
  try {
    // Superuser has all permissions
    if (user.isSuperuser) {
      return true
    }

    // Try to get database-driven permissions first
    try {
      const ability = await defineAbilitiesFor(user)
      return ability.can(action as any, subject as any, resource)
    } catch (dbError) {
      console.warn('Database permissions unavailable, falling back to basic permissions:', dbError)

      // Fallback to basic permissions
      const ability = createAbilityForUser(user)
      return ability.can(action as any, subject as any, resource)
    }
  } catch (error) {
    console.error('Error checking permission:', error)
    return false
  }
}

/**
 * Middleware helper to require authentication
 */
export async function requireAuth(request: NextRequest): Promise<AuthResult> {
  const authResult = await getAuthenticatedUser(request)

  if (!authResult.user) {
    return { user: null, error: authResult.error || 'Authentication required' }
  }

  return authResult
}

/**
 * Middleware helper to require specific permission
 */
export async function requirePermission(
  request: NextRequest,
  action: string,
  subject: string,
  resource?: any
): Promise<AuthResult & { hasPermission?: boolean }> {
  const authResult = await requireAuth(request)

  if (!authResult.user) {
    return authResult
  }

  const hasPermission = await checkPermission(authResult.user, action, subject, resource)

  if (!hasPermission) {
    return {
      user: authResult.user,
      hasPermission: false,
      error: `Insufficient permissions: cannot ${action} ${subject}`
    }
  }

  return { user: authResult.user, hasPermission: true }
}

/**
 * Middleware helper to require admin access
 */
export async function requireAdmin(request: NextRequest): Promise<AuthResult> {
  const authResult = await requireAuth(request)

  if (!authResult.user) {
    return authResult
  }

  if (!authResult.user.isSuperuser && !authResult.user.isStaff) {
    return {
      user: authResult.user,
      error: 'Admin access required'
    }
  }

  return authResult
}

/**
 * Middleware helper to require superuser access
 */
export async function requireSuperuser(request: NextRequest): Promise<AuthResult> {
  const authResult = await requireAuth(request)

  if (!authResult.user) {
    return authResult
  }

  if (!authResult.user.isSuperuser) {
    return {
      user: authResult.user,
      error: 'Superuser access required'
    }
  }

  return authResult
}

/**
 * Check if user owns a resource (for resource-based permissions)
 */
export function checkResourceOwnership(
  user: AuthenticatedUser,
  resource: any,
  ownerField: string = 'createdBy'
): boolean {
  if (!resource || !resource[ownerField]) {
    return false
  }

  return resource[ownerField] === user.id
}

/**
 * Get user roles as an array of strings
 */
export function getUserRoles(user: AuthenticatedUser): string[] {
  const roles: string[] = []

  if (user.isSuperuser) {
    roles.push('superuser')
  }

  if (user.isStaff) {
    roles.push('staff')
  }

  roles.push('user')

  return roles
}
