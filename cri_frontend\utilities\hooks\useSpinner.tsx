'use client';
import { ChildContainerProps } from '@/types';
import { LoadingSpinnerContextProps } from '@/types/layout';
import React, { useState, createContext, useContext } from 'react';

export const LoadingSpinnerContext = createContext({} as LoadingSpinnerContextProps)
    
export const LoadingSpinnerProvider = ({ children }: ChildContainerProps) => {
    
    const [loadingSpinner, setLoadingSpinner] = useState<boolean>(false);
    const value = {
        loadingSpinner, setLoadingSpinner
    }
    return  <LoadingSpinnerContext.Provider value={value}>
                { children }
            </LoadingSpinnerContext.Provider>
   
}

export const useLoadingSpinner = () => {
    const { loadingSpinner, setLoadingSpinner } = useContext(LoadingSpinnerContext)
    return { loadingSpinner, setLoadingSpinner }
}