"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/recommendations/followup/page",{

/***/ "(app-client)/./app/(main)/recommendations/(components)/editForm.tsx":
/*!**************************************************************!*\
  !*** ./app/(main)/recommendations/(components)/editForm.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RecommendationEditForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_inputtext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/inputtext */ \"(app-client)/./node_modules/primereact/inputtext/inputtext.esm.js\");\n/* harmony import */ var primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! primereact/inputtextarea */ \"(app-client)/./node_modules/primereact/inputtextarea/inputtextarea.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/calendar */ \"(app-client)/./node_modules/primereact/calendar/calendar.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var _utilities_service_fr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utilities/service/fr */ \"(app-client)/./utilities/service/fr.ts\");\n/* harmony import */ var primereact_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/api */ \"(app-client)/./node_modules/primereact/api/api.esm.js\");\n/* harmony import */ var primereact_togglebutton__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/togglebutton */ \"(app-client)/./node_modules/primereact/togglebutton/togglebutton.esm.js\");\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var _ActionWidget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActionWidget */ \"(app-client)/./app/(main)/recommendations/(components)/ActionWidget.tsx\");\n/* harmony import */ var primereact_datatable__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! primereact/datatable */ \"(app-client)/./node_modules/primereact/datatable/datatable.esm.js\");\n/* harmony import */ var primereact_column__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! primereact/column */ \"(app-client)/./node_modules/primereact/column/column.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_progressbar__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/progressbar */ \"(app-client)/./node_modules/primereact/progressbar/progressbar.esm.js\");\n/* harmony import */ var sanitize_html__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! sanitize-html */ \"(app-client)/./node_modules/sanitize-html/index.js\");\n/* harmony import */ var sanitize_html__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(sanitize_html__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var primereact_slider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/slider */ \"(app-client)/./node_modules/primereact/slider/slider.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var primereact_inputnumber__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! primereact/inputnumber */ \"(app-client)/./node_modules/primereact/inputnumber/inputnumber.esm.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var _lib_schemas__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/schemas */ \"(app-client)/./lib/schemas.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RecommendationEditForm(props) {\n    var _getCookie, _causes, _causes_data_results, _causes1, _dataTableRef_current, _props_row__valuesCache_error_data, _props_row__valuesCache_error, _props_row__valuesCache_error_data1, _props_row__valuesCache_error1, _props_row__valuesCache_error_data2, _props_row__valuesCache_error2, _props_row__valuesCache_error_data3, _props_row__valuesCache_error3, _missions, _props_row__valuesCache_error_data4, _props_row__valuesCache_error4, _props_row__valuesCache_error_data5, _props_row__valuesCache_error5, _props_row__valuesCache_error_data6, _props_row__valuesCache_error6, _concerned_structures, _props_row__valuesCache_error_data7, _props_row__valuesCache_error7, _props_row__valuesCache_error_data8, _props_row__valuesCache_error8, _props_row__valuesCache_error_data9, _props_row__valuesCache_error9, _props_row__valuesCache_error_data10, _props_row__valuesCache_error10, _props_row__valuesCache_error_data11, _props_row__valuesCache_error11, _props_row__valuesCache_error_data12, _props_row__valuesCache_error12, _props_row__valuesCache_error_data13, _props_row__valuesCache_error13, _props_row__valuesCache_error_data14, _props_row__valuesCache_error14, _props_row__valuesCache_error_data15, _props_row__valuesCache_error15, _props_row__valuesCache_error_data16, _props_row__valuesCache_error16, _props_row__valuesCache_error_data17, _props_row__valuesCache_error17, _props_row__valuesCache_error_data18, _props_row__valuesCache_error18, _props_row__valuesCache_error_data19, _props_row__valuesCache_error19, _props_row__valuesCache_error_data20, _props_row__valuesCache_error20;\n    _s();\n    (0,primereact_api__WEBPACK_IMPORTED_MODULE_8__.addLocale)(\"fr\", _utilities_service_fr__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n    (0,primereact_api__WEBPACK_IMPORTED_MODULE_8__.locale)(\"fr\");\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_5__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    // columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} body={(data) => <><Button icon='pi pi-paperclip' onClick={attachementProofClick}></Button><InputText onChange={attachementProofChanged} type='file' hidden ref={attachementProofRef} /></>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)\n    const attachementProofChanged = (event)=>{\n        console.log(\"Plan d'actions | PREUVES\", event);\n    };\n    function attachementProofClick(event) {\n        console.log(\"click\");\n        attachementProofRef.current.click();\n    }\n    const attachementProofRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const recommandationNew = {\n        \"mission\": 0,\n        \"concerned_structure\": 0,\n        \"causes\": [],\n        // \"actions\": [],\n        \"recommendation\": \"\",\n        \"priority\": \"FAIBLE\",\n        \"validated\": true,\n        \"status\": \"R\\xe9alis\\xe9e\",\n        \"accepted\": true,\n        \"responsible\": \"\"\n    };\n    const steps = [\n        \"Recommendation\",\n        \"Actions\"\n    ];\n    const dataTableRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [activeStep, setActiveStep] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const [skipped, setSkipped] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    // const { data: recommendationsrefs, isLoading, error } = useApiRecommendationRefList();\n    const { data: causes, isLoading: causes_isLoading, error: causes_error } = useApiCauseList();\n    const { data: concerned_structures, isLoading: concerned_structures_isLoading, error: concerned_structures_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiStructurelqsList)();\n    const { data: users, isLoading: users_isLoading, error: users_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList)();\n    const { data: missions, isLoading: missions_isLoading, error: missions_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiMissionList)();\n    const [picklistSourceValueCauses, setPicklistSourceValueCauses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? (_causes = causes) === null || _causes === void 0 ? void 0 : _causes.data.results : (_causes1 = causes) === null || _causes1 === void 0 ? void 0 : (_causes_data_results = _causes1.data.results) === null || _causes_data_results === void 0 ? void 0 : _causes_data_results.filter((val, idx)=>!props.row.original.causes.map((val_)=>val_.id).includes(val.id)));\n    const [picklistTargetValueCauses, setPicklistTargetValueCauses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.original.causes ? props.row.original.causes : []);\n    const [recommandationActions, setRecommandationActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.original.actions || []);\n    const [actionsItems, setActionsIems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActionWidget__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 77,\n            columnNumber: 70\n        }, this),\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActionWidget__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 77,\n            columnNumber: 86\n        }, this)\n    ]);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [itemAccepted, setItemAccepted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.original.accepted || false);\n    const [itemValidated, setItemValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.original.validated || false);\n    const [dropdownItemEtat, setDropdownItemEtat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"name\": props.row.original.status || \"\",\n        \"code\": props.row.original.status || \"\"\n    });\n    const [dropdownItemConcernedStructure, setDropdownItemConcernedStructure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"name\": props.row.original.concerned_structure.code_mnemonique || props.row.original.concerned_structure.libell_stru || props.row.original.concerned_structure.code_stru,\n        \"code\": \"\".concat(props.row.original.concerned_structure.id)\n    });\n    const [dropdownItemRecommendation, setDropdownItemRecommendation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : props.row.original.recommendation);\n    const [dropdownItemResponsible, setDropdownItemResponsible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : props.row.original.responsible);\n    const [dropdownItemMission, setDropdownItemMission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : {\n        \"name\": props.row.original.mission || \"\",\n        \"code\": props.row.original.mission\n    });\n    var _props_row_original_numrecommandation;\n    const [numRecommdantaionMission, setNumRecommdantaionMission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_props_row_original_numrecommandation = props.row.original.numrecommandation) !== null && _props_row_original_numrecommandation !== void 0 ? _props_row_original_numrecommandation : -1);\n    const [dropdownItemPriority, setDropdownItemPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"name\": props.row.original.priority || \"\",\n        \"code\": props.row.original.priority || \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _causes, _causes_data_results, _causes1;\n        setPicklistTargetValueCauses(props.row.original.causes);\n        setPicklistSourceValueCauses(props.row.id === \"mrt-row-create\" ? (_causes = causes) === null || _causes === void 0 ? void 0 : _causes.data.results : (_causes1 = causes) === null || _causes1 === void 0 ? void 0 : (_causes_data_results = _causes1.data.results) === null || _causes_data_results === void 0 ? void 0 : _causes_data_results.filter((val, idx)=>!props.row.original.causes.map((val_)=>val_.id).includes(val.id)));\n        // setDropdownItemTheme({ \"name\": mission_theme?.theme.title || \"\", \"code\": mission_theme?.theme.code || \"\" });\n        // setActions(props.row.original.action_plan?.actions || [])\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            causes: props.row.original.causes || []\n        };\n    }, [\n        causes\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"useEffect\", recommandationActions);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            actions: recommandationActions\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            actions: recommandationActions.map((action)=>{\n                console.log(action);\n                return {\n                    \"id\": action.id,\n                    \"job_leader\": action.job_leader,\n                    \"description\": action.description,\n                    \"start_date\": action.start_date,\n                    \"end_date\": action.end_date,\n                    \"validated\": action.validated,\n                    \"status\": action.status,\n                    \"progress\": action.progress,\n                    \"accepted\": action.accepted\n                };\n            })\n        };\n    }, [\n        recommandationActions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n    // props.row._valuesCache = { ...props.row._valuesCache, ...recommandationNew }\n    }, []);\n    // useEffect(() => {\n    //     console.log(\"######################editform############################\", props.row._valuesCache)\n    // }, [props.row._valuesCache]);\n    const handleNumRecommendationChange = (e)=>{\n        console.log(\"handleNumRecommendationChange\", e.value);\n        setNumRecommdantaionMission(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                numrecommandation: e.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            numrecommandation: e.value\n        };\n    };\n    const handleMissionChange = (e)=>{\n        var _missions, _missions_data_results_find, _missions1, _missions_data_results_find1, _missions2;\n        console.log(\"handleMissionChange\", e.value);\n        console.log((_missions = missions) === null || _missions === void 0 ? void 0 : _missions.data.results.filter((mission)=>mission.code === e.value.name));\n        setDropdownItemMission(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                mission: (_missions1 = missions) === null || _missions1 === void 0 ? void 0 : (_missions_data_results_find = _missions1.data.results.find((mission)=>mission.code === e.value.name)) === null || _missions_data_results_find === void 0 ? void 0 : _missions_data_results_find.id\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            mission: (_missions2 = missions) === null || _missions2 === void 0 ? void 0 : (_missions_data_results_find1 = _missions2.data.results.find((mission)=>mission.code === e.value.name)) === null || _missions_data_results_find1 === void 0 ? void 0 : _missions_data_results_find1.id\n        };\n    };\n    const handleAcceptationChange = (e)=>{\n        console.log(\"handleAcceptationChange\", e.value);\n        setItemAccepted(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                accepted: e.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            accepted: e.value\n        };\n    };\n    const handleValidationChange = (e)=>{\n        console.log(\"handleValidationChange\", e.value);\n        setItemValidated(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                validated: e.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            validated: e.value\n        };\n    };\n    const handleEtatChange = (e)=>{\n        console.log(\"handleEtatChange\", e.value);\n        setDropdownItemEtat(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                status: e.value.name\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            status: e.value.name\n        };\n    };\n    const handlePriorityChange = (e)=>{\n        console.log(\"handlePriorityChange\", e.value);\n        setDropdownItemPriority(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                priority: e.value.name\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            priority: e.value.name\n        };\n    };\n    const handleConcernedStructureChange = (e)=>{\n        console.log(\"handleConcernedStructureChange\", e.value);\n        setDropdownItemConcernedStructure(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                concerned_structure: e.value.code\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            concerned_structure: e.value.code\n        };\n    };\n    const handleRecommendationRefChange = (e)=>{\n        console.log(\"handleRecommendationRefChange\", e);\n        setDropdownItemRecommendation(e.target.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                recommendation: e.target.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            recommendation: e.target.value\n        };\n    };\n    const handleResponsibleChange = (e)=>{\n        console.log(\"handleRecommendationRefChange\", e);\n        setDropdownItemResponsible(e.target.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                responsible: e.target.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            responsible: e.target.value\n        };\n    };\n    const handleCauses = (e)=>{\n        console.info(\"handleCauses\");\n        setPicklistSourceValueCauses(e.source);\n        console.log(\"source Causes\", e.source);\n        setPicklistTargetValueCauses(e.target);\n        console.log(\"target Causes\", e.target);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                causes: e.target.map((cause)=>{\n                    return cause.id;\n                })\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            causes: e.target.map((cause)=>{\n                return cause.id;\n            })\n        };\n    };\n    // const cellEditor = (options) => {\n    //     if (options.field === 'price') return priceEditor(options);\n    //     else return textEditor(options);\n    // };\n    const insertAction = ()=>{\n        const newaction = {\n            // \"id\": recommandationActions.length,\n            \"job_leader\": {\n                \"username\": \"\",\n                \"first_name\": \"job\",\n                \"last_name\": \"leader\",\n                \"email\": \"\"\n            },\n            \"description\": \"D\\xe9finir une nouvelle t\\xe2che\",\n            \"start_date\": new Date().toISOString().split(\"T\")[0],\n            \"end_date\": new Date().toISOString().split(\"T\")[0],\n            \"validated\": false,\n            \"status\": \"En cours\",\n            \"progress\": 0,\n            \"accepted\": false,\n            \"dependencies\": [],\n            \"proof\": null\n        };\n        const recommandationActions_ = [\n            ...recommandationActions\n        ];\n        recommandationActions_.push(newaction);\n        setRecommandationActions(recommandationActions_);\n    };\n    const textEditor = (options)=>{\n        console.log(\"##################################\", options);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_9__.InputText, {\n            type: \"text\",\n            value: options.value,\n            onChange: (e)=>options.editorCallback(e.target.value),\n            onKeyDown: (e)=>e.stopPropagation()\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 235,\n            columnNumber: 16\n        }, this);\n    };\n    const dateEditor = (options)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n            value: options.value,\n            onChange: (e)=>options.editorCallback(e.value)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 238,\n            columnNumber: 16\n        }, this);\n    };\n    const statusEditor = (options)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_11__.Dropdown, {\n            value: options.value,\n            onChange: (e)=>options.editorCallback(e.value),\n            options: [\n                \"En cours\",\n                \"R\\xe9alis\\xe9e\",\n                \"Non R\\xe9alis\\xe9e\"\n            ]\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 241,\n            columnNumber: 16\n        }, this);\n    };\n    const jobLeaderEditor = (options)=>{\n        var _options_value, _options_value1, _options_value2, _users;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_11__.Dropdown, {\n            onAbort: (e)=>console.log(\"aborting\", e),\n            filter: true,\n            showFilterClear: true,\n            optionLabel: \"name\",\n            value: {\n                name: \"\".concat((_options_value = options.value) === null || _options_value === void 0 ? void 0 : _options_value.last_name, \" \").concat((_options_value1 = options.value) === null || _options_value1 === void 0 ? void 0 : _options_value1.first_name),\n                code: \"\".concat((_options_value2 = options.value) === null || _options_value2 === void 0 ? void 0 : _options_value2.id)\n            },\n            onChange: (e)=>{\n                console.log(\"aborting\", e);\n                options.editorCallback(e.value);\n            },\n            options: (_users = users) === null || _users === void 0 ? void 0 : _users.data.results.map((user)=>{\n                return {\n                    name: \"\".concat(user.last_name, \" \").concat(user.first_name),\n                    code: \"\".concat(user.id)\n                };\n            })\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 244,\n            columnNumber: 16\n        }, this);\n    };\n    const progressEditor = (options)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_9__.InputText, {\n                    value: options.value,\n                    onChange: (e)=>options.editorCallback(e.target.value)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 18\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_slider__WEBPACK_IMPORTED_MODULE_12__.Slider, {\n                    value: options.value,\n                    onChange: (e)=>options.editorCallback(e.value)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 110\n                }, this)\n            ]\n        }, void 0, true);\n    };\n    const boolEditor = (options)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_togglebutton__WEBPACK_IMPORTED_MODULE_13__.ToggleButton, {\n            checked: options.value,\n            onChange: (e)=>options.editorCallback(e.value)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 250,\n            columnNumber: 16\n        }, this);\n    };\n    const onCellEditComplete = (e)=>{\n        var _dataTableRef_current, _dataTableRef_current1;\n        let { rowData, newValue, field, originalEvent: event } = e;\n        if (field === \"description\") {\n            rowData[field] = newValue;\n        } else if (field === \"job_leader\") {\n            if (newValue !== undefined) {\n                var _users, _users1;\n                if (newValue.id) rowData.job_leader = (_users = users) === null || _users === void 0 ? void 0 : _users.data.results.find((user)=>{\n                    var _newValue;\n                    return user.id === parseInt((_newValue = newValue) === null || _newValue === void 0 ? void 0 : _newValue.id);\n                });\n                if (newValue.code) rowData.job_leader = (_users1 = users) === null || _users1 === void 0 ? void 0 : _users1.data.results.find((user)=>{\n                    var _newValue;\n                    return user.id === parseInt((_newValue = newValue) === null || _newValue === void 0 ? void 0 : _newValue.code);\n                });\n            }\n        } else if ([\n            \"start_date\",\n            \"end_date\"\n        ].includes(field) && newValue !== undefined) {\n            var _newValue;\n            rowData[field] = newValue instanceof Date ? (_newValue = newValue) === null || _newValue === void 0 ? void 0 : _newValue.toISOString().split(\"T\")[0] : new Date(newValue).toISOString().split(\"T\")[0];\n        } else if ([\n            \"validated\",\n            \"accepted\"\n        ].includes(field)) {\n            rowData[field] = newValue;\n        } else if ([\n            \"progress\"\n        ].includes(field)) {\n            rowData[field] = newValue;\n        } else if ([\n            \"status\"\n        ].includes(field)) {\n            rowData[field] = newValue;\n        } else {\n            rowData[field] = newValue;\n        }\n        console.log(\"settings actions\", (_dataTableRef_current = dataTableRef.current) === null || _dataTableRef_current === void 0 ? void 0 : _dataTableRef_current.props.value);\n        const recommandationActions_ = [\n            ...(_dataTableRef_current1 = dataTableRef.current) === null || _dataTableRef_current1 === void 0 ? void 0 : _dataTableRef_current1.props.value\n        ];\n        setRecommandationActions(recommandationActions_);\n    };\n    const generateColumns = ()=>{\n        let columns = [];\n        for (const [key, value] of Object.entries(_lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"validated\",\n                \"accepted\",\n                \"created_by\",\n                \"dependencies\",\n                \"modified_by\",\n                \"created\",\n                \"modified\",\n                \"id\"\n            ].includes(key);\n        })){\n            if (key === \"description\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_14__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>textEditor(options),\n                    body: (data)=>(0,html_react_parser__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(sanitize_html__WEBPACK_IMPORTED_MODULE_15___default()(data.description)),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 30\n                }, this));\n            } else if (key === \"job_leader\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_14__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>jobLeaderEditor(options),\n                    body: (data)=>{\n                        var _data_job_leader, _data_job_leader1;\n                        return \"\".concat((_data_job_leader = data.job_leader) === null || _data_job_leader === void 0 ? void 0 : _data_job_leader.last_name, \" \").concat((_data_job_leader1 = data.job_leader) === null || _data_job_leader1 === void 0 ? void 0 : _data_job_leader1.first_name);\n                    },\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"start_date\",\n                \"end_date\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_14__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>dateEditor(options),\n                    body: (data)=>new Date(data[key]).toLocaleDateString(),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"validated\",\n                \"accepted\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_14__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>boolEditor(options),\n                    body: (data)=>data[key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"pi pi-check-circle\",\n                            style: {\n                                color: \"green\"\n                            }\n                        }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"pi pi-times-circle\",\n                            style: {\n                                color: \"red\"\n                            }\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"progress\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_14__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>progressEditor(options),\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressbar__WEBPACK_IMPORTED_MODULE_16__.ProgressBar, {\n                            value: data[key]\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"status\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_14__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>statusEditor(options),\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_17__.Tag, {\n                            value: data[key]\n                        }, void 0, false, void 0, void 0),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"proof\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_14__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                    icon: \"pi pi-paperclip\",\n                                    onClick: attachementProofClick\n                                }, void 0, false, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_9__.InputText, {\n                                    onChange: attachementProofChanged,\n                                    type: \"file\",\n                                    hidden: true,\n                                    ref: attachementProofRef\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true),\n                    header: _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title ? _lib_schemas__WEBPACK_IMPORTED_MODULE_7__.$Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 30\n                }, this));\n            }\n        }\n        columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_14__.Column, {\n            header: \"Action\",\n            sortableDisabled: true,\n            field: \"action\",\n            body: (options)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                    icon: \"pi pi-trash\",\n                    onClick: (e)=>{\n                        var _dataTableRef_current_props_value, _dataTableRef_current;\n                        console.log(options);\n                        setRecommandationActions((_dataTableRef_current = dataTableRef.current) === null || _dataTableRef_current === void 0 ? void 0 : (_dataTableRef_current_props_value = _dataTableRef_current.props.value) === null || _dataTableRef_current_props_value === void 0 ? void 0 : _dataTableRef_current_props_value.filter((act)=>act.id != options.id));\n                    }\n                }, void 0, false, void 0, void 0),\n            sortable: true,\n            style: {\n                width: \"35%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 311,\n            columnNumber: 22\n        }, this));\n        return columns;\n    };\n    ///////////////////////////////////////////////////////////////////////////////    \n    const isStepOptional = (step)=>{\n        return step === 1;\n    };\n    const isStepSkipped = (step)=>{\n        return skipped.has(step);\n    };\n    console.log((_dataTableRef_current = dataTableRef.current) === null || _dataTableRef_current === void 0 ? void 0 : _dataTableRef_current.props.value);\n    console.log(recommandationActions);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                zIndex: \"1302 !important\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_19__.Sidebar, {\n                position: \"right\",\n                header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            variant: \"h4\",\n                            className: \"align-content-center \",\n                            children: props.row.id === \"mrt-row-create\" ? \"Nouvelle recommandation\" : props.row._valuesCache.actions_add ? \"Saisie des actions pour la recommandation N\\xb0 \".concat(props.row.original.id) : \"Editer Recommandation N\\xb0 \".concat(props.row.original.id)\n                        }, void 0, false, void 0, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_22__.MRT_EditActionButtons, {\n                                variant: \"text\",\n                                table: props.table,\n                                row: props.row\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    ]\n                }, void 0, true, void 0, void 0),\n                visible: editVisible,\n                onHide: ()=>{\n                    props.table.setEditingRow(null);\n                    setEditVisible(false);\n                },\n                className: \"w-full md:w-9 lg:w-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: \"1.5rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                !props.row._valuesCache.actions_add && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-fluid formgrid grid\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"numrecommandation\",\n                                                        children: \"N\\xb0 Recommandation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputnumber__WEBPACK_IMPORTED_MODULE_24__.InputNumber, {\n                                                        className: ((_props_row__valuesCache_error = props.row._valuesCache.error) === null || _props_row__valuesCache_error === void 0 ? void 0 : (_props_row__valuesCache_error_data = _props_row__valuesCache_error.data) === null || _props_row__valuesCache_error_data === void 0 ? void 0 : _props_row__valuesCache_error_data[\"numrecommandation\"]) ? \"p-invalid\" : \"\",\n                                                        id: \"numrecommandation\",\n                                                        value: numRecommdantaionMission,\n                                                        onChange: (e)=>handleNumRecommendationChange(e),\n                                                        placeholder: \"Saisir le num\\xe9ro de la recommandation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error1 = props.row._valuesCache.error) === null || _props_row__valuesCache_error1 === void 0 ? void 0 : (_props_row__valuesCache_error_data1 = _props_row__valuesCache_error1.data) === null || _props_row__valuesCache_error_data1 === void 0 ? void 0 : _props_row__valuesCache_error_data1[\"numrecommandation\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error2 = props.row._valuesCache.error) === null || _props_row__valuesCache_error2 === void 0 ? void 0 : (_props_row__valuesCache_error_data2 = _props_row__valuesCache_error2.data) === null || _props_row__valuesCache_error_data2 === void 0 ? void 0 : _props_row__valuesCache_error_data2[\"numrecommandation\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 111\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        children: \"Automatiquement incr\\xe9mentable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"mission\",\n                                                        children: \"Mission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_11__.Dropdown, {\n                                                        filter: true,\n                                                        className: ((_props_row__valuesCache_error3 = props.row._valuesCache.error) === null || _props_row__valuesCache_error3 === void 0 ? void 0 : (_props_row__valuesCache_error_data3 = _props_row__valuesCache_error3.data) === null || _props_row__valuesCache_error_data3 === void 0 ? void 0 : _props_row__valuesCache_error_data3[\"mission\"]) ? \"p-invalid\" : \"\",\n                                                        id: \"mission\",\n                                                        value: dropdownItemMission,\n                                                        onChange: (e)=>handleMissionChange(e),\n                                                        options: (_missions = missions) === null || _missions === void 0 ? void 0 : _missions.data.results.map(function(val) {\n                                                            return {\n                                                                \"name\": val.code,\n                                                                \"code\": val.code\n                                                            };\n                                                        }),\n                                                        optionLabel: \"name\",\n                                                        placeholder: \"Choisir une mission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error4 = props.row._valuesCache.error) === null || _props_row__valuesCache_error4 === void 0 ? void 0 : (_props_row__valuesCache_error_data4 = _props_row__valuesCache_error4.data) === null || _props_row__valuesCache_error_data4 === void 0 ? void 0 : _props_row__valuesCache_error_data4[\"mission\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error5 = props.row._valuesCache.error) === null || _props_row__valuesCache_error5 === void 0 ? void 0 : (_props_row__valuesCache_error_data5 = _props_row__valuesCache_error5.data) === null || _props_row__valuesCache_error_data5 === void 0 ? void 0 : _props_row__valuesCache_error_data5[\"mission\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 101\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"concerned_structure\",\n                                                        children: \"Structure Concern\\xe9e\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_11__.Dropdown, {\n                                                        className: ((_props_row__valuesCache_error6 = props.row._valuesCache.error) === null || _props_row__valuesCache_error6 === void 0 ? void 0 : (_props_row__valuesCache_error_data6 = _props_row__valuesCache_error6.data) === null || _props_row__valuesCache_error_data6 === void 0 ? void 0 : _props_row__valuesCache_error_data6[\"concerned_structure\"]) ? \"p-invalid\" : \"\",\n                                                        filter: true,\n                                                        id: \"concerned_structure\",\n                                                        value: dropdownItemConcernedStructure,\n                                                        onChange: (e)=>handleConcernedStructureChange(e),\n                                                        options: (_concerned_structures = concerned_structures) === null || _concerned_structures === void 0 ? void 0 : _concerned_structures.data.results.map(function(val) {\n                                                            return {\n                                                                \"name\": val.code_mnemonique || val.libell_stru || val.code_stru,\n                                                                \"code\": \"\".concat(val.id)\n                                                            };\n                                                        }),\n                                                        optionLabel: \"name\",\n                                                        placeholder: \"Choisir une structure\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error7 = props.row._valuesCache.error) === null || _props_row__valuesCache_error7 === void 0 ? void 0 : (_props_row__valuesCache_error_data7 = _props_row__valuesCache_error7.data) === null || _props_row__valuesCache_error_data7 === void 0 ? void 0 : _props_row__valuesCache_error_data7[\"concerned_structure\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error8 = props.row._valuesCache.error) === null || _props_row__valuesCache_error8 === void 0 ? void 0 : (_props_row__valuesCache_error_data8 = _props_row__valuesCache_error8.data) === null || _props_row__valuesCache_error_data8 === void 0 ? void 0 : _props_row__valuesCache_error_data8[\"concerned_structure\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 113\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"responsible\",\n                                                        children: \"Responsable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_9__.InputText, {\n                                                        className: ((_props_row__valuesCache_error9 = props.row._valuesCache.error) === null || _props_row__valuesCache_error9 === void 0 ? void 0 : (_props_row__valuesCache_error_data9 = _props_row__valuesCache_error9.data) === null || _props_row__valuesCache_error_data9 === void 0 ? void 0 : _props_row__valuesCache_error_data9[\"responsible\"]) ? \"p-invalid\" : \"\",\n                                                        id: \"responsible\",\n                                                        value: dropdownItemResponsible,\n                                                        onChange: (e)=>handleResponsibleChange(e),\n                                                        placeholder: \"Saisir le responsable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error10 = props.row._valuesCache.error) === null || _props_row__valuesCache_error10 === void 0 ? void 0 : (_props_row__valuesCache_error_data10 = _props_row__valuesCache_error10.data) === null || _props_row__valuesCache_error_data10 === void 0 ? void 0 : _props_row__valuesCache_error_data10[\"responsible\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error11 = props.row._valuesCache.error) === null || _props_row__valuesCache_error11 === void 0 ? void 0 : (_props_row__valuesCache_error_data11 = _props_row__valuesCache_error11.data) === null || _props_row__valuesCache_error_data11 === void 0 ? void 0 : _props_row__valuesCache_error_data11[\"responsible\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 105\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"status\",\n                                                        children: \"Statut\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_11__.Dropdown, {\n                                                        className: ((_props_row__valuesCache_error12 = props.row._valuesCache.error) === null || _props_row__valuesCache_error12 === void 0 ? void 0 : (_props_row__valuesCache_error_data12 = _props_row__valuesCache_error12.data) === null || _props_row__valuesCache_error_data12 === void 0 ? void 0 : _props_row__valuesCache_error_data12[\"status\"]) ? \"p-invalid\" : \"\",\n                                                        filter: true,\n                                                        id: \"status\",\n                                                        value: dropdownItemEtat,\n                                                        onChange: (e)=>handleEtatChange(e),\n                                                        options: $StatusEnum.enum.map(function(val) {\n                                                            return {\n                                                                \"name\": val,\n                                                                \"code\": val\n                                                            };\n                                                        }),\n                                                        optionLabel: \"name\",\n                                                        placeholder: \"Choisir un statut\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error13 = props.row._valuesCache.error) === null || _props_row__valuesCache_error13 === void 0 ? void 0 : (_props_row__valuesCache_error_data13 = _props_row__valuesCache_error13.data) === null || _props_row__valuesCache_error_data13 === void 0 ? void 0 : _props_row__valuesCache_error_data13[\"status\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error14 = props.row._valuesCache.error) === null || _props_row__valuesCache_error14 === void 0 ? void 0 : (_props_row__valuesCache_error_data14 = _props_row__valuesCache_error14.data) === null || _props_row__valuesCache_error_data14 === void 0 ? void 0 : _props_row__valuesCache_error_data14[\"status\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 100\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"priority\",\n                                                        children: \"Priorit\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_11__.Dropdown, {\n                                                        className: ((_props_row__valuesCache_error15 = props.row._valuesCache.error) === null || _props_row__valuesCache_error15 === void 0 ? void 0 : (_props_row__valuesCache_error_data15 = _props_row__valuesCache_error15.data) === null || _props_row__valuesCache_error_data15 === void 0 ? void 0 : _props_row__valuesCache_error_data15[\"priority\"]) ? \"p-invalid\" : \"\",\n                                                        filter: true,\n                                                        id: \"priority\",\n                                                        value: dropdownItemPriority,\n                                                        onChange: (e)=>handlePriorityChange(e),\n                                                        options: $PriorityEnum.enum.map(function(val) {\n                                                            return {\n                                                                \"name\": val,\n                                                                \"code\": val\n                                                            };\n                                                        }),\n                                                        optionLabel: \"name\",\n                                                        placeholder: \"Choisir une priorit\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error16 = props.row._valuesCache.error) === null || _props_row__valuesCache_error16 === void 0 ? void 0 : (_props_row__valuesCache_error_data16 = _props_row__valuesCache_error16.data) === null || _props_row__valuesCache_error_data16 === void 0 ? void 0 : _props_row__valuesCache_error_data16[\"priority\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error17 = props.row._valuesCache.error) === null || _props_row__valuesCache_error17 === void 0 ? void 0 : (_props_row__valuesCache_error_data17 = _props_row__valuesCache_error17.data) === null || _props_row__valuesCache_error_data17 === void 0 ? void 0 : _props_row__valuesCache_error_data17[\"priority\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 102\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"recommendation\",\n                                                        children: \"Recommendation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_25__.InputTextarea, {\n                                                        className: ((_props_row__valuesCache_error18 = props.row._valuesCache.error) === null || _props_row__valuesCache_error18 === void 0 ? void 0 : (_props_row__valuesCache_error_data18 = _props_row__valuesCache_error18.data) === null || _props_row__valuesCache_error_data18 === void 0 ? void 0 : _props_row__valuesCache_error_data18[\"recommendation\"]) ? \"p-invalid\" : \"\",\n                                                        id: \"recommendation\",\n                                                        value: dropdownItemRecommendation,\n                                                        onChange: (e)=>handleRecommendationRefChange(e),\n                                                        placeholder: \"Saisir la recommendation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error19 = props.row._valuesCache.error) === null || _props_row__valuesCache_error19 === void 0 ? void 0 : (_props_row__valuesCache_error_data19 = _props_row__valuesCache_error19.data) === null || _props_row__valuesCache_error_data19 === void 0 ? void 0 : _props_row__valuesCache_error_data19[\"recommendation\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error20 = props.row._valuesCache.error) === null || _props_row__valuesCache_error20 === void 0 ? void 0 : (_props_row__valuesCache_error_data20 = _props_row__valuesCache_error20.data) === null || _props_row__valuesCache_error_data20 === void 0 ? void 0 : _props_row__valuesCache_error_data20[\"recommendation\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 108\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"picklist_causes\",\n                                                        children: \"Causes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_26__.PickList, {\n                                                        id: \"picklist_causes\",\n                                                        source: picklistSourceValueCauses,\n                                                        target: picklistTargetValueCauses,\n                                                        sourceHeader: \"Disponibles\",\n                                                        targetHeader: \"S\\xe9l\\xe9ctionn\\xe9s\",\n                                                        itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: item.content\n                                                            }, void 0, false, void 0, void 0),\n                                                        onChange: (e)=>{\n                                                            handleCauses(e);\n                                                        },\n                                                        sourceStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        targetStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        filter: true,\n                                                        filterBy: \"content\",\n                                                        filterMatchMode: \"contains\",\n                                                        sourceFilterPlaceholder: \"Recherche\",\n                                                        targetFilterPlaceholder: \"Recherche\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 45\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 37\n                                }, this),\n                                props.row._valuesCache.actions_add && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            label: \"Ajouter une action\",\n                                            icon: \"pi pi-plus-circle\",\n                                            onClick: ()=>insertAction()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_27__.DataTable, {\n                                            ref: dataTableRef,\n                                            editMode: \"cell\",\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            value: recommandationActions,\n                                            rows: 5,\n                                            paginator: true,\n                                            responsiveLayout: \"scroll\",\n                                            children: generateColumns()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                lineNumber: 329,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 328,\n            columnNumber: 13\n        }, this)\n    }, void 0, false);\n}\n_s(RecommendationEditForm, \"Llwc8PKh7N3DSS48h4EEKk56w/Y=\", true, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiStructurelqsList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiMissionList\n    ];\n});\n_c = RecommendationEditForm;\nvar _c;\n$RefreshReg$(_c, \"RecommendationEditForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/recommendations/(components)/editForm.tsx\n"));

/***/ })

});