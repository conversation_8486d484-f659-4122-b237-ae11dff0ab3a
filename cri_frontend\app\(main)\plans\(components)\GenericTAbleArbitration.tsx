'use client'
import { Box, DialogActions, DialogContent, Stack } from '@mui/material';
import {
  MRT_EditActionButtons,
  MRT_RowData,
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef
} from 'material-react-table';
import { MRT_Localization_FR } from 'material-react-table/locales/fr';
import { Button } from 'primereact/button';
import { ConfirmPopup, confirmPopup } from 'primereact/confirmpopup';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { useMemo, useRef, useState } from 'react';
import parse from 'html-react-parser';
// import { Editor } from '@tinymce/tinymce-react';
import { PickList } from 'primereact/picklist';
import { Dropdown } from 'primereact/dropdown';
import { getCookie } from 'cookies-next';
import { usebaseData } from '@/utilities/hooks/useBaseData';
import { getUserFullname } from '@/utilities/functions/utils';
import { Editor } from 'primereact/editor';
import { Can } from '@/app/Can';
import { useApiArbitrationCreate, useApiArbitrationPartialUpdate } from '@/hooks/useNextApi';

export default function GenericTable<T extends MRT_RowData>(data_: { isLoading: any; data_: any, error: any, data_type: any | undefined, pagination: any }) {
  const user = JSON.parse(getCookie('user')?.toString() || '{}')

  const toast = useRef<Toast | null>(null);
  const { plans, users, arbitrations } = usebaseData()

  const users_data = useMemo(() => users.data?.data, [users.data?.data])
  const [arbitrationID, setArbitrationID] = useState(0)
  const [editVisible, setEditVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  const [picklistTargetValueTeam, setPicklistTargetValueTeam] = useState([]);
  const [picklistSourceValueTeam, setPicklistSourceValueTeam] = useState(users_data);
  const [rowTobe, setRowTobe] = useState({});
  const { mutate: arbitration_create_trigger, isPending: isCreateMutating } = useApiArbitrationCreate()
  const { mutate: arbitration_patch_trigger, isPending: isPatchMutating } = useApiArbitrationPartialUpdate()
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [rowActionEnabled, setRowActionEnabled] = useState(false);
  const open = Boolean(anchorEl);

  function onPaginationChange(state: any) {
    console.log(data_.pagination);
    data_.pagination.set(state)
  };
  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setPageNumber(1);
  }
  function changePage(offset: number) {
    setPageNumber(prevPageNumber => prevPageNumber + offset);
  }
  function previousPage() {
    changePage(-1);
  }
  function nextPage() {
    changePage(1);
  }
  const accept_row_deletion = () => {
    toast.current?.show({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 });
  };
  const reject_row_deletion = () => {
    toast.current?.show({ severity: 'warn', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const columns = useMemo<MRT_ColumnDef<T>[]>(
    () =>
      Object.entries(data_.data_type.properties).filter(([key, value], index) => !['modified_by', 'created_by', 'created', 'modified',].includes(key)).map(([key, value], index) => {
        if (['report', 'content', 'note', 'order', 'comment', 'description'].includes(key)) {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            // Cell: ({ cell }) => <div>{parse(cell.getValue<string>())}</div>,
            // Cell: ({ cell }) => { if (["description", "content","report"].includes(key)) return null; else return <div>{parse(cell.getValue<string>())}</div> },
            Edit: ({ cell, column, row, table }) => {
              return (
                <>
                  <label className='font-bold'>Rapport</label>
                  <Editor
                    // initialValue={row.original[key]}
                    // tinymceScriptSrc="http://localhost:3000/tinymce/tinymce.min.js"
                    // apiKey='none'
                    value={row.original.report}
                    // onChange={(e) => { row._valuesCache.report = e.target.getContent() }}
                    onTextChange={(e) => { row._valuesCache.report = e.htmlValue }}
                    style={{ height: '320px' }}
                  // init={{
                  //   licenseKey:'gpl',
                  //   height: 500,
                  //   menubar: true,
                  //   plugins: [
                  //     'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'print', 'preview', 'anchor',
                  //     'searchreplace', 'visualblocks', 'code', 'fullscreen',
                  //     'insertdatetime', 'media', 'table', 'paste', 'code', 'help', 'wordcount'
                  //   ],
                  //   toolbar:
                  //     'undo redo | formatselect | bold italic backcolor | \
                  //     alignleft aligncenter alignright alignjustify | \
                  //     bullist numlist outdent indent | removeformat | help |visualblocks code fullscreen'
                  // }}
                  />
                </>
              );
            },
          }
        }
        if (key === "plan") {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: 'plan',

            muiTableHeadCellProps: {
              align: 'left',
            },
            muiTableBodyCellProps: {
              align: 'left',
            },

            muiTableFooterCellProps: {
              align: 'center',
            },
            Cell: ({ cell, row }) => <Tag className='w-11rem text-sm'>{cell.getValue<Plan>().code}</Tag>,
            Edit: ({ row }) => (
              <>
                <label className='font-bold'>Plan</label>
                <Dropdown
                  optionLabel="name"
                  placeholder="Choisir un plan"
                  onChange={(e) => {
                    console.log(e);
                    setRowTobe({ ...rowTobe, plan: plans?.data?.data.results.find((plan: Plan) => plan.id === e.value.code) });
                    row._valuesCache = { ...row._valuesCache, plan: plans?.data?.data.results.find((plan: Plan) => plan.id === e.value.code) }
                  }}
                  value={{ code: row._valuesCache.plan?.id || null, name: row._valuesCache.plan?.code || null }}
                  options={plans?.data?.data.results.map((plan: Plan) => { return { code: plan.id, name: plan.code } })}
                >
                </Dropdown>
              </>
            )
          }
        }
        if (data_.data_type.properties[key].format === 'date-time' || data_.data_type.properties[key].format === 'date') {

          return {
            accessorFn: (row) => new Date(key == "created" ? row.created : row.modified),
            header: data_.data_type.properties[key].title ?? key,
            filterVariant: 'date',
            filterFn: 'lessThan',
            sortingFn: 'datetime',
            accessorKey: key,
            Cell: ({ cell }) => cell.getValue<Date>()?.toLocaleDateString('fr'),
            id: key,
            Edit: () => null,
          }
        }

        if (key === "id") return {
          header: data_.data_type.properties[key].title ?? key,
          accessorKey: key,
          id: key,
          Edit: () => null,
        }
        if (key === "team") return {
          header: data_.data_type.properties[key].title ?? key,
          accessorKey: key,
          id: key,
          Cell: ({ cell, row }) => <ul>{cell.getValue<User[]>().map((usr) => <li>{getUserFullname(usr)}</li>)}</ul>,
          Edit: ({ row }) => {
            console.log('[ARBITRATION]', row._valuesCache.team)
            return (<>
              <label className='font-bold'>Membres</label>
              <PickList
                source={picklistTargetValueTeam.length === 0 ? picklistSourceValueTeam : picklistSourceValueTeam.filter(user => picklistTargetValueTeam.map(user => user.username).includes(user.username))}
                id='picklist_team'
                target={picklistTargetValueTeam.length > 0 ? picklistTargetValueTeam : row._valuesCache.team}
                sourceHeader="De"
                targetHeader="A"
                itemTemplate={(item) => <div key={item.username}>{item.first_name} {item.last_name}</div>}
                onChange={(e) => {
                  console.log('source Team', e.source)
                  setPicklistSourceValueTeam([...e.source])
                  setPicklistTargetValueTeam([...e.target])
                  row._valuesCache.team = e.target
                }}
                sourceStyle={{ height: '200px' }}
                targetStyle={{ height: '200px' }}
                filter filterBy='username,email,first_name,last_name'
                filterMatchMode='contains'
                sourceFilterPlaceholder="Rechercher par nom & prénom" targetFilterPlaceholder="Rechercher par nom & prénom"
              >

              </PickList>
            </>)
          }
        }
        else {
          return {
            header: data_.data_type.properties[key].title ?? key,
            accessorKey: key,
            id: key,
            // Edit: () => null,
          };

        }
      })
    ,
    [users.data?.data, plans.data?.data, arbitrations.data?.data],
  );

  const table = useMaterialReactTable({
    columns,
    data: data_.error ? [] : data_.data_.data ? data_.data_.data : [data_.data_.data], //must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    rowCount: data_.error ? 0 : data_.data_.data ? data_.data_.data.length : 1,
    enableRowSelection: true, //enable some features
    enableColumnOrdering: true, //enable a feature for all columns
    enableGlobalFilter: true, //turn off a feature
    enableGrouping: true,
    enableRowActions: true,
    enableRowPinning: true,
    enableStickyHeader: true,
    enableStickyFooter: true,
    enableColumnPinning: true,
    enableColumnResizing: true,
    enableRowNumbers: true,
    enableExpandAll: true,
    enableEditing: true,
    enableExpanding: true,
    manualPagination: true,
    initialState: {
      pagination: { pageSize: 5, pageIndex: 1 },
      columnVisibility: { report: false, created_by: false, created: false, modfied_by: false, modified: false, modified_by: false, staff: false, assistants: false, id: false, document: false },
      density: 'compact',
      showGlobalFilter: true,
      sorting: [{ id: 'id', desc: false }],
    },
    state: {
      pagination: data_.pagination.pagi,
      isLoading: data_.isLoading, //cell skeletons and loading overlay
      //showProgressBars: isLoading, //progress bars while refetching
      // isSaving: isSavingTodos, //progress bars and save button spinners
    },
    localization: MRT_Localization_FR,
    onPaginationChange: onPaginationChange,
    displayColumnDefOptions: {
      'mrt-row-pin': {
        enableHiding: true,
      },
      'mrt-row-expand': {
        enableHiding: true,
      },
      'mrt-row-actions': {
        // header: 'Edit', //change "Actions" to "Edit"
        size: 100,
        enableHiding: true,

        //use a text button instead of a icon button
        // Cell: ({ row, table }) => (
        //   <Button onClick={() => table.setEditingRow(row)}>Edit Customer</Button>
        // ),
      },
      'mrt-row-numbers': {
        enableHiding: true, //now row numbers are hidable too
      },
    },
    defaultColumn: {
      grow: true,
      enableMultiSort: true,
    },
    muiTablePaperProps: ({ table }) => ({
      //elevation: 0, //change the mui box shadow
      //customize paper styles
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      classes: { root: 'p-datatable-gridlines text-900 font-medium text-xl' },

      sx: {
        height: `calc(100vh - 9rem)`,
        // height: `calc(100vh - 200px)`,
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        "& .MuiTablePagination-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
        "& .MuiBox-root ": {
          backgroundColor: "var(--surface-card) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
          color: "var(--surface-900) !important",
        },
      },
    }),
    muiEditRowDialogProps: ({ row, table }) => ({
      open: editVisible || createVisible,
      sx: {
        '& .MuiDialog-root': {
          display: 'none'
        },
        '& .MuiDialog-container': {
          display: 'none'
        },
        zIndex: 1100

      }
    }),
    editDisplayMode: 'modal',
    createDisplayMode: 'modal',
    onEditingRowSave: ({ table, values, row }) => {
      console.log("onEditingRowSave", values)
      const { id, ...rest } = values
      rest.plan = values.plan.id
      rest.team = values.team?.map(user => user.id) || []
      arbitration_patch_trigger(rest, {
        revalidate: true,
        onSuccess: () => {
          table.setEditingRow(null); //exit creating mode
          toast.current!.show({ severity: 'success', summary: 'Info', detail: `Arbitrage ${values.plan.code} mis à ajour` });
        },
        onError: (error) => {
          toast.current!.show({ severity: 'error', summary: 'Info', detail: `${error.response?.statusText}` });
          //console.log("onEditingRowSave", error.response);
          row._valuesCache = { error: error.response, ...row._valuesCache };
          return;
        }
      })
    },
    onEditingRowCancel: () => {
      //clear any validation errors
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });
    },
    onCreatingRowSave: ({ table, values, row }) => {
      console.log("onCreatingRowSave", values)
      const { id, ...rest } = values
      rest.plan = values.plan.id
      rest.team = values.team?.map(user => user.id) || []
      arbitration_create_trigger(rest, {
        revalidate: true,
        onSuccess: () => {
          table.setCreatingRow(null); //exit creating mode
          toast.current!.show({ severity: 'success', summary: 'Info', detail: `Arbitrage ${values.plan.code} créé` });
        },
        onError: (error) => {
          toast.current!.show({ severity: 'error', summary: 'Info', detail: `${error.response?.statusText}` });
          //console.log("onEditingRowSave", error.response);
          row._valuesCache = { error: error.response, ...row._valuesCache };
          return;
        }
      })
    },
    onCreatingRowCancel: () => {
      toast.current?.show({ severity: 'info', summary: 'Info', detail: 'Annulation' });
    },
    muiTableFooterProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        "& .MuiTableFooter-root": {
          backgroundColor: "var(--surface-card) !important",
        },
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableContainerProps: ({ table }) => ({
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        height: table.getState().isFullScreen ? `calc(100vh)` : `calc(100vh - 9rem - ${table.refs.topToolbarRef.current?.offsetHeight}px - ${table.refs.bottomToolbarRef.current?.offsetHeight}px)`

      },
    }),
    muiPaginationProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {

        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card) !important",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTableHeadCellProps: {
      sx: {
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important",
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
      },
    },
    muiTopToolbarProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        // borderRadius: '0',
        // border: '1px dashed #e0e0e0',
        backgroundColor: "var(--surface-card)",
        color: "var(--surface-900) !important"
      },

    },
    muiTableBodyProps: {
      className: "p-datatable-gridlines text-900 font-medium text-xl",
      sx: {
        fontFamily: "var(--font-family)",
        fontFeatureSettings: "var(--font-feature-settings, normal)",
        //stripe the rows, make odd rows a darker color
        '& tr:nth-of-type(odd) > td': {
          backgroundColor: 'var(--surface-card)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
        '& tr:nth-of-type(even) > td': {
          backgroundColor: 'var(--surface-border)',
          color: "var(--surface-900) !important",
          fontFamily: "var(--font-family)",
          fontFeatureSettings: "var(--font-feature-settings, normal)",
        },
      },
    },
    renderTopToolbarCustomActions: ({ table }) => (
      <Stack direction={"row"} spacing={1}>
        <Can I="add" a='arbitration'>
          <Button
            icon="pi pi-plus"
            rounded
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={(event) => {
              table.setCreatingRow(true); setCreateVisible(true), console.log("creating row ...");
            }}
            size="small"
          >
          </Button>
        </Can>
        <Can I="delete" a='arbitration'>
          <Button
            rounded
            disabled={table.getIsSomeRowsSelected()}
            // id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
            icon="pi pi-trash"
            // style={{  borderRadius: '0', boxShadow: 'none', backgroundColor: 'transparent' }}
            size="small"
          />
        </Can>
      </Stack>
    ),
    muiDetailPanelProps: () => ({
      sx: (theme) => ({
        backgroundColor:
          theme.palette.mode === 'dark'
            ? 'rgba(255,210,244,0.1)'
            : 'rgba(0,0,0,0.1)',
      }),
    }),
    renderCreateRowDialogContent: ({ internalEditComponents, row, table }) =>
      <div style={{ zIndex: '1302 !important' }}>
        <Sidebar position='right'
          header={
            <div className='flex flex-row w-full flex-wrap justify-content-between'>
              <span className='align-content-center '>Création nouveau arbitrage</span>
              <DialogActions>
                <MRT_EditActionButtons variant="text" table={table} row={row} />
              </DialogActions>
            </div>
          }
          visible={createVisible}
          onHide={() => { table.setCreatingRow(null); setCreateVisible(false) }}
          className="w-full md:w-9 lg:w-8">
          <DialogContent
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1.5rem'
            }}
          >
            {internalEditComponents}
          </DialogContent>
        </Sidebar>
      </div>,
    renderEditRowDialogContent: ({ internalEditComponents, row, table }) =>
      <div style={{ zIndex: '1302 !important' }}>
        <Sidebar position='right'
          header={
            <div className='flex flex-row w-full flex-wrap justify-content-between'>
              <h3 className='align-content-center '>Editer l'arbitrage n° {row.original.id}</h3>
              <DialogActions>
                <MRT_EditActionButtons variant="text" table={table} row={row} />
              </DialogActions>
            </div>
          }
          visible={editVisible}
          onHide={() => {
            table.setEditingRow(null);
            setEditVisible(false)
          }}
          className="w-full md:w-9 lg:w-8">
          <DialogContent
            sx={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}
          >
            {internalEditComponents} {/* or render custom edit components here */}
          </DialogContent>
        </Sidebar>
      </div>,
    renderDetailPanel: ({ row }) =>
      parse(row.original.report),
    renderRowActions: ({ cell, row, table }) => (
      <span className="p-buttonset flex p-1">
        <Can I="update" a='arbitration'>
          <Button size='small' icon="pi pi-pencil" onClick={() => {
            setArbitrationID(row.original.id);
            table.setEditingRow(row);
            setEditVisible(true);
            console.log("editing row ...");
          }} rounded outlined />
        </Can>
        <Can I="delete" a='arbitration'>
          <Button size='small' icon="pi pi-trash" rounded outlined
            onClick={(event) => confirmPopup({
              target: event.currentTarget,
              message: 'Voulez-vous supprimer cette ligne?',
              icon: 'pi pi-info-circle',
              // defaultFocus: 'reject',
              acceptClassName: 'p-button-danger',
              acceptLabel: 'Oui',
              rejectLabel: 'Non',
              accept: accept_row_deletion,
              reject: reject_row_deletion
            })}
          />
        </Can>
        <ConfirmPopup />
      </span>
    ),
  });
  return <><MaterialReactTable table={table} /><Toast ref={toast} /></>;
}
