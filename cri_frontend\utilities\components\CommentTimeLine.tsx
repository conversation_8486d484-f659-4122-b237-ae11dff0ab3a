/* eslint-disable @next/next/no-img-element */
'use client';

import React from 'react';

import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Timeline } from 'primereact/timeline';
import { CustomEvent } from '@/types';
import { Comment } from '@/services/schemas';
import sanitize from 'sanitize-html';
import parse from 'html-react-parser'

const CommentTimeLine = (props : {data : any}) => {
    const customEvents: CustomEvent[] = [
        // {
        //     status: 'Ordered',
        //     date: '15/10/2025 10:30',
        //     icon: 'pi pi-shopping-cart',
        //     color: '#9C27B0',
        //     image: 'game-controller.jpg'
        // },
        {
            status: 'Processing',
            date: '15/10/2025 14:00',
            icon: 'pi pi-cog',
            color: '#673AB7'
        },
        {
            status: 'Shipped',
            date: '15/10/2025 16:15',
            icon: 'pi pi-envelope',
            color: '#FF9800'
        },
        {
            status: 'Delivered',
            date: '16/10/2025 10:00',
            icon: 'pi pi-check',
            color: '#607D8B'
        }
    ];

    const horizontalEvents = ['2023', '2024', '2025', '2026'];

    const customizedContent = (item: Comment) => {
        return (
            <Card title={item.created_by.username} subTitle={`${new Date(item.created).toLocaleDateString('fr')} ${new Date(item.created).toLocaleTimeString("fr")}`}>
                {/* {item.image && <img src={`/demo/images/product/${item.image}`} onError={(e) => (e.currentTarget.src = 'https://www.primefaces.org/wp-content/uploads/2020/05/placeholder.png')} alt={item.name} width={200} className="shadow-2 mb-3" />} */}
                <p>
                    {parse(sanitize(item.comment))}
                </p>
                {/* <Button label="Read more" text></Button> */}
            </Card>
        );
    };

    const customizedMarker = (item: Comment, index : number) => {
        return (
            <span className="custom-marker shadow-1" style={{ backgroundColor: index % 2 ===0 ? '#FF9800': '#9C27B0' }}>
                <i className={"pi pi-envelope"}></i>
            </span>
        );
    };

    return (
        <div>
            <div className="grid">
                <div className="col-12">
                    <div className="card timeline-demo">
                        <h5>Commentaires</h5>
                        <Timeline value={props.data} align="alternate" className="customized-timeline" marker={customizedMarker} content={customizedContent} />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CommentTimeLine;
