'use client'

import React, { useState, useEffect, useRef } from 'react'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { But<PERSON> } from 'primereact/button'
import { InputText } from 'primereact/inputtext'
import { Dialog } from 'primereact/dialog'
import { InputTextarea } from 'primereact/inputtextarea'
import { Toast } from 'primereact/toast'
import { Toolbar } from 'primereact/toolbar'
import { Badge } from 'primereact/badge'
import { MultiSelect } from 'primereact/multiselect'
import { FilterMatchMode } from 'primereact/api'
import { Can } from '@/app/Can'

interface Role {
  id: string
  name: string
  description?: string
  createdAt: Date
  updatedAt: Date
  userCount?: number
  permissions?: Permission[]
}

interface Permission {
  id: string
  action: string
  subject: string
}

export default function RolesPage() {
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [globalFilter, setGlobalFilter] = useState('')
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [roleDialog, setRoleDialog] = useState(false)
  const [deleteRoleDialog, setDeleteRoleDialog] = useState(false)
  const [role, setRole] = useState<Partial<Role>>({})
  const [selectedPermissions, setSelectedPermissions] = useState<Permission[]>([])
  const toast = useRef<Toast>(null)

  useEffect(() => {
    fetchRoles()
    fetchPermissions()
  }, [])

  const fetchRoles = async () => {
    try {
      setLoading(true)
      // Mock data for now - replace with actual API call
      setTimeout(() => {
        setRoles([
          {
            id: '1',
            name: 'Super Admin',
            description: 'Full system access',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            userCount: 1
          },
          {
            id: '2',
            name: 'Admin',
            description: 'Administrative access to most features',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            userCount: 3
          },
          {
            id: '3',
            name: 'Mission Manager',
            description: 'Can manage missions and related entities',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            userCount: 5
          },
          {
            id: '4',
            name: 'Auditor',
            description: 'Can create and manage audit missions',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            userCount: 8
          },
          {
            id: '5',
            name: 'Viewer',
            description: 'Read-only access to most content',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
            userCount: 15
          }
        ])
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Error fetching roles:', error)
      setLoading(false)
    }
  }

  const fetchPermissions = async () => {
    try {
      // Mock permissions data
      setPermissions([
        { id: '1', action: 'create', subject: 'Mission' },
        { id: '2', action: 'read', subject: 'Mission' },
        { id: '3', action: 'update', subject: 'Mission' },
        { id: '4', action: 'delete', subject: 'Mission' },
        { id: '5', action: 'create', subject: 'Recommendation' },
        { id: '6', action: 'read', subject: 'Recommendation' },
        { id: '7', action: 'update', subject: 'Recommendation' },
        { id: '8', action: 'delete', subject: 'Recommendation' },
      ])
    } catch (error) {
      console.error('Error fetching permissions:', error)
    }
  }

  const openNew = () => {
    setRole({})
    setSelectedPermissions([])
    setRoleDialog(true)
  }

  const hideDialog = () => {
    setRoleDialog(false)
  }

  const hideDeleteRoleDialog = () => {
    setDeleteRoleDialog(false)
  }

  const saveRole = () => {
    // Save role logic here
    toast.current?.show({ severity: 'success', summary: 'Successful', detail: 'Role saved', life: 3000 })
    setRoleDialog(false)
    setRole({})
    setSelectedPermissions([])
  }

  const editRole = (role: Role) => {
    setRole({ ...role })
    setSelectedPermissions(role.permissions || [])
    setRoleDialog(true)
  }

  const confirmDeleteRole = (role: Role) => {
    setSelectedRole(role)
    setDeleteRoleDialog(true)
  }

  const deleteRole = () => {
    // Delete role logic here
    toast.current?.show({ severity: 'success', summary: 'Successful', detail: 'Role deleted', life: 3000 })
    setDeleteRoleDialog(false)
    setSelectedRole(null)
  }

  const onInputChange = (e: any, name: string) => {
    const val = (e.target && e.target.value) || ''
    setRole(prev => ({ ...prev, [name]: val }))
  }

  const leftToolbarTemplate = () => {
    return (
      <div className="flex flex-wrap gap-2">
        <Can I="create" a="Role">
          <Button label="New Role" icon="pi pi-plus" severity="success" onClick={openNew} />
        </Can>
      </div>
    )
  }

  const rightToolbarTemplate = () => {
    return (
      <div className="flex align-items-center gap-2">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            type="search"
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search roles..."
          />
        </span>
      </div>
    )
  }

  const actionBodyTemplate = (rowData: Role) => {
    return (
      <div className="flex gap-2">
        <Can I="update" a="Role">
          <Button
            icon="pi pi-pencil"
            rounded
            outlined
            className="mr-2"
            onClick={() => editRole(rowData)}
          />
        </Can>
        <Can I="delete" a="Role">
          <Button
            icon="pi pi-trash"
            rounded
            outlined
            severity="danger"
            onClick={() => confirmDeleteRole(rowData)}
          />
        </Can>
      </div>
    )
  }

  const userCountBodyTemplate = (rowData: Role) => {
    return <Badge value={rowData.userCount?.toString() || '0'} severity="info" />
  }

  const dateBodyTemplate = (rowData: Role) => {
    return new Date(rowData.createdAt).toLocaleDateString()
  }

  const roleDialogFooter = (
    <div>
      <Button label="Cancel" icon="pi pi-times" outlined onClick={hideDialog} />
      <Button label="Save" icon="pi pi-check" onClick={saveRole} />
    </div>
  )

  const deleteRoleDialogFooter = (
    <div>
      <Button label="No" icon="pi pi-times" outlined onClick={hideDeleteRoleDialog} />
      <Button label="Yes" icon="pi pi-check" severity="danger" onClick={deleteRole} />
    </div>
  )

  return (
    <div className="card">
      <Toast ref={toast} />
      
      <Toolbar className="mb-4" left={leftToolbarTemplate} right={rightToolbarTemplate} />

      <DataTable
        value={roles}
        selection={selectedRole}
        onSelectionChange={(e) => setSelectedRole(e.value)}
        dataKey="id"
        paginator
        rows={10}
        rowsPerPageOptions={[5, 10, 25]}
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} roles"
        globalFilter={globalFilter}
        loading={loading}
        header="Roles Management"
        responsiveLayout="scroll"
      >
        <Column field="name" header="Role Name" sortable filter filterPlaceholder="Search by name" />
        <Column field="description" header="Description" sortable />
        <Column field="userCount" header="Users" body={userCountBodyTemplate} sortable />
        <Column field="createdAt" header="Created" body={dateBodyTemplate} sortable />
        <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '12rem' }} />
      </DataTable>

      <Dialog
        visible={roleDialog}
        style={{ width: '600px' }}
        header="Role Details"
        modal
        className="p-fluid"
        footer={roleDialogFooter}
        onHide={hideDialog}
      >
        <div className="field">
          <label htmlFor="name">Role Name</label>
          <InputText
            id="name"
            value={role.name || ''}
            onChange={(e) => onInputChange(e, 'name')}
            required
            autoFocus
          />
        </div>
        <div className="field">
          <label htmlFor="description">Description</label>
          <InputTextarea
            id="description"
            value={role.description || ''}
            onChange={(e) => onInputChange(e, 'description')}
            rows={3}
            cols={20}
          />
        </div>
        <div className="field">
          <label htmlFor="permissions">Permissions</label>
          <MultiSelect
            id="permissions"
            value={selectedPermissions}
            onChange={(e) => setSelectedPermissions(e.value)}
            options={permissions}
            optionLabel={(permission) => `${permission.action} ${permission.subject}`}
            placeholder="Select permissions"
            maxSelectedLabels={3}
            className="w-full"
          />
        </div>
      </Dialog>

      <Dialog
        visible={deleteRoleDialog}
        style={{ width: '450px' }}
        header="Confirm"
        modal
        footer={deleteRoleDialogFooter}
        onHide={hideDeleteRoleDialog}
      >
        <div className="confirmation-content">
          <i className="pi pi-exclamation-triangle mr-3" style={{ fontSize: '2rem' }} />
          {selectedRole && (
            <span>
              Are you sure you want to delete role <b>{selectedRole.name}</b>?
            </span>
          )}
        </div>
      </Dialog>
    </div>
  )
}
