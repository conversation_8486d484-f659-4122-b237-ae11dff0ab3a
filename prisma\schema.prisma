// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model (extends Django's built-in User)
model User {
  id          Int      @id @default(autoincrement())
  username    String   @unique
  email       String   @unique
  firstName   String   @map("first_name")
  lastName    String   @map("last_name")
  isActive    Boolean  @default(true) @map("is_active")
  isStaff     <PERSON>olean  @default(false) @map("is_staff")
  isSuperuser <PERSON>an  @default(false) @map("is_superuser")
  dateJoined  DateTime @default(now()) @map("date_joined")
  lastLogin   DateTime? @map("last_login")
  password    String

  // Relations
  userProfile UserProfile?

  // Mission relations
  missionHead       Mission[] @relation("MissionHead")
  missionSupervisor Mission[] @relation("MissionSupervisor")
  missionStaff      Mission[] @relation("MissionStaff")
  missionAssistants Mission[] @relation("MissionAssistants")

  // Other relations
  createdMissions      Mission[]      @relation("CreatedByMission")
  modifiedMissions     Mission[]      @relation("ModifiedByMission")
  createdThemes        Theme[]        @relation("CreatedByTheme")
  modifiedThemes       Theme[]        @relation("ModifiedByTheme")
  createdDomains       Domain[]       @relation("CreatedByDomain")
  modifiedDomains      Domain[]       @relation("ModifiedByDomain")
  createdProcesses     Process[]      @relation("CreatedByProcess")
  modifiedProcesses    Process[]      @relation("ModifiedByProcess")
  createdPlans         Plan[]         @relation("CreatedByPlan")
  modifiedPlans        Plan[]         @relation("ModifiedByPlan")
  createdActions       Action[]       @relation("CreatedByAction")
  modifiedActions      Action[]       @relation("ModifiedByAction")
  createdRecommendations Recommendation[] @relation("CreatedByRecommendation")
  modifiedRecommendations Recommendation[] @relation("ModifiedByRecommendation")
  createdComments      Comment[]      @relation("CreatedByComment")
  modifiedComments     Comment[]      @relation("ModifiedByComment")

  // Action job leader
  actionJobLeader Action[] @relation("ActionJobLeader")

  // Structure correspondents
  structureCorrespondents StructureLQSCorrespondents[] @relation("StructureCorrespondents")

  // Interim relations
  structureInterim StructureLQSInterim[] @relation("StructureInterim")

  // Arbitration team
  arbitrationTeam Arbitration[] @relation("ArbitrationTeam")

  // Notifications
  notifications Notification[]

  @@map("auth_user")
}

// CRI Agents (external table)
model CriAgents {
  idStruct       BigInt?  @map("ID_STRUCT")
  matricAgnt     String   @id @map("matric_agnt")
  nomAgnt        String?  @map("nom_agnt")
  prenomAgnt     String?  @map("prenom_agnt")
  intituFonc     String?  @map("intitu_fonc")
  libellStru     String?  @map("libell_stru")
  codeUnit       String?  @map("code_unit")
  codeStru       String?  @map("code_stru")
  codeSer        String?  @map("code_ser")
  codeMnemonique String?  @map("code_mnemonique")

  // Relations
  userProfile UserProfile?

  @@map("cri_agents")
}

// User Profile
model UserProfile {
  id        Int      @id @default(autoincrement())
  userId    Int      @unique @map("user_id")
  agentId   String?  @unique @map("agent_id")
  created   DateTime @default(now())
  modified  DateTime @updatedAt
  createdBy Int?     @map("created_by")
  modifiedBy Int?    @map("modified_by")

  // Relations
  user  User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  agent CriAgents? @relation(fields: [agentId], references: [matricAgnt], onDelete: Cascade)

  @@map("backend_userprofile")
}

// CRI Structure View (external table)
model CriStructview {
  id             BigInt  @id
  codeStru       String  @map("code_stru")
  libellStru     String? @map("libell_stru")
  codeUnit       String? @map("code_unit")
  codeMnemonique String? @map("code_mnemonique")

  // Relations
  structureCorrespondents StructureLQSCorrespondents[]
  structureInterim        StructureLQSInterim[]
  themeProposing          Theme[]                      @relation("ThemeProposingStructures")
  themeConcerned          Theme[]                      @relation("ThemeConcernedStructures")
  recommendations         Recommendation[]

  @@map("cri_structview")
}

// Structure LQS Correspondents
model StructureLQSCorrespondents {
  id          Int      @id @default(autoincrement())
  structureId BigInt   @map("structure_id")
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  structure       CriStructview @relation(fields: [structureId], references: [id], onDelete: NoAction)
  correspondents  User[]        @relation("StructureCorrespondents")

  @@map("backend_structurelqscorrespondents")
}

// Structure LQS Interim
model StructureLQSInterim {
  id          Int      @id @default(autoincrement())
  structureId BigInt   @map("structure_id")
  interimId   Int      @map("interim_id")
  start       DateTime
  end         DateTime
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  structure CriStructview @relation(fields: [structureId], references: [id], onDelete: NoAction)
  interim   User          @relation("StructureInterim", fields: [interimId], references: [id], onDelete: NoAction)

  @@map("backend_structurelqsinterim")
}

// Domain
model Domain {
  id          Int      @id @default(autoincrement())
  title       String
  shortTitle  String   @map("short_title")
  parentId    Int?     @map("parent_id")
  type        String?
  observation String?
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  parent         Domain?  @relation("DomainParent", fields: [parentId], references: [id], onDelete: NoAction)
  children       Domain[] @relation("DomainParent")
  createdByUser  User?    @relation("CreatedByDomain", fields: [createdBy], references: [id], onDelete: NoAction)
  modifiedByUser User?    @relation("ModifiedByDomain", fields: [modifiedBy], references: [id], onDelete: NoAction)
  themes         Theme[]

  @@unique([title, parentId])
  @@map("backend_domain")
}

// Process
model Process {
  id          Int      @id @default(autoincrement())
  title       String
  shortTitle  String   @map("short_title")
  parentId    Int?     @map("parent_id")
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  parent         Process?  @relation("ProcessParent", fields: [parentId], references: [id], onDelete: NoAction)
  children       Process[] @relation("ProcessParent")
  createdByUser  User?     @relation("CreatedByProcess", fields: [createdBy], references: [id], onDelete: NoAction)
  modifiedByUser User?     @relation("ModifiedByProcess", fields: [modifiedBy], references: [id], onDelete: NoAction)
  themes         Theme[]

  @@map("backend_process")
}

// Risk
model Risk {
  id          Int      @id @default(autoincrement())
  title       String
  description String?
  validated   Boolean  @default(false)
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  themes Theme[] @relation("ThemeRisks")

  @@map("backend_risk")
}

// Goal
model Goal {
  id          Int      @id @default(autoincrement())
  title       String
  description String?
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  themes Theme[] @relation("ThemeGoals")

  @@map("backend_goal")
}

// Plan
model Plan {
  id         Int      @id @default(autoincrement())
  exercise   Int
  type       PlanType
  created    DateTime @default(now())
  modified   DateTime @updatedAt
  createdBy  Int?     @map("created_by")
  modifiedBy Int?     @map("modified_by")

  // Relations
  createdByUser  User?        @relation("CreatedByPlan", fields: [createdBy], references: [id], onDelete: NoAction)
  modifiedByUser User?        @relation("ModifiedByPlan", fields: [modifiedBy], references: [id], onDelete: NoAction)
  missions       Mission[]
  arbitrations   Arbitration[]

  @@map("backend_plan")
}

enum PlanType {
  AUDIT_INTERN @map("Audit Interne")
  CTRL_INTERN  @map("Contrôle Interne")
  HORS_PLAN    @map("Hors Plan")
}

enum MissionType {
  COMMANDED    @map("Commandée")
  PLANIFIED    @map("Planifiée")
  AVIS_CONSEIL @map("Avis & Conseils")
}

enum MissionEtat {
  NotStarted  @map("Non Lancée")
  Suspended   @map("Suspendue")
  InProgress  @map("En cours")
  Closed      @map("Clôturée")
}

enum ThemeProposingEntity {
  VP     @map("Vice Président")
  CI     @map("Contrôle Interne")
  AI     @map("Audit Interne")
  STRUCT @map("Structures")
}

enum RecommendationPriority {
  LOW    @map("FAIBLE")
  NORMAL @map("NORMALE")
  HIGH   @map("ELEVEE")
}

enum RecommendationEtat {
  Accomplished    @map("Réalisée")
  NotAccomplished @map("Non Réalisée")
  InProgress      @map("En cours")
}

enum ActionEtat {
  Accomplished    @map("Réalisée")
  NotAccomplished @map("Non Réalisée")
  InProgress      @map("En cours")
}

enum DocumentType {
  MISSION        @map("MISSION")
  ACTION         @map("ACTION")
  RECOMMENDATION @map("RECOMMENDATION")
}

enum ValidationEnum {
  YES @map("Validé")
  NO  @map("Non Validé")
  AC  @map("Accepté")
  NA  @map("Non Accepté")
}

enum RecommendationActionType {
  accepted      @map("Retenue")
  not_accepted  @map("Non Retenue")
  not_concerned @map("Non Concerné")
}

// Theme
model Theme {
  id                    Int                   @id @default(autoincrement())
  validated             Boolean               @default(false)
  code                  String?
  domainId              Int?                  @map("domain_id")
  processId             Int?                  @map("process_id")
  title                 String
  proposedBy            ThemeProposingEntity  @map("proposed_by")
  monthStart            String                @map("month_start") // MonthField equivalent
  monthEnd              String                @map("month_end")   // MonthField equivalent
  created               DateTime              @default(now())
  modified              DateTime              @updatedAt
  createdBy             Int?                  @map("created_by")
  modifiedBy            Int?                  @map("modified_by")

  // Relations
  domain                Domain?               @relation(fields: [domainId], references: [id], onDelete: NoAction)
  process               Process?              @relation(fields: [processId], references: [id], onDelete: NoAction)
  createdByUser         User?                 @relation("CreatedByTheme", fields: [createdBy], references: [id], onDelete: NoAction)
  modifiedByUser        User?                 @relation("ModifiedByTheme", fields: [modifiedBy], references: [id], onDelete: NoAction)
  proposingStructures   CriStructview[]       @relation("ThemeProposingStructures")
  concernedStructures   CriStructview[]       @relation("ThemeConcernedStructures")
  risks                 Risk[]                @relation("ThemeRisks")
  goals                 Goal[]                @relation("ThemeGoals")
  arbitratedThemes      ArbitratedTheme[]

  @@map("backend_theme")
}

// Arbitration
model Arbitration {
  id               Int      @id @default(autoincrement())
  planId           Int      @map("plan_id")
  report           String?
  created          DateTime @default(now())
  modified         DateTime @updatedAt
  createdBy        Int?     @map("created_by")
  modifiedBy       Int?     @map("modified_by")

  // Relations
  plan             Plan              @relation(fields: [planId], references: [id], onDelete: NoAction)
  team             User[]            @relation("ArbitrationTeam")
  arbitratedThemes ArbitratedTheme[]

  @@map("backend_arbitration")
}

// Arbitrated Theme
model ArbitratedTheme {
  id            Int      @id @default(autoincrement())
  arbitrationId Int      @map("arbitration_id")
  themeId       Int      @map("theme_id")
  note          String?
  created       DateTime @default(now())
  modified      DateTime @updatedAt
  createdBy     Int?     @map("created_by")
  modifiedBy    Int?     @map("modified_by")

  // Relations
  arbitration   Arbitration @relation(fields: [arbitrationId], references: [id], onDelete: NoAction)
  theme         Theme       @relation(fields: [themeId], references: [id], onDelete: NoAction)
  missions      Mission[]

  @@unique([arbitrationId, themeId])
  @@map("backend_arbitratedtheme")
}

// Mission
model Mission {
  id               Int              @id @default(autoincrement())
  planId           Int?             @map("plan_id")
  exercise         String?
  type             MissionType
  code             String           @unique
  etat             MissionEtat
  startDate        DateTime         @map("start_date") @db.Date
  endDate          DateTime         @map("end_date") @db.Date
  themeId          Int?             @map("theme_id")
  headId           Int?             @map("head_id")
  supervisorId     Int?             @map("supervisor_id")
  created          DateTime         @default(now())
  modified         DateTime         @updatedAt
  createdBy        Int?             @map("created_by")
  modifiedBy       Int?             @map("modified_by")

  // Relations
  plan             Plan?            @relation(fields: [planId], references: [id], onDelete: NoAction)
  theme            ArbitratedTheme? @relation(fields: [themeId], references: [id], onDelete: Restrict)
  head             User?            @relation("MissionHead", fields: [headId], references: [id], onDelete: NoAction)
  supervisor       User?            @relation("MissionSupervisor", fields: [supervisorId], references: [id], onDelete: NoAction)
  createdByUser    User?            @relation("CreatedByMission", fields: [createdBy], references: [id], onDelete: NoAction)
  modifiedByUser   User?            @relation("ModifiedByMission", fields: [modifiedBy], references: [id], onDelete: NoAction)
  staff            User[]           @relation("MissionStaff")
  assistants       User[]           @relation("MissionAssistants")
  documents        MissionDocument[]
  recommendations  Recommendation[]
  constats         Constat[]

  @@unique([planId, code, themeId], name: "uniq_miss_thm_pln")
  @@map("backend_mission")
}

// Mission Document
model MissionDocument {
  id          Int          @id @default(autoincrement())
  document    String       // FileField equivalent
  size        BigInt?      @default(0)
  name        String?
  type        String?
  missionId   Int          @map("mission_id")
  context     DocumentType
  description String?
  created     DateTime     @default(now())
  modified    DateTime     @updatedAt
  createdBy   Int?         @map("created_by")
  modifiedBy  Int?         @map("modified_by")

  // Relations
  mission     Mission      @relation(fields: [missionId], references: [id], onDelete: Cascade)

  @@map("backend_missiondocument")
}

// Constat
model Constat {
  id          Int      @id @default(autoincrement())
  numconstat  Int      @default(1)
  content     String
  missionId   Int      @map("mission_id")
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  mission     Mission  @relation(fields: [missionId], references: [id], onDelete: NoAction)
  facts       FACT[]
  causes      Cause[]
  consequences Consequence[]
  recommendations Recommendation[] @relation("ConstatRecommendations")

  @@map("backend_constat")
}

// FACT
model FACT {
  id          Int      @id @default(autoincrement())
  code        String
  description String
  constatId   Int      @map("constat_id")
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  constat     Constat  @relation(fields: [constatId], references: [id], onDelete: NoAction)

  @@map("backend_fact")
}

// Cause
model Cause {
  id          Int      @id @default(autoincrement())
  numcause    Int      @default(1)
  content     String
  constatId   Int      @map("constat_id")
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  constat         Constat          @relation(fields: [constatId], references: [id], onDelete: NoAction)
  recommendations Recommendation[] @relation("RecommendationCauses")

  @@map("backend_cause")
}

// Consequence
model Consequence {
  id             Int      @id @default(autoincrement())
  numconsequence Int      @default(1)
  code           String
  content        String
  constatId      Int      @map("constat_id")
  created        DateTime @default(now())
  modified       DateTime @updatedAt
  createdBy      Int?     @map("created_by")
  modifiedBy     Int?     @map("modified_by")

  // Relations
  constat        Constat  @relation(fields: [constatId], references: [id], onDelete: NoAction)

  @@map("backend_consequence")
}

// Recommendation
model Recommendation {
  id                    Int                    @id @default(autoincrement())
  numrecommandation     Int                    @default(1)
  recommendation        String
  concernedStructureId  BigInt                 @map("concerned_structure_id")
  priority              RecommendationPriority
  status                RecommendationEtat?
  validated             Boolean                @default(false)
  accepted              Boolean?
  responsible           String
  missionId             Int                    @map("mission_id")
  created               DateTime               @default(now())
  modified              DateTime               @updatedAt
  createdBy             Int?                   @map("created_by")
  modifiedBy            Int?                   @map("modified_by")

  // Relations
  concernedStructure    CriStructview          @relation(fields: [concernedStructureId], references: [id], onDelete: Restrict)
  mission               Mission                @relation(fields: [missionId], references: [id], onDelete: NoAction)
  createdByUser         User?                  @relation("CreatedByRecommendation", fields: [createdBy], references: [id], onDelete: NoAction)
  modifiedByUser        User?                  @relation("ModifiedByRecommendation", fields: [modifiedBy], references: [id], onDelete: NoAction)
  causes                Cause[]                @relation("RecommendationCauses")
  constats              Constat[]              @relation("ConstatRecommendations")
  actions               Action[]
  comments              Comment[]              @relation("RecommendationComments")
  actionResponsables    ActionResponsable[]

  @@unique([id, concernedStructureId, missionId], name: "uniq_miss_recomm_followup")
  @@map("backend_recommendation")
}

// Action
model Action {
  id              Int         @id @default(autoincrement())
  description     String
  jobLeaderId     Int         @map("job_leader_id")
  startDate       DateTime    @map("start_date")
  endDate         DateTime    @map("end_date")
  validated       Boolean     @default(false)
  status          ActionEtat
  progress        Int         @default(0)
  accepted        Boolean     @default(false)
  proof           String?     // FileField equivalent
  recommendationId Int        @map("recommendation_id")
  created         DateTime    @default(now())
  modified        DateTime    @updatedAt
  createdBy       Int?        @map("created_by")
  modifiedBy      Int?        @map("modified_by")

  // Relations
  jobLeader       User         @relation("ActionJobLeader", fields: [jobLeaderId], references: [id], onDelete: Restrict)
  recommendation  Recommendation @relation(fields: [recommendationId], references: [id], onDelete: Cascade)
  createdByUser   User?        @relation("CreatedByAction", fields: [createdBy], references: [id], onDelete: NoAction)
  modifiedByUser  User?        @relation("ModifiedByAction", fields: [modifiedBy], references: [id], onDelete: NoAction)
  comments        Comment[]    @relation("ActionComments")
  actionResponsables ActionResponsable[]

  @@map("backend_action")
}

// Comment
model Comment {
  id               Int                        @id @default(autoincrement())
  recommendationId Int?                       @map("recommendation_id")
  actionId         Int?                       @map("action_id")
  comment          String
  type             RecommendationActionType?
  value            String?
  validMeraci      ValidationEnum             @default(NO) @map("valid_meraci")
  validDirecteur   ValidationEnum             @default(NO) @map("valid_directeur")
  created          DateTime                   @default(now())
  modified         DateTime                   @updatedAt
  createdBy        Int?                       @map("created_by")
  modifiedBy       Int?                       @map("modified_by")

  // Relations
  recommendation   Recommendation?            @relation("RecommendationComments", fields: [recommendationId], references: [id], onDelete: NoAction)
  action           Action?                    @relation("ActionComments", fields: [actionId], references: [id], onDelete: NoAction)
  createdByUser    User?                      @relation("CreatedByComment", fields: [createdBy], references: [id], onDelete: NoAction)
  modifiedByUser   User?                      @relation("ModifiedByComment", fields: [modifiedBy], references: [id], onDelete: NoAction)
  commentDocuments CommentDocument[]

  @@map("backend_comment")
}

// Comment Document
model CommentDocument {
  id          Int      @id @default(autoincrement())
  commentId   Int      @map("comment_id")
  document    String   // FileField equivalent
  size        BigInt?  @default(0)
  name        String?
  type        String?
  description String?
  created     DateTime @default(now())
  modified    DateTime @updatedAt
  createdBy   Int?     @map("created_by")
  modifiedBy  Int?     @map("modified_by")

  // Relations
  comment     Comment  @relation(fields: [commentId], references: [id], onDelete: NoAction)

  @@map("backend_commentdocument")
}

// Action Responsable
model ActionResponsable {
  id               Int             @id @default(autoincrement())
  recommendationId Int?            @map("recommendation_id")
  actionId         Int?            @map("action_id")
  created          DateTime        @default(now())
  modified         DateTime        @updatedAt
  createdBy        Int?            @map("created_by")
  modifiedBy       Int?            @map("modified_by")

  // Relations
  recommendation   Recommendation? @relation(fields: [recommendationId], references: [id], onDelete: NoAction)
  action           Action?         @relation(fields: [actionId], references: [id], onDelete: NoAction)

  @@map("backend_actionresponsable")
}

// Notification
model Notification {
  id           Int      @id @default(autoincrement())
  userId       Int      @map("user_id")
  read         Boolean  @default(false)
  contentType  String   @map("content_type")
  objectId     Int      @map("object_id")
  created      DateTime @default(now())
  modified     DateTime @updatedAt
  createdBy    Int?     @map("created_by")
  modifiedBy   Int?     @map("modified_by")

  // Relations
  user         User     @relation(fields: [userId], references: [id], onDelete: NoAction)

  @@index([contentType, objectId])
  @@map("backend_notification")
}
