# 🌱 Seed Data Documentation

This document describes the comprehensive seed data created for the CRI Frontend application.

## 📋 Overview

The seed data includes realistic, hierarchical data for the following entities:
- **Domains** (10 records) - Audit domains with parent-child relationships
- **Processes** (10 records) - Audit processes with hierarchical structure
- **CriStructview** (10 records) - Organizational structure data
- **CriAgents** (10 records) - Personnel/agent data

## 🏗️ Data Structure

### 1. Domains (backend_domain)

#### Root Domains (4):
1. **Gestion Financière** (Finance)
   - ID: 1, Short: Finance, Type: ROOT
2. **Opérations et Processus** (Opérations)
   - ID: 2, Short: Opérations, Type: ROOT
3. **Ressources Humaines** (RH)
   - ID: 3, Short: RH, Type: ROOT
4. **Systèmes d'Information** (SI)
   - ID: 4, Short: SI, Type: ROOT

#### Sub-Domains (6):
**Finance Sub-domains:**
- **Comptabilité Générale** (ID: 5, Parent: Finance)
- **Contrôle Budgétaire** (ID: 6, Parent: Finance)
- **Trésorerie** (ID: 7, Parent: Finance)

**Operations Sub-domains:**
- **Gestion des Achats** (ID: 8, Parent: Opérations)
- **Gestion des Stocks** (ID: 9, Parent: Opérations)
- **Qualité et Conformité** (ID: 10, Parent: Opérations)

### 2. Processes (backend_process)

#### Root Processes (3):
1. **Processus d'Audit** (Audit)
   - ID: 1, Short: Audit
2. **Gestion des Risques** (Risques)
   - ID: 2, Short: Risques
3. **Conformité et Contrôle** (Conformité)
   - ID: 3, Short: Conformité

#### Sub-Processes (7):
**Audit Sub-processes:**
- **Planification d'Audit** (ID: 4, Parent: Audit)
- **Exécution d'Audit** (ID: 5, Parent: Audit)
- **Rapport d'Audit** (ID: 6, Parent: Audit)
- **Suivi des Recommandations** (ID: 7, Parent: Audit)

**Risk Management Sub-processes:**
- **Identification des Risques** (ID: 8, Parent: Risques)
- **Évaluation des Risques** (ID: 9, Parent: Risques)
- **Mitigation des Risques** (ID: 10, Parent: Risques)

### 3. CRI Structure View (cri_structview)

Organizational structures representing different departments:

| ID | Code | Department | Unit Code | Mnemonic |
|----|------|------------|-----------|----------|
| 1 | DG | Direction Générale | DG001 | DG |
| 2 | DAF | Direction Administrative et Financière | DAF001 | DAF |
| 3 | DRH | Direction des Ressources Humaines | DRH001 | DRH |
| 4 | DSI | Direction des Systèmes d'Information | DSI001 | DSI |
| 5 | DCOM | Direction Commerciale | DCOM001 | DCOM |
| 6 | DPROD | Direction de Production | DPROD001 | DPROD |
| 7 | DQUAL | Direction Qualité | DQUAL001 | DQUAL |
| 8 | DLOG | Direction Logistique | DLOG001 | DLOG |
| 9 | DAUD | Direction Audit Interne | DAUD001 | DAUD |
| 10 | DJUR | Direction Juridique | DJUR001 | DJUR |

### 4. CRI Agents (cri_agents)

Personnel data with realistic French names and positions:

| Matricule | Name | Position | Structure |
|-----------|------|----------|-----------|
| AGT001 | Jean MARTIN | Directeur Général | DG |
| AGT002 | Marie DUBOIS | Directrice Administrative et Financière | DAF |
| AGT003 | Pierre BERNARD | Directeur des Ressources Humaines | DRH |
| AGT004 | Sophie LEROY | Directrice des Systèmes d'Information | DSI |
| AGT005 | Luc MOREAU | Directeur Commercial | DCOM |
| AGT006 | Anne SIMON | Directrice de Production | DPROD |
| AGT007 | François MICHEL | Directeur Qualité | DQUAL |
| AGT008 | Carmen GARCIA | Directrice Logistique | DLOG |
| AGT009 | Philippe ROUX | Directeur Audit Interne | DAUD |
| AGT010 | Isabelle BLANC | Directrice Juridique | DJUR |

## 🚀 Usage

### Running the Seed Scripts

#### Full Database Seed (includes all data):
```bash
npm run db:seed
```

#### Entity Seed Only (just Domains, Processes, CriStructview, CriAgents):
```bash
npm run db:seed-entities
```

#### Database Reset with Full Seed:
```bash
npm run db:reset
```

### Manual Execution

You can also run the seed scripts directly:

```bash
# Full seed
npx tsx prisma/seed.ts

# Entities only
npx tsx prisma/seed-entities-only.ts
```

## 🔧 Customization

### Adding More Data

To add more seed data, edit `prisma/seed-entities.ts`:

1. **Add more domains**: Extend the domains array with new entries
2. **Add more processes**: Extend the processes array with new entries
3. **Add more structures**: Extend the structures array with new entries
4. **Add more agents**: Extend the agents array with new entries

### Modifying Existing Data

The seed script uses `upsert` operations, so you can:
- Modify existing records by changing the data in the arrays
- Re-run the seed script to update the database

## 📊 Data Relationships

### Domain Hierarchy
```
Finance (1)
├── Comptabilité Générale (5)
├── Contrôle Budgétaire (6)
└── Trésorerie (7)

Opérations (2)
├── Gestion des Achats (8)
├── Gestion des Stocks (9)
└── Qualité et Conformité (10)

RH (3)
SI (4)
```

### Process Hierarchy
```
Audit (1)
├── Planification d'Audit (4)
├── Exécution d'Audit (5)
├── Rapport d'Audit (6)
└── Suivi des Recommandations (7)

Risques (2)
├── Identification des Risques (8)
├── Évaluation des Risques (9)
└── Mitigation des Risques (10)

Conformité (3)
```

### Structure-Agent Relationships
Each agent is linked to their respective organizational structure through the `idStruct` field.

## ✅ Validation

After running the seed scripts, you can verify the data:

1. **Check record counts**:
   - Domains: 10 records (4 root + 6 sub)
   - Processes: 10 records (3 root + 7 sub)
   - CriStructview: 10 records
   - CriAgents: 10 records

2. **Verify relationships**:
   - Domain parent-child relationships
   - Process parent-child relationships
   - Agent-structure associations

3. **Test in application**:
   - Domain dropdowns should populate
   - Process dropdowns should populate
   - Structure data should be available for themes
   - Agent data should be available for user profiles

## 🎯 Benefits

This seed data provides:
- **Realistic test data** for development and testing
- **Hierarchical structures** to test parent-child relationships
- **French localization** appropriate for the application context
- **Complete organizational structure** for comprehensive testing
- **Consistent data relationships** across all entities
