"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/recommendations/followup/page",{

/***/ "(app-client)/./app/(main)/recommendations/(components)/editForm.tsx":
/*!**************************************************************!*\
  !*** ./app/(main)/recommendations/(components)/editForm.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RecommendationEditForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/material */ \"(app-client)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var material_react_table__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! material-react-table */ \"(app-client)/./node_modules/material-react-table/dist/index.esm.js\");\n/* harmony import */ var primereact_sidebar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! primereact/sidebar */ \"(app-client)/./node_modules/primereact/sidebar/sidebar.esm.js\");\n/* harmony import */ var primereact_inputtext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! primereact/inputtext */ \"(app-client)/./node_modules/primereact/inputtext/inputtext.esm.js\");\n/* harmony import */ var primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! primereact/inputtextarea */ \"(app-client)/./node_modules/primereact/inputtextarea/inputtextarea.esm.js\");\n/* harmony import */ var primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! primereact/dropdown */ \"(app-client)/./node_modules/primereact/dropdown/dropdown.esm.js\");\n/* harmony import */ var primereact_picklist__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! primereact/picklist */ \"(app-client)/./node_modules/primereact/picklist/picklist.esm.js\");\n/* harmony import */ var primereact_calendar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! primereact/calendar */ \"(app-client)/./node_modules/primereact/calendar/calendar.esm.js\");\n/* harmony import */ var primereact_button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! primereact/button */ \"(app-client)/./node_modules/primereact/button/button.esm.js\");\n/* harmony import */ var _utilities_service_fr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utilities/service/fr */ \"(app-client)/./utilities/service/fr.ts\");\n/* harmony import */ var primereact_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! primereact/api */ \"(app-client)/./node_modules/primereact/api/api.esm.js\");\n/* harmony import */ var primereact_togglebutton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! primereact/togglebutton */ \"(app-client)/./node_modules/primereact/togglebutton/togglebutton.esm.js\");\n/* harmony import */ var html_react_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! html-react-parser */ \"(app-client)/./node_modules/html-react-parser/esm/index.mjs\");\n/* harmony import */ var _ActionWidget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ActionWidget */ \"(app-client)/./app/(main)/recommendations/(components)/ActionWidget.tsx\");\n/* harmony import */ var primereact_datatable__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! primereact/datatable */ \"(app-client)/./node_modules/primereact/datatable/datatable.esm.js\");\n/* harmony import */ var primereact_column__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! primereact/column */ \"(app-client)/./node_modules/primereact/column/column.esm.js\");\n/* harmony import */ var primereact_tag__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! primereact/tag */ \"(app-client)/./node_modules/primereact/tag/tag.esm.js\");\n/* harmony import */ var primereact_progressbar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! primereact/progressbar */ \"(app-client)/./node_modules/primereact/progressbar/progressbar.esm.js\");\n/* harmony import */ var sanitize_html__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! sanitize-html */ \"(app-client)/./node_modules/sanitize-html/index.js\");\n/* harmony import */ var sanitize_html__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(sanitize_html__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var primereact_slider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! primereact/slider */ \"(app-client)/./node_modules/primereact/slider/slider.esm.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! cookies-next */ \"(app-client)/./node_modules/cookies-next/lib/index.js\");\n/* harmony import */ var cookies_next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(cookies_next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var primereact_inputnumber__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! primereact/inputnumber */ \"(app-client)/./node_modules/primereact/inputnumber/inputnumber.esm.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RecommendationEditForm(props) {\n    var _getCookie, _causes, _causes_data_results, _causes1, _dataTableRef_current, _props_row__valuesCache_error_data, _props_row__valuesCache_error, _props_row__valuesCache_error_data1, _props_row__valuesCache_error1, _props_row__valuesCache_error_data2, _props_row__valuesCache_error2, _props_row__valuesCache_error_data3, _props_row__valuesCache_error3, _missions, _props_row__valuesCache_error_data4, _props_row__valuesCache_error4, _props_row__valuesCache_error_data5, _props_row__valuesCache_error5, _props_row__valuesCache_error_data6, _props_row__valuesCache_error6, _concerned_structures, _props_row__valuesCache_error_data7, _props_row__valuesCache_error7, _props_row__valuesCache_error_data8, _props_row__valuesCache_error8, _props_row__valuesCache_error_data9, _props_row__valuesCache_error9, _props_row__valuesCache_error_data10, _props_row__valuesCache_error10, _props_row__valuesCache_error_data11, _props_row__valuesCache_error11, _props_row__valuesCache_error_data12, _props_row__valuesCache_error12, _props_row__valuesCache_error_data13, _props_row__valuesCache_error13, _props_row__valuesCache_error_data14, _props_row__valuesCache_error14, _props_row__valuesCache_error_data15, _props_row__valuesCache_error15, _props_row__valuesCache_error_data16, _props_row__valuesCache_error16, _props_row__valuesCache_error_data17, _props_row__valuesCache_error17, _props_row__valuesCache_error_data18, _props_row__valuesCache_error18, _props_row__valuesCache_error_data19, _props_row__valuesCache_error19, _props_row__valuesCache_error_data20, _props_row__valuesCache_error20;\n    _s();\n    (0,primereact_api__WEBPACK_IMPORTED_MODULE_7__.addLocale)(\"fr\", _utilities_service_fr__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n    (0,primereact_api__WEBPACK_IMPORTED_MODULE_7__.locale)(\"fr\");\n    const user = JSON.parse(((_getCookie = (0,cookies_next__WEBPACK_IMPORTED_MODULE_5__.getCookie)(\"user\")) === null || _getCookie === void 0 ? void 0 : _getCookie.toString()) || \"{}\");\n    // columns.push(<Column field={key} onCellEditComplete={onCellEditComplete} body={(data) => <><Button icon='pi pi-paperclip' onClick={attachementProofClick}></Button><InputText onChange={attachementProofChanged} type='file' hidden ref={attachementProofRef} /></>} header={$Action.properties[key].title ? $Action.properties[key].title : key} sortable style={{ width: '35%' }} />)\n    const attachementProofChanged = (event)=>{\n        console.log(\"Plan d'actions | PREUVES\", event);\n    };\n    function attachementProofClick(event) {\n        console.log(\"click\");\n        attachementProofRef.current.click();\n    }\n    const attachementProofRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const recommandationNew = {\n        \"mission\": 0,\n        \"concerned_structure\": 0,\n        \"causes\": [],\n        // \"actions\": [],\n        \"recommendation\": \"\",\n        \"priority\": \"FAIBLE\",\n        \"validated\": true,\n        \"status\": \"R\\xe9alis\\xe9e\",\n        \"accepted\": true,\n        \"responsible\": \"\"\n    };\n    const steps = [\n        \"Recommendation\",\n        \"Actions\"\n    ];\n    const dataTableRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [activeStep, setActiveStep] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const [skipped, setSkipped] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    // const { data: recommendationsrefs, isLoading, error } = useApiRecommendationRefList();\n    const { data: causes, isLoading: causes_isLoading, error: causes_error } = useApiCauseList();\n    const { data: concerned_structures, isLoading: concerned_structures_isLoading, error: concerned_structures_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiStructurelqsList)();\n    const { data: users, isLoading: users_isLoading, error: users_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList)();\n    const { data: missions, isLoading: missions_isLoading, error: missions_error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiMissionList)();\n    const [picklistSourceValueCauses, setPicklistSourceValueCauses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? (_causes = causes) === null || _causes === void 0 ? void 0 : _causes.data.results : (_causes1 = causes) === null || _causes1 === void 0 ? void 0 : (_causes_data_results = _causes1.data.results) === null || _causes_data_results === void 0 ? void 0 : _causes_data_results.filter((val, idx)=>!props.row.original.causes.map((val_)=>val_.id).includes(val.id)));\n    const [picklistTargetValueCauses, setPicklistTargetValueCauses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.original.causes ? props.row.original.causes : []);\n    const [recommandationActions, setRecommandationActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.original.actions || []);\n    const [actionsItems, setActionsIems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActionWidget__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 76,\n            columnNumber: 70\n        }, this),\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ActionWidget__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 76,\n            columnNumber: 86\n        }, this)\n    ]);\n    const [editVisible, setEditVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [itemAccepted, setItemAccepted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.original.accepted || false);\n    const [itemValidated, setItemValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.original.validated || false);\n    const [dropdownItemEtat, setDropdownItemEtat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"name\": props.row.original.status || \"\",\n        \"code\": props.row.original.status || \"\"\n    });\n    const [dropdownItemConcernedStructure, setDropdownItemConcernedStructure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"name\": props.row.original.concerned_structure.code_mnemonique || props.row.original.concerned_structure.libell_stru || props.row.original.concerned_structure.code_stru,\n        \"code\": \"\".concat(props.row.original.concerned_structure.id)\n    });\n    const [dropdownItemRecommendation, setDropdownItemRecommendation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : props.row.original.recommendation);\n    const [dropdownItemResponsible, setDropdownItemResponsible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : props.row.original.responsible);\n    const [dropdownItemMission, setDropdownItemMission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(props.row.id === \"mrt-row-create\" ? null : {\n        \"name\": props.row.original.mission || \"\",\n        \"code\": props.row.original.mission\n    });\n    var _props_row_original_numrecommandation;\n    const [numRecommdantaionMission, setNumRecommdantaionMission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_props_row_original_numrecommandation = props.row.original.numrecommandation) !== null && _props_row_original_numrecommandation !== void 0 ? _props_row_original_numrecommandation : -1);\n    const [dropdownItemPriority, setDropdownItemPriority] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"name\": props.row.original.priority || \"\",\n        \"code\": props.row.original.priority || \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _causes, _causes_data_results, _causes1;\n        setPicklistTargetValueCauses(props.row.original.causes);\n        setPicklistSourceValueCauses(props.row.id === \"mrt-row-create\" ? (_causes = causes) === null || _causes === void 0 ? void 0 : _causes.data.results : (_causes1 = causes) === null || _causes1 === void 0 ? void 0 : (_causes_data_results = _causes1.data.results) === null || _causes_data_results === void 0 ? void 0 : _causes_data_results.filter((val, idx)=>!props.row.original.causes.map((val_)=>val_.id).includes(val.id)));\n        // setDropdownItemTheme({ \"name\": mission_theme?.theme.title || \"\", \"code\": mission_theme?.theme.code || \"\" });\n        // setActions(props.row.original.action_plan?.actions || [])\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            causes: props.row.original.causes || []\n        };\n    }, [\n        causes\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"useEffect\", recommandationActions);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            actions: recommandationActions\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            actions: recommandationActions.map((action)=>{\n                console.log(action);\n                return {\n                    \"id\": action.id,\n                    \"job_leader\": action.job_leader,\n                    \"description\": action.description,\n                    \"start_date\": action.start_date,\n                    \"end_date\": action.end_date,\n                    \"validated\": action.validated,\n                    \"status\": action.status,\n                    \"progress\": action.progress,\n                    \"accepted\": action.accepted\n                };\n            })\n        };\n    }, [\n        recommandationActions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n    // props.row._valuesCache = { ...props.row._valuesCache, ...recommandationNew }\n    }, []);\n    // useEffect(() => {\n    //     console.log(\"######################editform############################\", props.row._valuesCache)\n    // }, [props.row._valuesCache]);\n    const handleNumRecommendationChange = (e)=>{\n        console.log(\"handleNumRecommendationChange\", e.value);\n        setNumRecommdantaionMission(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                numrecommandation: e.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            numrecommandation: e.value\n        };\n    };\n    const handleMissionChange = (e)=>{\n        var _missions, _missions_data_results_find, _missions1, _missions_data_results_find1, _missions2;\n        console.log(\"handleMissionChange\", e.value);\n        console.log((_missions = missions) === null || _missions === void 0 ? void 0 : _missions.data.results.filter((mission)=>mission.code === e.value.name));\n        setDropdownItemMission(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                mission: (_missions1 = missions) === null || _missions1 === void 0 ? void 0 : (_missions_data_results_find = _missions1.data.results.find((mission)=>mission.code === e.value.name)) === null || _missions_data_results_find === void 0 ? void 0 : _missions_data_results_find.id\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            mission: (_missions2 = missions) === null || _missions2 === void 0 ? void 0 : (_missions_data_results_find1 = _missions2.data.results.find((mission)=>mission.code === e.value.name)) === null || _missions_data_results_find1 === void 0 ? void 0 : _missions_data_results_find1.id\n        };\n    };\n    const handleAcceptationChange = (e)=>{\n        console.log(\"handleAcceptationChange\", e.value);\n        setItemAccepted(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                accepted: e.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            accepted: e.value\n        };\n    };\n    const handleValidationChange = (e)=>{\n        console.log(\"handleValidationChange\", e.value);\n        setItemValidated(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                validated: e.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            validated: e.value\n        };\n    };\n    const handleEtatChange = (e)=>{\n        console.log(\"handleEtatChange\", e.value);\n        setDropdownItemEtat(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                status: e.value.name\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            status: e.value.name\n        };\n    };\n    const handlePriorityChange = (e)=>{\n        console.log(\"handlePriorityChange\", e.value);\n        setDropdownItemPriority(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                priority: e.value.name\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            priority: e.value.name\n        };\n    };\n    const handleConcernedStructureChange = (e)=>{\n        console.log(\"handleConcernedStructureChange\", e.value);\n        setDropdownItemConcernedStructure(e.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                concerned_structure: e.value.code\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            concerned_structure: e.value.code\n        };\n    };\n    const handleRecommendationRefChange = (e)=>{\n        console.log(\"handleRecommendationRefChange\", e);\n        setDropdownItemRecommendation(e.target.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                recommendation: e.target.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            recommendation: e.target.value\n        };\n    };\n    const handleResponsibleChange = (e)=>{\n        console.log(\"handleRecommendationRefChange\", e);\n        setDropdownItemResponsible(e.target.value);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                responsible: e.target.value\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            responsible: e.target.value\n        };\n    };\n    const handleCauses = (e)=>{\n        console.info(\"handleCauses\");\n        setPicklistSourceValueCauses(e.source);\n        console.log(\"source Causes\", e.source);\n        setPicklistTargetValueCauses(e.target);\n        console.log(\"target Causes\", e.target);\n        props.row._valuesCache = {\n            ...props.row._valuesCache,\n            ...{\n                causes: e.target.map((cause)=>{\n                    return cause.id;\n                })\n            }\n        };\n        props.row._valuesCache._changedValues = {\n            ...props.row._valuesCache._changedValues,\n            causes: e.target.map((cause)=>{\n                return cause.id;\n            })\n        };\n    };\n    // const cellEditor = (options) => {\n    //     if (options.field === 'price') return priceEditor(options);\n    //     else return textEditor(options);\n    // };\n    const insertAction = ()=>{\n        const newaction = {\n            // \"id\": recommandationActions.length,\n            \"job_leader\": {\n                \"username\": \"\",\n                \"first_name\": \"job\",\n                \"last_name\": \"leader\",\n                \"email\": \"\"\n            },\n            \"description\": \"D\\xe9finir une nouvelle t\\xe2che\",\n            \"start_date\": new Date().toISOString().split(\"T\")[0],\n            \"end_date\": new Date().toISOString().split(\"T\")[0],\n            \"validated\": false,\n            \"status\": \"En cours\",\n            \"progress\": 0,\n            \"accepted\": false,\n            \"dependencies\": [],\n            \"proof\": null\n        };\n        const recommandationActions_ = [\n            ...recommandationActions\n        ];\n        recommandationActions_.push(newaction);\n        setRecommandationActions(recommandationActions_);\n    };\n    const textEditor = (options)=>{\n        console.log(\"##################################\", options);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_8__.InputText, {\n            type: \"text\",\n            value: options.value,\n            onChange: (e)=>options.editorCallback(e.target.value),\n            onKeyDown: (e)=>e.stopPropagation()\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 234,\n            columnNumber: 16\n        }, this);\n    };\n    const dateEditor = (options)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_calendar__WEBPACK_IMPORTED_MODULE_9__.Calendar, {\n            value: options.value,\n            onChange: (e)=>options.editorCallback(e.value)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 237,\n            columnNumber: 16\n        }, this);\n    };\n    const statusEditor = (options)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n            value: options.value,\n            onChange: (e)=>options.editorCallback(e.value),\n            options: [\n                \"En cours\",\n                \"R\\xe9alis\\xe9e\",\n                \"Non R\\xe9alis\\xe9e\"\n            ]\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 240,\n            columnNumber: 16\n        }, this);\n    };\n    const jobLeaderEditor = (options)=>{\n        var _options_value, _options_value1, _options_value2, _users;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n            onAbort: (e)=>console.log(\"aborting\", e),\n            filter: true,\n            showFilterClear: true,\n            optionLabel: \"name\",\n            value: {\n                name: \"\".concat((_options_value = options.value) === null || _options_value === void 0 ? void 0 : _options_value.last_name, \" \").concat((_options_value1 = options.value) === null || _options_value1 === void 0 ? void 0 : _options_value1.first_name),\n                code: \"\".concat((_options_value2 = options.value) === null || _options_value2 === void 0 ? void 0 : _options_value2.id)\n            },\n            onChange: (e)=>{\n                console.log(\"aborting\", e);\n                options.editorCallback(e.value);\n            },\n            options: (_users = users) === null || _users === void 0 ? void 0 : _users.data.results.map((user)=>{\n                return {\n                    name: \"\".concat(user.last_name, \" \").concat(user.first_name),\n                    code: \"\".concat(user.id)\n                };\n            })\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 243,\n            columnNumber: 16\n        }, this);\n    };\n    const progressEditor = (options)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_8__.InputText, {\n                    value: options.value,\n                    onChange: (e)=>options.editorCallback(e.target.value)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 18\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_slider__WEBPACK_IMPORTED_MODULE_11__.Slider, {\n                    value: options.value,\n                    onChange: (e)=>options.editorCallback(e.value)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 110\n                }, this)\n            ]\n        }, void 0, true);\n    };\n    const boolEditor = (options)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_togglebutton__WEBPACK_IMPORTED_MODULE_12__.ToggleButton, {\n            checked: options.value,\n            onChange: (e)=>options.editorCallback(e.value)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 249,\n            columnNumber: 16\n        }, this);\n    };\n    const onCellEditComplete = (e)=>{\n        var _dataTableRef_current, _dataTableRef_current1;\n        let { rowData, newValue, field, originalEvent: event } = e;\n        if (field === \"description\") {\n            rowData[field] = newValue;\n        } else if (field === \"job_leader\") {\n            if (newValue !== undefined) {\n                var _users, _users1;\n                if (newValue.id) rowData.job_leader = (_users = users) === null || _users === void 0 ? void 0 : _users.data.results.find((user)=>{\n                    var _newValue;\n                    return user.id === parseInt((_newValue = newValue) === null || _newValue === void 0 ? void 0 : _newValue.id);\n                });\n                if (newValue.code) rowData.job_leader = (_users1 = users) === null || _users1 === void 0 ? void 0 : _users1.data.results.find((user)=>{\n                    var _newValue;\n                    return user.id === parseInt((_newValue = newValue) === null || _newValue === void 0 ? void 0 : _newValue.code);\n                });\n            }\n        } else if ([\n            \"start_date\",\n            \"end_date\"\n        ].includes(field) && newValue !== undefined) {\n            var _newValue;\n            rowData[field] = newValue instanceof Date ? (_newValue = newValue) === null || _newValue === void 0 ? void 0 : _newValue.toISOString().split(\"T\")[0] : new Date(newValue).toISOString().split(\"T\")[0];\n        } else if ([\n            \"validated\",\n            \"accepted\"\n        ].includes(field)) {\n            rowData[field] = newValue;\n        } else if ([\n            \"progress\"\n        ].includes(field)) {\n            rowData[field] = newValue;\n        } else if ([\n            \"status\"\n        ].includes(field)) {\n            rowData[field] = newValue;\n        } else {\n            rowData[field] = newValue;\n        }\n        console.log(\"settings actions\", (_dataTableRef_current = dataTableRef.current) === null || _dataTableRef_current === void 0 ? void 0 : _dataTableRef_current.props.value);\n        const recommandationActions_ = [\n            ...(_dataTableRef_current1 = dataTableRef.current) === null || _dataTableRef_current1 === void 0 ? void 0 : _dataTableRef_current1.props.value\n        ];\n        setRecommandationActions(recommandationActions_);\n    };\n    const generateColumns = ()=>{\n        let columns = [];\n        for (const [key, value] of Object.entries($Action.properties).filter((param, index)=>{\n            let [key, value] = param;\n            return ![\n                \"validated\",\n                \"accepted\",\n                \"created_by\",\n                \"dependencies\",\n                \"modified_by\",\n                \"created\",\n                \"modified\",\n                \"id\"\n            ].includes(key);\n        })){\n            if (key === \"description\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_13__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>textEditor(options),\n                    body: (data)=>(0,html_react_parser__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(sanitize_html__WEBPACK_IMPORTED_MODULE_14___default()(data.description)),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 30\n                }, this));\n            } else if (key === \"job_leader\") {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_13__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>jobLeaderEditor(options),\n                    body: (data)=>{\n                        var _data_job_leader, _data_job_leader1;\n                        return \"\".concat((_data_job_leader = data.job_leader) === null || _data_job_leader === void 0 ? void 0 : _data_job_leader.last_name, \" \").concat((_data_job_leader1 = data.job_leader) === null || _data_job_leader1 === void 0 ? void 0 : _data_job_leader1.first_name);\n                    },\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"start_date\",\n                \"end_date\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_13__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>dateEditor(options),\n                    body: (data)=>new Date(data[key]).toLocaleDateString(),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"validated\",\n                \"accepted\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_13__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>boolEditor(options),\n                    body: (data)=>data[key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"pi pi-check-circle\",\n                            style: {\n                                color: \"green\"\n                            }\n                        }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"pi pi-times-circle\",\n                            style: {\n                                color: \"red\"\n                            }\n                        }, void 0, false, void 0, void 0),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"progress\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_13__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>progressEditor(options),\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_progressbar__WEBPACK_IMPORTED_MODULE_15__.ProgressBar, {\n                            value: data[key]\n                        }, void 0, false, void 0, void 0),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"status\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_13__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    editor: (options)=>statusEditor(options),\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_tag__WEBPACK_IMPORTED_MODULE_16__.Tag, {\n                            value: data[key]\n                        }, void 0, false, void 0, void 0),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 30\n                }, this));\n            } else if ([\n                \"proof\"\n            ].includes(key)) {\n                columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_13__.Column, {\n                    field: key,\n                    onCellEditComplete: onCellEditComplete,\n                    body: (data)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                    icon: \"pi pi-paperclip\",\n                                    onClick: attachementProofClick\n                                }, void 0, false, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_8__.InputText, {\n                                    onChange: attachementProofChanged,\n                                    type: \"file\",\n                                    hidden: true,\n                                    ref: attachementProofRef\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true),\n                    header: $Action.properties[key].title ? $Action.properties[key].title : key,\n                    sortable: true,\n                    style: {\n                        width: \"35%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 30\n                }, this));\n            }\n        }\n        columns.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_column__WEBPACK_IMPORTED_MODULE_13__.Column, {\n            header: \"Action\",\n            sortableDisabled: true,\n            field: \"action\",\n            body: (options)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                    icon: \"pi pi-trash\",\n                    onClick: (e)=>{\n                        var _dataTableRef_current_props_value, _dataTableRef_current;\n                        console.log(options);\n                        setRecommandationActions((_dataTableRef_current = dataTableRef.current) === null || _dataTableRef_current === void 0 ? void 0 : (_dataTableRef_current_props_value = _dataTableRef_current.props.value) === null || _dataTableRef_current_props_value === void 0 ? void 0 : _dataTableRef_current_props_value.filter((act)=>act.id != options.id));\n                    }\n                }, void 0, false, void 0, void 0),\n            sortable: true,\n            style: {\n                width: \"35%\"\n            }\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 310,\n            columnNumber: 22\n        }, this));\n        return columns;\n    };\n    ///////////////////////////////////////////////////////////////////////////////    \n    const isStepOptional = (step)=>{\n        return step === 1;\n    };\n    const isStepSkipped = (step)=>{\n        return skipped.has(step);\n    };\n    console.log((_dataTableRef_current = dataTableRef.current) === null || _dataTableRef_current === void 0 ? void 0 : _dataTableRef_current.props.value);\n    console.log(recommandationActions);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                zIndex: \"1302 !important\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_sidebar__WEBPACK_IMPORTED_MODULE_18__.Sidebar, {\n                position: \"right\",\n                header: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row w-full flex-wrap justify-content-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            variant: \"h4\",\n                            className: \"align-content-center \",\n                            children: props.row.id === \"mrt-row-create\" ? \"Nouvelle recommandation\" : props.row._valuesCache.actions_add ? \"Saisie des actions pour la recommandation N\\xb0 \".concat(props.row.original.id) : \"Editer Recommandation N\\xb0 \".concat(props.row.original.id)\n                        }, void 0, false, void 0, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(material_react_table__WEBPACK_IMPORTED_MODULE_21__.MRT_EditActionButtons, {\n                                variant: \"text\",\n                                table: props.table,\n                                row: props.row\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    ]\n                }, void 0, true, void 0, void 0),\n                visible: editVisible,\n                onHide: ()=>{\n                    props.table.setEditingRow(null);\n                    setEditVisible(false);\n                },\n                className: \"w-full md:w-9 lg:w-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: \"1.5rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                !props.row._valuesCache.actions_add && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-fluid formgrid grid\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"numrecommandation\",\n                                                        children: \"N\\xb0 Recommandation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputnumber__WEBPACK_IMPORTED_MODULE_23__.InputNumber, {\n                                                        className: ((_props_row__valuesCache_error = props.row._valuesCache.error) === null || _props_row__valuesCache_error === void 0 ? void 0 : (_props_row__valuesCache_error_data = _props_row__valuesCache_error.data) === null || _props_row__valuesCache_error_data === void 0 ? void 0 : _props_row__valuesCache_error_data[\"numrecommandation\"]) ? \"p-invalid\" : \"\",\n                                                        id: \"numrecommandation\",\n                                                        value: numRecommdantaionMission,\n                                                        onChange: (e)=>handleNumRecommendationChange(e),\n                                                        placeholder: \"Saisir le num\\xe9ro de la recommandation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error1 = props.row._valuesCache.error) === null || _props_row__valuesCache_error1 === void 0 ? void 0 : (_props_row__valuesCache_error_data1 = _props_row__valuesCache_error1.data) === null || _props_row__valuesCache_error_data1 === void 0 ? void 0 : _props_row__valuesCache_error_data1[\"numrecommandation\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error2 = props.row._valuesCache.error) === null || _props_row__valuesCache_error2 === void 0 ? void 0 : (_props_row__valuesCache_error_data2 = _props_row__valuesCache_error2.data) === null || _props_row__valuesCache_error_data2 === void 0 ? void 0 : _props_row__valuesCache_error_data2[\"numrecommandation\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 111\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        children: \"Automatiquement incr\\xe9mentable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"mission\",\n                                                        children: \"Mission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n                                                        filter: true,\n                                                        className: ((_props_row__valuesCache_error3 = props.row._valuesCache.error) === null || _props_row__valuesCache_error3 === void 0 ? void 0 : (_props_row__valuesCache_error_data3 = _props_row__valuesCache_error3.data) === null || _props_row__valuesCache_error_data3 === void 0 ? void 0 : _props_row__valuesCache_error_data3[\"mission\"]) ? \"p-invalid\" : \"\",\n                                                        id: \"mission\",\n                                                        value: dropdownItemMission,\n                                                        onChange: (e)=>handleMissionChange(e),\n                                                        options: (_missions = missions) === null || _missions === void 0 ? void 0 : _missions.data.results.map(function(val) {\n                                                            return {\n                                                                \"name\": val.code,\n                                                                \"code\": val.code\n                                                            };\n                                                        }),\n                                                        optionLabel: \"name\",\n                                                        placeholder: \"Choisir une mission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error4 = props.row._valuesCache.error) === null || _props_row__valuesCache_error4 === void 0 ? void 0 : (_props_row__valuesCache_error_data4 = _props_row__valuesCache_error4.data) === null || _props_row__valuesCache_error_data4 === void 0 ? void 0 : _props_row__valuesCache_error_data4[\"mission\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error5 = props.row._valuesCache.error) === null || _props_row__valuesCache_error5 === void 0 ? void 0 : (_props_row__valuesCache_error_data5 = _props_row__valuesCache_error5.data) === null || _props_row__valuesCache_error_data5 === void 0 ? void 0 : _props_row__valuesCache_error_data5[\"mission\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 101\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"concerned_structure\",\n                                                        children: \"Structure Concern\\xe9e\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n                                                        className: ((_props_row__valuesCache_error6 = props.row._valuesCache.error) === null || _props_row__valuesCache_error6 === void 0 ? void 0 : (_props_row__valuesCache_error_data6 = _props_row__valuesCache_error6.data) === null || _props_row__valuesCache_error_data6 === void 0 ? void 0 : _props_row__valuesCache_error_data6[\"concerned_structure\"]) ? \"p-invalid\" : \"\",\n                                                        filter: true,\n                                                        id: \"concerned_structure\",\n                                                        value: dropdownItemConcernedStructure,\n                                                        onChange: (e)=>handleConcernedStructureChange(e),\n                                                        options: (_concerned_structures = concerned_structures) === null || _concerned_structures === void 0 ? void 0 : _concerned_structures.data.results.map(function(val) {\n                                                            return {\n                                                                \"name\": val.code_mnemonique || val.libell_stru || val.code_stru,\n                                                                \"code\": \"\".concat(val.id)\n                                                            };\n                                                        }),\n                                                        optionLabel: \"name\",\n                                                        placeholder: \"Choisir une structure\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error7 = props.row._valuesCache.error) === null || _props_row__valuesCache_error7 === void 0 ? void 0 : (_props_row__valuesCache_error_data7 = _props_row__valuesCache_error7.data) === null || _props_row__valuesCache_error_data7 === void 0 ? void 0 : _props_row__valuesCache_error_data7[\"concerned_structure\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error8 = props.row._valuesCache.error) === null || _props_row__valuesCache_error8 === void 0 ? void 0 : (_props_row__valuesCache_error_data8 = _props_row__valuesCache_error8.data) === null || _props_row__valuesCache_error_data8 === void 0 ? void 0 : _props_row__valuesCache_error_data8[\"concerned_structure\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 113\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"responsible\",\n                                                        children: \"Responsable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtext__WEBPACK_IMPORTED_MODULE_8__.InputText, {\n                                                        className: ((_props_row__valuesCache_error9 = props.row._valuesCache.error) === null || _props_row__valuesCache_error9 === void 0 ? void 0 : (_props_row__valuesCache_error_data9 = _props_row__valuesCache_error9.data) === null || _props_row__valuesCache_error_data9 === void 0 ? void 0 : _props_row__valuesCache_error_data9[\"responsible\"]) ? \"p-invalid\" : \"\",\n                                                        id: \"responsible\",\n                                                        value: dropdownItemResponsible,\n                                                        onChange: (e)=>handleResponsibleChange(e),\n                                                        placeholder: \"Saisir le responsable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error10 = props.row._valuesCache.error) === null || _props_row__valuesCache_error10 === void 0 ? void 0 : (_props_row__valuesCache_error_data10 = _props_row__valuesCache_error10.data) === null || _props_row__valuesCache_error_data10 === void 0 ? void 0 : _props_row__valuesCache_error_data10[\"responsible\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error11 = props.row._valuesCache.error) === null || _props_row__valuesCache_error11 === void 0 ? void 0 : (_props_row__valuesCache_error_data11 = _props_row__valuesCache_error11.data) === null || _props_row__valuesCache_error_data11 === void 0 ? void 0 : _props_row__valuesCache_error_data11[\"responsible\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 105\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"status\",\n                                                        children: \"Statut\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n                                                        className: ((_props_row__valuesCache_error12 = props.row._valuesCache.error) === null || _props_row__valuesCache_error12 === void 0 ? void 0 : (_props_row__valuesCache_error_data12 = _props_row__valuesCache_error12.data) === null || _props_row__valuesCache_error_data12 === void 0 ? void 0 : _props_row__valuesCache_error_data12[\"status\"]) ? \"p-invalid\" : \"\",\n                                                        filter: true,\n                                                        id: \"status\",\n                                                        value: dropdownItemEtat,\n                                                        onChange: (e)=>handleEtatChange(e),\n                                                        options: $StatusEnum.enum.map(function(val) {\n                                                            return {\n                                                                \"name\": val,\n                                                                \"code\": val\n                                                            };\n                                                        }),\n                                                        optionLabel: \"name\",\n                                                        placeholder: \"Choisir un statut\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error13 = props.row._valuesCache.error) === null || _props_row__valuesCache_error13 === void 0 ? void 0 : (_props_row__valuesCache_error_data13 = _props_row__valuesCache_error13.data) === null || _props_row__valuesCache_error_data13 === void 0 ? void 0 : _props_row__valuesCache_error_data13[\"status\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error14 = props.row._valuesCache.error) === null || _props_row__valuesCache_error14 === void 0 ? void 0 : (_props_row__valuesCache_error_data14 = _props_row__valuesCache_error14.data) === null || _props_row__valuesCache_error_data14 === void 0 ? void 0 : _props_row__valuesCache_error_data14[\"status\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 100\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12 md:col-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"priority\",\n                                                        children: \"Priorit\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, {\n                                                        className: ((_props_row__valuesCache_error15 = props.row._valuesCache.error) === null || _props_row__valuesCache_error15 === void 0 ? void 0 : (_props_row__valuesCache_error_data15 = _props_row__valuesCache_error15.data) === null || _props_row__valuesCache_error_data15 === void 0 ? void 0 : _props_row__valuesCache_error_data15[\"priority\"]) ? \"p-invalid\" : \"\",\n                                                        filter: true,\n                                                        id: \"priority\",\n                                                        value: dropdownItemPriority,\n                                                        onChange: (e)=>handlePriorityChange(e),\n                                                        options: $PriorityEnum.enum.map(function(val) {\n                                                            return {\n                                                                \"name\": val,\n                                                                \"code\": val\n                                                            };\n                                                        }),\n                                                        optionLabel: \"name\",\n                                                        placeholder: \"Choisir une priorit\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error16 = props.row._valuesCache.error) === null || _props_row__valuesCache_error16 === void 0 ? void 0 : (_props_row__valuesCache_error_data16 = _props_row__valuesCache_error16.data) === null || _props_row__valuesCache_error_data16 === void 0 ? void 0 : _props_row__valuesCache_error_data16[\"priority\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error17 = props.row._valuesCache.error) === null || _props_row__valuesCache_error17 === void 0 ? void 0 : (_props_row__valuesCache_error_data17 = _props_row__valuesCache_error17.data) === null || _props_row__valuesCache_error_data17 === void 0 ? void 0 : _props_row__valuesCache_error_data17[\"priority\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 102\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"recommendation\",\n                                                        children: \"Recommendation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_inputtextarea__WEBPACK_IMPORTED_MODULE_24__.InputTextarea, {\n                                                        className: ((_props_row__valuesCache_error18 = props.row._valuesCache.error) === null || _props_row__valuesCache_error18 === void 0 ? void 0 : (_props_row__valuesCache_error_data18 = _props_row__valuesCache_error18.data) === null || _props_row__valuesCache_error_data18 === void 0 ? void 0 : _props_row__valuesCache_error_data18[\"recommendation\"]) ? \"p-invalid\" : \"\",\n                                                        id: \"recommendation\",\n                                                        value: dropdownItemRecommendation,\n                                                        onChange: (e)=>handleRecommendationRefChange(e),\n                                                        placeholder: \"Saisir la recommendation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    ((_props_row__valuesCache_error19 = props.row._valuesCache.error) === null || _props_row__valuesCache_error19 === void 0 ? void 0 : (_props_row__valuesCache_error_data19 = _props_row__valuesCache_error19.data) === null || _props_row__valuesCache_error_data19 === void 0 ? void 0 : _props_row__valuesCache_error_data19[\"recommendation\"]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"p-error\",\n                                                        children: (_props_row__valuesCache_error20 = props.row._valuesCache.error) === null || _props_row__valuesCache_error20 === void 0 ? void 0 : (_props_row__valuesCache_error_data20 = _props_row__valuesCache_error20.data) === null || _props_row__valuesCache_error_data20 === void 0 ? void 0 : _props_row__valuesCache_error_data20[\"recommendation\"][0]\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 108\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"field col-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"picklist_causes\",\n                                                        children: \"Causes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_picklist__WEBPACK_IMPORTED_MODULE_25__.PickList, {\n                                                        id: \"picklist_causes\",\n                                                        source: picklistSourceValueCauses,\n                                                        target: picklistTargetValueCauses,\n                                                        sourceHeader: \"Disponibles\",\n                                                        targetHeader: \"S\\xe9l\\xe9ctionn\\xe9s\",\n                                                        itemTemplate: (item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: item.content\n                                                            }, void 0, false, void 0, void 0),\n                                                        onChange: (e)=>{\n                                                            handleCauses(e);\n                                                        },\n                                                        sourceStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        targetStyle: {\n                                                            height: \"200px\"\n                                                        },\n                                                        filter: true,\n                                                        filterBy: \"content\",\n                                                        filterMatchMode: \"contains\",\n                                                        sourceFilterPlaceholder: \"Recherche\",\n                                                        targetFilterPlaceholder: \"Recherche\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 45\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 41\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 37\n                                }, this),\n                                props.row._valuesCache.actions_add && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            label: \"Ajouter une action\",\n                                            icon: \"pi pi-plus-circle\",\n                                            onClick: ()=>insertAction()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 41\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(primereact_datatable__WEBPACK_IMPORTED_MODULE_26__.DataTable, {\n                                            ref: dataTableRef,\n                                            editMode: \"cell\",\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            value: recommandationActions,\n                                            rows: 5,\n                                            paginator: true,\n                                            responsiveLayout: \"scroll\",\n                                            children: generateColumns()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n                lineNumber: 328,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\recommendations\\\\(components)\\\\editForm.tsx\",\n            lineNumber: 327,\n            columnNumber: 13\n        }, this)\n    }, void 0, false);\n}\n_s(RecommendationEditForm, \"Llwc8PKh7N3DSS48h4EEKk56w/Y=\", true, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiStructurelqsList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiUserList,\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_6__.useApiMissionList\n    ];\n});\n_c = RecommendationEditForm;\nvar _c;\n$RefreshReg$(_c, \"RecommendationEditForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/recommendations/(components)/editForm.tsx\n"));

/***/ })

});