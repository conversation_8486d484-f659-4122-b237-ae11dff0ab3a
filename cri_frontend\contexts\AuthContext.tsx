'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { createAbilityForUser, defineAbilitiesFor, AppAbility, createDefaultAbility } from '@/app/ability';
import { AbilityContext } from '@/app/Can';

interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  image?: string;
  isActive: boolean;
  isStaff: boolean;
  isSuperuser: boolean;
  dateJoined: Date;
  lastLogin?: Date;
  emailVerified?: Date;
}

interface AuthContextType {
  user: User | null;
  ability: AppAbility;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  loading: boolean;
  updateAbility: (user: User) => Promise<void>;
}

interface RegisterData {
  email: string;
  password: string;
  username: string;
  firstName?: string;
  lastName?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [ability, setAbility] = useState<AppAbility>(createDefaultAbility());
  const [loading, setLoading] = useState(true);

  // Update ability when user changes
  const updateAbility = async (currentUser: User | null) => {
    if (currentUser) {
      try {
        // Try async version first (with database permissions)
        const newAbility = await defineAbilitiesFor(currentUser);
        setAbility(newAbility);
      } catch (error) {
        console.error('Error loading user permissions, falling back to basic permissions:', error);
        // Fallback to synchronous version
        const fallbackAbility = createAbilityForUser(currentUser);
        setAbility(fallbackAbility);
      }
    } else {
      setAbility(createDefaultAbility());
    }
  };

  // Check for existing session on mount
  useEffect(() => {
    // checkSession();
  }, []);

  // Update ability when user changes
  useEffect(() => {
    updateAbility(user);
  }, [user]);

  const checkSession = async () => {
    try {
      const response = await fetch('/api/auth/get-session', {
        method: 'GET',
        credentials: 'include', // Important for session cookies
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const sessionData = await response.json();
        console.log('Session data:', sessionData);
        if (sessionData?.user) {
          setUser(sessionData.user);
        } else {
          setUser(null);
        }
      } else {
        console.log('No active session');
        setUser(null);
      }
    } catch (error) {
      console.error('Error checking session:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/sign-in/email', {
        method: 'POST',
        credentials: 'include', // Important for session cookies
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Login failed');
      }

      const data = await response.json();
      console.log('Login successful:', data);
      if (data.user) {
        setUser(data.user);
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const register = async (data: RegisterData) => {
    try {
      const response = await fetch('/api/auth/sign-up/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Registration failed');
      }

      const result = await response.json();
      setUser(result.user);
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/sign-out', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      setUser(null); // Set to null anyway
    }
  };

  const value = {
    user,
    ability,
    login,
    logout,
    register,
    loading,
    updateAbility: () => updateAbility(user),
  };

  return (
    <AuthContext.Provider value={value}>
      <AbilityContext.Provider value={ability}>
        {children}
      </AbilityContext.Provider>
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function useAbility() {
  return useContext(AbilityContext);
}
