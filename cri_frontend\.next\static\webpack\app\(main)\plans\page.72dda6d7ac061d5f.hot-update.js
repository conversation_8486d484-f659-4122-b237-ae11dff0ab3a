"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/page",{

/***/ "(app-client)/./app/ability.ts":
/*!************************!*\
  !*** ./app/ability.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAbilityForUser: function() { return /* binding */ createAbilityForUser; },\n/* harmony export */   createDefaultAbility: function() { return /* binding */ createDefaultAbility; },\n/* harmony export */   defineAbilitiesFor: function() { return /* binding */ defineAbilitiesFor; }\n/* harmony export */ });\n/* harmony import */ var _casl_ability__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @casl/ability */ \"(app-client)/./node_modules/@casl/ability/dist/es6m/index.mjs\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(app-client)/./lib/prisma.ts\");\n\n\n// Cache for user permissions to avoid repeated database queries\nconst permissionCache = new Map();\n// Define abilities based on user roles and permissions from database\nasync function defineAbilitiesFor(user) {\n    const { can, cannot, build } = new _casl_ability__WEBPACK_IMPORTED_MODULE_1__.AbilityBuilder(_casl_ability__WEBPACK_IMPORTED_MODULE_1__.createMongoAbility);\n    if (!user) {\n        // Unauthenticated users - minimal permissions\n        can(\"read\", [\n            \"Mission\",\n            \"Recommendation\",\n            \"Plan\",\n            \"Theme\"\n        ]);\n        return build();\n    }\n    // Superuser has all permissions\n    if (user.isSuperuser) {\n        can(\"manage\", \"all\");\n        return build();\n    }\n    // Check cache first\n    const cacheKey = \"\".concat(user.id, \"_\").concat(user.updatedAt || user.dateJoined);\n    let userPermissions = permissionCache.get(cacheKey);\n    if (!userPermissions) {\n        try {\n            // Fetch user roles and permissions from database\n            userPermissions = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.userRole.findMany({\n                where: {\n                    userId: user.id\n                },\n                include: {\n                    role: {\n                        include: {\n                            rolePermissions: {\n                                include: {\n                                    permission: true\n                                }\n                            }\n                        }\n                    }\n                }\n            });\n            // Cache the permissions\n            permissionCache.set(cacheKey, userPermissions);\n        } catch (error) {\n            console.error(\"Error fetching user permissions:\", error);\n            userPermissions = [];\n        }\n    }\n    // Apply permissions from database\n    for (const userRole of userPermissions){\n        for (const rolePermission of userRole.role.rolePermissions){\n            const permission = rolePermission.permission;\n            // Apply the permission\n            if (permission.conditions) {\n                can(permission.action, permission.subject, permission.conditions);\n            } else {\n                can(permission.action, permission.subject);\n            }\n            // Apply field-level permissions if specified\n            if (permission.fields && permission.fields.length > 0) {\n                can(permission.action, permission.subject, permission.fields);\n            }\n        }\n    }\n    // Default permissions for authenticated users\n    can(\"read\", [\n        \"Mission\",\n        \"Recommendation\",\n        \"Plan\",\n        \"Theme\",\n        \"Domain\",\n        \"Process\"\n    ]);\n    can(\"read\", \"User\", {\n        id: user.id\n    }); // Users can read their own profile\n    can(\"update\", \"User\", {\n        id: user.id\n    }); // Users can update their own profile\n    // Staff members get additional permissions\n    if (user.isStaff) {\n        can(\"create\", [\n            \"Mission\",\n            \"Recommendation\",\n            \"Comment\"\n        ]);\n        can(\"update\", [\n            \"Mission\",\n            \"Recommendation\"\n        ], {\n            createdBy: user.id\n        });\n        can(\"delete\", [\n            \"Comment\"\n        ], {\n            createdBy: user.id\n        });\n        can(\"change\", [\n            \"Mission\",\n            \"Plan\",\n            \"Recommendation\"\n        ]);\n        can(\"add\", [\n            \"MissionDocument\"\n        ]);\n    }\n    return build();\n}\n// Create a default ability (for unauthenticated users)\nconst createDefaultAbility = ()=>{\n    const { can, build } = new _casl_ability__WEBPACK_IMPORTED_MODULE_1__.AbilityBuilder(_casl_ability__WEBPACK_IMPORTED_MODULE_1__.createMongoAbility);\n    can(\"read\", [\n        \"Mission\",\n        \"Recommendation\",\n        \"Plan\",\n        \"Theme\"\n    ]);\n    return build();\n};\n// Synchronous ability factory for immediate use (fallback)\nfunction createAbilityForUser(user) {\n    const { can, build } = new _casl_ability__WEBPACK_IMPORTED_MODULE_1__.AbilityBuilder(_casl_ability__WEBPACK_IMPORTED_MODULE_1__.createMongoAbility);\n    if (!user) {\n        can(\"read\", [\n            \"Mission\",\n            \"Recommendation\",\n            \"Plan\",\n            \"Theme\"\n        ]);\n        return build();\n    }\n    if (user.isSuperuser) {\n        can(\"manage\", \"all\");\n        return build();\n    }\n    // Basic permissions for authenticated users\n    can(\"read\", [\n        \"Mission\",\n        \"Recommendation\",\n        \"Plan\",\n        \"Theme\",\n        \"Domain\",\n        \"Process\"\n    ]);\n    can(\"read\", \"User\", {\n        id: user.id\n    });\n    can(\"update\", \"User\", {\n        id: user.id\n    });\n    if (user.isStaff) {\n        can(\"create\", [\n            \"Mission\",\n            \"Recommendation\",\n            \"Comment\"\n        ]);\n        can(\"update\", [\n            \"Mission\",\n            \"Recommendation\"\n        ], {\n            createdBy: user.id\n        });\n        can(\"delete\", [\n            \"Comment\"\n        ], {\n            createdBy: user.id\n        });\n        can(\"change\", [\n            \"Mission\",\n            \"Plan\",\n            \"Recommendation\"\n        ]);\n        can(\"add\", [\n            \"MissionDocument\"\n        ]);\n        can(\"edit\", [\n            \"Mission\",\n            \"Plan\",\n            \"Recommendation\"\n        ]);\n    }\n    return build();\n}\n// Default ability for initial load\nconst ability = createDefaultAbility();\n/* harmony default export */ __webpack_exports__[\"default\"] = (ability);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/ability.ts\n"));

/***/ })

});