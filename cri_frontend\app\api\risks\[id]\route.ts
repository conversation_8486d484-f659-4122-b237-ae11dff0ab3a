import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getSession } from '@/lib/auth-custom'

// GET /api/risks/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid risk ID' }, { status: 400 })
    }

    const risk = await prisma.risk.findUnique({
      where: { id },
      include: {
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
            domain: {
              select: {
                id: true,
                title: true,
              }
            },
            process: {
              select: {
                id: true,
                title: true,
              }
            }
          }
        }
      },
    })

    if (!risk) {
      return NextResponse.json({ error: 'Risk not found' }, { status: 404 })
    }

    return NextResponse.json({ data: risk })
  } catch (error) {
    console.error('Error fetching risk:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH /api/risks/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can update risks
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid risk ID' }, { status: 400 })
    }

    const body = await request.json()
    const { title, description, validated } = body

    // Check if risk exists
    const existingRisk = await prisma.risk.findUnique({
      where: { id },
    })

    if (!existingRisk) {
      return NextResponse.json({ error: 'Risk not found' }, { status: 404 })
    }

    // Update risk
    const risk = await prisma.risk.update({
      where: { id },
      data: {
        ...(title !== undefined && { title }),
        ...(description !== undefined && { description }),
        ...(validated !== undefined && { validated }),
        modifiedBy: session.user.id.toString(),
      },
      include: {
        themes: {
          select: {
            id: true,
            title: true,
            validated: true,
            code: true,
          }
        }
      },
    })

    return NextResponse.json({ data: risk })
  } catch (error) {
    console.error('Error updating risk:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/risks/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication - only staff can delete risks
    const session = await getSession(request)
    if (!session || !session.user.isStaff) {
      return NextResponse.json({ error: 'Unauthorized - Staff access required' }, { status: 401 })
    }

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid risk ID' }, { status: 400 })
    }

    // Check if risk exists
    const existingRisk = await prisma.risk.findUnique({
      where: { id },
      include: {
        themes: true,
      },
    })

    if (!existingRisk) {
      return NextResponse.json({ error: 'Risk not found' }, { status: 404 })
    }

    // Check if risk has associated themes
    if (existingRisk.themes.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete risk with associated themes' },
        { status: 400 }
      )
    }

    // Delete risk
    await prisma.risk.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Risk deleted successfully' })
  } catch (error) {
    console.error('Error deleting risk:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
