import { AbilityBuilder, createMongoAbility, MongoAbility } from '@casl/ability';
import { prisma } from '@/lib/prisma';

// Define the subjects that can be managed
type Subjects =
  | 'User'
  | 'Mission'
  | 'Recommendation'
  | 'Plan'
  | 'Theme'
  | 'ArbitratedTheme'
  | 'Domain'
  | 'Process'
  | 'Action'
  | 'Comment'
  | 'Document'
  | 'MissionDocument'
  | 'Arbitration'
  | 'Constat'
  | 'all';

// Define the actions that can be performed
type Actions = 'manage' | 'create' | 'read' | 'update' | 'delete' | 'view' | 'edit' | 'add' | 'change';

export type AppAbility = MongoAbility<[Actions, Subjects]>;

// Cache for user permissions to avoid repeated database queries
const permissionCache = new Map<string, any[]>();

// Define abilities based on user roles and permissions from database
export async function defineAbilitiesFor(user: any): Promise<AppAbility> {
  const { can, cannot, build } = new AbilityBuilder<AppAbility>(createMongoAbility);

  if (!user) {
    // Unauthenticated users - minimal permissions
    can('read', ['Mission', 'Recommendation', 'Plan', 'Theme']);
    return build();
  }

  // Superuser has all permissions
  if (user.isSuperuser) {
    can('manage', 'all');
    return build();
  }

  // Check cache first
  const cacheKey = `${user.id}_${user.updatedAt || user.dateJoined}`;
  let userPermissions = permissionCache.get(cacheKey);

  if (!userPermissions) {
    try {
      // Fetch user roles and permissions from database
      userPermissions = await prisma.userRole.findMany({
        where: { userId: user.id },
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      });

      // Cache the permissions
      permissionCache.set(cacheKey, userPermissions);
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      userPermissions = [];
    }
  }

  // Apply permissions from database
  for (const userRole of userPermissions) {
    for (const rolePermission of userRole.role.rolePermissions) {
      const permission = rolePermission.permission;

      // Apply the permission
      if (permission.conditions) {
        can(permission.action as Actions, permission.subject as Subjects, permission.conditions);
      } else {
        can(permission.action as Actions, permission.subject as Subjects);
      }

      // Apply field-level permissions if specified
      if (permission.fields && permission.fields.length > 0) {
        can(permission.action as Actions, permission.subject as Subjects, permission.fields);
      }
    }
  }

  // Default permissions for authenticated users
  can('read', ['Mission', 'Recommendation', 'Plan', 'Theme', 'Domain', 'Process']);
  can('read', 'User', { id: user.id }); // Users can read their own profile
  can('update', 'User', { id: user.id }); // Users can update their own profile

  // Staff members get additional permissions
  if (user.isStaff) {
    can('create', ['Mission', 'Recommendation', 'Comment']);
    can('update', ['Mission', 'Recommendation'], { createdBy: user.id });
    can('delete', ['Comment'], { createdBy: user.id });
    can('change', ['Mission', 'Plan', 'Recommendation']);
    can('add', ['MissionDocument']);
  }

  return build();
}

// Create a default ability (for unauthenticated users)
export const createDefaultAbility = (): AppAbility => {
  const { can, build } = new AbilityBuilder<AppAbility>(createMongoAbility);
  can('read', ['Mission', 'Recommendation', 'Plan', 'Theme']);
  return build();
};

// Synchronous ability factory for immediate use (fallback)
export function createAbilityForUser(user: any): AppAbility {
  const { can, build } = new AbilityBuilder<AppAbility>(createMongoAbility);

  if (!user) {
    can('read', ['Mission', 'Recommendation', 'Plan', 'Theme']);
    return build();
  }

  if (user.isSuperuser) {
    can('manage', 'all');
    return build();
  }

  // Basic permissions for authenticated users
  can('read', ['Mission', 'Recommendation', 'Plan', 'Theme', 'Domain', 'Process']);
  can('read', 'User', { id: user.id });
  can('update', 'User', { id: user.id });

  if (user.isStaff) {
    can('create', ['Mission', 'Recommendation', 'Comment']);
    can('update', ['Mission', 'Recommendation'], { createdBy: user.id });
    can('delete', ['Comment'], { createdBy: user.id });
    can('change', ['Mission', 'Plan', 'Recommendation']);
    can('add', ['MissionDocument']);
    can('edit', ['Mission', 'Plan', 'Recommendation']);
  }

  return build();
}

// Default ability for initial load
const ability = createDefaultAbility();

export default ability;