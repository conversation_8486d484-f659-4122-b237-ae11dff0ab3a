import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/permissions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search') || ''
    const action = searchParams.get('action')
    const subject = searchParams.get('subject')
    
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { action: { contains: search, mode: 'insensitive' as const } },
        { subject: { contains: search, mode: 'insensitive' as const } },
      ]
    }
    
    if (action) {
      where.action = action
    }
    
    if (subject) {
      where.subject = subject
    }
    
    const [permissions, total] = await Promise.all([
      prisma.permission.findMany({
        where,
        skip,
        take: limit,
        include: {
          rolePermissions: {
            include: {
              role: true,
            },
          },
        },
        orderBy: [{ subject: 'asc' }, { action: 'asc' }],
      }),
      prisma.permission.count({ where }),
    ])
    
    return NextResponse.json({
      data: {
        results: permissions,
        count: total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching permissions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/permissions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, subject, conditions, fields } = body
    
    const permission = await prisma.permission.create({
      data: {
        action,
        subject,
        conditions,
        fields: fields || [],
      },
    })
    
    return NextResponse.json(permission, { status: 201 })
  } catch (error) {
    console.error('Error creating permission:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
