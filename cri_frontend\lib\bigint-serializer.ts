/**
 * Utility functions for handling BigInt serialization in API responses
 * 
 * BigInt values cannot be serialized to JSON by default, so we need to convert them
 * to strings before sending API responses.
 */

/**
 * Recursively converts BigInt values to strings in an object
 * @param obj - The object to process
 * @returns The object with BigInt values converted to strings
 */
export function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }
  
  if (typeof obj === 'bigint') {
    return obj.toString()
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => serializeBigInt(item))
  }
  
  if (typeof obj === 'object') {
    const serialized: any = {}
    for (const [key, value] of Object.entries(obj)) {
      serialized[key] = serializeBigInt(value)
    }
    return serialized
  }
  
  return obj
}

/**
 * Serializes a single structure object (CriStructview)
 * @param structure - The structure object to serialize
 * @returns The structure with BigInt id converted to string
 */
export function serializeStructure(structure: any) {
  return {
    ...structure,
    id: structure.id?.toString() || structure.id,
  }
}

/**
 * Serializes an array of structures
 * @param structures - Array of structure objects
 * @returns Array of structures with BigInt ids converted to strings
 */
export function serializeStructures(structures: any[]) {
  return structures.map(serializeStructure)
}

/**
 * Serializes a CriAgent object
 * @param agent - The agent object to serialize
 * @returns The agent with BigInt idStruct converted to string
 */
export function serializeAgent(agent: any) {
  if (!agent) return agent
  
  return {
    ...agent,
    idStruct: agent.idStruct?.toString() || null,
  }
}

/**
 * Serializes a user object that may contain agent data
 * @param user - The user object to serialize
 * @returns The user with any BigInt values in agent data converted to strings
 */
export function serializeUser(user: any) {
  return {
    ...user,
    userProfile: user.userProfile ? {
      ...user.userProfile,
      agent: serializeAgent(user.userProfile.agent),
    } : null,
  }
}

/**
 * Serializes an array of users
 * @param users - Array of user objects
 * @returns Array of users with BigInt values converted to strings
 */
export function serializeUsers(users: any[]) {
  return users.map(serializeUser)
}

/**
 * Generic function to serialize any object that might contain BigInt values
 * This is the most comprehensive function that handles all cases
 * @param data - The data to serialize
 * @returns The data with all BigInt values converted to strings
 */
export function serializeApiResponse(data: any): any {
  return serializeBigInt(data)
}

/**
 * Serializes a paginated API response
 * @param data - The paginated response data
 * @returns The response with all BigInt values converted to strings
 */
export function serializePaginatedResponse(data: {
  results: any[]
  count: number
  page: number
  limit: number
  totalPages: number
}) {
  return {
    ...data,
    results: data.results.map(item => serializeBigInt(item)),
  }
}
