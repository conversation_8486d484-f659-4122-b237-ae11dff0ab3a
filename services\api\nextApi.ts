// Next.js API Service to replace Django API calls
import { getCookie } from 'cookies-next'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  body?: any
}

class NextApiService {
  private async request<T>(endpoint: string, options: ApiOptions = {}): Promise<T> {
    const { method = 'GET', headers = {}, body } = options
    
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    
    const config: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
        ...(user?.token && { Authorization: `Token ${user.token}` }),
      },
    }
    
    if (body && method !== 'GET') {
      config.body = JSON.stringify(body)
    }
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, config)
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }
    
    return response.json()
  }

  // Mission API methods
  async getMissions(params?: { page?: number; limit?: number; search?: string }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    
    const query = searchParams.toString()
    return this.request(`/missions${query ? `?${query}` : ''}`)
  }

  async getMission(id: number) {
    return this.request(`/missions/${id}`)
  }

  async createMission(data: any) {
    return this.request('/missions', {
      method: 'POST',
      body: data,
    })
  }

  async updateMission(id: number, data: any) {
    return this.request(`/missions/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteMission(id: number) {
    return this.request(`/missions/${id}`, {
      method: 'DELETE',
    })
  }

  // Recommendation API methods
  async getRecommendations(params?: { 
    page?: number; 
    limit?: number; 
    search?: string; 
    missionId?: number 
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.missionId) searchParams.append('missionId', params.missionId.toString())
    
    const query = searchParams.toString()
    return this.request(`/recommendations${query ? `?${query}` : ''}`)
  }

  async getRecommendation(id: number) {
    return this.request(`/recommendations/${id}`)
  }

  async createRecommendation(data: any) {
    return this.request('/recommendations', {
      method: 'POST',
      body: data,
    })
  }

  async updateRecommendation(id: number, data: any) {
    return this.request(`/recommendations/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  async deleteRecommendation(id: number) {
    return this.request(`/recommendations/${id}`, {
      method: 'DELETE',
    })
  }

  // User API methods
  async getUsers(params?: { page?: number; limit?: number; search?: string }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    
    const query = searchParams.toString()
    return this.request(`/users${query ? `?${query}` : ''}`)
  }

  async getUser(id: number) {
    return this.request(`/users/${id}`)
  }

  async createUser(data: any) {
    return this.request('/users', {
      method: 'POST',
      body: data,
    })
  }

  // Plan API methods
  async getPlans(params?: { 
    page?: number; 
    limit?: number; 
    exercise?: number; 
    type?: string 
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.exercise) searchParams.append('exercise', params.exercise.toString())
    if (params?.type) searchParams.append('type', params.type)
    
    const query = searchParams.toString()
    return this.request(`/plans${query ? `?${query}` : ''}`)
  }

  async getPlan(id: number) {
    return this.request(`/plans/${id}`)
  }

  async createPlan(data: any) {
    return this.request('/plans', {
      method: 'POST',
      body: data,
    })
  }

  // Theme API methods
  async getThemes(params?: { 
    page?: number; 
    limit?: number; 
    search?: string; 
    validated?: boolean;
    domainId?: number;
  }) {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.validated !== undefined) searchParams.append('validated', params.validated.toString())
    if (params?.domainId) searchParams.append('domainId', params.domainId.toString())
    
    const query = searchParams.toString()
    return this.request(`/themes${query ? `?${query}` : ''}`)
  }

  async getTheme(id: number) {
    return this.request(`/themes/${id}`)
  }

  async createTheme(data: any) {
    return this.request('/themes', {
      method: 'POST',
      body: data,
    })
  }

  async updateTheme(id: number, data: any) {
    return this.request(`/themes/${id}`, {
      method: 'PATCH',
      body: data,
    })
  }

  // Comment API methods
  async createComment(data: any) {
    return this.request('/comments', {
      method: 'POST',
      body: data,
    })
  }

  // Document API methods
  async uploadDocument(data: FormData) {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    
    const response = await fetch(`${API_BASE_URL}/documents`, {
      method: 'POST',
      headers: {
        ...(user?.token && { Authorization: `Token ${user.token}` }),
      },
      body: data,
    })
    
    if (!response.ok) {
      throw new Error(`Upload Error: ${response.status} ${response.statusText}`)
    }
    
    return response.json()
  }

  async deleteDocument(id: number) {
    return this.request(`/documents/${id}`, {
      method: 'DELETE',
    })
  }
}

export const nextApiService = new NextApiService()
export default nextApiService
