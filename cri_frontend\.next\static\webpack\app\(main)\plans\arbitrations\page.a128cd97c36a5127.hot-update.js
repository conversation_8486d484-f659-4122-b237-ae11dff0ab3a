"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/plans/arbitrations/page",{

/***/ "(app-client)/./app/(main)/plans/arbitrations/page.tsx":
/*!************************************************!*\
  !*** ./app/(main)/plans/arbitrations/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-client)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useNextApi */ \"(app-client)/./hooks/useNextApi.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-client)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_GenericTAbleArbitration__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../(components)/GenericTAbleArbitration */ \"(app-client)/./app/(main)/plans/(components)/GenericTAbleArbitration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst TableDemo = ()=>{\n    _s();\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        pageIndex: 0,\n        pageSize: 5\n    });\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { data: arbitrations, isLoading, error } = (0,_hooks_useNextApi__WEBPACK_IMPORTED_MODULE_1__.useApiArbitrationList)({\n        page: pagination.pageIndex + 1\n    });\n    // const { data: date_create, error: error_create, isMutating: isMutating_create, trigger: trigger_create } = useApiMissionCreate();\n    // const { data, error: error_, isMutating, trigger } = useApiMissionDestroy(1);\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: \"Loading ...\"\n    }, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\arbitrations\\\\page.tsx\",\n        lineNumber: 26,\n        columnNumber: 28\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GenericTAbleArbitration__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                data_: arbitrations,\n                isLoading: isLoading,\n                error: error,\n                data_type: {},\n                pagination: {\n                    \"set\": setPagination,\n                    \"pagi\": pagination\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\arbitrations\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\arbitrations\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\graci_home\\\\cri_frontend\\\\app\\\\(main)\\\\plans\\\\arbitrations\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 9\n    }, undefined);\n};\n_s(TableDemo, \"oHkHpiiAewtHKr5KLf6/rsQT9cI=\", false, function() {\n    return [\n        _hooks_useNextApi__WEBPACK_IMPORTED_MODULE_1__.useApiArbitrationList\n    ];\n});\n_c = TableDemo;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TableDemo);\nvar _c;\n$RefreshReg$(_c, \"TableDemo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./app/(main)/plans/arbitrations/page.tsx\n"));

/***/ })

});