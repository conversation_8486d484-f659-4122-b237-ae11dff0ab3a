 'use client';

import { ApiMissionListEtat } from '@/services/schemas';
import GenericTable from './(components)/GenericTAble';
import { useApiArbitrationList, useApiMissionList, useApiPlanList } from '@/services/api/api/api';
import { $Plan, Plan, MissionSerializerRead } from '@/services/openapi_client';
import { useState } from 'react';
import { MRT_PaginationState } from 'material-react-table';
import { getCookie } from 'cookies-next';

const TableDemo = () => {
    const user = JSON.parse(getCookie('user')?.toString() || '{}')
    const [pagination, setPagination] = useState<MRT_PaginationState>({
        pageIndex:0,
        pageSize: 5, //customize the default page size
      });
    const { data: arbitrations, isLoading :isLoadingCMD,error:error_arbitrations} = useApiArbitrationList({},{ axios: { headers: { Authorization: `Token ${user?.token}` } }});    
    const { data: plans, isLoading:isLoading ,error:error} = useApiPlanList({page:pagination.pageIndex+1},{ axios: { headers: { Authorization: `Token ${user?.token}` } }});
    

    if( isLoading )  return (<div></div>)
    return   (        
        <div className="grid">
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Thèmes arbitrés</span>
                            <div className="text-900 font-medium text-xl">{arbitrations?.data.count! ?? 0 }</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-blue-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-book text-blue-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">24 new </span>
                    <span className="text-500">since last visit</span> */}
                </div>
            </div>
            
            <div className="col-12 lg:col-6 xl:col-3">
                <div className="card mb-0">
                    <div className="flex justify-content-between mb-3">
                        <div>
                            <span className="block text-500 font-medium mb-3">Plans</span>
                            <div className="text-900 font-medium text-xl">{plans?.data.count ?? 0}</div>
                        </div>
                        <div className="flex align-items-center justify-content-center bg-cyan-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                            <i className="pi pi-clock text-cyan-500 text-xl" />
                        </div>
                    </div>
                    {/* <span className="text-green-500 font-medium">520 </span>
                    <span className="text-500">newly registered</span> */}
                </div>
            </div>
            
            <div className="col-12">
                {/* <div className="card"> */}
                    {/* <h5>Missions</h5> */}
                    <GenericTable<Plan> data_={plans} isLoading={isLoading} error={error} data_type={$Plan} pagination={{"set":setPagination,"pagi":pagination}}></GenericTable>                    
                {/* </div> */}
            </div>
        </div>
    );
};

export default TableDemo;
