'use client'

import React from 'react'
import { Card } from 'primereact/card'
import { TabMenu } from 'primereact/tabmenu'
import { useRouter, usePathname } from 'next/navigation'
import { Can } from '@/app/Can'

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()

  const menuItems = [
    {
      label: 'Dashboard',
      icon: 'pi pi-fw pi-home',
      command: () => router.push('/admin')
    },
    {
      label: 'Users',
      icon: 'pi pi-fw pi-users',
      command: () => router.push('/admin/users')
    },
    {
      label: 'Roles',
      icon: 'pi pi-fw pi-shield',
      command: () => router.push('/admin/roles')
    },
    {
      label: 'Permissions',
      icon: 'pi pi-fw pi-key',
      command: () => router.push('/admin/permissions')
    }
  ]

  // Find active index based on current path
  const activeIndex = menuItems.findIndex(item => {
    const path = item.command?.toString().match(/router\.push\('([^']+)'\)/)?.[1]
    return path === pathname
  })

  return (
    <Can I="read" a="User">
      <div className="grid">
        <div className="col-12">
          <div className="card">
            <h5>Administration</h5>
            <TabMenu
              model={menuItems}
              activeIndex={activeIndex >= 0 ? activeIndex : 0}
            />
          </div>
        </div>
        <div className="col-12">
          {children}
        </div>
      </div>
    </Can>
  )
}
