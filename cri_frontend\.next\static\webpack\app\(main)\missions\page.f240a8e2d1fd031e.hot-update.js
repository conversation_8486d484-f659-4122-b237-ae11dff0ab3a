"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/missions/page",{

/***/ "(app-client)/./hooks/useNextApi.ts":
/*!*****************************!*\
  !*** ./hooks/useNextApi.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useApiActionCreate: function() { return /* binding */ useApiActionCreate; },\n/* harmony export */   useApiActionDestroy: function() { return /* binding */ useApiActionDestroy; },\n/* harmony export */   useApiActionList: function() { return /* binding */ useApiActionList; },\n/* harmony export */   useApiActionPartialUpdate: function() { return /* binding */ useApiActionPartialUpdate; },\n/* harmony export */   useApiActionRetrieve: function() { return /* binding */ useApiActionRetrieve; },\n/* harmony export */   useApiActionUpdate: function() { return /* binding */ useApiActionUpdate; },\n/* harmony export */   useApiArbitratedThemeCreate: function() { return /* binding */ useApiArbitratedThemeCreate; },\n/* harmony export */   useApiArbitratedThemeDestroy: function() { return /* binding */ useApiArbitratedThemeDestroy; },\n/* harmony export */   useApiArbitratedThemeList: function() { return /* binding */ useApiArbitratedThemeList; },\n/* harmony export */   useApiArbitratedThemePartialUpdate: function() { return /* binding */ useApiArbitratedThemePartialUpdate; },\n/* harmony export */   useApiArbitratedThemeRetrieve: function() { return /* binding */ useApiArbitratedThemeRetrieve; },\n/* harmony export */   useApiArbitratedThemeUpdate: function() { return /* binding */ useApiArbitratedThemeUpdate; },\n/* harmony export */   useApiArbitrationCreate: function() { return /* binding */ useApiArbitrationCreate; },\n/* harmony export */   useApiArbitrationDestroy: function() { return /* binding */ useApiArbitrationDestroy; },\n/* harmony export */   useApiArbitrationList: function() { return /* binding */ useApiArbitrationList; },\n/* harmony export */   useApiArbitrationPartialUpdate: function() { return /* binding */ useApiArbitrationPartialUpdate; },\n/* harmony export */   useApiArbitrationRetrieve: function() { return /* binding */ useApiArbitrationRetrieve; },\n/* harmony export */   useApiCommentCreate: function() { return /* binding */ useApiCommentCreate; },\n/* harmony export */   useApiCommentDestroy: function() { return /* binding */ useApiCommentDestroy; },\n/* harmony export */   useApiCommentList: function() { return /* binding */ useApiCommentList; },\n/* harmony export */   useApiCommentPartialUpdate: function() { return /* binding */ useApiCommentPartialUpdate; },\n/* harmony export */   useApiCommentRetrieve: function() { return /* binding */ useApiCommentRetrieve; },\n/* harmony export */   useApiDocsCreate: function() { return /* binding */ useApiDocsCreate; },\n/* harmony export */   useApiDocsDestroy: function() { return /* binding */ useApiDocsDestroy; },\n/* harmony export */   useApiDocsUpdate: function() { return /* binding */ useApiDocsUpdate; },\n/* harmony export */   useApiDocumentsList: function() { return /* binding */ useApiDocumentsList; },\n/* harmony export */   useApiDomainCreate: function() { return /* binding */ useApiDomainCreate; },\n/* harmony export */   useApiDomainDestroy: function() { return /* binding */ useApiDomainDestroy; },\n/* harmony export */   useApiDomainList: function() { return /* binding */ useApiDomainList; },\n/* harmony export */   useApiDomainRetrieve: function() { return /* binding */ useApiDomainRetrieve; },\n/* harmony export */   useApiDomainUpdate: function() { return /* binding */ useApiDomainUpdate; },\n/* harmony export */   useApiGoalCreate: function() { return /* binding */ useApiGoalCreate; },\n/* harmony export */   useApiGoalDestroy: function() { return /* binding */ useApiGoalDestroy; },\n/* harmony export */   useApiGoalList: function() { return /* binding */ useApiGoalList; },\n/* harmony export */   useApiGoalRetrieve: function() { return /* binding */ useApiGoalRetrieve; },\n/* harmony export */   useApiGoalUpdate: function() { return /* binding */ useApiGoalUpdate; },\n/* harmony export */   useApiMissionCreate: function() { return /* binding */ useApiMissionCreate; },\n/* harmony export */   useApiMissionDestroy: function() { return /* binding */ useApiMissionDestroy; },\n/* harmony export */   useApiMissionDocsCreate: function() { return /* binding */ useApiMissionDocsCreate; },\n/* harmony export */   useApiMissionDocumentsList: function() { return /* binding */ useApiMissionDocumentsList; },\n/* harmony export */   useApiMissionList: function() { return /* binding */ useApiMissionList; },\n/* harmony export */   useApiMissionPartialUpdate: function() { return /* binding */ useApiMissionPartialUpdate; },\n/* harmony export */   useApiMissionRetrieve: function() { return /* binding */ useApiMissionRetrieve; },\n/* harmony export */   useApiPlanCreate: function() { return /* binding */ useApiPlanCreate; },\n/* harmony export */   useApiPlanDestroy: function() { return /* binding */ useApiPlanDestroy; },\n/* harmony export */   useApiPlanList: function() { return /* binding */ useApiPlanList; },\n/* harmony export */   useApiPlanRetrieve: function() { return /* binding */ useApiPlanRetrieve; },\n/* harmony export */   useApiPlanUpdate: function() { return /* binding */ useApiPlanUpdate; },\n/* harmony export */   useApiProcessCreate: function() { return /* binding */ useApiProcessCreate; },\n/* harmony export */   useApiProcessDestroy: function() { return /* binding */ useApiProcessDestroy; },\n/* harmony export */   useApiProcessList: function() { return /* binding */ useApiProcessList; },\n/* harmony export */   useApiProcessRetrieve: function() { return /* binding */ useApiProcessRetrieve; },\n/* harmony export */   useApiProcessUpdate: function() { return /* binding */ useApiProcessUpdate; },\n/* harmony export */   useApiRecommendationCreate: function() { return /* binding */ useApiRecommendationCreate; },\n/* harmony export */   useApiRecommendationDestroy: function() { return /* binding */ useApiRecommendationDestroy; },\n/* harmony export */   useApiRecommendationList: function() { return /* binding */ useApiRecommendationList; },\n/* harmony export */   useApiRecommendationPartialUpdate: function() { return /* binding */ useApiRecommendationPartialUpdate; },\n/* harmony export */   useApiRecommendationRetrieve: function() { return /* binding */ useApiRecommendationRetrieve; },\n/* harmony export */   useApiRiskCreate: function() { return /* binding */ useApiRiskCreate; },\n/* harmony export */   useApiRiskDestroy: function() { return /* binding */ useApiRiskDestroy; },\n/* harmony export */   useApiRiskList: function() { return /* binding */ useApiRiskList; },\n/* harmony export */   useApiRiskRetrieve: function() { return /* binding */ useApiRiskRetrieve; },\n/* harmony export */   useApiRiskUpdate: function() { return /* binding */ useApiRiskUpdate; },\n/* harmony export */   useApiStructurelqsList: function() { return /* binding */ useApiStructurelqsList; },\n/* harmony export */   useApiStructurelqsRetrieve: function() { return /* binding */ useApiStructurelqsRetrieve; },\n/* harmony export */   useApiThemeCreate: function() { return /* binding */ useApiThemeCreate; },\n/* harmony export */   useApiThemeDestroy: function() { return /* binding */ useApiThemeDestroy; },\n/* harmony export */   useApiThemeList: function() { return /* binding */ useApiThemeList; },\n/* harmony export */   useApiThemePartialUpdate: function() { return /* binding */ useApiThemePartialUpdate; },\n/* harmony export */   useApiThemeRetrieve: function() { return /* binding */ useApiThemeRetrieve; },\n/* harmony export */   useApiThemeUpdate: function() { return /* binding */ useApiThemeUpdate; },\n/* harmony export */   useApiUserCreate: function() { return /* binding */ useApiUserCreate; },\n/* harmony export */   useApiUserList: function() { return /* binding */ useApiUserList; },\n/* harmony export */   useApiUserRetrieve: function() { return /* binding */ useApiUserRetrieve; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-client)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api/nextApi */ \"(app-client)/./services/api/nextApi.ts\");\n// React hooks for Next.js API to replace Django API hooks\n\n\n// Mission hooks\nfunction useApiMissionList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"missions\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMissions(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiMissionRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"missions\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMission(id),\n        enabled: !!id\n    });\n}\nfunction useApiMissionCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createMission(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateMission(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteMission(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// Recommendation hooks\nfunction useApiRecommendationList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"recommendations\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRecommendations(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiRecommendationRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"recommendations\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRecommendation(id),\n        enabled: !!id\n    });\n}\nfunction useApiRecommendationCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createRecommendation(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRecommendationPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateRecommendation(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRecommendationDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteRecommendation(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// User hooks\nfunction useApiUserList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"users\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUsers(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiUserRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"users\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUser(id),\n        enabled: !!id\n    });\n}\nfunction useApiUserCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createUser(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"users\"\n                ]\n            });\n        }\n    });\n}\n// Plan hooks\nfunction useApiPlanList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"plans\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlans(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiPlanRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"plans\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlan(id),\n        enabled: !!id\n    });\n}\nfunction useApiPlanCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createPlan(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiPlanUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updatePlan(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiPlanDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deletePlan(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n        }\n    });\n}\n// Theme hooks\nfunction useApiThemeList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"themes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getThemes(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiThemeRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"themes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getTheme(id),\n        enabled: !!id\n    });\n}\nfunction useApiThemeCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createTheme(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemeUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemePartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\",\n                    variables.id\n                ]\n            });\n        }\n    });\n}\nfunction useApiThemeDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteTheme(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Arbitration hooks\nfunction useApiArbitrationList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrations\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitrations(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiArbitrationRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrations\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitration(id),\n        enabled: !!id\n    });\n}\nfunction useApiArbitrationCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createArbitration(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitrationPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitration(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitrationDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteArbitration(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"plans\"\n                ]\n            });\n        }\n    });\n}\n// Comment hooks\nfunction useApiCommentList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"comments\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComments(params),\n        staleTime: 2 * 60 * 1000\n    });\n}\nfunction useApiCommentRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"comments\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getComment(id),\n        enabled: !!id\n    });\n}\nfunction useApiCommentCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createComment(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiCommentPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateComment(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiCommentDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteComment(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"comments\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n        }\n    });\n}\n// Document hooks\nfunction useApiDocsCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].uploadDocument(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDocsCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { missionId, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].uploadMissionDocuments(missionId, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\",\n                    variables.missionId\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\",\n                    variables.missionId\n                ]\n            });\n        }\n    });\n}\nfunction useApiMissionDocumentsList(missionId) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"mission-documents\",\n            missionId\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMissionDocuments(missionId),\n        enabled: !!missionId\n    });\n}\nfunction useApiDocumentsList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"documents\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getDocuments(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiDocsDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteDocument(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"missions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiDocsUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateDocument(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"documents\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"mission-documents\"\n                ]\n            });\n        }\n    });\n}\n// Action hooks\nfunction useApiActionList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"actions\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getActions(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiActionRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"actions\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAction(id),\n        enabled: !!id\n    });\n}\nfunction useApiActionCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createAction(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateAction(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionPartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateAction(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiActionDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteAction(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"actions\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"recommendations\"\n                ]\n            });\n        }\n    });\n}\n// ArbitratedTheme hooks\nfunction useApiArbitratedThemeList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrated-themes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitratedThemes(params),\n        staleTime: 5 * 60 * 1000\n    });\n}\nfunction useApiArbitratedThemeRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"arbitrated-themes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getArbitratedTheme(id),\n        enabled: !!id\n    });\n}\nfunction useApiArbitratedThemeCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createArbitratedTheme(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemeUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitratedTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemePartialUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateArbitratedTheme(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiArbitratedThemeDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteArbitratedTheme(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrated-themes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"arbitrations\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Domain hooks\nfunction useApiDomainList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"domains\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getDomains(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiDomainRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"domains\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getDomain(id),\n        enabled: !!id\n    });\n}\nfunction useApiDomainCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createDomain(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"domains\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiDomainUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateDomain(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"domains\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"domains\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiDomainDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteDomain(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"domains\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Process hooks\nfunction useApiProcessList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"processes\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getProcesses(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiProcessRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"processes\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getProcess(id),\n        enabled: !!id\n    });\n}\nfunction useApiProcessCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createProcess(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"processes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiProcessUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateProcess(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"processes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"processes\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiProcessDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteProcess(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"processes\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Risk hooks\nfunction useApiRiskList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"risks\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRisks(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiRiskRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"risks\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getRisk(id),\n        enabled: !!id\n    });\n}\nfunction useApiRiskCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createRisk(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"risks\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRiskUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateRisk(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"risks\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"risks\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiRiskDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteRisk(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"risks\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Goal hooks\nfunction useApiGoalList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"goals\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getGoals(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiGoalRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"goals\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getGoal(id),\n        enabled: !!id\n    });\n}\nfunction useApiGoalCreate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].createGoal(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"goals\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiGoalUpdate() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].updateGoal(id, data);\n        },\n        onSuccess: (_, variables)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"goals\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"goals\",\n                    variables.id\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\nfunction useApiGoalDestroy() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (id)=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].deleteGoal(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"goals\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"themes\"\n                ]\n            });\n        }\n    });\n}\n// Structure hooks\nfunction useApiStructurelqsList(params) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"structures\",\n            params\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getStructures(params),\n        staleTime: 10 * 60 * 1000\n    });\n}\nfunction useApiStructurelqsRetrieve(id) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)({\n        queryKey: [\n            \"structures\",\n            id\n        ],\n        queryFn: ()=>_services_api_nextApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getStructure(id),\n        enabled: !!id\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-client)/./hooks/useNextApi.ts\n"));

/***/ })

});